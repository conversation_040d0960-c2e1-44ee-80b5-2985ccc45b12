package com.payne.upms.selector.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.core.enums.UserType;
import com.payne.upms.handler.CustomDataPermissionHandlerImpl;
import com.payne.upms.selector.entity.SelectorAccount;
import com.payne.upms.selector.mapper.SelectorAccountMapper;
import com.payne.upms.selector.service.SelectorAccountService;
import jakarta.annotation.Resource;
import net.sf.jsqlparser.expression.Expression;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service实现
 *
 * 
 * @since 2024-05-27 14:32:46
 */
@Service
public class SelectorAccountServiceImpl extends ServiceImpl<SelectorAccountMapper, SelectorAccount> implements SelectorAccountService {

    @Resource
    private SelectorAccountMapper mapper;

    @Override
    public boolean checkExistSelectorAccount(String itemId, String xgh) {
        if (StrUtil.isNotBlank(xgh)) {
            LambdaQueryWrapper<SelectorAccount> wrapper = new LambdaQueryWrapper<>(SelectorAccount.class).eq(SelectorAccount::getItemId, itemId);
            List<SelectorAccount> selectorAccounts = mapper.selectList(wrapper);
            if (CollectionUtils.isNotEmpty(selectorAccounts)) {
                List<String> xghs = selectorAccounts.stream().map(SelectorAccount::getXgh).collect(Collectors.toList());
                if (xghs.contains(xgh)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<Expression> expressionList(String alias, UserType userType) {
        return new CustomDataPermissionHandlerImpl().expressionList(alias, userType, false);
    }
}
