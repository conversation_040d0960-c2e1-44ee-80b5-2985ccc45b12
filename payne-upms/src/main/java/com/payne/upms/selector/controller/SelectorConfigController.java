package com.payne.upms.selector.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payne.core.annotation.OperationLog;
import com.payne.core.enums.State;
import com.payne.core.utils.SortHelper;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.selector.entity.SelectorConfig;
import com.payne.upms.selector.param.SelectorConfigParam;
import com.payne.upms.selector.service.SelectorConfigService;
import com.payne.upms.system.entity.SysRole;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.payne.upms.utils.SecurityUtil.getRole;

/**
 * 人员选择器/配置控制器
 *
 * 
 * @since 2024-05-10 15:13:59
 */
@RestController
@RequestMapping("/api/selector/selector-config")
public class SelectorConfigController extends BaseController {
    @Resource
    private SelectorConfigService selectorConfigService;

    /**
     * 分页查询选择器配置（权限标识：selector:selectorConfig:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorConfig:list')")
    @GetMapping("/page")
    public PageResult<SelectorConfig> page(SelectorConfigParam param) {
        PageParam<SelectorConfig, SelectorConfigParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = selectorConfigService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部选择器配置（权限标识：selector:selectorConfig:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorConfig:list')")
    @GetMapping()
    public List<SelectorConfig> list(SelectorConfigParam param) {
        PageParam<SelectorConfig, SelectorConfigParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        SysRole role = getRole();
        QueryWrapper<SelectorConfig> queryWrapper = page.getOrderWrapper();
        queryWrapper.eq("STATUS", State.enabled.getValue());
        queryWrapper.nested(qOr -> qOr.like("ROLE", "[]").or().like("ROLE", role.getId()));
        //定制字符base
        if ("base".equals(param.getParamMode())) {
            queryWrapper.nested(qOr -> qOr.isNull("PARAM_MODE").or().eq("PARAM_MODE", ""));
        } else {
            queryWrapper.eq("PARAM_MODE", param.getParamMode());
        }
        return selectorConfigService.list(queryWrapper);
    }

    /**
     * 根据id查询选择器配置（权限标识：selector:selectorConfig:list）
     */
    @PreAuthorize("hasAuthority('selector:selectorConfig:list')")
    @GetMapping("/{id}")
    public SelectorConfig get(@PathVariable("id") String id) {
        return selectorConfigService.getById(id);
    }

    /**
     * 添加或修改选择器配置（权限标识：selector:selectorConfig:operation）
     */
    @PreAuthorize("hasAuthority('selector:selectorConfig:operation')")
    @OperationLog(module = "选择器配置", comments = "保存选择器配置")
    @PostMapping("/operation")
    public void save(@RequestBody SelectorConfig selectorConfig) {
        if (StringUtils.hasLength(selectorConfig.getId())) {
            selectorConfigService.updateById(selectorConfig);
        } else {
            if (selectorConfig.getSort() == null) {
                SortHelper<SelectorConfig> sortHelper = new SortHelper<>(selectorConfigService, "sort");
                selectorConfig.setSort(sortHelper.next());
            }
            selectorConfigService.save(selectorConfig);
        }
    }

    /**
     * 批量删除选择器配置（权限标识：selector:selectorConfig:remove）
     */
    @PreAuthorize("hasAuthority('selector:selectorConfig:remove')")
    @OperationLog(module = "选择器配置", comments = "批量删除选择器配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        selectorConfigService.removeByIds(ids);
    }
}
