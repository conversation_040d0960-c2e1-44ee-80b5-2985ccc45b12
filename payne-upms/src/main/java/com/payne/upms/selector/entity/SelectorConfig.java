package com.payne.upms.selector.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.payne.core.enums.State;
import com.payne.core.enums.UserType;
import com.payne.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.List;

/**
 * 选择器配置
 *
 * 
 * @since 2024-05-10 15:13:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(value = "SELECTOR_CONFIG", autoResultMap = true)
//@Entity
//@Table(name = "SELECTOR_CONFIG")
public class SelectorConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * key,匹配UserInfo查询
     */
    @Column(name = "KEY")
    @TableField("KEY")
    private String key;

    /**
     * 选项代码
     */
    @Column(name = "NAME")
    @TableField("NAME")
    private String name;

    /**
     * 选项名称
     */
    @Column(name = "TITLE")
    @TableField("TITLE")
    private String title;

    /**
     * 数据接口
     */
    @Column(name = "URL")
    @TableField("URL")
    private String url;

    /**
     * 字段配置
     */
    @Column(name = "COLUMNS")
    @TableField("COLUMNS")
    private String columns;

    /**
     * 授权角色
     */
    @Column(name = "ROLE")
    @TableField(value = "ROLE", typeHandler = JacksonTypeHandler.class)
    private List<String> role;

    /**
     * 排序
     */
    @Column(name = "SORT")
    @TableField("SORT")
    private Integer sort;

    /**
     * 状态（1：启用, 0: 停用）
     */
    @Column(name = "STATUS", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "STATUS", jdbcType = JdbcType.INTEGER)
    private State status;

    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE", columnDefinition = ColumnType.NUMBER_1)
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;

    /**
     * 参数模块
     */
    @Column(name = "PARAM_MODE")
    @TableField("PARAM_MODE")
    private String paramMode;
}
