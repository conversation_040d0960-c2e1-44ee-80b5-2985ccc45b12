package com.payne.upms.selector.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.upms.selector.entity.SelectorConfig;
import com.payne.upms.selector.mapper.SelectorConfigMapper;
import com.payne.upms.selector.service.SelectorConfigService;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 选择器配置Service实现
 *
 * 
 * @since 2024-05-10 15:13:59
 */
@Service
public class SelectorConfigServiceImpl extends ServiceImpl<SelectorConfigMapper, SelectorConfig> implements SelectorConfigService {

    @Resource
    private SelectorConfigMapper mapper;

}
