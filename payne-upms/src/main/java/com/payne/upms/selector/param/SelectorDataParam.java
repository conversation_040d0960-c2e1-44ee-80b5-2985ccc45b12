package com.payne.upms.selector.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.enums.JudgeMark;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 选择器数据查询参数
 *
 * 
 * @since 2024-05-10 15:13:59
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SelectorDataParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 选择项名称
     */
    @QueryField(type = QueryType.LIKE)
    private String name;

    /**
     * 选择项值
     */
    @QueryField(type = QueryType.EQ)
    private String value;

    /**
     * 选择器类型key
     */
    @QueryField(type = QueryType.EQ)
    private String configKey;

    /**
     * 选择器类型名称
     */
    @QueryField(type = QueryType.EQ)
    private String configName;

    /**
     * 关联项目id
     */
    @QueryField(type = QueryType.EQ)
    private String itemId;

    /**
     * 是否并联
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfbl;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }
}
