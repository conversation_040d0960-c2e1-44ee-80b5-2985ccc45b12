package com.payne.upms.selector.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.core.enums.JudgeMark;
import com.payne.core.enums.UserType;
import com.payne.upms.selector.entity.SelectorData;
import com.payne.upms.selector.param.SelectorParam;
import com.payne.upms.system.entity.UserInfo;

import java.util.List;

/**
 * 选择器数据Service
 *
 * 
 * @since 2024-05-10 15:13:59
 */
public interface SelectorDataService extends IService<SelectorData> {

    void saveSelectotData(String itemId, JudgeMark sfbl, List<SelectorData> selectorDatas) throws Exception;

    void removeSelectotData(List<String> ids);

    /**
     * 根据选择起数据查询用户列表(注:未过滤数据权限!!!)
     *
     * @param param 参数
     * @return {@link List }<{@link UserInfo }>
     */
    List<UserInfo> queryUserListBySelData(SelectorParam param);

    /**
     * 根据selectorDataList查询权限范围内用户列表
     *
     * @param selectorDataList 选择器数据列表
     * @return {@link List }<{@link UserInfo }>
     */
    List<UserInfo> queryUserListBySelDataWithPermission(List<SelectorData> selectorDataList);

    QueryWrapper<UserInfo> queryUserQueryWrapperBySelData(SelectorParam param);

    QueryWrapper<UserInfo> queryUserQueryWrapperBySelData(List<SelectorData> selectorDataList);

    QueryWrapper<SelectorData> querySelectorDataWrapper(SelectorParam param);

    /**
     * 检查是否在选择器数据范围内 @Deprecated 直接判断SelectorAccount
     *
     * @param param    参数
     * @param userInfo 用户信息
     * @return boolean
     */
    @Deprecated
    boolean checkExistSelectorData(SelectorParam param,UserInfo userInfo);

    /**
     * 按项目id获取不同用户类型
     *
     * @param itemId 项目id
     * @return {@link List }<{@link UserType }>
     */
    public List<UserType> getDistinctUserTypesByItemId(String itemId);
}
