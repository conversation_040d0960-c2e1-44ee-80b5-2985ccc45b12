package com.payne.upms.selector.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.core.enums.UserType;
import com.payne.upms.selector.entity.SelectorAccount;
import net.sf.jsqlparser.expression.Expression;

import java.util.List;

/**
 * Service
 *
 * 
 * @since 2024-05-27 14:32:46
 */
public interface SelectorAccountService extends IService<SelectorAccount> {

    boolean checkExistSelectorAccount(String itemId, String xgh);

    List<Expression> expressionList(String alias, UserType userType);
}
