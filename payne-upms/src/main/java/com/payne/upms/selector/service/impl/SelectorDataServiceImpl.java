package com.payne.upms.selector.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.core.config.AsyncConfig;
import com.payne.core.enums.JudgeMark;
import com.payne.core.enums.UserType;
import com.payne.core.utils.CommonUtil;
import com.payne.upms.selector.entity.SelectorAccount;
import com.payne.upms.selector.entity.SelectorData;
import com.payne.upms.selector.mapper.SelectorDataMapper;
import com.payne.upms.selector.param.SelectorParam;
import com.payne.upms.selector.service.SelectorAccountService;
import com.payne.upms.selector.service.SelectorDataService;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.mapper.UserInfoMapper;
import com.payne.upms.system.service.UserInfoService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 选择器数据Service实现
 *
 * 
 * @since 2024-05-10 15:13:59
 */
@Service
public class SelectorDataServiceImpl extends ServiceImpl<SelectorDataMapper, SelectorData> implements SelectorDataService {

    @Resource
    private SelectorDataMapper mapper;
    @Resource
    protected UserInfoMapper userInfoMapper;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private SelectorAccountService selectorAccountService;

    @Override
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void saveSelectotData(String itemId, JudgeMark sfbl, List<SelectorData> selectorDatas) throws Exception{
//        if (CollectionUtils.isEmpty(selectorDatas))
//            AssertUtil.throwMessage("对象选择器数据不能为空");
        selectorDatas.forEach(selectorData -> {
            selectorData.setItemId(itemId);
            selectorData.setSfbl(sfbl);
        });
        remove(new LambdaQueryWrapper<SelectorData>().eq(SelectorData::getItemId, itemId));
        saveOrUpdateBatch(selectorDatas);
        selectorAccountService.remove(new LambdaQueryWrapper<SelectorAccount>().eq(SelectorAccount::getItemId, itemId));
        //保存名单
        List<UserInfo> userInfos = queryUserListBySelDataWithPermission(selectorDatas);
        if (CollectionUtils.isNotEmpty(userInfos)) {
            selectorAccountService.saveOrUpdateBatch(userInfos.stream().map(user -> {
                SelectorAccount selectorAccount = new SelectorAccount();
                selectorAccount.setItemId(itemId);
                selectorAccount.setXgh(user.getXgh());
                return selectorAccount;
            }).collect(Collectors.toList()));
        }
        this.saveOrUpdateBatch(selectorDatas);
    }

    @Override
    @Async(AsyncConfig.ASYNC_EXECUTOR)
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void removeSelectotData(List<String> ids) {
        remove(new LambdaQueryWrapper<SelectorData>().in(SelectorData::getItemId, ids));
        selectorAccountService.remove(new LambdaQueryWrapper<SelectorAccount>().in(SelectorAccount::getItemId, ids));
    }

    @Override
    public List<UserInfo> queryUserListBySelData(SelectorParam param) {
        return userInfoService.list(queryUserQueryWrapperBySelData(param));
//        return userInfoMapper.selectListWithPermission(queryUserQueryWrapperBySelData(param));
    }

    @Override
    public List<UserInfo> queryUserListBySelDataWithPermission(List<SelectorData> selectorDataList) {
//        return userInfoService.list(queryUserQueryWrapperBySelData(selectorDataList));
        return userInfoMapper.selectListWithPermission(queryUserQueryWrapperBySelData(selectorDataList));
    }

    @Override
    public QueryWrapper<UserInfo> queryUserQueryWrapperBySelData(SelectorParam param) {
        LambdaQueryWrapper<SelectorData> wrapper = new QueryWrapper<SelectorData>().lambda()
                .eq(SelectorData::getItemId, param.getItemId())
                .in(StrUtil.isNotBlank(param.getUserType()), SelectorData::getUserType, CommonUtil.toIntSet(param.getUserType()));
        List<SelectorData> selectorDataList = this.list(wrapper);
        return queryUserQueryWrapperBySelData(selectorDataList);

    }

    @Override
    public QueryWrapper<UserInfo> queryUserQueryWrapperBySelData(List<SelectorData> selectorDataList) {
        QueryWrapper<UserInfo> userInfoQueryWrapper = new QueryWrapper<>();
        if (CollectionUtils.isNotEmpty(selectorDataList)) {
            SelectorData selData = selectorDataList.get(0);
            Map<String, Set<String>> selectorDataMap = new HashMap<>();
            selectorDataList.forEach(selectorData -> {
                String key = selectorData.getUserType().getValue() + "_" + selectorData.getConfigKey();
                Set<String> strings = selectorDataMap.get(key);
                if (CollectionUtils.isEmpty(strings)) {
                    strings = new HashSet<>();
                }
                strings.add(selectorData.getValue());
                selectorDataMap.put(key, strings);
            });

//            JudgeMark sfbl = param.getSfbl();
            JudgeMark sfbl = selData.getSfbl();
            //是否并联
            if (sfbl != null && sfbl.getValue() == 1) {
                selectorDataMap.forEach((key, value) -> {
                    String userType = key.split("_")[0];
                    String filed = key.split("_")[1];
                    if ("roleId".equals(filed)) {
                        filed = "ROLE_ID";
                    }
                    if (CollectionUtils.isNotEmpty(value)) {
                        String finalFiled = filed;
                        userInfoQueryWrapper.and(wrapperAnd -> wrapperAnd.eq("USER_TYPE", userType).in(finalFiled, value));
                    }
                });
            } else {
                selectorDataMap.forEach((key, value) -> {
                    String userType = key.split("_")[0];
                    String filed = key.split("_")[1];
                    if ("roleId".equals(filed)) {
                        filed = "ROLE_ID";
                    }
                    if (CollectionUtils.isNotEmpty(value)) {
                        String finalFiled = filed;
                        userInfoQueryWrapper.or(wrapperOr -> wrapperOr.eq("USER_TYPE", userType).in(finalFiled, value));
                    }
                });
            }
        }
        return userInfoQueryWrapper;
    }

    @Override
    public QueryWrapper<SelectorData> querySelectorDataWrapper(SelectorParam param) {
        LambdaQueryWrapper<SelectorData> wrapper = new QueryWrapper<SelectorData>().lambda()
                .eq(SelectorData::getItemId, param.getItemId())
                .in(StrUtil.isNotBlank(param.getUserType()), SelectorData::getUserType, CommonUtil.toIntSet(param.getUserType()));
        List<SelectorData> selectorDataList = this.list(wrapper);
        QueryWrapper<SelectorData> selectorDataQueryWrapper = new QueryWrapper<>();
        if (CollectionUtils.isNotEmpty(selectorDataList)) {
            SelectorData selData = selectorDataList.get(0);
            Map<String, Set<String>> selectorDataMap = new HashMap<>();
            selectorDataList.forEach(selectorData -> {
                String key = selectorData.getUserType().getValue() + "_" + selectorData.getConfigKey();
                Set<String> strings = selectorDataMap.get(key);
                if (CollectionUtils.isEmpty(strings)) {
                    strings = new HashSet<>();
                }
                strings.add(selectorData.getValue());
                selectorDataMap.put(key, strings);
            });

//            JudgeMark sfbl = param.getSfbl();
            JudgeMark sfbl = selData.getSfbl();
            //是否并联
            if (sfbl != null && sfbl.getValue() == 1) {
                selectorDataMap.forEach((key, value) -> {
                    String userType = key.split("_")[0];
                    String filed = key.split("_")[1];
                    if (CollectionUtils.isNotEmpty(value)) {
                        selectorDataQueryWrapper.and(wrapperAnd -> wrapperAnd.eq("USER_TYPE", userType).eq("CONFIG_KEY", filed).in("VALUE", value));
                    }
                });
            } else {
                selectorDataMap.forEach((key, value) -> {
                    String userType = key.split("_")[0];
                    String filed = key.split("_")[1];
                    if (CollectionUtils.isNotEmpty(value)) {
                        selectorDataQueryWrapper.or(wrapperOr -> wrapperOr.eq("USER_TYPE", userType).eq("CONFIG_KEY", filed).in("VALUE", value));
                    }
                });

            }
        }
        return selectorDataQueryWrapper;
    }

    @Override
    public boolean checkExistSelectorData(SelectorParam param, UserInfo userInfo) {
        if (Objects.nonNull(userInfo)) {
            List<UserInfo> userInfos = this.queryUserListBySelData(param);
            if (CollectionUtils.isNotEmpty(userInfos)) {
                List<String> xghs = userInfos.stream().map(UserInfo::getXgh).collect(Collectors.toList());
                if (xghs.contains(userInfo.getXgh())) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<UserType> getDistinctUserTypesByItemId(String itemId) {
        return this.baseMapper.selectList(
                        new QueryWrapper<SelectorData>()
                                .select("DISTINCT user_type")
                                .eq("item_id", itemId)
                )
                .stream()
                .map(SelectorData::getUserType)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

}
