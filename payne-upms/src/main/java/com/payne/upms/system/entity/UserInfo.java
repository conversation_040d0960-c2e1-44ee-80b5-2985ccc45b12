package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息表
 */
//@EncryptedTable
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("USER_INFO")
@Table(name = "USER_INFO")
public class UserInfo extends BaseUserInfo {
    private static final long serialVersionUID = 1L;


}
