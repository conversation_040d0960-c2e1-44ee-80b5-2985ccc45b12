package com.payne.upms.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payne.upms.system.entity.Menu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 菜单Mapper
 */
public interface SysMenuMapper extends BaseMapper<Menu> {
    public List<Menu> childList(@Param("menuId") String menuId, @Param("menuType") Integer menuType);
    public List<Menu> getAllChildList(@Param("menuId") String menuId);
    public List<Menu> getAllParentList(@Param("menuId") String menuId);
}
