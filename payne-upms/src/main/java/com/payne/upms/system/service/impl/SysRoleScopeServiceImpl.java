package com.payne.upms.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.SysRoleScope;
import com.payne.upms.system.mapper.SysRoleScopeMapper;
import com.payne.upms.system.param.SysRoleScopeParam;
import com.payne.upms.system.service.SysRoleScopeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色标识Service实现
 *
 */
@Service
public class SysRoleScopeServiceImpl extends ServiceImpl<SysRoleScopeMapper, SysRoleScope> implements SysRoleScopeService {

    @Override
    public PageResult<SysRoleScope> pageRel(SysRoleScopeParam param) {
        PageParam<SysRoleScope, SysRoleScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<SysRoleScope> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<SysRoleScope> listRel(SysRoleScopeParam param) {
        List<SysRoleScope> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<SysRoleScope, SysRoleScopeParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public SysRoleScope getByIdRel(String name) {
        SysRoleScopeParam param = new SysRoleScopeParam();
        param.setName(name);
        return param.getOne(baseMapper.selectListRel(param));
    }

}
