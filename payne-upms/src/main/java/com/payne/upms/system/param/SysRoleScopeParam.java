package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色标识查询参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysRoleScopeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 角色标识名称
     */
    @QueryField(type = QueryType.EQ)
    private String name;

    /**
     * 角色标识名称对应类型（暂时保留）
     */
    private String type;

}
