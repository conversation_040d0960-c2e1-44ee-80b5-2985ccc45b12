package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统用户角色关系查询参数
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysAccountRoleParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    /**
     * 账户ID
     */
    private String accountId;

    /**
     * 角色ID
     */
    private String roleId;

}
