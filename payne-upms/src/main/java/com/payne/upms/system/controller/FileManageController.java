package com.payne.upms.system.controller;

import com.mongodb.client.gridfs.model.GridFSFile;
import com.payne.core.constant.Constants;
import com.payne.core.enums.ContentTypeDisposition;
import com.payne.core.utils.EasyExcelHelper;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.core.web.ExcelImportError;
import com.payne.core.web.GridFsService;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;

/**
 * 文件管理
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
public class FileManageController extends BaseController {
    @Resource
    private GridFsService gridFsService;

    /**
     * 文件上传
     *
     * @param file
     * @return
     */
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public @ResponseBody ApiResult<?> upload(@RequestParam("file") MultipartFile... file) {
        String fileString = gridFsService.save(null, file);
        return success(Constants.RESULT_OK_MSG,fileString);
    }

    /**
     * 删除文件
     *
     * @param id 文件ID
     * @return
     */
    @RequestMapping(value = "/remove", method = RequestMethod.POST)
    public @ResponseBody ApiResult<?> remove(String id) {
        if (!StringUtils.hasLength(id))
            return fail();
        gridFsService.remove(id);
        return success();
    }

    /**
     * 文件下载或预览
     *
     * @param disposition (attachment/inline)
     * @param id
     * @param response
     */
    @RequestMapping(value = "/{disposition}/{id}", method = RequestMethod.GET)
    public void preview(@PathVariable("disposition") ContentTypeDisposition disposition, @PathVariable("id") String id,
                        HttpServletResponse response) {
        GridFSFile file = gridFsService.getGridFSFile(id);
        if (file != null) {
            GridFsResource gridFsResource = gridFsService.getGridFsResource(file);
            if (gridFsResource != null) {
                try {
                    String contentType = String.valueOf(file.getMetadata().get("_contentType"));
                    if (!StringUtils.hasLength(contentType))
                        contentType = "application/octet-stream";
                    String fileName = StringUtils.hasLength(file.getFilename()) ? file.getFilename() : id;
                    String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                    response.setCharacterEncoding("UTF-8");
                    response.setContentType(contentType);
                    response.setHeader("Content-Disposition", disposition + "; filename*=UTF-8''" + encodedFileName);
                    InputStream input = gridFsResource.getInputStream();
                    ServletOutputStream output = response.getOutputStream();
                    IOUtils.copy(input, output);
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 导入错误信息下载
     *
     * @param id
     * @param response
     */
    @RequestMapping(value = "/importErrorExcel", method = RequestMethod.GET)
    public void importErrorExcel(@RequestParam String id, HttpServletResponse response) {
        new EasyExcelHelper<ExcelImportError>() {
        }.outErrorInfoExcel(id, response);
    }
}
