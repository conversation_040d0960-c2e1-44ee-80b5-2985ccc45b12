package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.enums.Gender;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统账户信息查询参数
 *
 */
//@EncryptedTable
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SysAccountParam extends BaseParam {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @QueryField(type = QueryType.EQ)
    private String id;
    /**
     * 账户是否过期
     */
    private Boolean accountNonExpired;
    /**
     * 是否锁定
     */
    private Boolean accountNonLocked;
    /**
     * 密码是否过期
     */
    private Boolean credentialsNonExpired;
    /**
     * 是否启用
     */
    private Boolean enabled;
    /**
     * 用户名
     */
    @QueryField(type = QueryType.EQ)
    private String username;
    /**
     * 姓名
     */
    private String realName;
    /**
     * 网名
     */
    private String onlineName;
    /**
     * 性别（男：1， 女：2）
     */
    @QueryField(type = QueryType.EQ)
    private Gender gender;
    /**
     * 激活标识
     */
    private Boolean activeFlag;
    /**
     * 证件类型, 证件号码
     */
    private String idType;
//    @EncryptedColumn
    @QueryField(type = QueryType.EQ)
    private String idCode;
    /**
     * 所属部门名称
     */
    private String deptName;
    /**
     * 手机号
     */
    @QueryField(type = QueryType.EQ)
    private String telMobile;
    /**
     * 邮件
     */
    @QueryField(type = QueryType.EQ)
    private String email;
    /**
     * 账户使用时间范围
     */
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    /**
     * 最后更新密码时间
     */
    private LocalDateTime passwordLastUpdateTime;
    /**
     * 角色ID
     */
    @QueryField(ignore = true)
    private String roleId;
}
