package com.payne.upms.system.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.param.UserInfoParam;

import java.util.List;

/**
 * 用户信息表Service
 */
public interface UserInfoService extends IService<UserInfo> {
    /**
     * 根据id查询
     *
     * @param xgh 学号/工号，对应账户表username
     * @return UserInfo
     */
    UserInfo get(String xgh);

    List<UserInfo> list(UserInfoParam param);

    /**
     * 构建查询 wrapper
     *
     * @param param         用户参数
     * @param columns       查询字段
     * @return
     */
    MPJLambdaWrapper<UserInfo> getQueryWrapper(UserInfoParam param, String... columns);

    /**
     * 构建查询 wrapper
     *
     * @param param         用户参数
     * @param sFunction     查询字段
     * @return
     */
    MPJLambdaWrapper<UserInfo> getQueryWrapper(UserInfoParam param,SFunction<UserInfo, ?>[] sFunction);

    void createUserAndAccountInfo(UserInfo userInfo, boolean checkAccountInfo, String passwordPattern);

}
