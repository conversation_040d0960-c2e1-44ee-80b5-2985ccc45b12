<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payne.upms.system.mapper.SysRoleScopeMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYS_ROLE_SCOPE a
        <where>
            <if test="param.name != null">
                AND a.NAME = #{param.name}
            </if>
            <if test="param.type != null">
                AND a.TYPE LIKE '%'||#{param.type}||'%'
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.payne.upms.system.entity.SysRoleScope">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.payne.upms.system.entity.SysRoleScope">
        <include refid="selectSql"></include>
    </select>

</mapper>
