package com.payne.upms.system.result;

import com.payne.upms.system.entity.SysRole;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;

/**
 * 登录返回结果
 *
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * access_token
     */
    private String access_token;
    /**
     * 用户信息
     */
    private HashMap<String, String> userInfo;

    public static LoginResult build(String access_token, String realName, SysRole role) {
        HashMap<String, String> hashMap = new HashMap() {{
            put("realName", realName);
            put("roleId", role.getId());
            put("roleName", role.getName());
        }};
        return new LoginResult(access_token, hashMap);
    }
}
