package com.payne.upms.system.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.LoginRecord;
import com.payne.upms.system.param.LoginRecordParam;
import com.payne.upms.system.service.LoginRecordService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 登录日志控制器
 *
 */
@RestController
@RequestMapping("/api/system/login-record")
public class LoginRecordController extends BaseController {
    @Resource
    private LoginRecordService loginRecordService;

    /**
     * 分页查询登录日志
     * 权限标识：sys:login-record:list
     *
     * @param param
     * @return
     */
    @PreAuthorize("hasAuthority('sys:login-record:list')")
    @OperationLog
    @GetMapping("/page")
    public PageResult<LoginRecord> page(LoginRecordParam param) {
        return loginRecordService.queryPage(param);
    }

    /**
     * 删除登录日志
     * 权限标识：sys:login-record:remove
     *
     * @param ids
     */
    @PreAuthorize("hasAuthority('sys:login-record:remove')")
    @OperationLog(module = "登录日志管理", comments = "批量删除")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        loginRecordService.remove(ids);
    }

}
