package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 系统参数
 *
 * 
 * @since 2024-04-09 11:09:28
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_PARAM")
@Table(name = "SYS_PARAM")
public class SysParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 参数名称
     */
    @NotEmpty(message = "参数名称不能为空")
    @Column(name = "PARAM_NAME", columnDefinition = "VARCHAR2(100) UNIQUE")
    @TableField("PARAM_NAME")
    private String paramName;

    /**
     * 参数值
     */
    @NotEmpty(message = "参数值不能为空")
    @Column(name = "PARAM_VALUE")
    @TableField("PARAM_VALUE")
    private String paramValue;

    /**
     * 参数模块
     */
    @Column(name = "PARAM_MODE")
    @TableField("PARAM_MODE")
    private String paramMode;

    /**
     * 备注
     */
    @Column(name = "DESCRIPTION", columnDefinition = "VARCHAR2(512)")
    @TableField("DESCRIPTION")
    private String description;

    /**
     * 附件信息
     */
    @Column(name = "ATTACHMENT", columnDefinition = "VARCHAR2(512)")
    @TableField("ATTACHMENT")
    private String attachment;
    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @TableField("create_time")
    private LocalDateTime createTime;
    /**
     * 删除的附件ID
     */
    @Transient
    @TableField(exist = false)
    private String deleteFileIds;
}
