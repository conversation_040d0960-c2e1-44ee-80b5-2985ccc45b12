package com.payne.upms.system.controller;

import com.payne.core.annotation.IgnoreResponseBodyAdvice;
import com.payne.core.config.ConfigProperties;
import com.payne.core.constant.ErrorInfo;
import com.payne.core.utils.AssertUtil;
import com.payne.core.utils.CommonUtil;
import com.payne.core.utils.JwtSubject;
import com.payne.core.utils.JwtUtil;
import com.payne.core.utils.rsa.DecryptMap;
import com.payne.core.utils.rsa.RSADecryptException;
import com.payne.core.utils.rsa.SecJS;
import com.payne.core.web.BaseController;
import com.payne.core.web.MyMediaType;
import com.payne.upms.system.entity.*;
import com.payne.upms.system.param.LoginParam;
import com.payne.upms.system.result.CaptchaResult;
import com.payne.upms.system.result.LoginResult;
import com.payne.upms.system.result.LoginUserResult;
import com.payne.upms.system.service.LoginRecordService;
import com.payne.upms.system.service.SysAccountRoleService;
import com.payne.upms.system.service.SysAccountService;
import com.payne.upms.system.service.UserInfoService;
import com.payne.upms.utils.UserInfoUtil;
import com.wf.captcha.SpecCaptcha;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.payne.upms.utils.SecurityUtil.getAccount;


/**
 * 登录认证控制器
 *
 * 
 * @since 2018-12-24 16:10:11
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class MainController extends BaseController {
    @Resource
    private ConfigProperties configProperties;
    @Resource
    private LoginRecordService loginRecordService;
    @Resource
    private SysAccountService accountService;
    @Resource
    private SysAccountRoleService accountRoleService;
    @Resource
    private UserInfoService userInfoService;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;


    /**
     * 用户登录
     *
     * @param param
     * @param request
     * @return
     */
    @PostMapping(value = "/login")
    public LoginResult login(@RequestBody LoginParam param, HttpServletRequest request) {
        AssertUtil.hasLength(param.getCid(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getCode(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getUsername(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        AssertUtil.hasLength(param.getPassword(), ErrorInfo.MISSING_REQUIRED_FIELD_PARAMETERS);
        DecryptMap usernameDecryptMap = null;
        DecryptMap passwordDecryptMap = null;
        DecryptMap cidDecryptMap = null;
        DecryptMap codeDecryptMap = null;
        try {
            usernameDecryptMap = SecJS.checkDynamicKeyAndGet(param.getUsername());
            passwordDecryptMap = SecJS.checkDynamicKeyAndGet(param.getPassword());
            cidDecryptMap = SecJS.checkDynamicKeyAndGet(param.getCid());
            codeDecryptMap = SecJS.checkDynamicKeyAndGet(param.getCode());
        } catch (RSADecryptException e) {
            log.error(e.getMessage());
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        } finally {
            if (usernameDecryptMap != null)
                SecJS.clearDynamicKey(usernameDecryptMap.getKey());
        }

        String cid = cidDecryptMap.getValue();
        String code = codeDecryptMap.getValue();
        String username = usernameDecryptMap.getValue();
        String password = passwordDecryptMap.getValue();
        Object val = redisTemplate.opsForValue().get(cid);
        boolean mark = (val != null && StringUtils.hasLength(code) && String.valueOf(val).equalsIgnoreCase(code));
        AssertUtil.isTrue(mark, ErrorInfo.CAPTCHA_ERROR_OR_EXPIRED);
        redisTemplate.delete(cid);

        SysAccount account = accountService.getByUsername(username);
        if (account == null) {
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD, request);
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        }

        if (!accountService.comparePassword(account.getPassword(), password)) {
            loginRecordService.saveAsync(username, LoginRecord.TYPE_ERROR, ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD, request);
            AssertUtil.throwMessage(ErrorInfo.INCORRECT_USERNAME_OR_PASSWORD);
        }
        loginRecordService.saveAsync(username, LoginRecord.TYPE_LOGIN, null, request);
        // 签发token
        Long expireTime = configProperties.getTokenExpireTime();
        String access_token = JwtUtil.buildToken(new JwtSubject(username),
                expireTime, configProperties.getTokenKey());
        redisTemplate.opsForValue().set(account.getUsername(), account, expireTime, TimeUnit.SECONDS);
        return LoginResult.build(access_token, account.getRealName(), account.getRole());
    }

    /**
     * 退出登录
     *
     * @param request
     */
    @GetMapping(value = "/logout")
    public void logout(HttpServletRequest request) {
        SysAccount account = getAccount();
        if (account != null) {
            loginRecordService.saveAsync(account.getUsername(), LoginRecord.TYPE_LOGOUT, null, request);
            redisTemplate.delete(account.getUsername());
            redisTemplate.delete(account.getUsername() + "_USER_DATA_SCOPE");
        }
    }


    /**
     * 获取登录用户信息
     *
     * @return
     */
    @PostMapping(value = "/auth/user")
    public LoginUserResult userInfo() {
        SysAccount account = getAccount();
//        UserInfo userInfo = userInfoService.get(account.getUsername());
        UserInfo userInfo = UserInfoUtil.convertUserInfo(account);
        List<Menu> authorities = account.getAuthorities();
        String parentId = "11111111111111111111111111111111";   // 接口目录parentId
        if (!CollectionUtils.isEmpty(authorities))
            authorities.removeIf(m -> Objects.equals(0, m.getMenuType()) && Objects.equals(parentId, m.getParentId()));
        return new LoginUserResult(userInfo, account.getRole(), authorities);
    }

    /**
     * 获取用户角色列表
     *
     * @return
     */
    @PostMapping(value = "/auth/userRoleList")
    public List<SysRole> userRoleList() {
        SysAccount account = getAccount();
        return accountRoleService.selectByAccountId(account.getId(), null);
    }

    /**
     * 切换角色
     *
     * @param roleId
     */
    @PostMapping("/auth/switch-role/{roleId}")
    public void switchRole(@PathVariable String roleId) {
        SysAccount account = getAccount();
        List<SysRole> roleList = accountRoleService.selectByAccountId(account.getId(), roleId);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(roleList), "操作失败, 角色错误");
        account = accountService.getByUsernameAndRoleId(account.getUsername(), roleId);
        redisTemplate.opsForValue().set(account.getUsername(), account, configProperties.getTokenExpireTime(), TimeUnit.SECONDS);
    }


    /**
     * 图形验证码
     *
     * @return
     */
    @GetMapping(value = "/captcha")
    public CaptchaResult captcha() {
        SpecCaptcha specCaptcha = new SpecCaptcha(130, 48, 5);
        String cid = CommonUtil.getUUID();
        redisTemplate.opsForValue().set(cid, specCaptcha.text().toLowerCase(), 60, TimeUnit.SECONDS);
        return new CaptchaResult(cid, specCaptcha.toBase64());
    }

    @IgnoreResponseBodyAdvice
    @GetMapping(value = "/sec_js", produces = {MyMediaType.TEXT_JAVASCRIPT_VALUE})
    public String secJs() {
        return SecJS.newDynamicKeyScript();
    }
}
