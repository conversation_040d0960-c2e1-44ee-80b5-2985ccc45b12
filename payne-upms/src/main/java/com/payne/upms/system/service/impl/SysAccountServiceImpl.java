package com.payne.upms.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.core.constant.ErrorInfo;
import com.payne.core.utils.AssertUtil;
import com.payne.core.utils.CommonUtil;
import com.payne.core.utils.rsa.DecryptMap;
import com.payne.core.utils.rsa.SecJS;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.*;
import com.payne.upms.system.mapper.SysAccountMapper;
import com.payne.upms.system.mapper.SysAccountRoleMapper;
import com.payne.upms.system.param.SysAccountParam;
import com.payne.upms.system.result.AccountRoleResult;
import com.payne.upms.system.result.SysAccountResult;
import com.payne.upms.system.service.RoleMenuService;
import com.payne.upms.system.service.SysAccountService;
import com.payne.upms.system.service.SysParamService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;

/**
 * 系统账户信息Service实现
 *
 * 
 * @since 2024-03-05 15:44:06
 */
@Service
public class SysAccountServiceImpl extends ServiceImpl<SysAccountMapper, SysAccount> implements SysAccountService {
    private static final BCryptPasswordEncoder bCryptPasswordEncoder = new BCryptPasswordEncoder();
    @Resource
    private SysAccountRoleMapper sysAccountRoleMapper;
    @Resource
    private RoleMenuService roleMenuService;
    @Resource
    protected SysParamService sysParamService;


    @Override
    public SysAccount getAccount(String username) {
        QueryWrapper<SysAccount> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(SysAccount::getUsername, username).or().
                eq(SysAccount::getTelMobile, username);
        List<SysAccount> sysAccounts = baseMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(sysAccounts)) {
            SysAccountParam param = new SysAccountParam();
            param.setIdCode(username);
            PageResult<SysAccountResult> pageResult = pageRel(param);
            SysAccountResult sysAccountResult = CommonUtil.listGetOne(pageResult.getList());
            if (sysAccountResult != null) {
                SysAccount account = new SysAccount();
                BeanUtils.copyProperties(sysAccountResult, account);
                return account;
            }
        }
        return CommonUtil.listGetOne(sysAccounts);
    }

    @Override
    public PageResult<SysAccountResult> pageRel(SysAccountParam param) {
        PageParam<SysAccountResult, SysAccountParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<SysAccountResult> list = baseMapper.selectListRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public SysAccount getByUsername(String username) {
        return getByUsernameAndRoleId(username, null);
    }

    @Override
    public SysAccount getByUsernameAndRoleId(String username, String roleId) {
        SysAccount account = getAccount(username);
        if (account != null) {
            List<SysRole> roleList = sysAccountRoleMapper.selectByAccountId(account.getId(), null);
            if (!CollectionUtils.isEmpty(roleList)) {
                String finalRoleId = !StringUtils.hasLength(roleId) ? roleList.get(0).getId() : roleId;
                Optional<SysRole> optional = roleList.stream().filter(r -> r.getId().equals(finalRoleId)).findFirst();
                SysRole role = null;
                try {
                    role = optional.get();
                } catch (NoSuchElementException e) {
//                    AssertUtil.isTrue(false, "角色信息错误");
                    role = roleList.get(0);
                }
                List<Menu> menus = roleMenuService.listMenuByAccount(account.getId(), role.getId(), null);
                account.setAuthorities(menus);
                account.setRole(role);
            }
            account.setRoleList(roleList);
        }
        return account;
    }

    @Override
    public boolean comparePassword(String dbPassword, String inputPassword) {
        return bCryptPasswordEncoder.matches(inputPassword, dbPassword);
    }

    @Override
    public String encodePassword(String password) {
        return password == null ? null : bCryptPasswordEncoder.encode(password);
    }

    @Transactional
    @Override
    public void edit(SysAccount account) {
        List<SysRole> roleList = account.getRoleList();
        String username = account.getUsername();
        AssertUtil.hasLength(username, ErrorInfo.USERNAME_IS_EMPTY);
        AssertUtil.notNull(roleList, ErrorInfo.ROLE_INFO_IS_EMPTY);
        if (StringUtils.hasLength(account.getId())) {
            SysAccount accountINdb = getById(account.getId());
            SysAccount byUsername = null;
            if (!accountINdb.getUsername().equals(username)) {
                byUsername = getAccount(username);
                boolean flag = byUsername != null && !byUsername.getId().equals(accountINdb.getId());
                AssertUtil.isTrue(!flag, ErrorInfo.USERNAME_EXISTS);
            }

            if (StringUtils.hasLength(account.getIdCode())) {
                byUsername = getAccount(account.getIdCode());
                boolean flag = byUsername != null && !byUsername.getId().equals(accountINdb.getId());
                AssertUtil.isTrue(!flag, ErrorInfo.ID_CODE_EXISTS);
            }

            if (StringUtils.hasLength(account.getTelMobile())) {
                byUsername = getAccount(account.getTelMobile());
                boolean flag = byUsername != null && !byUsername.getId().equals(accountINdb.getId());
                AssertUtil.isTrue(!flag, ErrorInfo.PHONE_NUMBER_ALREADY_EXISTS);
            }

            account.setEnabled(accountINdb.isEnabled());
            account.setAccountNonExpired(accountINdb.isAccountNonExpired());
            account.setAccountNonLocked(accountINdb.isAccountNonLocked());
            account.setCredentialsNonExpired(accountINdb.isCredentialsNonExpired());
            account.setActiveFlag(accountINdb.getActiveFlag());
            account.setPasswordLastUpdateTime(accountINdb.getPasswordLastUpdateTime());
            account.setPassword(accountINdb.getPassword());
            sysAccountRoleMapper.delete(new QueryWrapper<SysAccountRole>().eq("account_id", account.getId()));
        } else {
            SysAccount sysAccount = getAccount(username);
            AssertUtil.isTrue(sysAccount == null, ErrorInfo.USERNAME_EXISTS);
            if (StringUtils.hasLength(account.getIdCode())) {
                sysAccount = getAccount(account.getIdCode());
                AssertUtil.isTrue(sysAccount == null, ErrorInfo.ID_CODE_EXISTS);
            }

            if (StringUtils.hasLength(account.getTelMobile())) {
                sysAccount = getAccount(account.getTelMobile());
                AssertUtil.isTrue(sysAccount == null, ErrorInfo.PHONE_NUMBER_ALREADY_EXISTS);
            }
            String password = sysParamService.getInitialPassword();
            account.setPassword(encodePassword(password));
            account.setEnabled(true);
            account.setAccountNonExpired(true);
            account.setAccountNonLocked(true);
            account.setCredentialsNonExpired(true);
            account.setActiveFlag(true);
        }

        saveOrUpdate(account);
        roleList.forEach(role -> {
            SysAccountRole accountRole = new SysAccountRole();
            accountRole.setAccountId(account.getId());
            accountRole.setRoleId(role.getId());
            sysAccountRoleMapper.insert(accountRole);
        });
    }

    @Override
    public void modifyPassword(String id, String password) {
        AssertUtil.hasLength(id, "id不能为空");
        AssertUtil.hasLength(password, "密码不能为空");
        SysAccount account = getById(id);
        if (account != null) {
            DecryptMap decryptMap = SecJS.checkDynamicKeyAndGet(password);
            password = decryptMap.getValue();
            account.setPassword(encodePassword(password));
            account.setPasswordLastUpdateTime(LocalDateTime.now());
            updateById(account);
            SecJS.clearDynamicKey(decryptMap.getKey());
        }
    }

    @Transactional
    @Override
    public void removeAccount(List<String> idList) {
        sysAccountRoleMapper.delete(new QueryWrapper<SysAccountRole>().in("account_id", idList));
        removeByIds(idList);
    }

    @Override
    public PageResult<AccountRoleResult> queryAllAccount(SysAccountParam param) {
        PageParam<AccountRoleResult, SysAccountParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<AccountRoleResult> list = baseMapper.queryAllAccount(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public void updateByProperty(String accountId, SFunction<SysAccount, ?> column, Object value) {
        baseMapper.update(new LambdaUpdateWrapper<SysAccount>()
                .set(column, value)
                .eq(SysAccount::getId, accountId));
    }

    @Override
    public SysAccount checkAccountBasicInfoAndGet(UserInfo userInfo) {
        if (userInfo == null || !StringUtils.hasText(userInfo.getXgh()))
            AssertUtil.throwMessage("工号不能为空");
        SysAccount account = null;
        if (StringUtils.hasText(userInfo.getSjh())) {
            account = getAccount(userInfo.getSjh());
            if (account != null && !account.getUsername().equals(userInfo.getXgh()))
                AssertUtil.throwMessage(ErrorInfo.PHONE_NUMBER_ALREADY_EXISTS);
        }

        if (StringUtils.hasText(userInfo.getZjhm())) {
            account = getAccount(userInfo.getZjhm());
            if (account != null && !account.getUsername().equals(userInfo.getXgh()))
                AssertUtil.throwMessage(ErrorInfo.ID_CODE_EXISTS);
        }
        return account;
    }

    @Override
    public void setAccountBasicInfo(UserInfo userInfo, SysAccount account) {
        account.setGender(userInfo.getXb());
        account.setRealName(userInfo.getXm());
        account.setIdType(userInfo.getZjlx());
        account.setIdCode(userInfo.getZjhm());
        account.setTelMobile(userInfo.getSjh());
        account.setDeptName(userInfo.getDeptName());
        account.setOnlineName(userInfo.getOnlineName());
    }

    @Override
    public void updateAccountBasicInfo(UserInfo userInfo) {
        SysAccount account = checkAccountBasicInfoAndGet(userInfo);
        if (account == null)
            account = getAccount(userInfo.getXgh());
        setAccountBasicInfo(userInfo, account);
        baseMapper.updateById(account);
    }
}
