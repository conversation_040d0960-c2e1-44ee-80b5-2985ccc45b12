package com.payne.upms.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.upms.system.entity.SysParam;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 系统参数Service
 *
 */
public interface SysParamService extends IService<SysParam> {

    SysParam getByParamName(String paramName);

    String getParamValue(@NotEmpty String paramName);

    void edit(@Validated SysParam sysParam, Map<String, List<MultipartFile>> fileMap);

    void removeByIds(String...ids);

    String getInitialPassword();
}
