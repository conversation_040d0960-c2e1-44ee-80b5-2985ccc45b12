package com.payne.upms.system.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.OperationRecord;
import com.payne.upms.system.param.OperationRecordParam;
import com.payne.upms.system.service.OperationRecordService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 操作日志控制器
 *
 */
@RestController
@RequestMapping("/api/system/operation-record")
public class OperationRecordController extends BaseController {
    @Resource
    private OperationRecordService operationRecordService;

    /**
     * 分页查询操作日志
     * 权限标识：sys:operation-record:list
     */
    @PreAuthorize("hasAuthority('sys:operation-record:list')")
    @GetMapping("/page")
    public PageResult<OperationRecord> page(OperationRecordParam param) {
        return operationRecordService.queryPage(param);
    }

    /**
     * 批量删除操作日志
     * 权限标识：sys:operation-record:remove
     *
     * @param ids
     */
    @PreAuthorize("hasAuthority('sys:operation-record:remove')")
    @OperationLog(module = "操作日志管理", comments = "批量删除")
    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        operationRecordService.remove(ids);
    }
}
