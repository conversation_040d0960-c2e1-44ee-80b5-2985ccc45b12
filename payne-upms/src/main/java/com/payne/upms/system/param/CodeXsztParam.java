package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.enums.JudgeMark;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

/**
 * 学生状态代码查询参数
 *
 * 
 * @since 2024-03-21 17:45:41
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CodeXsztParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 状态名称
     */
    private String ztmc;

    /**
     * 是否在校（1：是， 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfzx;

    /**
     * 是否在籍（1：是， 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfzj;

    /**
     * 是否在读（1：是， 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfzd;

    /**
     * 是否毕业（1：是， 0：否）
     */
    @QueryField(type = QueryType.EQ)
    private JudgeMark sfby;

    @Override
    public String getSort() {
        return StringUtils.hasLength(super.getSort()) ? super.getSort() : "sort desc";
    }

    public CodeXsztParam() {
    }

    public CodeXsztParam(JudgeMark sfzx, JudgeMark sfby) {
        this.sfzx = sfzx;
        this.sfby = sfby;
    }
}
