package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 登录参数
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoginParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账号
     **/
    private String username;
    /**
     * 密码
     **/
    private String password;

    /**
     * 验证码ID
     */
    private String cid;
    /**
     * 验证码
     **/
    private String code;
}
