package com.payne.upms.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payne.upms.system.entity.SysAccount;
import com.payne.upms.system.param.SysAccountParam;
import com.payne.upms.system.result.AccountRoleResult;
import com.payne.upms.system.result.SysAccountResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统账户信息Mapper
 *
 */
public interface SysAccountMapper extends BaseMapper<SysAccount> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<SysAccount>
     */
    List<SysAccount> selectPageRel(@Param("page") IPage<SysAccount> page,
                                   @Param("param") SysAccountParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<SysAccountResult> selectListRel(@Param("page") IPage<SysAccountResult> page, @Param("param") SysAccountParam param);

    List<AccountRoleResult> queryAllAccount(@Param("page") IPage<AccountRoleResult> page, @Param("param") SysAccountParam param);
}
