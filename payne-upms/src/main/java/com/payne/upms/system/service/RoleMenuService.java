package com.payne.upms.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.upms.system.entity.Menu;
import com.payne.upms.system.entity.RoleMenu;

import java.util.List;

/**
 * 角色菜单Service
 *
 */
public interface RoleMenuService extends IService<RoleMenu> {

    /**
     * 查询用户对应的菜单
     *
     * @param accountId   用户id
     * @param menuType 菜单类型
     * @return List<Menu>
     */
    List<Menu> listMenuByAccount(String accountId, String roleId, Integer menuType);

    /**
     * 查询用户对应的菜单
     *
     * @param roleIds  角色id
     * @param menuType 菜单类型
     * @return List<Menu>
     */
    List<Menu> listMenuByRoleIds(List<String> roleIds, Integer menuType);

}
