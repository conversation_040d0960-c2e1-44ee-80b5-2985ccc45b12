package com.payne.upms.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.core.constant.Constants;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.PageParam;
import com.payne.upms.system.entity.UserDataScope;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.mapper.UserDataScopeMapper;
import com.payne.upms.system.mapper.UserInfoMapper;
import com.payne.upms.system.param.UserDataScopeParam;
import com.payne.upms.system.service.UserDataScopeService;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户数据权限Service实现
 *
 * 
 * @since 2024-04-22 13:37:34
 */
@AllArgsConstructor
@Service
public class UserDataScopeServiceImpl extends ServiceImpl<UserDataScopeMapper, UserDataScope> implements UserDataScopeService {
    private final UserDataScopeMapper userDataScopeMapper;
    private final UserInfoMapper userInfoMapper;
    private RedisTemplate<String, Object> redisTemplate;

    @Transactional
    @Override
    public void edit(UserDataScope object) {
        List<UserDataScope> scopes = new ArrayList<>();
        if (StringUtils.hasLength(object.getXgh())) {
            addDataScopeByUser(scopes, object);
        } else if (StringUtils.hasLength(object.getBjid())) {
            addDataScopeByClass(scopes, object);
        } else if (StringUtils.hasLength(object.getZyid())) {
            addDataScopeByMajor(scopes, object);
        } else if (StringUtils.hasLength(object.getXyid())) {
            addDataScopeByDept(scopes, object);
        } else if (StringUtils.hasLength(object.getNjid()) || StringUtils.hasLength(object.getPyccid())) {
            addDataScope(scopes, object);
        }

        userDataScopeMapper.delete(new LambdaQueryWrapper<UserDataScope>()
                .eq(UserDataScope::getGlzXgh, object.getGlzXgh())
                .eq(UserDataScope::getRoleId, object.getRoleId()));
        scopes.forEach(userDataScopeMapper::insert);
        String key = String.format(Constants.USER_DATA_SCOPE_KEY, object.getGlzXgh());
        redisTemplate.delete(key);
    }

    @Override
    public List<UserDataScope> queryList(UserDataScopeParam param) {
        PageParam<UserDataScope, UserDataScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return userDataScopeMapper.selectList(page.getOrderWrapper());
    }

    private Set<String> filterHasLength2Set(List<UserDataScope> list, Function<? super UserDataScope, ? extends String> mapper) {
        return list.stream().map(mapper).filter(StringUtils::hasLength).collect(Collectors.toSet());
    }

    @Override
    public UserDataScope list2Single(List<UserDataScope> list, boolean showAll) {
        if (CollectionUtils.isEmpty(list)) return null;
        Set<String> classIds = filterHasLength2Set(list, UserDataScope::getBjid);
        Set<String> majorIds = filterHasLength2Set(list, UserDataScope::getZyid);
        Set<String> deptIds = filterHasLength2Set(list, UserDataScope::getXyid);
        Set<String> eduLevelIds = filterHasLength2Set(list, UserDataScope::getPyccid);
        Set<String> gradeIds = filterHasLength2Set(list, UserDataScope::getNjid);
        Set<String> username = filterHasLength2Set(list, UserDataScope::getXgh);
        Set<String> roleIds = filterHasLength2Set(list, UserDataScope::getRoleId);
        Set<String> managerUsername = filterHasLength2Set(list, UserDataScope::getGlzXgh);

        UserDataScope dataScope = getUserDataScope(CommonUtil.strJoin(roleIds), CommonUtil.strJoin(managerUsername),
                CommonUtil.strJoin(username));

        if (showAll) {
            if (StringUtils.hasLength(dataScope.getBjid())) {
                dataScope.setZyid(CommonUtil.strJoin(majorIds));
                dataScope.setXyid(CommonUtil.strJoin(deptIds));
            } else if (StringUtils.hasLength(dataScope.getZyid())) {
                dataScope.setXyid(CommonUtil.strJoin(deptIds));
            }
        }
        return dataScope;
    }

    private void addDataScopeByUser(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        String[] strings = CommonUtil.split(dataScope.getXgh());
        List<UserInfo> userInfos = userInfoMapper.selectList(new LambdaQueryWrapper<UserInfo>().in(UserInfo::getXgh, strings));
        Map<String, UserInfo> userInfoMap = userInfos.stream().collect((Collectors.toMap(UserInfo::getXgh, Function.identity())));
        for (String xgh : strings) {
            UserInfo userInfo = userInfoMap.get(xgh);
            UserDataScope scope = getUserDataScope(dataScope.getRoleId(), dataScope.getGlzXgh(), xgh);
            userDataScopes.add(scope);
        }
    }

    private void addDataScope(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        Result result = addDataScopeEduLevelOrGrade(dataScope, null);
        result.strList.forEach(str -> {
            String[] strings = CommonUtil.split(str);
            String gradeId = null;
            String eduLevelId = null;
            if (result.addEduLevel && result.addGrade) {
                eduLevelId = strings[0];
                gradeId = strings[1];
            } else if (result.addEduLevel) {
                eduLevelId = strings[0];
            } else if (result.addGrade) {
                gradeId = strings[0];
            }
            userDataScopes.add(getUserDataScope(dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        });
    }

    private void addDataScopeByDept(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        List<String> strList = Arrays.asList(CommonUtil.split(dataScope.getXyid()));
        Result result = addDataScopeEduLevelOrGrade(dataScope, strList);
        result.strList.forEach(str -> {
            TempObj tempObj = new TempObj(result, CommonUtil.split(str));
            userDataScopes.add(getUserDataScope(dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        });
    }

    private void addDataScopeByMajor(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        List<String> strList = Arrays.asList(CommonUtil.split(dataScope.getZyid()));
        Result result = addDataScopeEduLevelOrGrade(dataScope, strList);
        strList = result.strList;
        for (String str : strList) {
            TempObj tempObj = new TempObj(result, CommonUtil.split(str));
            userDataScopes.add(getUserDataScope(dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        }
    }

    private Result addDataScopeEduLevelOrGrade(UserDataScope dataScope, List<String> strList) {
        if (strList == null) strList = new ArrayList<>();
        boolean addEduLevel = false;
        if (StringUtils.hasLength(dataScope.getPyccid())) {
            strList = addDataScope(dataScope.getPyccid(), strList);
            addEduLevel = true;
        }

        boolean addGrade = false;
        if (StringUtils.hasLength(dataScope.getNjid())) {
            strList = addDataScope(dataScope.getNjid(), strList);
            addGrade = true;
        }
        return new Result(strList, addEduLevel, addGrade);
    }

    public List<String> addDataScope(String ids, List<String> strList) {
        List<String> list = Arrays.asList(CommonUtil.split(ids));
        if (CollectionUtils.isEmpty(strList)) {
            strList = list;
        } else {
            strList = strList.stream().flatMap(str -> list.stream().map(id ->
                            (str.endsWith(",") ? str.concat(id) : str.concat(",").concat(id))))
                    .collect(Collectors.toList());
        }
        return strList;
    }

    private void addDataScopeByClass(List<UserDataScope> userDataScopes, UserDataScope dataScope) {
        String[] array = CommonUtil.split(dataScope.getBjid());
        List<String> strList = Arrays.asList(array);
        Result result = addDataScopeEduLevelOrGrade(dataScope, strList);
        strList = result.strList;
        for (String str : strList) {
            TempObj tempObj = new TempObj(result, CommonUtil.split(str));
            userDataScopes.add(getUserDataScope(dataScope.getRoleId(), dataScope.getGlzXgh(), dataScope.getXgh()));
        }
    }

    private static UserDataScope getUserDataScope(String roleId, String glzXgh, String xgh) {
        UserDataScope userDataScope = new UserDataScope();
        userDataScope.setRoleId(roleId);
        userDataScope.setGlzXgh(glzXgh);
        userDataScope.setXgh(xgh);
        return userDataScope;
    }

    private class Result {
        public List<String> strList;
        public boolean addEduLevel;
        public boolean addGrade;

        public Result(List<String> strList, boolean addEduLevel, boolean addGrade) {
            this.strList = strList;
            this.addEduLevel = addEduLevel;
            this.addGrade = addGrade;
        }
    }

    private static class TempObj {
        private final String id;
        public String gradeId;
        public String eduLevelId;

        public TempObj(Result result, String[] strings) {
            id = strings[0];
            if (strings.length == 3) {
                this.eduLevelId = strings[1];
                this.gradeId = strings[2];
            } else {
                if (result.addEduLevel) this.eduLevelId = strings[1];
                if (result.addGrade) this.gradeId = strings[1];
            }
        }
    }
}
