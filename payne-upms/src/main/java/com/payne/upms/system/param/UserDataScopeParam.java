package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户数据权限查询参数
 *
 * 
 * @since 2024-04-22 13:37:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserDataScopeParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    @QueryField(type = QueryType.EQ)
    private String id;

    private String xyid;

    private String zyid;

    private String bjid;

    private String njid;

    private String pyccid;

    private String xgh;
    @QueryField(type = QueryType.EQ)
    private String glzXgh;
    @QueryField(type = QueryType.EQ)
    private String roleId;

    /**
     * 状态（1：有效，0：无效）
     */
    @QueryField(type = QueryType.EQ)
    private Integer status;

}
