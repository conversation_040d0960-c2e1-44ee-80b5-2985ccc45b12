package com.payne.upms.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.core.constant.Constants;
import com.payne.core.constant.ErrorInfo;
import com.payne.core.utils.AssertUtil;
import com.payne.core.web.FileInfo;
import com.payne.core.web.GridFsService;
import com.payne.upms.system.entity.SysParam;
import com.payne.upms.system.mapper.SysParamMapper;
import com.payne.upms.system.service.SysParamService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 系统参数Service实现
 *
 */
@Service
public class SysParamServiceImpl extends ServiceImpl<SysParamMapper, SysParam> implements SysParamService {
    @Resource
    private GridFsService gridFsService;

    @Override
    public SysParam getByParamName(String paramName) {
        List<SysParam> list = list(new LambdaQueryWrapper<SysParam>().eq(SysParam::getParamName, paramName));
        return !CollectionUtils.isEmpty(list) ? list.get(0) : null;
    }

    @Override
    public String getParamValue(String paramName) {
        SysParam sysParam = getByParamName(paramName);
        return sysParam != null ? sysParam.getParamValue() : null;
    }

    @Transactional
    @Override
    public void edit(@Validated SysParam sysParam, Map<String, List<MultipartFile>> fileMap) {
        SysParam byParamName = getByParamName(sysParam.getParamName());
        List<FileInfo> fileInfos = new ArrayList<>();
        if (StringUtils.hasLength(sysParam.getId())) {
            if (byParamName != null && !sysParam.getId().equals(byParamName.getId()))
                AssertUtil.throwMessage(ErrorInfo.PARAMETER_NAME_EXISTS);
            SysParam param = getById(sysParam.getId());
            sysParam.setCreateTime(param.getCreateTime());
            if (StringUtils.hasLength(param.getAttachment())) {
                fileInfos = JSON.parseArray(param.getAttachment(), FileInfo.class);
                if (StringUtils.hasLength(sysParam.getDeleteFileIds())) {
                    String[] ids = sysParam.getDeleteFileIds().split(",");
                    Iterator<FileInfo> it = fileInfos.iterator();
                    while (it.hasNext()) {
                        FileInfo fileInfo = it.next();
                        if (Arrays.asList(ids).contains(fileInfo.getId()))
                            it.remove();
                    }
                    gridFsService.remove(ids);
                }
            }
        } else {
            if (byParamName != null)
                AssertUtil.throwMessage(ErrorInfo.PARAMETER_NAME_EXISTS);
            sysParam.setCreateTime(LocalDateTime.now());
        }

        if (!CollectionUtils.isEmpty(fileMap)) {
//            String currentUsername = SecurityUtil.getUsername();
            List<MultipartFile> attachment = fileMap.get("attachment");
//            String fileInfoStr = gridFsService.save(currentUsername, attachment.toArray(new MultipartFile[]{}));
//            fileInfos.addAll(JSON.parseArray(fileInfoStr, FileInfo.class));
        }
        if (!CollectionUtils.isEmpty(fileInfos))
            sysParam.setAttachment(JSON.toJSONString(fileInfos));
        saveOrUpdate(sysParam);
    }

    @Override
    public void removeByIds(String... ids) {
        for (String id : ids) {
            SysParam param = getById(id);
            gridFsService.removeByFileInfoStr(param.getAttachment());
            removeById(id);
        }
    }

    @Override
    public String getInitialPassword() {
        SysParam param = getByParamName(Constants.INITIAL_PASSWORD_PATTERN);
        return param == null ? "123456" : param.getParamValue();
    }
}
