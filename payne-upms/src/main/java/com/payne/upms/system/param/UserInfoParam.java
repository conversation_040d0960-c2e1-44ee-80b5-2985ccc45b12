package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.enums.Gender;
import com.payne.core.enums.UserType;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户信息表查询参数
 *
 */
//@EncryptedTable
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserInfoParam extends BaseParam {
    private static final long serialVersionUID = 1L;

    /**
     * 学号/工号，对应账户表username
     */
    @QueryField(type = QueryType.IN_STR)
    private String xgh;
    /**
     * 姓名
     */
    private String xm;
    /**
     * 网名
     */
    private String onlineName;
    /**
     * 性别
     */
    @QueryField(type = QueryType.EQ)
    private Gender xb;

    /**
     * 所属公司名称
     */
    private String deptName;

    /**
     * 手机号
     */
    private String sjh;

    /**
     * 出生日期
     */
    private String csrq;

    /**
     * 籍贯
     */
    private String jg;

    /**
     * 民族
     */
    private String mzmc;

    /**
     * 证件号码
     */
//    @EncryptedColumn
    private String zjhm;

    /**
     * 证件类型
     */
    private String zjlx;

    /**
     * 人员状态ID
     */
    private String ryztid;

    @QueryField(type = QueryType.EQ)
    private UserType userType;


    @QueryField(ignore = true)
    private String roleId;
    /**
     * 高级查询参数, json
     */
    @QueryField(ignore = true)
    private String customQueryData;
    /**
     * 图表数据字段
     */
    @QueryField(ignore = true)
    private String chartDataField;

    public UserInfoParam() {

    }
}
