package com.payne.upms.system.service;

import cn.hutool.extra.servlet.JakartaServletUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import com.payne.core.config.AsyncConfig;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.LoginRecord;
import com.payne.upms.system.param.LoginRecordParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 登录日志Service
 *
 */
@Service
public class LoginRecordService {
    @Resource
    private MongoTemplate mongoTemplate;

    @Async(AsyncConfig.ASYNC_EXECUTOR)
    public void saveAsync(String username, Integer type, String comments, HttpServletRequest request) {
        if (username == null) {
            return;
        }
        LoginRecord loginRecord = new LoginRecord();
        loginRecord.setId(CommonUtil.getUUID());
        loginRecord.setUsername(username);
        loginRecord.setLoginType(type);
        loginRecord.setComments(comments);
        UserAgent ua = UserAgentUtil.parse(JakartaServletUtil.getHeaderIgnoreCase(request, "User-Agent"));
        if (ua != null) {
            if (ua.getPlatform() != null) {
                loginRecord.setOs(ua.getPlatform().toString());
            }
            if (ua.getOs() != null) {
                loginRecord.setDevice(ua.getOs().toString());
            }
            if (ua.getBrowser() != null) {
                loginRecord.setBrowser(ua.getBrowser().toString());
            }
        }
        loginRecord.setIp(JakartaServletUtil.getClientIP(request));
        loginRecord.setCreateTime(new Date());
        mongoTemplate.save(loginRecord);
    }

    public PageResult<LoginRecord> queryPage(LoginRecordParam param) {
        Query query = getQuery(param);
        long count = mongoTemplate.count(query, LoginRecord.class);
        Pageable pageable = PageRequest.of(param.getPage().intValue() - 1, param.getLimit().intValue(),
                Sort.by(Sort.Order.desc("createTime")));
        query.with(pageable);
        List<LoginRecord> list = mongoTemplate.find(query, LoginRecord.class);
        return new PageResult(list, count);
    }

    public void remove(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(ids));
            mongoTemplate.remove(query, LoginRecord.class);
        }
    }

    private Query getQuery(LoginRecordParam param) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.hasLength(param.getId())) {
            criteria.and("id").is(param.getId());
        }
        if (StringUtils.hasLength(param.getUsername())) {
            Pattern pattern = Pattern.compile("^.*" + param.getUsername() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("username").regex(pattern);
        }

        if (StringUtils.hasLength(param.getIp())) {
            Pattern pattern = Pattern.compile("^.*" + param.getIp() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("ip").regex(pattern);
        }
        if (param.getLoginType() != null) {
            criteria.and("loginType").is(param.getLoginType());
        }
        if (StringUtils.hasLength(param.getComments())) {
            Pattern pattern = Pattern.compile("^.*" + param.getComments() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("comments").regex(pattern);
        }
        query.addCriteria(criteria);
        return query;
    }
}
