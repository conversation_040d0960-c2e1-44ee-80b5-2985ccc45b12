package com.payne.upms.system.service;

import com.payne.core.enums.UserType;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;

@Component
public class PersonInfoFactory {
    @Resource
    private Map<String, PersonInfoService> serviceMap;

    public PersonInfoService getService(UserType userType) {
        return serviceMap.getOrDefault(userType.getText() + "InfoService", null);
    }

    public <R> R apply(UserType userType, Function<PersonInfoService, R> function) {
        PersonInfoService service = getService(userType);
        return function.apply(service);
    }

    public void accept(UserType userType, Consumer<PersonInfoService> consumer) {
        PersonInfoService service = getService(userType);
        consumer.accept(service);
    }
}
