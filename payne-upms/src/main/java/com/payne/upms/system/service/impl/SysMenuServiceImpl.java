package com.payne.upms.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.upms.system.entity.Menu;
import com.payne.upms.system.mapper.SysMenuMapper;
import com.payne.upms.system.service.SysMenuService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 菜单Service实现
 *
 */
@Service
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, Menu> implements SysMenuService {

    @Resource
    private SysMenuMapper sysMenuMapper;

    @Override
    public List<Menu> childList(String menuId, Integer menuType) {
        return sysMenuMapper.childList(menuId, menuType);
    }

    @Override
    public List<Menu> getAllChildList(String menuId) {
        return sysMenuMapper.getAllChildList(menuId);
    }

    @Override
    public List<Menu> getAllParentList(String menuId) {
        return sysMenuMapper.getAllParentList(menuId);
    }
}
