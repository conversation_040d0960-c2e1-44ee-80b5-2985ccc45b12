<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payne.upms.system.mapper.SysAccountMapper">

    <sql id="Base_Column_List">
        a.ID, a.ACCOUNT_NON_EXPIRED, a.ACCOUNT_NON_LOCKED, a.CREDENTIALS_NON_EXPIRED, a.ENABLED, a.PASSWORD, a.USERNAME,a.ONLINE_NAME,a.DEPT_NAME, a.ACTIVE_FLAG,
          a.EMAIL, a.PASSWORD_LAST_UPDATE_TIME, a.END_TIME, a.GENDER, a.REAL_NAME, a.START_TIME, a.TEL_MOBILE, a.ID_TYPE, a.ID_CODE
    </sql>

    <sql id="Base_where_sql">
        <if test="param.id != null">
            AND a.ID = #{param.id}
        </if>
        <if test="param.accountNonExpired != null">
            AND a.ACCOUNT_NON_EXPIRED = #{param.accountNonExpired}
        </if>
        <if test="param.accountNonLocked != null">
            AND a.ACCOUNT_NON_LOCKED = #{param.accountNonLocked}
        </if>
        <if test="param.credentialsNonExpired != null">
            AND a.CREDENTIALS_NON_EXPIRED = #{param.credentialsNonExpired}
        </if>
        <if test="param.enabled != null">
            AND a.ENABLED = #{param.enabled}
        </if>
        <if test="param.username != null">
<!--            AND a.USERNAME LIKE '%'||#{param.username}||'%'-->
            AND a.USERNAME LIKE concat(concat('%', #{param.username}), '%')
        </if>
        <if test="param.onlineName != null">
            AND a.ONLINE_NAME LIKE concat(concat('%', #{param.onlineName}), '%')
        </if>
        <if test="param.deptName != null">
            AND a.DEPT_NAME LIKE concat(concat('%', #{param.deptName}), '%')
        </if>
        <if test="param.activeFlag != null">
            AND a.ACTIVE_FLAG = #{param.activeFlag}
        </if>
        <if test="param.idCode != null">
            AND a.ID_CODE LIKE concat(concat('%', #{param.idCode}), '%')
        </if>
        <if test="param.telMobile != null">
            AND a.TEL_MOBILE LIKE concat(concat('%', #{param.telMobile}), '%')
        </if>
        <if test="param.email != null">
            AND a.EMAIL LIKE concat(concat('%', #{param.email}), '%')
        </if>
        <if test="param.realName != null">
            AND a.REAL_NAME LIKE concat(concat('%', #{param.realName}), '%')
        </if>
    </sql>

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*
        FROM SYS_ACCOUNT a
        <where>
            <include refid="Base_where_sql"/>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.payne.upms.system.entity.SysAccount">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.payne.upms.system.result.SysAccountResult">
        select<include refid="Base_Column_List"/>,LISTAGG(d.name, ',') WITHIN GROUP(ORDER BY d.name) roleName
        from sys_account a
        , (select b.account_id, b.role_id, c.name, c.role_scope
        from sys_account_role b, sys_role c
        where b.role_id = c.id) d
        where a.id = d.account_id
        <include refid="Base_where_sql"/>
        group by
        <include refid="Base_Column_List"/>
    </select>

    <select id="queryAllAccount" resultType="com.payne.upms.system.result.AccountRoleResult">
        SELECT A.USERNAME,A.REAL_NAME, A.TEL_MOBILE, A.GENDER, C.NAME ROLE_NAME, C.ID ROLE_ID,C.ROLE_SCOPE, A.DEPT_NAME
        FROM SYS_ACCOUNT A, SYS_ACCOUNT_ROLE B, SYS_ROLE C
        WHERE A.ID = B.ACCOUNT_ID
        AND B.ROLE_ID = C.ID
        <include refid="Base_where_sql"/>
        <if test="param.roleId != null">
            AND C.ID = #{param.roleId}
        </if>
    </select>
</mapper>
