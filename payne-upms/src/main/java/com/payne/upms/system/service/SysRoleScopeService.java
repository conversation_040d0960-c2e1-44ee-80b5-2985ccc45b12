package com.payne.upms.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.SysRoleScope;
import com.payne.upms.system.param.SysRoleScopeParam;

import java.util.List;

/**
 * 角色标识Service
 *
 */
public interface SysRoleScopeService extends IService<SysRoleScope> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<SysRoleScope>
     */
    PageResult<SysRoleScope> pageRel(SysRoleScopeParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<SysRoleScope>
     */
    List<SysRoleScope> listRel(SysRoleScopeParam param);

    /**
     * 根据id查询
     *
     * @param name 角色标识名称
     * @return SysRoleScope
     */
    SysRoleScope getByIdRel(String name);

}
