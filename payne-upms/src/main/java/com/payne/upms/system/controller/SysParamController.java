package com.payne.upms.system.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.SysParam;
import com.payne.upms.system.param.SysParamParam;
import com.payne.upms.system.service.SysParamService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.util.List;

/**
 * 系统参数控制器
 *
 */
@RestController
@RequestMapping("/api/system/sysParam")
public class SysParamController extends BaseController {
    @Resource
    private SysParamService sysParamService;

    /**
     * 分页查询系统参数（权限标识：system:sysParam:list）
     */
//    @PreAuthorize("hasAuthority('system:sysParam:list')")
    @GetMapping("/page")
    public PageResult<SysParam> page(SysParamParam param) {
        PageParam<SysParam, SysParamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = sysParamService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部系统参数（权限标识：system:sysParam:list）
     */
//    @PreAuthorize("hasAuthority('system:sysParam:list')")
    @GetMapping()
    public List<SysParam> list(SysParamParam param) {
        PageParam<SysParam, SysParamParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return sysParamService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询系统参数（权限标识：system:sysParam:list）
     */
//    @PreAuthorize("hasAuthority('system:sysParam:list')")
    @GetMapping("/{id}")
    public SysParam get(@PathVariable("id") String id) {
        return sysParamService.getById(id);
    }

    /**
     * 根据参数名称查询
     *
     * @param paramName
     * @return
     */
    @GetMapping("/getByParamName")
    public SysParam getByParamName(@RequestParam("paramName") String paramName) {
        SysParam sysParam = sysParamService.getByParamName(paramName);
        return sysParam;
    }

    /**
     * 添加或修改系统参数（权限标识：system:sysParam:operation）
     */
//    @PreAuthorize("hasAuthority('system:sysParam:operation')")
    @OperationLog(module = "系统参数", comments = "保存系统参数")
    @PostMapping("/operation")
    public void save(MultipartHttpServletRequest request) {
        SysParam sysParam = new SysParam();
        CommonUtil.setValueFromRequest(sysParam, request);
        sysParamService.edit(sysParam, CommonUtil.getFileFromRequest(request));
    }

    /**
     * 批量删除系统参数（权限标识：system:sysParam:remove）
     */
//    @PreAuthorize("hasAuthority('system:sysParam:remove')")
    @OperationLog(module = "系统参数", comments = "批量删除系统参数")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        sysParamService.removeByIds(ids.toArray(new String[]{}));
    }
}
