package com.payne.upms.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.payne.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.payne.core.utils.AssertUtil;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.PageParam;
import com.payne.upms.system.entity.SysAccount;
import com.payne.upms.system.entity.SysAccountRole;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.mapper.SysAccountMapper;
import com.payne.upms.system.mapper.SysAccountRoleMapper;
import com.payne.upms.system.mapper.UserInfoMapper;
import com.payne.upms.system.param.UserInfoParam;
import com.payne.upms.system.service.SysAccountService;
import com.payne.upms.system.service.SysParamService;
import com.payne.upms.system.service.UserInfoService;
import com.payne.upms.utils.UserInfoUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * 用户信息表Service实现
 *
 */
@RequiredArgsConstructor
@Service
public class UserInfoServiceImpl extends ServiceImpl<UserInfoMapper, UserInfo> implements UserInfoService {

    public final SysAccountMapper sysAccountMapper;
    public final SysAccountRoleMapper accountRoleMapper;
    public final SysParamService sysParamService;
    public final SysAccountService accountService;
    @Override
    public UserInfo get(String xgh) {
        return baseMapper.selectById(xgh);
    }

    @Override
    public List<UserInfo> list(UserInfoParam param) {
        PageParam<UserInfo, UserInfoParam> page = new PageParam<>(param);
        return baseMapper.selectListWithPermission(page.getOrderWrapper());
    }

    @Override
    public MPJLambdaWrapper<UserInfo> getQueryWrapper(UserInfoParam param, String... columns) {
        return getQueryWrapper(param, null, columns);
    }

    @Override
    public MPJLambdaWrapper<UserInfo> getQueryWrapper(UserInfoParam param,SFunction<UserInfo, ?>[] sFunction) {
        return getQueryWrapper(param, sFunction, null);
    }

    public MPJLambdaWrapper<UserInfo> getQueryWrapper(UserInfoParam param,SFunction<UserInfo, ?>[] sFunction, String... columns) {
        String alias = "zt";
        MyMPJLambdaWrapper<UserInfo, UserInfoParam> wrapper = new MyMPJLambdaWrapper<>();
//        wrapper.leftJoin(CodeXszt.class, alias, CodeXszt::getId, UserInfo::getRyztid);
        if (sFunction != null && sFunction.length > 0) {
            wrapper.select(sFunction);
        } else if (columns != null && columns.length > 0) {
            wrapper.select(columns);
        } else {
            wrapper.selectAll(UserInfo.class);  // 查询user表全部字段
        }
        if (!Objects.isNull(param)) {
            String customQueryData = param.getCustomQueryData();
            if (StringUtils.hasLength(customQueryData)) {
                wrapper.eq(UserInfo::getUserType, param.getUserType());
//                wrapper.and(q -> appendCustomQueryCondition(q, wrapper, customQueryData));
            } else {
                wrapper.buildQueryCondition(null, param, true);
            }
        }
        return wrapper;
    }

    @Override
    public void createUserAndAccountInfo(UserInfo userInfo, boolean checkAccountInfo, String passwordPattern) {
        if (checkAccountInfo)
            checkUserInfo(userInfo);
        SysAccount account = new SysAccount();
        account.setUsername(userInfo.getXgh());
        accountService.setAccountBasicInfo(userInfo, account);
        String password = UserInfoUtil.buildInitPassword(account, passwordPattern);
        password = UserInfoUtil.encodePassword(password);
        account.setPassword(password);
        sysAccountMapper.insert(account);
        String[] roleIdArray = CommonUtil.split(userInfo.getRoleId());
        for (String s : roleIdArray) {
            SysAccountRole accountRole = new SysAccountRole();
            accountRole.setAccountId(account.getId());
            accountRole.setRoleId(s);
            accountRole.setUsername(userInfo.getXgh());
            accountRoleMapper.insert(accountRole);
        }
        baseMapper.insert(userInfo);
    }

    private void checkUserInfo(UserInfo userInfo) {
        if (!StringUtils.hasText(userInfo.getRoleId()))
            AssertUtil.throwMessage("缺少角色信息");
        if (userInfo.getUserType() == null)
            AssertUtil.throwMessage("缺少用户类型");
        SysAccount account = accountService.getAccount(userInfo.getXgh());
        if (!Objects.isNull(account))
            AssertUtil.throwMessage("学号或工号已存在");
        if (StringUtils.hasText(userInfo.getSjh())) {
            account = accountService.getAccount(userInfo.getZjhm());
            if (!Objects.isNull(account))
                AssertUtil.throwMessage("手机号码已存在");
        }
        if (StringUtils.hasText(userInfo.getZjhm())) {
            account = accountService.getAccount(userInfo.getZjhm());
            if (!Objects.isNull(account))
                AssertUtil.throwMessage("证件号已存在");
        }
    }

}
