package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.payne.core.enums.Gender;
import com.payne.core.enums.UserType;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Transient;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;

/**
 *  2024/7/15.
 */
@Data
@MappedSuperclass
public class BaseUserInfo implements Serializable {
    /**
     * 工号，对应账户表username
     */
    @Id
    @Column(name = "XGH", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "XGH", type = IdType.ASSIGN_UUID)
    private String xgh;

    /**
     * 姓名
     */
    @Column(name = "XM")
    @TableField("XM")
    private String xm;

    /**
     * 性别（男：1， 女：2）
     */
    @Column(name = "XB", columnDefinition = "NUMBER(1)")
    @TableField(value = "XB", jdbcType = JdbcType.INTEGER)
    private Gender xb;

    /**
     * 网名
     */
    @Column(name = "ONLINE_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("ONLINE_NAME")
    private String onlineName;

    /**
     * 所属公司名称
     */
    @Column(name = "DEPT_NAME", columnDefinition = "varchar2(100)")
    @TableField("DEPT_NAME")
    private String deptName;

    /**
     * 手机号
     */
    @Column(name = "SJH")
    @TableField("SJH")
    private String sjh;

    /**
     * 出生日期
     */
    @Column(name = "CSRQ")
    @TableField("CSRQ")
    private String csrq;

    /**
     * 籍贯
     */
    @Column(name = "JG")
    @TableField("JG")
    private String jg;

    /**
     * 民族
     */
    @Column(name = "MZMC")
    @TableField("MZMC")
    private String mzmc;


    /**
     * 证件号码
     */
//    @FieldMask(value = Mask.ID_CARD)
//    @EncryptedColumn
    @Column(name = "ZJHM")
    @TableField("ZJHM")
    private String zjhm;

    /**
     * 证件类型
     */
    @Column(name = "ZJLX")
    @TableField("ZJLX")
    private String zjlx;

    /**
     * 人员状态ID
     */
    @Column(name = "RYZTID")
    @TableField("RYZTID")
    private String ryztid;
    /**
     * 角色
     */
    @Column(name = "ROLE_ID")
    @TableField(value = "ROLE_ID")
    private String roleId;
    /**
     * 用户类别
     */
    @Column(name = "USER_TYPE", columnDefinition = "NUMBER(1)")
    @TableField(value = "USER_TYPE", jdbcType = JdbcType.INTEGER)
    private UserType userType;
    /**
     * 照片信息
     */
    @Column(name = "PHOTO")
    @TableField(value = "PHOTO")
    private String photo;

    /**
     * 人员状态
     */
    @TableField(exist = false)
    @Transient
    private String ryzt;
}
