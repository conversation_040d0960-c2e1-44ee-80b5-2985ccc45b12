<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payne.upms.system.mapper.UserInfoMapper">
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.XGH, a.XM, a.XB, a.ZYID, a.BJID, a.NJID, a.SJH, a.CSRQ, a.JG, a.MZMC, a.P<PERSON>, a.<PERSON>MM<PERSON>, a.XQMC,
            a.ZJHM, a.ZJLX, a.XYID, a.XSLB, a.XZLX, a.RYZTID, a.ROLE_ID,a.USER_TYPE,a.PHOTO,a.BZ1, a.BZ2, a.BZ3,
            a.BZ4,a.<PERSON>Z5,a.B<PERSON>6,a<PERSON>BZ7,a<PERSON><PERSON>Z8,a<PERSON>BZ9,a<PERSON><PERSON>Z10,a<PERSON><PERSON>Z11,a.BZ12,a.BZ13,a.BZ14,a.BZ15,a.<PERSON>Z16,a.BZ17,a.BZ18,
            a.BZ19,a.BZ20,a.BZ21,a.BZ22,a.BZ23,a.BZ24,a.BZ25,a.BZ26,a.BZ27,a.BZ28,a.BZ29,a.BZ30,a.BZ31,a.BZ32,a.BZ33,
            a.BZ34,a.BZ35,a.BZ36,a.BZ37,a.BZ38,a.BZ39,a.BZ40,a.BZ41,a.BZ42,a.BZ43,a.BZ44,a.BZ45,a.BZ46,a.BZ47,a.BZ48,a.BZ49,
            a.BZ50,a.BZ51,a.BZ52,a.BZ53,a.BZ54,a.BZ55,a.BZ56,a.BZ57,a.BZ58,a.BZ59,a.BZ60
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Query_Condition">
        <if test="param.xm != null">
            AND a.XM LIKE concat(concat('%', #{param.xm}), '%')
        </if>
        <if test="param.xb != null">
            AND a.XB = #{param.xb}
        </if>
        <if test="param.zyid != null">
            AND a.ZYID = #{param.zyid}
        </if>
        <if test="param.bjid != null">
            AND a.BJID = #{param.bjid}
        </if>
        <if test="param.njid != null">
            AND a.NJID = #{param.njid}
        </if>
        <if test="param.xgh != null">
            AND a.XGH = #{param.xgh}
        </if>
        <if test="param.csrq != null">
            AND a.CSRQ LIKE concat(concat('%',#{param.csrq}), '%')
        </if>
        <if test="param.jg != null">
            AND a.JG LIKE concat(concat('%',#{param.jg}), '%')
        </if>
        <if test="param.mzmc != null">
            AND a.MZMC LIKE concat(concat('%',#{param.mzmc}), '%')
        </if>
        <if test="param.pyccid != null">
            AND a.PYCCID=#{param.pyccid}
        </if>
        <if test="param.zzmmmc != null">
            AND a.ZZMMMC LIKE concat(concat('%',#{param.zzmmmc}), '%')
        </if>
        <if test="param.xqmc != null">
            AND a.XQMC LIKE concat(concat('%',#{param.xqmc}), '%')
        </if>
        <if test="param.zjhm != null">
            AND a.ZJHM LIKE concat(concat('%',#{param.zjhm}), '%')
        </if>
        <if test="param.zjlx != null">
            AND a.ZJLX LIKE concat(concat('%',#{param.zjlx}), '%')
        </if>
        <if test="param.userType != null">
            AND a.USER_TYPE = #{param.userType}
        </if>
    </sql>

    <sql id="selectRoleSql">
        SELECT ACC.ID ACCOUNT_ID, ACC.USERNAME, R.ID ROLE_ID, R.NAME ROLE_NAME, R.ROLE_SCOPE
        FROM SYS_ROLE R, SYS_ACCOUNT_ROLE AR, SYS_ACCOUNT ACC WHERE R.ID = AR.ROLE_ID AND ACC.ID = AR.ACCOUNT_ID
    </sql>

    <sql id="Base_Query">
        SELECT <include refid="Base_Column_List"/>,B.NAME XYMC,C.NAME ZYMC, D.NAME BJMC, E.ZTMC RYZT
        FROM USER_INFO A LEFT JOIN CODE_DWB B ON A.XYID = B.ID
        LEFT JOIN CODE_ZYB C ON A.ZYID = C.ID LEFT JOIN CODE_BJB D ON A.BJID = D.ID
        LEFT JOIN CODE_XSZT E ON A.RYZTID = E.ID
        <where>
            <include refid="Base_Query_Condition"/>
            <if test="param.roleId != null">
                and exists ( <include refid="selectRoleSql"/> AND A.XGH = ACC.USERNAME AND R.ID = #{param.roleId} )
            </if>
        </where>
        <if test="param.sort != null">
            <if test="param.order != null">
                ORDER BY #{param.sort} #{param.order}
            </if>
        </if>
    </sql>

    <select id="queryList" resultType="com.payne.upms.system.entity.UserInfo">
        <include refid="Base_Query"/>
    </select>

    <select id="queryPage" resultType="com.payne.upms.system.entity.UserInfo">
        <include refid="Base_Query"/>
    </select>
</mapper>
