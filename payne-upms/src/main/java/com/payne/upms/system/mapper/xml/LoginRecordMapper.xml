<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.payne.upms.system.mapper.LoginRecordMapper">

    <!-- 关联查询sql -->
    <sql id="selectSql">
        SELECT a.*,
        b.user_id,
        b.nickname
        FROM sys_login_record a
        LEFT JOIN sys_user b ON a.username = b.username
        <where>
            <if test="param.id != null">
                AND a.id = #{param.id}
            </if>
            <if test="param.username != null">
                AND a.username LIKE '%'||#{param.username}||'%'
            </if>
            <if test="param.os != null">
                AND a.os LIKE '%'||#{param.os}||'%'
            </if>
            <if test="param.device != null">
                AND a.device LIKE '%'||#{param.device}||'%'
            </if>
            <if test="param.browser != null">
                AND a.browser LIKE '%'||#{param.browser}||'%'
            </if>
            <if test="param.ip != null">
                AND a.ip LIKE '%'||#{param.ip}||'%'
            </if>
            <if test="param.loginType != null">
                AND a.login_type LIKE '%'||#{param.loginType}||'%'
            </if>
            <if test="param.comments != null">
                AND a.comments LIKE '%'||#{param.comments}||'%'
            </if>
            <if test="param.createTimeStart != null">
                AND a.create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null">
                AND a.create_time &lt;= #{param.createTimeEnd}
            </if>
            <if test="param.userId != null">
                AND b.user_id = #{param.userId}
            </if>
            <if test="param.nickname != null">
                AND b.nickname LIKE '%'||#{param.nickname}||'%'
            </if>
        </where>
    </sql>

    <!-- 分页查询 -->
    <select id="selectPageRel" resultType="com.payne.upms.system.entity.LoginRecord">
        <include refid="selectSql"></include>
    </select>

    <!-- 查询全部 -->
    <select id="selectListRel" resultType="com.payne.upms.system.entity.LoginRecord">
        <include refid="selectSql"></include>
    </select>

</mapper>
