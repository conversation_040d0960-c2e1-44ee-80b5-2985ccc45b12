package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色菜单
 */
@Entity
@Data
@TableName("SYS_ROLE_MENU")
@Table(name = "SYS_ROLE_MENU")
public class RoleMenu implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Id
    @Column(name = "id", columnDefinition = "VARCHAR2(50)")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 角色id
     */
    @Column(name = "role_id", columnDefinition = "VARCHAR2(50)")
    @TableField("role_id")
    private String roleId;

    /**
     * 菜单id
     */
    @Column(name = "menu_id", columnDefinition = "VARCHAR2(50)")
    @TableField("menu_id")
    private String menuId;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("create_time")
    private Date updateTime;

}
