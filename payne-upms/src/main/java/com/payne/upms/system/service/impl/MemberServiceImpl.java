package com.payne.upms.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payne.core.enums.UserType;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.ExcelImportError;
import com.payne.upms.system.entity.SysRole;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.service.PersonInfoService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("memberInfoService")
public class MemberServiceImpl extends PersonInfoService {
    @Override
    protected UserType getUserType() {
        return UserType.MEMBER;
    }

    @Override
    protected void setRoleId(LinkedHashMap<Integer, UserInfo> userInfoMap, UserInfo userInfo, List<ExcelImportError> errors) {
        List<SysRole> sysRoles = sysRoleMapper.selectList(new QueryWrapper<>());
        Map<String, String> stringMap = sysRoles.stream().collect(Collectors.toMap(SysRole::getName, SysRole::getId));
        if (userInfoMap != null) {
            userInfoMap.forEach((k, obj) -> {
                String s = stringMap.get(obj.getRoleId());
                if (StringUtils.hasText(s)) {
                    setRoleId(k, obj, stringMap, errors);
                } else {
                    if (errors != null)
                        errors.add(ExcelImportError.data(k, userInfo.getXgh(), "角色不能为空"));
                }
            });
        }

        if (userInfo != null && StringUtils.hasText(userInfo.getRoleId()))
            setRoleId(null, userInfo, stringMap, errors);
    }

    private static void setRoleId(Integer row, UserInfo obj, Map<String, String> stringMap, List<ExcelImportError> errors) {
        String[] strings = CommonUtil.split(obj.getRoleId());
        StringBuilder ids = new StringBuilder();
        for (String string : strings) {
            String roleId = stringMap.get(string);
            if (!StringUtils.hasText(roleId) && errors != null) {
                errors.add(ExcelImportError.data(row, obj.getXgh(), "角色不能为空"));
                continue;
            }
            ids.append(roleId).append(",");
        }
        if (!ids.isEmpty())
            ids.deleteCharAt(ids.length() - 1);
        obj.setRoleId(ids.toString());
    }
}
