package com.payne.upms.system.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * 修改密码参数
 *
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdatePasswordParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 原始密码
     */
    private String oldPassword;
    /**
     * 新密码
     */
    private String password;

}
