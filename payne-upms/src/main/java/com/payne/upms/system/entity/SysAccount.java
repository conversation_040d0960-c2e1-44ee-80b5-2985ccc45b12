package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.payne.core.enums.Gender;
import jakarta.persistence.*;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.ibatis.type.JdbcType;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统账户信息
 *
 */
//@EncryptedTable
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_ACCOUNT")
@Table(name = "SYS_ACCOUNT")
public class SysAccount  implements UserDetails {
    private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 账户是否过期
     */
    @Getter(value = AccessLevel.NONE)
    @Column(name = "ACCOUNT_NON_EXPIRED", columnDefinition = "NUMBER(1) DEFAULT 1")
    @TableField("ACCOUNT_NON_EXPIRED")
    private Boolean accountNonExpired;
    /**
     * 是否锁定
     */
    @Getter(value = AccessLevel.NONE)
    @Column(name = "ACCOUNT_NON_LOCKED", columnDefinition = "NUMBER(1) DEFAULT 1")
    @TableField("ACCOUNT_NON_LOCKED")
    private Boolean accountNonLocked;
    /**
     * 密码是否过期
     */
    @Getter(value = AccessLevel.NONE)
    @Column(name = "CREDENTIALS_NON_EXPIRED", columnDefinition = "NUMBER(1) DEFAULT 1")
    @TableField("CREDENTIALS_NON_EXPIRED")
    private Boolean credentialsNonExpired;
    /**
     * 是否启用
     */
    @Getter(value = AccessLevel.NONE)
    @Column(name = "ENABLED", columnDefinition = "NUMBER(1) DEFAULT 1")
    @TableField("ENABLED")
    private Boolean enabled;
    /**
     * 密码
     */
    @Column(name = "PASSWORD")
    @TableField("PASSWORD")
    private String password;
    /**
     * 用户名
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;
    /**
     * 姓名
     */
    @Column(name = "REAL_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("REAL_NAME")
    private String realName;
    /**
     * 网名
     */
    @Column(name = "ONLINE_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("ONLINE_NAME")
    private String onlineName;
    /**
     * 性别（男：1， 女：2）
     */
    @Column(name = "GENDER", columnDefinition = "NUMBER(1)")
    @TableField(value = "GENDER", jdbcType = JdbcType.INTEGER)
    private Gender gender;
    /**
     * 激活标识
     */
    @Column(name = "ACTIVE_FLAG", columnDefinition = "NUMBER(1) DEFAULT 1")
    @TableField("ACTIVE_FLAG")
    private Boolean activeFlag;
    /**
     * 证件类型, 证件号码
     */
    @Column(name = "ID_TYPE", columnDefinition = "varchar2(50)")
    @TableField("ID_TYPE")
    private String idType;
//    @FieldMask(value = Mask.ID_CARD)
//    @EncryptedColumn
    @Column(name = "ID_CODE")
    @TableField("ID_CODE")
    private String idCode;
    /**
     * 所属公司名称
     */
    @Column(name = "DEPT_NAME", columnDefinition = "varchar2(100)")
    @TableField("DEPT_NAME")
    private String deptName;
    /**
     * 手机号
     */
//    @FieldMask(value = MaskEnum.MOBILE_PHONE)
    @Column(name = "TEL_MOBILE")
    @TableField("TEL_MOBILE")
    private String telMobile;
    /**
     * 邮件
     */
    @Column(name = "EMAIL")
    @TableField("EMAIL")
    private String email;
    /**
     * 账户使用时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "START_TIME")
    @TableField("START_TIME")
    private LocalDateTime startTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "END_TIME")
    @TableField("END_TIME")
    private LocalDateTime endTime;
    /**
     * 最后更新密码时间
     */
    @Column(name = "PASSWORD_LAST_UPDATE_TIME")
    @TableField("PASSWORD_LAST_UPDATE_TIME")
    private LocalDateTime passwordLastUpdateTime;

//    @Column(name = "DELETED", columnDefinition = "NUMBER(1) DEFAULT 0")
//    @TableLogic
//    private Integer deleted;

    /**
     * 角色列表
     */
    @Transient
    @TableField(exist = false)
    private List<SysRole> roleList;
    /**
     * 当前角色
     */
    @Transient
    @TableField(exist = false)
    private SysRole role;
    /**
     * 权限列表
     */
    @Transient
    @TableField(exist = false)
    private List<Menu> authorities;

    @Override
    public boolean isAccountNonExpired() {
        return accountNonExpired == null ? true : accountNonExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return accountNonLocked == null ? true : accountNonLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return credentialsNonExpired == null ? true : credentialsNonExpired;
    }

    @Override
    public boolean isEnabled() {
        return enabled == null ? true : enabled;
    }
}
