package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 系统用户角色关系
 *
 * 
 * @since 2024-03-05 16:40:52
 */
@Data
@Entity
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_ACCOUNT_ROLE")
@Table(name = "SYS_ACCOUNT_ROLE")
public class SysAccountRole implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 账户ID
     */
    @Column(name = "ACCOUNT_ID")
    @TableField("ACCOUNT_ID")
    private String accountId;

    /**
     * 角色ID
     */
    @Column(name = "ROLE_ID")
    @TableField("ROLE_ID")
    private String roleId;

    /**
     * 用户名
     */
    @Column(name = "USERNAME")
    @TableField("USERNAME")
    private String username;

    /*@Column(name = "DELETED", columnDefinition = "NUMBER(1) DEFAULT 0")
    @TableLogic
    private Integer deleted;*/

}
