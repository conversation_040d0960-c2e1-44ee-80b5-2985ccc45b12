package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;

import java.util.Date;
import java.util.List;

/**
 * 菜单
 */
@Entity
@Data
@TableName("SYS_MENU")
@Table(name = "SYS_MENU")
public class Menu implements GrantedAuthority {
    private static final long serialVersionUID = 1L;
    public static final int TYPE_MENU = 0;  // 菜单类型
    public static final int TYPE_BTN = 1;  // 按钮类型

    /**
     * 菜单id
     */
    @Id
    @Column(name = "menu_id", columnDefinition = "VARCHAR2(50)")
    @TableId(type = IdType.ASSIGN_UUID)
    private String menuId;

    /**
     * 上级id, 0是顶级
     */
    @Column(name = "parent_id", columnDefinition = "VARCHAR2(50)")
    private String parentId;

    /**
     * 菜单名称
     */
    private String title;

    /**
     * 菜单路由地址
     */
    private String path;

    /**
     * 菜单组件地址
     */
    private String component;

    /**
     * 菜单类型, 0目录, 1菜单, 2按钮
     */
    @Column(name = "menu_type")
    private Integer menuType;

    /**
     * 排序号
     */
    @Column(name = "sort_number")
    @TableField("sort_number")
    private Integer sortNumber;

    /**
     * 权限标识
     */
    private String authority;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 是否隐藏, 0否, 1是(仅注册路由不显示左侧菜单)
     */
    private Integer hide;

    /**
     * 路由元信息
     */
    private String meta;
    /**
     * 路由别名
     */
    @Column(name = "path_name")
    @TableField("path_name")
    private String pathName;

    /**
     * 是否删除, 0否, 1是
     */
    @TableLogic
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 子菜单
     */
    @Transient
    @TableField(exist = false)
    private List<Menu> children;

    /**
     * 角色权限树选中状态
     */
    @Transient
    @TableField(exist = false)
    private Boolean checked;

}
