package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
/**
 * 角色标识
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("SYS_ROLE_SCOPE")
@Table(name = "SYS_ROLE_SCOPE")
public class SysRoleScope implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 角色标识名称
     */
    @Id
    @Column(name = "NAME", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "NAME", type = IdType.ASSIGN_UUID)
    private String name;

    /**
     * 角色标识名称对应类型（暂时保留）
     */
    @Column(name = "TYPE")
    @TableField("TYPE")
    private String type;

}
