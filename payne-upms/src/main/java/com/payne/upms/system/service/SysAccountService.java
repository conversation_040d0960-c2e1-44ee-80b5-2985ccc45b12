package com.payne.upms.system.service;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.SysAccount;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.param.SysAccountParam;
import com.payne.upms.system.result.AccountRoleResult;
import com.payne.upms.system.result.SysAccountResult;
import jakarta.validation.constraints.NotNull;

import java.util.List;

/**
 * 系统账户信息Service
 *
 */
public interface SysAccountService extends IService<SysAccount> {

    SysAccount getAccount(String xgh);
    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<SysAccountResult>
     */
    PageResult<SysAccountResult> pageRel(SysAccountParam param);

    /**
     * 根据用户名获取账户信息, 多角色随机获取角色并获取权限信息
     *
     * @param username
     * @return
     */
    SysAccount getByUsername(String username);

    /**
     * 根据用户名获取账户信息, 获取指定角色信息同时获取权限信息
     *
     * @param username
     * @return
     */
    SysAccount getByUsernameAndRoleId(String username, String roleId);

    boolean comparePassword(String dbPassword, String inputPassword);

    String encodePassword(String password);

    /**
     * 新增或编辑账户信息
     *
     * @param account
     */
    void edit(SysAccount account);

    /**
     * 修改密码
     *
     * @param id
     * @param password
     */
    void modifyPassword(@NotNull String id, @NotNull String password);

    /**
     * 删除账户信息
     *
     * @param idList
     */
    void removeAccount(List<String> idList);

    /**
     * 查询全部账户信息
     */
    PageResult<AccountRoleResult> queryAllAccount(SysAccountParam param);

    void updateByProperty(String accountId, SFunction<SysAccount, ?> column, Object value);

    SysAccount checkAccountBasicInfoAndGet(UserInfo userInfo);

    void setAccountBasicInfo(@NotNull UserInfo userInfo, @NotNull SysAccount account);

    void updateAccountBasicInfo(UserInfo userInfo);

}
