package com.payne.upms.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.payne.core.web.ColumnType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 用户数据权限
 *
 * 
 * @since 2024-04-22 13:37:34
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("USER_DATA_SCOPE")
@Entity
@Table(name = "USER_DATA_SCOPE")
public class UserDataScope implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = ColumnType.CHAR_32)
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

    @Column(name = "XYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("XYID")
    private String xyid;

    @Column(name = "ZYID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("ZYID")
    private String zyid;

    @Column(name = "BJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("BJID")
    private String bjid;

    @Column(name = "NJID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("NJID")
    private String njid;

    @Column(name = "PYCCID", columnDefinition = ColumnType.VARCHAR2_32)
    @TableField("PYCCID")
    private String pyccid;

    @Column(name = "XGH")
    @TableField("XGH")
    private String xgh;

    @NotEmpty(message = "管理者学工号不能为空")
    @Column(name = "GLZ_XGH")
    @TableField("GLZ_XGH")
    private String glzXgh;

    @NotEmpty(message = "角色信息不能为空")
    @Column(name = "ROLE_ID", columnDefinition = ColumnType.CHAR_32)
    @TableField("ROLE_ID")
    private String roleId;

    /**
     * 状态（1：有效，0：无效）
     */
    @Column(name = "STATUS")
    @TableField("STATUS")
    private Integer status;

}
