package com.payne.upms.system.entity;

import jakarta.persistence.Id;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * 登录日志
 *
 */

@Data
@Document(collection = "SYS_LOGIN_RECORD")
public class LoginRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    public static final int TYPE_LOGIN = 0;  // 登录成功
    public static final int TYPE_ERROR = 1;  // 登录失败
    public static final int TYPE_LOGOUT = 2;  // 退出登录
    public static final int TYPE_REFRESH = 3;  // 续签token

    /**
     * 主键id
     **/
    @Id
    private String id;

    /**
     * 用户账号
     **/
    private String username;

    /**
     * 操作系统
     **/
    private String os;

    /**
     * 设备名称
     **/
    private String device;

    /**
     * 浏览器类型
     **/
    private String browser;

    /**
     * ip地址
     **/
    private String ip;

    /**
     * 操作类型, 0登录成功, 1登录失败, 2退出登录, 3续签token
     **/
    private Integer loginType;

    /**
     * 备注
     **/
    private String comments;
    /**
     * 操作时间
     **/
    private Date createTime;

    /**
     * 修改时间
     **/
    private Date updateTime;
}
