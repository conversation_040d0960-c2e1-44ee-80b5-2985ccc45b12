package com.payne.upms.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.upms.system.entity.Menu;

import java.util.List;

/**
 * 菜单Service
 */
public interface SysMenuService extends IService<Menu> {

    public List<Menu> childList(String menuId, Integer menuType);
    public List<Menu> getAllChildList(String menuId);
    public List<Menu> getAllParentList(String menuId);
}
