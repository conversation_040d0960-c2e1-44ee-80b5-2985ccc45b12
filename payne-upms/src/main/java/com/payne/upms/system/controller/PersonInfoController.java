package com.payne.upms.system.controller;

import com.payne.core.annotation.EnableMask;
import com.payne.core.annotation.OperationLog;
import com.payne.core.enums.UserType;
import com.payne.core.web.BaseController;
import com.payne.core.web.GridFsService;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.param.UserInfoParam;
import com.payne.upms.system.service.PersonInfoFactory;
import com.payne.upms.system.service.UserInfoService;
import com.payne.upms.utils.SecurityUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.parameters.P;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人员（学生/教师）信息管理控制器
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api/personInfo")
public class PersonInfoController extends BaseController {
    private final PersonInfoFactory personInfoFactory;
    private final GridFsService gridFsService;
    private final UserInfoService userInfoService;


    /**
     * 查询本人用户信息
     *
     * @return
     */
    @EnableMask
    @GetMapping
    public UserInfo userInfo() {
        UserInfo userInfo = userInfoService.get(SecurityUtil.getUsername());
//        UserInfoUtil.codeTextSet(Collections.singletonList(userInfo));
        return userInfo;
    }

    /**
     * 分页查询（权限标识：personInfo:'+(#param.userType.text)+':list）
     *
     * @param param
     * @return
     */
    @EnableMask
    @PreAuthorize("hasAuthority('personInfo:'+(#param.userType.text)+':list')")
    @GetMapping("/{userType}/queryPage")
    public PageResult<UserInfo> queryPage(@P("param") UserInfoParam param) {
        return personInfoFactory.apply(param.getUserType(), service -> service.queryPage(param));
    }

    /**
     * 根据工号查询（权限标识：personInfo:'+(#userType.text)+':list）
     *
     * @param userType
     * @param xgh
     * @return
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':list')")
    @GetMapping("/{userType}/{xgh}")
    public UserInfo get(@P("userType") @PathVariable("userType") UserType userType, @PathVariable("xgh") String xgh) {
        return personInfoFactory.apply(userType, service -> service.get(xgh));
    }

    /**
     * 添加或编辑（权限标识：personInfo:'+(#userType.text)+':operation）
     *
     * @param userType
     * @param param
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':operation')")
    @OperationLog(module = "人员信息管理", comments = "添加或编辑")
    @PostMapping("/{userType}/operation")
    public void operation(@P("userType") @PathVariable(name = "userType") UserType userType, @RequestBody UserInfoParam param) {
        personInfoFactory.accept(userType, personInfoService -> personInfoService.operation(param));
    }

    /**
     * 删除（权限标识：personInfo:'+(#userType.text)+':remove）
     *
     * @param userType
     * @param ids
     */
    @PreAuthorize("hasAuthority('personInfo:'+(#userType.text)+':remove')")
    @OperationLog(module = "人员信息管理", comments = "用户信息删除")
    @PostMapping("/{userType}/remove")
    public void remove(@P("userType") @PathVariable("userType") UserType userType, @RequestBody List<String> ids) {
        removeParamCheck(ids);
        personInfoFactory.accept(userType, service -> service.remove(ids));
    }

}
