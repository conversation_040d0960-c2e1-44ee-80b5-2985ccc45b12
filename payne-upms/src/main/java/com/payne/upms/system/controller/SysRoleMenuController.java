package com.payne.upms.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.payne.core.annotation.OperationLog;
import com.payne.core.exception.BusinessException;
import com.payne.core.utils.AssertUtil;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.upms.system.entity.Menu;
import com.payne.upms.system.entity.RoleMenu;
import com.payne.upms.system.param.MenuParam;
import com.payne.upms.system.param.RoleMenuParam;
import com.payne.upms.system.service.RoleMenuService;
import com.payne.upms.system.service.SysMenuService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色菜单控制器
 *
 */
@RestController
@RequestMapping("/api/system/role-menu")
public class SysRoleMenuController extends BaseController {
    @Resource
    private RoleMenuService roleMenuService;
    @Resource
    private SysMenuService sysMenuService;

    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping("/list")
    public List<Menu> list(MenuParam param) {
        if (!StringUtils.hasLength(param.getRoleId()))
            AssertUtil.throwImportError("缺失必须参数");
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        List<Menu> menus = sysMenuService.list(page.getOrderWrapper());
        List<RoleMenu> roleMenus = roleMenuService.list(new LambdaQueryWrapper<RoleMenu>()
                .eq(RoleMenu::getRoleId, param.getRoleId()));
        menus.forEach((m) -> m.setChecked(roleMenus.stream().anyMatch((d) -> d.getMenuId().equals(m.getMenuId()))));
        return menus;
    }

    @Transactional(rollbackFor = {Exception.class})
    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog(module = "角色资源", comments = "更新角色资源信息")
    @PutMapping("/{id}")
    public ApiResult<?> update(@PathVariable("id") String roleId, @RequestBody List<String> menuIds) {
        roleMenuService.remove(new LambdaUpdateWrapper<RoleMenu>().eq(RoleMenu::getRoleId, roleId));
        if (menuIds != null && menuIds.size() > 0) {
            List<RoleMenu> roleMenuList = new ArrayList<>();
            for (String menuId : menuIds) {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(menuId);
                roleMenuList.add(roleMenu);
            }
            if (!roleMenuService.saveBatch(roleMenuList)) {
                throw new BusinessException("保存失败");
            }
        }
        return success("保存成功");
    }

    @OperationLog(module = "角色资源", comments = "更新角色资源信息")
    @PostMapping("/operation")
    public void operation(@Validated @RequestBody RoleMenuParam param) {
        List<Menu> childList = sysMenuService.getAllChildList(param.getMenuId());
        List<Menu> menus = roleMenuService.listMenuByRoleIds(Arrays.asList(param.getRoleId()), null);
        if (param.getAdd()) {
            List<Menu> parentList = sysMenuService.getAllParentList(param.getMenuId());
            childList.addAll(parentList);
            Set<String> menuIds = childList.stream().map((Menu::getMenuId)).collect(Collectors.toSet());
            menuIds.stream().filter((m) -> !menus.stream().anyMatch((d) -> d.getMenuId().equals(m))).forEach((m) -> {
                RoleMenu roleMenu = new RoleMenu();
                roleMenu.setRoleId(param.getRoleId());
                roleMenu.setMenuId(m);
                roleMenuService.save(roleMenu);
            });
        } else {
            Optional<Menu> optional = childList.stream().filter((m) -> param.getMenuId().equals(m.getMenuId())).findFirst();
            List<String> childMenuIds = childList.stream().map((Menu::getMenuId)).collect(Collectors.toList());
            List<String> roleMenuIds = menus.stream().map((Menu::getMenuId)).collect(Collectors.toList());
            roleMenuService.remove(new LambdaQueryWrapper<RoleMenu>()
                    .eq(RoleMenu::getRoleId, param.getRoleId()).in(RoleMenu::getMenuId, childMenuIds));
            roleMenuIds.removeAll(childMenuIds);
            removeParent(param.getRoleId(), optional.get(), roleMenuIds);
        }
    }

    private void removeParent(String roleId, Menu menu, List<String> roleMenuIds) {
        while (true) {
            Menu parent = sysMenuService.getById(menu.getParentId());
            if (parent == null) break;
            List<Menu> childList = sysMenuService.getAllChildList(parent.getMenuId());
            if (!CollectionUtils.isEmpty(childList)) {
                List<String> ids = childList.stream().map((Menu::getMenuId)).collect(Collectors.toList());
                List<RoleMenu> list = roleMenuService.list(new LambdaQueryWrapper<RoleMenu>()
                        .eq(RoleMenu::getRoleId, roleId).in(RoleMenu::getMenuId, ids)
                        .notIn(RoleMenu::getMenuId, parent.getMenuId()));
                if (CollectionUtils.isEmpty(list)) {
                    roleMenuService.remove(new LambdaQueryWrapper<RoleMenu>()
                            .eq(RoleMenu::getRoleId, roleId).eq(RoleMenu::getMenuId, parent.getMenuId()));
                    roleMenuIds.remove(parent.getMenuId());
                }
            }
            menu = parent;
        }
    }

    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog
    @PostMapping("/{id}")
    public ApiResult<?> addRoleAuth(@PathVariable("id") String roleId, @RequestBody String menuId) {
        RoleMenu roleMenu = new RoleMenu();
        roleMenu.setRoleId(roleId);
        roleMenu.setMenuId(menuId);
        if (roleMenuService.save(roleMenu)) {
            return success();
        }
        return fail();
    }

    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog(module = "角色资源", comments = "删除角色资源信息")
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") String roleId, @RequestBody String menuId) {
        if (roleMenuService.remove(new LambdaUpdateWrapper<RoleMenu>()
                .eq(RoleMenu::getRoleId, roleId).eq(RoleMenu::getMenuId, menuId))) {
            return success();
        }
        return fail();
    }
}
