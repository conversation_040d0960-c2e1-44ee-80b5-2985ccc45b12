package com.payne.upms.system.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.UserDataScope;
import com.payne.upms.system.param.UserDataScopeParam;
import com.payne.upms.system.service.UserDataScopeService;
import com.payne.upms.system.vo.UserDataScopeVO;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户数据权限控制器
 *
 * 
 * @since 2024-04-22 13:37:34
 */
@RestController
@RequestMapping("/api/userInfo/userDataScope")
public class UserDataScopeController extends BaseController {
    @Resource
    private UserDataScopeService userDataScopeService;

    /**
     * 分页查询用户数据权限（权限标识：userInfo:userDataScope:list）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:list')")
    @GetMapping("/page")
    public PageResult<UserDataScope> page(UserDataScopeParam param) {
        PageParam<UserDataScope, UserDataScopeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = userDataScopeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部用户数据权限（权限标识：userInfo:userDataScope:list）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:list')")
    @GetMapping()
    public List<UserDataScope> list(UserDataScopeParam param) {
        return userDataScopeService.queryList(param);
    }

    /**
     * 添加或修改用户数据权限（权限标识：userInfo:userDataScope:operation）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:operation')")
    @OperationLog(module = "用户数据权限", comments = "保存用户数据权限")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody UserDataScope userDataScope) {
        userDataScopeService.edit(userDataScope);
    }

    /**
     * 批量删除用户数据权限（权限标识：userInfo:userDataScope:remove）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:remove')")
    @OperationLog(module = "用户数据权限", comments = "批量删除用户数据权限")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        userDataScopeService.removeByIds(ids);
    }

    /**
     * 获取用户数据权限（权限标识：userInfo:userDataScope:get）
     */
    @PreAuthorize("hasAuthority('userInfo:userDataScope:get')")
    @GetMapping("/{glzXgh}/{roleId}")
    public UserDataScope get(@PathVariable("glzXgh") String glzXgh, @PathVariable("roleId") String roleId) {
        UserDataScopeParam param = new UserDataScopeParam();
        param.setGlzXgh(glzXgh);
        param.setRoleId(roleId);
        List<UserDataScope> list = userDataScopeService.queryList(param);
        UserDataScope dataScope = userDataScopeService.list2Single(list, true);
        UserDataScopeVO userDataScopeVO = new UserDataScopeVO();
        BeanUtils.copyProperties(dataScope, userDataScopeVO);
        return userDataScopeVO;
    }
}
