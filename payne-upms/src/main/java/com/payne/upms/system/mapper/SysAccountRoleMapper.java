package com.payne.upms.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payne.upms.system.entity.SysAccountRole;
import com.payne.upms.system.entity.SysRole;
import com.payne.upms.system.param.SysAccountRoleParam;
import jakarta.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 系统用户角色关系Mapper
 */
public interface SysAccountRoleMapper extends BaseMapper<SysAccountRole> {

    /**
     * 分页查询
     *
     * @param page  分页对象
     * @param param 查询参数
     * @return List<SysAccountRole>
     */
    List<SysAccountRole> selectPageRel(@Param("page") IPage<SysAccountRole> page,
                                       @Param("param") SysAccountRoleParam param);

    /**
     * 查询全部
     *
     * @param param 查询参数
     * @return List<User>
     */
    List<SysAccountRole> selectListRel(@Param("param") SysAccountRoleParam param);

    List<SysRole> selectByAccountId(@NotNull @Param("accountId") String accountId, @Param("roleId") String roleId);
}
