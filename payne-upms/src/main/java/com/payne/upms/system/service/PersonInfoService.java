package com.payne.upms.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.payne.core.constant.Constants;
import com.payne.core.enums.UserType;
import com.payne.core.mybatisplus.base.MyMPQueryParams;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.ExcelImportError;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.SysAccount;
import com.payne.upms.system.entity.SysAccountRole;
import com.payne.upms.system.entity.SysRole;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.mapper.SysAccountMapper;
import com.payne.upms.system.mapper.SysAccountRoleMapper;
import com.payne.upms.system.mapper.SysRoleMapper;
import com.payne.upms.system.mapper.UserInfoMapper;
import com.payne.upms.system.param.UserInfoParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public abstract class PersonInfoService {
    @Resource
    protected SysAccountMapper sysAccountMapper;
    @Resource
    protected SysAccountRoleMapper accountRoleMapper;
    @Resource
    protected SysRoleMapper sysRoleMapper;
    @Resource
    protected UserInfoMapper userInfoMapper;
    @Resource
    protected UserInfoService userInfoService;
    @Resource
    private SysAccountService accountService;
    @Resource
    private SysParamService sysParamService;
    @Resource
    private Environment env;

    protected abstract UserType getUserType();

    protected abstract void setRoleId(LinkedHashMap<Integer, UserInfo> userInfoMap, UserInfo userInfo, List<ExcelImportError> errors);

    public PageResult<UserInfo> queryPage(UserInfoParam param) {
        param.setUserType(getUserType());
        MPJLambdaWrapper<UserInfo> wrapper = userInfoService.getQueryWrapper(param);
        Page<UserInfo> page = new Page<>(param.getPage(), param.getLimit());
        IPage<UserInfo> iPage = userInfoMapper.selectJoinPageWithPermission(page, UserInfo.class, wrapper, new MyMPQueryParams(getUserType()));
        List<UserInfo> records = iPage.getRecords();
//        UserInfoUtil.codeTextSet(records);
        return new PageResult<>(records, iPage.getTotal());
    }

    @SafeVarargs
    public final List<UserInfo> queryList(UserInfoParam param, SFunction<UserInfo, ?>... sFunction) {
        param = Objects.isNull(param) ? new UserInfoParam() : param;
        param.setUserType(getUserType());
        MPJLambdaWrapper<UserInfo> wrapper = userInfoService.getQueryWrapper(param, sFunction);
        List<UserInfo> list = userInfoMapper.selectJoinListWithPermission(UserInfo.class, wrapper, new MyMPQueryParams(getUserType()));
//        UserInfoUtil.codeTextSet(list);
        return list;
    }

    public UserInfo get(String xgh) {
        UserInfoParam param = new UserInfoParam();
        param.setXgh(xgh);
        List<UserInfo> list = queryList(param, null);
        return CommonUtil.listGetOne(list);
    }

    @Transactional
    public void operation(UserInfoParam param) {
        UserInfo userInfo = userInfoMapper.selectById(param.getXgh());
        if (userInfo == null) {
            userInfo = new UserInfo();
            BeanUtils.copyProperties(param, userInfo);
            SysRole sysRole = sysRoleMapper.selectOne(new LambdaQueryWrapper<SysRole>().eq(SysRole::getRoleScope, Constants.ROLE_SCOPE_USER));
            String paramValue = sysParamService.getParamValue(Constants.INITIAL_PASSWORD_PATTERN);
            userInfo.setRoleId(sysRole.getId());
            userInfo.setUserType(UserType.MEMBER);
            userInfoService.createUserAndAccountInfo(userInfo, true, paramValue);
        } else {
            BeanUtils.copyProperties(param, userInfo);
            userInfoService.updateById(userInfo);
        }
    }

    @Transactional
    public void remove(List<String> list) {
        for (String xgh : list) {
            List<SysAccount> accounts = sysAccountMapper.selectList(new LambdaQueryWrapper<SysAccount>().eq(SysAccount::getUsername, xgh));
            List<String> accountIds = accounts.stream().map(SysAccount::getId).collect(Collectors.toList());
            accountRoleMapper.delete(new LambdaQueryWrapper<SysAccountRole>().in(SysAccountRole::getAccountId, accountIds));
            sysAccountMapper.delete(new LambdaQueryWrapper<SysAccount>().in(SysAccount::getId, accountIds));
        }
        userInfoMapper.deleteBatchIds(list);
    }
}
