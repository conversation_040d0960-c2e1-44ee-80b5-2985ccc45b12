package com.payne.upms.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.payne.core.annotation.OperationLog;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.SysRole;
import com.payne.upms.system.param.RoleParam;
import com.payne.upms.system.service.SysRoleService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 角色控制器
 *
 */
@RestController
@RequestMapping("/api/system/role")
public class SysRoleController extends BaseController {
    @Resource
    private SysRoleService roleService;

//    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping("/page")
    public ApiResult<PageResult<SysRole>> page(RoleParam param) {
        PageParam<SysRole, RoleParam> page = new PageParam<>(param);
        page = roleService.page(page, page.getWrapper());
        return success(page);
    }

//    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping()
    public ApiResult<List<SysRole>> list(RoleParam param) {
        PageParam<SysRole, RoleParam> page = new PageParam<>(param);
        return success(roleService.list(page.getOrderWrapper()));
    }

//    @PreAuthorize("hasAuthority('sys:role:list')")
    @GetMapping("/{id}")
    public ApiResult<SysRole> get(@PathVariable("id") String id) {
        return success(roleService.getById(id));
    }

//    @PreAuthorize("hasAuthority('sys:role:save')")
    @OperationLog(module = "角色管理", comments = "保存角色")
    @PostMapping()
    public ApiResult<?> save(@RequestBody SysRole sysRole) {
        if (roleService.count(new LambdaQueryWrapper<SysRole>().eq(SysRole::getName, sysRole.getName())) > 0) {
            return fail("角色名称已存在");
        }
        if (roleService.save(sysRole)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

//    @PreAuthorize("hasAuthority('sys:role:update')")
    @OperationLog(module = "角色管理", comments = "修改角色")
    @PutMapping()
    public ApiResult<?> update(@RequestBody SysRole sysRole) {
        if (sysRole.getName() != null && roleService.count(new LambdaQueryWrapper<SysRole>()
                .eq(SysRole::getName, sysRole.getName())
                .ne(SysRole::getId, sysRole.getId())) > 0) {
            return fail("角色名称已存在");
        }
        if (roleService.updateById(sysRole)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

//    @PreAuthorize("hasAuthority('sys:role:remove')")
    @OperationLog(module = "角色管理", comments = "删除角色")
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") String id) {
        if (roleService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

//    @PreAuthorize("hasAuthority('sys:role:save')")
    @OperationLog(module = "角色管理", comments = "批量保存")
    @PostMapping("/batch")
    public ApiResult<List<String>> saveBatch(@RequestBody List<SysRole> list) {
        // 校验是否重复
        if (CommonUtil.checkRepeat(list, SysRole::getName)) {
            return fail("角色名称存在重复", null);
        }

        if (roleService.saveBatch(list)) {
            return success("添加成功", null);
        }
        return fail("添加失败", null);
    }

//    @PreAuthorize("hasAuthority('sys:role:remove')")
    @OperationLog(module = "角色管理", comments = "批量删除")
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (roleService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
