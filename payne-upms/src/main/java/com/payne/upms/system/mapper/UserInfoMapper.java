package com.payne.upms.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payne.core.annotation.DataPermission;
import com.payne.core.mybatisplus.base.MyMPJBaseMapper;
import com.payne.upms.system.entity.UserInfo;

/**
 * 用户信息表Mapper
 *
 */
public interface UserInfoMapper extends MyMPJBaseMapper<UserInfo> {
    @DataPermission(alias = "USER_INFO")
    default IPage<UserInfo> selectPageDP(IPage page, Wrapper<UserInfo> queryWrapper) {
        return selectPage(page, queryWrapper);
    }
}
