package com.payne.upms.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.upms.system.entity.UserDataScope;
import com.payne.upms.system.param.UserDataScopeParam;

import java.util.List;

/**
 * 用户数据权限Service
 *
 * 
 * @since 2024-04-22 13:37:34
 */
public interface UserDataScopeService extends IService<UserDataScope> {

    public void edit(UserDataScope object);

    public List<UserDataScope> queryList(UserDataScopeParam param);

    public UserDataScope list2Single(List<UserDataScope> list, boolean showAll);
}
