package com.payne.upms.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.upms.system.entity.Menu;
import com.payne.upms.system.entity.RoleMenu;
import com.payne.upms.system.mapper.RoleMenuMapper;
import com.payne.upms.system.service.RoleMenuService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色菜单Service实现
 *
 */
@Service
public class RoleMenuServiceImpl extends ServiceImpl<RoleMenuMapper, RoleMenu> implements RoleMenuService {

    @Override
    public List<Menu> listMenuByAccount(String accountId, String roleId, Integer menuType) {
        return baseMapper.listMenuByAccount(accountId, roleId, menuType);
    }

    @Override
    public List<Menu> listMenuByRoleIds(List<String> roleIds, Integer menuType) {
        return baseMapper.listMenuByRoleIds(roleIds, menuType);
    }

}
