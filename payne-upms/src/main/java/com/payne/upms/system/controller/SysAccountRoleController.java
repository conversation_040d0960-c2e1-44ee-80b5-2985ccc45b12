package com.payne.upms.system.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.*;
import com.payne.upms.system.entity.SysAccountRole;
import com.payne.upms.system.param.SysAccountRoleParam;
import com.payne.upms.system.service.SysAccountRoleService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统用户角色关系控制器
 *
 */
@RestController
@RequestMapping("/api/system/sysAccountRole")
public class SysAccountRoleController extends BaseController {
    @Resource
    private SysAccountRoleService sysAccountRoleService;

    /**
     * 分页查询系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:list')")
    @OperationLog
    @GetMapping("/page")
    public ApiResult<PageResult<SysAccountRole>> page(SysAccountRoleParam param) {
        PageParam<SysAccountRole, SysAccountRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(sysAccountRoleService.page(page, page.getWrapper()));
        // 使用关联查询
        //return success(sysAccountRoleService.pageRel(param));
    }

    /**
     * 查询全部系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:list')")
    @OperationLog
    @GetMapping()
    public ApiResult<List<SysAccountRole>> list(SysAccountRoleParam param) {
        PageParam<SysAccountRole, SysAccountRoleParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return success(sysAccountRoleService.list(page.getOrderWrapper()));
        // 使用关联查询
        //return success(sysAccountRoleService.listRel(param));
    }

    /**
     * 根据id查询系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:list')")
    @OperationLog
    @GetMapping("/{id}")
    public ApiResult<SysAccountRole> get(@PathVariable("id") Integer id) {
        return success(sysAccountRoleService.getById(id));
        // 使用关联查询
        //return success(sysAccountRoleService.getByIdRel(id));
    }

    /**
     * 添加系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:save')")
    @OperationLog
    @PostMapping()
    public ApiResult<?> save(@RequestBody SysAccountRole sysAccountRole) {
        if (sysAccountRoleService.save(sysAccountRole)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 修改系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:update')")
    @OperationLog
    @PutMapping()
    public ApiResult<?> update(@RequestBody SysAccountRole sysAccountRole) {
        if (sysAccountRoleService.updateById(sysAccountRole)) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 删除系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:remove')")
    @OperationLog
    @DeleteMapping("/{id}")
    public ApiResult<?> remove(@PathVariable("id") Integer id) {
        if (sysAccountRoleService.removeById(id)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

    /**
     * 批量添加系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:save')")
    @OperationLog
    @PostMapping("/batch")
    public ApiResult<?> saveBatch(@RequestBody List<SysAccountRole> list) {
        if (sysAccountRoleService.saveBatch(list)) {
            return success("添加成功");
        }
        return fail("添加失败");
    }

    /**
     * 批量修改系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:update')")
    @OperationLog
    @PutMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody BatchParam<SysAccountRole> batchParam) {
        if (batchParam.update(sysAccountRoleService, "ID")) {
            return success("修改成功");
        }
        return fail("修改失败");
    }

    /**
     * 批量删除系统用户角色关系
     */
//    @PreAuthorize("hasAuthority('system:sysAccountRole:remove')")
    @OperationLog
    @DeleteMapping("/batch")
    public ApiResult<?> removeBatch(@RequestBody List<String> ids) {
        if (sysAccountRoleService.removeByIds(ids)) {
            return success("删除成功");
        }
        return fail("删除失败");
    }

}
