package com.payne.upms.system.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.payne.core.annotation.OperationLog;
import com.payne.core.utils.SortHelper;
import com.payne.core.web.BaseController;
import com.payne.core.web.BatchParam;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.system.entity.Menu;
import com.payne.upms.system.entity.RoleMenu;
import com.payne.upms.system.param.MenuParam;
import com.payne.upms.system.service.RoleMenuService;
import com.payne.upms.system.service.SysMenuService;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单控制器
 *
 */
@RestController
@RequestMapping("/api/system/menu")
public class SysMenuController extends BaseController {
    @Resource
    private SysMenuService sysMenuService;
    @Resource
    private RoleMenuService roleMenuService;

//    @PreAuthorize("hasAuthority('sys:menu:list')")
    @GetMapping("/page")
    public PageResult<Menu> page(MenuParam param) {
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        page = sysMenuService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

//    @PreAuthorize("hasAuthority('sys:menu:list')")
    @GetMapping()
    public List<Menu> list(MenuParam param) {
        PageParam<Menu, MenuParam> page = new PageParam<>(param);
        List<Menu> list = sysMenuService.list(page.getOrderWrapper());
        return list;
    }

//    @PreAuthorize("hasAuthority('sys:menu:list')")
    @GetMapping("/{id}")
    public Menu get(@PathVariable("id") String id) {
        return sysMenuService.getById(id);
    }

//    @PreAuthorize("hasAuthority('sys:menu:save')")
    @OperationLog(module = "菜单资源", comments = "保存")
    @PostMapping()
    public void add(@RequestBody Menu menu) {
        String defaultParentId = "00000000000000000000000000000000";
        if (menu.getSortNumber() == null) {
            SortHelper<Menu> sortHelper = new SortHelper<>(sysMenuService, "sort_number");
            menu.setSortNumber(sortHelper.next());
        }
        if (!StringUtils.hasLength(menu.getParentId()))
            menu.setParentId(defaultParentId);
        sysMenuService.save(menu);
    }

//    @PreAuthorize("hasAuthority('sys:menu:update')")
    @OperationLog(module = "菜单资源", comments = "修改")
    @PutMapping()
    public void update(@RequestBody Menu menu) {
        sysMenuService.updateById(menu);
    }

//    @PreAuthorize("hasAuthority('sys:menu:remove')")
    @OperationLog(module = "菜单资源", comments = "删除")
    @DeleteMapping("/{id}")
    public void remove(@PathVariable("id") String id) {
        deleteMenu(id);
    }

    private void deleteMenu(String id) {
        List<Menu> childList = sysMenuService.getAllChildList(id);
        List<String> menuIdList = childList.stream().map((Menu::getMenuId)).collect(Collectors.toList());
        sysMenuService.remove(new LambdaQueryWrapper<Menu>().in(Menu::getMenuId, menuIdList));
        roleMenuService.remove(new LambdaQueryWrapper<RoleMenu>().in(RoleMenu::getMenuId, menuIdList));
    }

//    @PreAuthorize("hasAuthority('sys:menu:save')")
    @OperationLog(module = "菜单资源", comments = "批量保存")
    @PostMapping("/batch")
    public void saveBatch(@RequestBody List<Menu> menus) {
        sysMenuService.saveBatch(menus);
    }

//    @PreAuthorize("hasAuthority('sys:menu:update')")
    @OperationLog(module = "菜单资源", comments = "批量更新")
    @PutMapping("/batch")
    public void updateBatch(@RequestBody BatchParam<Menu> batchParam) {
        batchParam.update(sysMenuService, "menu_id");
    }

    //    @PreAuthorize("hasAuthority('sys:menu:remove')")
//    @OperationLog(module = "菜单资源", comments = "批量删除")
//    @DeleteMapping("/batch")
    public void removeBatch(@RequestBody List<String> ids) {
        sysMenuService.removeByIds(ids);
    }

    @GetMapping("/childList/{menuType}/{menuId}")
    public List<Menu> childList(@PathVariable("menuType") Integer menuType, @PathVariable("menuId") String menuId) {
        return sysMenuService.childList(menuId, menuType);
    }
}
