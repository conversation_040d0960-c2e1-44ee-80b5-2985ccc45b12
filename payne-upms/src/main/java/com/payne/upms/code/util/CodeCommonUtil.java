package com.payne.upms.code.util;

import com.payne.core.utils.SpringContextHolder;
import com.payne.upms.code.entity.CodeCommon;
import com.payne.upms.code.param.CodeCommonParam;
import com.payne.upms.code.service.CodeCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 码表工具类 - 动态获取码表数据
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Slf4j
public class CodeCommonUtil {

    private static CodeCommonService codeCommonService;

    private static CodeCommonService getCodeCommonService() {
        if (codeCommonService == null) {
            codeCommonService = SpringContextHolder.getBean(CodeCommonService.class);
        }
        return codeCommonService;
    }

    /**
     * 获取钱币类型列表 - 替代硬编码
     * 
     * @return 钱币类型Map列表，包含value、label、code字段
     */
    public static List<Map<String, Object>> getCoinTypes() {
        try {
            CodeCommonParam param = new CodeCommonParam("coinType");
            List<CodeCommon> coinTypes = getCodeCommonService().list(param);
            
            if (CollectionUtils.isEmpty(coinTypes)) {
                log.warn("未找到钱币类型码表数据(coinType)，返回空列表");
                return new ArrayList<>();
            }
            
            return coinTypes.stream()
                    .sorted(Comparator.comparing(CodeCommon::getSort, Comparator.nullsLast(Comparator.naturalOrder())))
                    .map(codeCommon -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("value", codeCommon.getName());  // 钱币类型名称
                        map.put("label", codeCommon.getName());  // 显示标签
                        map.put("code", codeCommon.getCode());   // 钱币类型代码
                        return map;
                    })
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("获取钱币类型码表数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据代码获取钱币类型名称
     * 
     * @param code 钱币类型代码 (01-纸币, 02-古钱币, 03-机制币, 04-银锭)
     * @return 钱币类型名称
     */
    public static String getCoinTypeNameByCode(String code) {
        if (!StringUtils.hasLength(code)) {
            return null;
        }
        
        try {
            CodeCommonParam param = new CodeCommonParam("coinType");
            param.setCode(code);
            List<CodeCommon> list = getCodeCommonService().listRel(param);
            
            if (!CollectionUtils.isEmpty(list)) {
                return list.get(0).getName();
            }
            
        } catch (Exception e) {
            log.error("根据代码获取钱币类型名称失败, code: {}", code, e);
        }
        
        return null;
    }

    /**
     * 根据名称获取钱币类型代码
     * 
     * @param name 钱币类型名称
     * @return 钱币类型代码
     */
    public static String getCoinTypeCodeByName(String name) {
        if (!StringUtils.hasLength(name)) {
            return null;
        }
        
        try {
            CodeCommonParam param = new CodeCommonParam("coinType");
            param.setName(name);
            List<CodeCommon> list = getCodeCommonService().listRel(param);
            
            if (!CollectionUtils.isEmpty(list)) {
                return list.get(0).getCode();
            }
            
        } catch (Exception e) {
            log.error("根据名称获取钱币类型代码失败, name: {}", name, e);
        }
        
        return null;
    }

    /**
     * 获取钱币类型映射表 (代码 -> 名称)
     * 
     * @return Map<代码, 名称>
     */
    public static Map<String, String> getCoinTypeCodeToNameMap() {
        try {
            CodeCommonParam param = new CodeCommonParam("coinType");
            List<CodeCommon> coinTypes = getCodeCommonService().list(param);
            
            return coinTypes.stream()
                    .filter(c -> StringUtils.hasLength(c.getCode()) && StringUtils.hasLength(c.getName()))
                    .collect(Collectors.toMap(
                            CodeCommon::getCode,
                            CodeCommon::getName,
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));
                    
        } catch (Exception e) {
            log.error("获取钱币类型映射表失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取钱币类型映射表 (名称 -> 代码)
     * 
     * @return Map<名称, 代码>
     */
    public static Map<String, String> getCoinTypeNameToCodeMap() {
        try {
            CodeCommonParam param = new CodeCommonParam("coinType");
            List<CodeCommon> coinTypes = getCodeCommonService().list(param);
            
            return coinTypes.stream()
                    .filter(c -> StringUtils.hasLength(c.getCode()) && StringUtils.hasLength(c.getName()))
                    .collect(Collectors.toMap(
                            CodeCommon::getName,
                            CodeCommon::getCode,
                            (existing, replacement) -> existing
                    ));
                    
        } catch (Exception e) {
            log.error("获取钱币类型映射表失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取指定码表类型的所有数据
     * 
     * @param codeType 码表类型
     * @return 码表数据列表
     */
    public static List<CodeCommon> getCodeList(String codeType) {
        if (!StringUtils.hasLength(codeType)) {
            return new ArrayList<>();
        }
        
        try {
            CodeCommonParam param = new CodeCommonParam(codeType);
            return getCodeCommonService().list(param);
        } catch (Exception e) {
            log.error("获取码表数据失败, codeType: {}", codeType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定码表类型的键值对映射
     * 
     * @param codeType 码表类型
     * @param keyMapper 键映射函数
     * @param valueMapper 值映射函数
     * @return 键值对Map
     */
    public static <K, V> Map<K, V> getCodeMap(String codeType, 
                                              Function<CodeCommon, K> keyMapper,
                                              Function<CodeCommon, V> valueMapper) {
        List<CodeCommon> codeList = getCodeList(codeType);
        return codeList.stream()
                .collect(Collectors.toMap(keyMapper, valueMapper, (existing, replacement) -> existing));
    }
} 