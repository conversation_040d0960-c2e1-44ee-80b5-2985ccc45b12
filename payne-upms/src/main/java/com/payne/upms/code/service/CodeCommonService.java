package com.payne.upms.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.core.web.PageResult;
import com.payne.upms.code.entity.CodeCommon;
import com.payne.upms.code.param.CodeCommonParam;
import com.payne.upms.code.result.RegionResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 * 公共代码表Service
 *
 * 
 * @since 2024-03-18 10:23:59
 */
public interface CodeCommonService extends IService<CodeCommon> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeCommon>
     */
    PageResult<CodeCommon> pageRel(CodeCommonParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeCommon>
     */
    List<CodeCommon> listRel(CodeCommonParam param);

    /**
     * 根据id查询
     *
     * @param id
     * @return CodeCommon
     */
    CodeCommon getByIdRel(String id);
    List<RegionResult> selectRegionList(String likeStr, String code);
    List<CodeCommon> list(CodeCommonParam param);
    void operation(CodeCommon codeCommon);
    
    /**
     * 操作公共代码表（支持附件）
     *
     * @param codeCommon 代码数据
     * @param fileMap    文件映射
     */
    void operation(@Validated CodeCommon codeCommon, Map<String, List<MultipartFile>> fileMap);
    
    /**
     * 批量删除
     *
     * @param ids ID列表
     */
    void removeBatch(List<String> ids);
    
    /**
     * 导入数据
     *
     * @param file Excel文件
     * @param codeType 代码类型
     */
    void importData(MultipartFile file, String codeType);
}
