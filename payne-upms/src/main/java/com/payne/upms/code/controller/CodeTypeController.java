package com.payne.upms.code.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payne.core.annotation.OperationLog;
import com.payne.core.utils.AssertUtil;
import com.payne.core.utils.SortHelper;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.code.entity.CodeType;
import com.payne.upms.code.param.CodeTypeParam;
import com.payne.upms.code.service.CodeTypeService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 代码类型控制器
 *
 * 
 * @since 2024-04-08 10:58:40
 */
@RestController
@RequestMapping("/api/code/codeType")
public class CodeTypeController extends BaseController {
    @Resource
    private CodeTypeService codeTypeService;

    /**
     * 分页查询代码类型（权限标识：code:codeType:list）
     */
//    @PreAuthorize("hasAuthority('code:codeType:list')")
    @GetMapping("/page")
    public PageResult<CodeType> page(CodeTypeParam param) {
        PageParam<CodeType, CodeTypeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = codeTypeService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部代码类型（权限标识：code:codeType:list）
     */
//    @PreAuthorize("hasAuthority('code:codeType:list')")
    @GetMapping()
    public List<CodeType> list(CodeTypeParam param) {
        PageParam<CodeType, CodeTypeParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return codeTypeService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询代码类型（权限标识：code:codeType:list）
     */
//    @PreAuthorize("hasAuthority('code:codeType:list')")
    @GetMapping("/{id}")
    public CodeType get(@PathVariable("id") Integer id) {
        return codeTypeService.getById(id);
    }

    /**
     * 添加或修改代码类型（权限标识：code:codeType:operation）
     */
    @PreAuthorize("hasAuthority('code:codeType:operation')")
    @OperationLog(module = "代码类型", comments = "保存代码类型")
    @PostMapping("/operation")
    public void save(@Validated @RequestBody CodeType codeType) {
        List<CodeType> list = codeTypeService.list(new QueryWrapper<CodeType>().eq("code", codeType.getCode()));
        if (!CollectionUtils.isEmpty(list)) {
            CodeType type = list.get(0);
            if (!StringUtils.hasLength(codeType.getId()) || !type.getId().equals(codeType.getId())) {
                AssertUtil.throwMessage("代码类型已存在");
            }
        }
        if (codeType.getSort() == null) {
            SortHelper<CodeType> sortHelper = new SortHelper<>(codeTypeService, "sort");
            codeType.setSort(sortHelper.next());
        }

        if (StringUtils.hasLength(codeType.getId())) {
            CodeType type = codeTypeService.getById(codeType.getId());
            codeType.setCode(type.getCode());
        }
        codeTypeService.saveOrUpdate(codeType);
    }

    /**
     * 批量删除代码类型（权限标识：code:codeType:remove）
     */
    @PreAuthorize("hasAuthority('code:codeType:remove')")
    @OperationLog(module = "代码类型", comments = "批量删除代码类型")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        codeTypeService.remove(ids);
    }
}
