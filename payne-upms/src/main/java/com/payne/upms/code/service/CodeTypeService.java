package com.payne.upms.code.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.core.web.PageResult;
import com.payne.upms.code.entity.CodeType;
import com.payne.upms.code.param.CodeTypeParam;

import java.util.List;

/**
 * 代码类型Service
 *
 * 
 * @since 2024-04-08 10:58:40
 */
public interface CodeTypeService extends IService<CodeType> {

    /**
     * 分页关联查询
     *
     * @param param 查询参数
     * @return PageResult<CodeType>
     */
    PageResult<CodeType> pageRel(CodeTypeParam param);

    /**
     * 关联查询全部
     *
     * @param param 查询参数
     * @return List<CodeType>
     */
    List<CodeType> listRel(CodeTypeParam param);

    /**
     * 根据id查询
     *
     * @param id 主键
     * @return CodeType
     */
    CodeType getByIdRel(String id);

    /**
     * 根据code查询
     *
     * @param code
     * @return
     */
    CodeType getByCode(String code);

    /**
     * 删除
     *
     * @param ids
     */
    void remove(List<String> ids);
}
