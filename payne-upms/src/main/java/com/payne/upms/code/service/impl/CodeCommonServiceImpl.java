package com.payne.upms.code.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.core.utils.AssertUtil;
import com.payne.core.utils.EasyExcelHelper;
import com.payne.core.utils.SortHelper;
import com.payne.core.web.ExcelImportError;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.upms.code.constant.CodeConstants;
import com.payne.upms.code.entity.CodeCommon;
import com.payne.upms.code.mapper.CodeCommonMapper;
import com.payne.upms.code.param.CodeCommonParam;
import com.payne.upms.code.result.RegionResult;
import com.payne.upms.code.service.CodeCommonService;
import com.payne.core.web.GridFsService;
import com.payne.core.web.FileInfo;
import com.alibaba.fastjson.JSON;
import com.payne.upms.utils.SecurityUtil;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 公共代码表Service实现
 *
 * 
 * @since 2024-03-18 10:23:59
 */
@CacheConfig(cacheNames = CodeConstants.CACHE_DATA_COMMON)
@Service
public class CodeCommonServiceImpl extends ServiceImpl<CodeCommonMapper, CodeCommon> implements CodeCommonService {
    @Resource
    CodeCommonMapper codeCommonMapper;
    
    @Resource
    private GridFsService gridFsService;

    @Override
    public PageResult<CodeCommon> pageRel(CodeCommonParam param) {
        PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        List<CodeCommon> list = baseMapper.selectPageRel(page, param);
        return new PageResult<>(list, page.getTotal());
    }

    @Override
    public List<CodeCommon> listRel(CodeCommonParam param) {
        List<CodeCommon> list = baseMapper.selectListRel(param);
        // 排序
        PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>();
        //page.setDefaultOrder("create_time desc");
        return page.sortRecords(list);
    }

    @Override
    public CodeCommon getByIdRel(String id) {
        CodeCommonParam param = new CodeCommonParam();
        param.setId(id);
        return param.getOne(baseMapper.selectListRel(param));
    }

    @Cacheable
    @Override
    public List<RegionResult> selectRegionList(String likeStr, String code) {
        return baseMapper.selectRegionList(likeStr, code);
    }

    @Cacheable
    @Override
    public List<CodeCommon> list(CodeCommonParam param) {
        PageParam<CodeCommon, CodeCommonParam> page = new PageParam<>(param);
        return codeCommonMapper.selectList(page.getOrderWrapper());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void operation(CodeCommon code) {
        CodeCommonParam param = new CodeCommonParam();
        param.setCodeType(code.getCodeType());
        param.setCode(code.getCode());
        List<CodeCommon> list = listRel(param);
        if (!CollectionUtils.isEmpty(list)) {
            if (code.getId() == null || !list.get(0).getId().equals(code.getId()))
                AssertUtil.throwMessage("操作失败, 代码已存在");
        }

        param.setCode(null);
        param.setName(code.getName());
        list = listRel(param);
        if (!CollectionUtils.isEmpty(list)) {
            if (!list.get(0).getName().equals(code.getName()))
                AssertUtil.throwMessage("操作失败, 代码名称已存在");
        }
        if (code.getSort() == null) {
            QueryWrapper<CodeCommon> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("code_type", code.getCodeType());
            SortHelper<CodeCommon> sortHelper = new SortHelper<>(this, "sort", queryWrapper);
            code.setSort((long) sortHelper.next());
        }
        if (StringUtils.hasText(code.getId())) {
            codeCommonMapper.updateById(code);
        } else {
            codeCommonMapper.insert(code);
        }
    }

    @CacheEvict(allEntries = true)
    @Transactional
    @Override
    public void operation(@Validated CodeCommon codeCommon, Map<String, List<MultipartFile>> fileMap) {
        List<FileInfo> fileInfos = new ArrayList<>();
        if (StringUtils.hasLength(codeCommon.getId())) {
            CodeCommon existing = getById(codeCommon.getId());
            if (existing != null && StringUtils.hasLength(existing.getAttachment())) {
                fileInfos = JSON.parseArray(existing.getAttachment(), FileInfo.class);
                if (StringUtils.hasLength(codeCommon.getDeleteFileIds())) {
                    String[] ids = codeCommon.getDeleteFileIds().split(",");
                    Iterator<FileInfo> it = fileInfos.iterator();
                    while (it.hasNext()) {
                        FileInfo fileInfo = it.next();
                        if (Arrays.asList(ids).contains(fileInfo.getId()))
                            it.remove();
                    }
                    gridFsService.remove(ids);
                }
            }
        }

        if (!CollectionUtils.isEmpty(fileMap)) {
            List<MultipartFile> attachment = fileMap.get("attachment");
            if (!CollectionUtils.isEmpty(attachment)) {
                try {
                    String currentUsername = SecurityUtil.getUsername();
                    String fileInfoStr = gridFsService.save(currentUsername, attachment.toArray(new MultipartFile[]{}));
                    if (StringUtils.hasLength(fileInfoStr)) {
                        fileInfos.addAll(JSON.parseArray(fileInfoStr, FileInfo.class));
                    }
                } catch (Exception e) {
                    // 如果获取用户名失败，使用null作为默认值
                    String fileInfoStr = gridFsService.save(null, attachment.toArray(new MultipartFile[]{}));
                    if (StringUtils.hasLength(fileInfoStr)) {
                        fileInfos.addAll(JSON.parseArray(fileInfoStr, FileInfo.class));
                    }
                }
            }
        }
        
        if (!CollectionUtils.isEmpty(fileInfos)) {
            codeCommon.setAttachment(JSON.toJSONString(fileInfos));
        }

        // 调用原有的operation方法处理业务逻辑
        operation(codeCommon);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void removeBatch(List<String> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            removeByIds(ids);
        }
    }

    @CacheEvict(allEntries = true)
    @Override
    public void importData(MultipartFile file, String codeType) {
        try {
            EasyExcelHelper<CodeCommon> excelHelper = new EasyExcelHelper<CodeCommon>() {};
            List<CodeCommon> dataList = excelHelper.read(file.getInputStream());
            
            if (!CollectionUtils.isEmpty(dataList)) {
                List<ExcelImportError> errors = new ArrayList<>();
                
                for (int i = 0; i < dataList.size(); i++) {
                    CodeCommon codeCommon = dataList.get(i);
                    codeCommon.setCodeType(codeType);
                    try {
                        operation(codeCommon);
                    } catch (Exception e) {
                        errors.add(ExcelImportError.data(i + 2, codeCommon.getCode(), e.getMessage()));
                    }
                }
                
                if (!CollectionUtils.isEmpty(errors)) {
                    AssertUtil.throwMessage("数据导入失败，存在" + errors.size() + "条错误数据");
                }
            }
        } catch (IOException e) {
            AssertUtil.throwMessage("文件解析失败：" + e.getMessage());
        }
    }
}
