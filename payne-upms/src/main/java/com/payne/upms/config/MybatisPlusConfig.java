package com.payne.upms.config;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.payne.core.handler.EncryptInterceptor;
import com.payne.core.mybatisplus.injector.MyMPJSqlInjector;
import com.payne.upms.handler.CustomDataPermissionHandlerImpl;
import com.payne.upms.handler.CustomDataPermissionInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MybatisPlus配置
 */
@Configuration
public class MybatisPlusConfig {

    @Bean
    public MyMPJSqlInjector myMPJSqlInjector() {
        return new MyMPJSqlInjector();
    }

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        // 加解密拦截器
        interceptor.addInnerInterceptor(new EncryptInterceptor());
        // 数据权限拦截器
//        interceptor.addInnerInterceptor(new DataPermissionInterceptor(new MyDataPermissionHandler()));
        interceptor.addInnerInterceptor(new CustomDataPermissionInterceptor(new CustomDataPermissionHandlerImpl()));
        // 分页插件配置
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor());

        return interceptor;
    }

}
