package com.payne.upms.utils;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.github.yulichang.toolkit.TableList;
import com.payne.core.constant.Constants;
import com.payne.core.enums.UserState;
import com.payne.core.enums.UserType;
import com.payne.core.mybatisplus.wrapper.MyMPJLambdaWrapper;
import com.payne.core.utils.SpringContextHolder;
import com.payne.core.web.BaseParam;
import com.payne.core.web.CodeCacheData;
import com.payne.upms.code.entity.CodeCommon;
import com.payne.upms.code.param.CodeCommonParam;
import com.payne.upms.code.service.CodeCommonService;
import com.payne.upms.system.entity.SysAccount;
import com.payne.upms.system.entity.UserInfo;
import com.payne.upms.system.param.UserInfoParam;
import com.payne.upms.system.service.SysParamService;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class UserInfoUtil {

    public static UserInfo convertUserInfo(SysAccount account) {
        UserInfo userInfo = new UserInfo();
        userInfo.setXb(account.getGender());
        userInfo.setRoleId(account.getRole().getId());
        userInfo.setXgh(account.getUsername());
        userInfo.setXm(account.getRealName());
        userInfo.setSjh(account.getTelMobile());
        userInfo.setZjhm(account.getIdCode());
        userInfo.setDeptName(account.getDeptName());
        userInfo.setOnlineName(account.getOnlineName());
        return userInfo;
    }

    public static UserInfoParam convertUserInfoParam(UserInfo userInfo) {
        UserInfoParam param = new UserInfoParam();
        BeanUtils.copyProperties(userInfo, param);
        return param;
    }

    public static UserInfo convertUserInfo(UserInfoParam param) {
        UserInfo userInfo = new UserInfo();
        BeanUtils.copyProperties(param, userInfo);
        return userInfo;
    }

    public static CodeCacheData loadCodeCacheData() {
        CodeCommonService codeCommonService = SpringContextHolder.getBean(CodeCommonService.class);
        Map<String, String> codeCommonMap = toMap(codeCommonService.list(new CodeCommonParam("coinType,packType")), CodeCommon::getId, CodeCommon::getName);
        Map<String, String> stateMap = new HashMap<>();
        return new CodeCacheData(codeCommonMap, stateMap);
    }


    public static Map<String, Map<String, String>> loadCodeDataMap(CodeCacheData codeCacheData) {
        if (codeCacheData == null)
            codeCacheData = loadCodeCacheData();
        CodeCacheData finalCodeCacheData = codeCacheData;
        return new HashMap<>() {{
        }};
    }

    /**
     * UserInfo 或其他业务对象包含 UserInfo
     *
     * @param object
     * @param <T>
     */
    public static <T> void codeTextSet(T... object) {
        codeTextSet(Arrays.asList(object));
    }


    public static <T, K, V> Map<K, V> toMap(List<T> list, Function<? super T, ? extends K> keyMapper,
                                            Function<? super T, ? extends V> valueMapper) {
        if (CollectionUtils.isEmpty(list))
            return Collections.emptyMap();
        return list.stream().collect(Collectors.toMap(keyMapper, valueMapper));
    }

    /**
     * 构建业务表与用户表通用查询 wrapper
     *
     * @param userState     查询的用户群体
     * @param objectParam   业务表参数对象
     * @param joinField     业务表学工号字段SFunction
     * @param userInfoParam 用户表参数对象
     * @param userType      查询的用户类型
     * @param <T>           业务表对应实体对象类型
     * @param <U>           业务表对应参数对象类型
     */
    public static <T, U extends BaseParam> MyMPJLambdaWrapper<T, U> buildInnerJoinUserInfoWrapper(UserState userState, U objectParam, @NotNull SFunction<T, String> joinField, UserInfoParam userInfoParam,
                                                                                                  UserType... userType) {
        Map<Class<?>, BaseParam> paramMap = new HashMap<>();
        Class<UserInfo> userInfoClass = UserInfo.class;
        UserInfoParam newUserInfoParam = new UserInfoParam();
        if (!Objects.isNull(userInfoParam))
            BeanUtils.copyProperties(userInfoParam, newUserInfoParam);

        MyMPJLambdaWrapper<T, U> wrapper = new MyMPJLambdaWrapper<>(objectParam);
        wrapper.innerJoin(userInfoClass, UserInfo::getXgh, joinField);
        if (!Objects.isNull(userType) && userType.length > 0) {
            List<UserType> list = Arrays.asList(userType);
            newUserInfoParam.setUserType(null);
            if (!list.contains(UserType.ADMIN)) {
                wrapper.in(UserInfo::getUserType, list);
            } else {
            }
        }

        paramMap.put(userInfoClass, newUserInfoParam);
        List<TableList.Node> nodeList = wrapper.getTableList().getAll();
        nodeList.forEach(node -> {
            String alias = wrapper.getAlias() + node.getIndex();
            BaseParam baseParam = paramMap.get(node.getClazz());
            if (!Objects.isNull(baseParam))
                wrapper.buildQueryCondition(alias, baseParam);
            wrapper.setTableAlias(node.getClazz(), alias);
        });
        return wrapper;
    }

    public static String buildInitPassword(@NotNull SysAccount account) {
        SysParamService sysParamService = SpringContextHolder.getBean(SysParamService.class);
        String paramValue = sysParamService.getParamValue(Constants.INITIAL_PASSWORD_PATTERN);
        return buildInitPassword(account, paramValue);
    }

    public static String buildInitPassword(@NotNull SysAccount account, String passwordPattern) {
        String value = account.getUsername();
        String idCode = account.getIdCode();
        String telMobile = account.getTelMobile();
        int len = 6;
        if (StringUtils.hasText(idCode) && idCode.length() >= len) {
            value = idCode.substring(idCode.length() - len);
        } else if (StringUtils.hasText(telMobile) && idCode.length() >= len) {
            value = telMobile.substring(telMobile.length() - len);
        }
        return StringUtils.hasText(passwordPattern) ? MessageFormat.format(passwordPattern, value) : value;
    }

    public static String encodePassword(String password, BCryptPasswordEncoder encoder) {
        return encoder.encode(password);
    }

    public static String encodePassword(String password) {
        BCryptPasswordEncoder encoder = SpringContextHolder.getBean(BCryptPasswordEncoder.class);
        return encodePassword(password, encoder);
    }

    public static boolean comparePassword(String inputPassword, String dbPassword) {
        BCryptPasswordEncoder encoder = SpringContextHolder.getBean(BCryptPasswordEncoder.class);
        return encoder.matches(inputPassword, dbPassword);
    }
}
