import {
  getCoinTypeOptions,
  getCoinTypeMap,
  getCodeList
} from '@/api/code-common';

/**
 * 前端代码表工具类
 * 提供缓存机制和便捷方法，减少重复请求
 */
class CodeCommonUtil {
  constructor() {
    this.cache = new Map(); // 缓存代码表数据
    this.cacheTime = new Map(); // 缓存时间
    this.maxCacheTime = 5 * 60 * 1000; // 5分钟缓存时间
  }

  /**
   * 检查缓存是否有效
   * @param {string} key 缓存键
   * @returns {boolean}
   */
  _isCacheValid(key) {
    const cacheTime = this.cacheTime.get(key);
    if (!cacheTime) return false;
    return Date.now() - cacheTime < this.maxCacheTime;
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {*} data 数据
   */
  _setCache(key, data) {
    this.cache.set(key, data);
    this.cacheTime.set(key, Date.now());
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {*}
   */
  _getCache(key) {
    if (this._isCacheValid(key)) {
      return this.cache.get(key);
    }
    return null;
  }

  /**
   * 获取钱币类型选项 (带缓存)
   * @returns {Promise<Array>}
   */
  async getCoinTypeOptions() {
    const cacheKey = 'coinTypeOptions';
    let cached = this._getCache(cacheKey);

    if (cached) {
      return cached;
    }

    try {
      const options = await getCoinTypeOptions();
      this._setCache(cacheKey, options);
      return options;
    } catch (error) {
      console.error('获取钱币类型选项失败:', error);
      return [];
    }
  }

  /**
   * 获取钱币类型映射表 (带缓存)
   * @returns {Promise<Object>}
   */
  async getCoinTypeMap() {
    const cacheKey = 'coinTypeMap';
    let cached = this._getCache(cacheKey);

    if (cached) {
      return cached;
    }

    try {
      const map = await getCoinTypeMap();
      this._setCache(cacheKey, map);
      return map;
    } catch (error) {
      console.error('获取钱币类型映射失败:', error);
      return {};
    }
  }

  /**
   * 根据钱币类型代码获取名称
   * @param {string} code 钱币类型代码
   * @returns {Promise<string>}
   */
  async getCoinTypeName(code) {
    const map = await this.getCoinTypeMap();
    return map[code] || code;
  }

  /**
   * 根据钱币类型名称获取代码
   * @param {string} name 钱币类型名称
   * @returns {Promise<string>}
   */
  async getCoinTypeCode(name) {
    const map = await this.getCoinTypeMap();
    // 反向查找
    for (const [code, mappedName] of Object.entries(map)) {
      if (mappedName === name) {
        return code;
      }
    }
    return name;
  }

  /**
   * 检查是否为指定钱币类型
   * @param {string} coinType 要检查的类型
   * @param {string} targetType 目标类型代码或名称
   * @returns {Promise<boolean>}
   */
  async isCoinType(coinType, targetType) {
    const map = await this.getCoinTypeMap();
    // 支持代码和名称两种方式判断
    return (
      coinType === targetType ||
      coinType === map[targetType] ||
      map[coinType] === targetType
    );
  }

  /**
   * 批量类型判断方法
   */
  async isBanknote(coinType) {
    return this.isCoinType(coinType, 'banknote');
  }

  async isAncientCoin(coinType) {
    return this.isCoinType(coinType, 'ancientCoin');
  }

  async isMachineCoin(coinType) {
    return this.isCoinType(coinType, 'machineCoin');
  }

  async isSilverIngot(coinType) {
    return this.isCoinType(coinType, 'silverIngot');
  }

  /**
   * 获取指定代码表类型的数据 (带缓存)
   * @param {string} codeType 代码表类型
   * @returns {Promise<Array>}
   */
  async getCodeList(codeType) {
    const cacheKey = `codeList_${codeType}`;
    let cached = this._getCache(cacheKey);

    if (cached) {
      return cached;
    }

    try {
      const list = await getCodeList(codeType);
      this._setCache(cacheKey, list);
      return list;
    } catch (error) {
      console.error(`获取${codeType}代码表失败:`, error);
      return [];
    }
  }

  /**
   * 清除指定缓存
   * @param {string} key 缓存键，不传则清除所有缓存
   */
  clearCache(key) {
    if (key) {
      this.cache.delete(key);
      this.cacheTime.delete(key);
    } else {
      this.cache.clear();
      this.cacheTime.clear();
    }
  }

  /**
   * 预加载常用代码表数据
   */
  async preloadCommonData() {
    const tasks = [this.getCoinTypeOptions(), this.getCoinTypeMap()];

    try {
      await Promise.all(tasks);
      console.log('代码表数据预加载完成');
    } catch (error) {
      console.error('代码表数据预加载失败:', error);
    }
  }
}

// 创建单例实例
const codeCommonUtil = new CodeCommonUtil();

export default codeCommonUtil;

// 也可以直接导出方法使用
export const {
  getCoinTypeOptions: getCoinTypeOptionsUtil,
  getCoinTypeMap: getCoinTypeMapUtil,
  getCoinTypeName,
  getCoinTypeCode,
  isCoinType,
  isBanknote,
  isAncientCoin,
  isMachineCoin,
  isSilverIngot,
  getCodeList: getCodeListUtil,
  clearCache,
  preloadCommonData
} = codeCommonUtil;
