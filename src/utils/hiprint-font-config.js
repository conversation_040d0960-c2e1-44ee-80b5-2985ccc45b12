/**
 * hiprint 字体大小配置扩展
 * 解决字体大小限制问题，支持更大的字体设置
 */

/**
 * 自定义字体大小选项组件
 * 支持预设选择和自定义输入，突破21.75pt限制
 */
export function createCustomFontSizeOption() {
  function FontSizeOption() {
    this.name = "fontSize";
  }

  FontSizeOption.prototype.css = function (element, value) {
    if (element && element.length) {
      if (value) {
        element.css("font-size", value + "pt");
        return "font-size:" + value + "pt";
      }
      element[0].style.fontSize = "";
    }
    return null;
  };

  FontSizeOption.prototype.createTarget = function () {
    // 扩展字体大小列表，支持更大的字体
    const presetSizes = [
      8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 44, 48,
      54, 60, 72, 84, 96, 108, 120, 144, 180, 240, 300, 360, 480
    ];

    let optionsList = '<option value="">默认</option>';
    presetSizes.forEach(function (size) {
      optionsList += '<option value="' + size + '">' + size + 'pt</option>';
    });

    this.target = $(`
      <div class="hiprint-option-item">
        <div class="hiprint-option-item-label">字体大小</div>
        <div class="hiprint-option-item-field">
          <div style="display: flex; align-items: center; gap: 4px;">
            <select class="auto-submit font-size-select" >
              ${optionsList}
            </select>
            <!--<input type="number" class="font-size-input"
                   style="width: 60px; height: 22px; border: 1px solid #ccc; padding: 2px 4px; font-size: 12px;"
                   placeholder="自定义" min="1" max="9999" step="0.25" />-->
            <!--<span style="font-size: 12px; color: #666;">pt</span>-->
          </div>
          <!--<div style="font-size: 11px; color: #999; margin-top: 2px;">
            支持1-9999pt，可输入小数
          </div>-->
        </div>
      </div>
    `);

    const selectElement = this.target.find('.font-size-select');
    const inputElement = this.target.find('.font-size-input');

    // 下拉选择事件
    selectElement.on('change', function() {
      const value = $(this).val();
      if (value) {
        inputElement.val(value);
        // 清除自定义输入的焦点
        inputElement.blur();
      }
    });

    // 输入框事件
    inputElement.on('input change blur', function() {
      const value = parseFloat($(this).val());
      if (value && value > 0 && value <= 9999) {
        // 清空下拉选择，表示使用自定义值
        selectElement.val('');
      }
    });

    // 回车确认
    inputElement.on('keypress', function(e) {
      if (e.which === 13) { // Enter键
        $(this).blur();
      }
    });

    return this.target;
  };

  FontSizeOption.prototype.getValue = function () {
    const selectVal = this.target.find(".font-size-select").val();
    const inputVal = this.target.find(".font-size-input").val();

    // 优先使用输入框的值
    if (inputVal && parseFloat(inputVal) > 0) {
      return parseFloat(inputVal);
    } else if (selectVal) {
      return parseFloat(selectVal);
    }
    return null;
  };

  FontSizeOption.prototype.setValue = function (value) {
    if (value) {
      const selectElement = this.target.find('.font-size-select');
      const inputElement = this.target.find('.font-size-input');

      // 检查预设值中是否存在
      if (selectElement.find('option[value="' + value + '"]').length > 0) {
        selectElement.val(value);
        inputElement.val('');
      } else {
        // 使用自定义输入
        selectElement.val('');
        inputElement.val(value);
      }
    }
  };

  FontSizeOption.prototype.destroy = function () {
    this.target.remove();
  };

  return FontSizeOption;
}

/**
 * 应用自定义字体大小配置到hiprint
 * @param {Object} hiprint hiprint实例
 */
export function applyCustomFontSizeConfig(hiprint) {
  if (!hiprint) {
    console.warn('hiprint实例不存在，无法应用自定义字体大小配置');
    return;
  }

  try {
    // 设置自定义选项
    hiprint.setConfig({
      optionItems: [createCustomFontSizeOption()]
    });

    console.log('自定义字体大小配置已应用');
  } catch (error) {
    console.error('应用自定义字体大小配置失败:', error);
  }
}

/**
 * 扩展现有的字体大小列表（用于简单的下拉选择）
 * @returns {Array} 扩展后的字体大小列表
 */
export function getExtendedFontSizeList() {
  return [
    8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 36, 40, 44, 48,
    54, 60, 72, 84, 96, 108, 120, 144, 180, 240, 300, 360, 480
  ];
}

/**
 * 创建简单的扩展字体大小选择器（仅下拉选择）
 */
export function createExtendedFontSizeOption() {
  function FontSizeOption() {
    this.name = "fontSize";
  }

  FontSizeOption.prototype.css = function (element, value) {
    if (element && element.length) {
      if (value) {
        element.css("font-size", value + "pt");
        return "font-size:" + value + "pt";
      }
      element[0].style.fontSize = "";
    }
    return null;
  };

  FontSizeOption.prototype.createTarget = function () {
    const fontSizeList = getExtendedFontSizeList();
    let optionsList = '<option value="">默认</option>';

    fontSizeList.forEach(function (size) {
      optionsList += '<option value="' + size + '">' + size + 'pt</option>';
    });

    this.target = $(`
      <div class="hiprint-option-item">
        <div class="hiprint-option-item-label">字体大小</div>
        <div class="hiprint-option-item-field">
          <select class="auto-submit">${optionsList}</select>
        </div>
      </div>
    `);

    return this.target;
  };

  FontSizeOption.prototype.getValue = function () {
    const value = this.target.find("select").val();
    if (value) return parseFloat(value.toString());
  };

  FontSizeOption.prototype.setValue = function (value) {
    if (value) {
      // 如果选项中不存在该值，则添加它
      if (this.target.find('option[value="' + value + '"]').length === 0) {
        this.target.find("select").prepend('<option value="' + value + '">' + value + 'pt</option>');
      }
      this.target.find("select").val(value);
    }
  };

  FontSizeOption.prototype.destroy = function () {
    this.target.remove();
  };

  return FontSizeOption;
}

export default {
  createCustomFontSizeOption,
  createExtendedFontSizeOption,
  applyCustomFontSizeConfig,
  getExtendedFontSizeList
};
