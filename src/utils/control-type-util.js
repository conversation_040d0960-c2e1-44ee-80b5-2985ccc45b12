import request from '@/utils/request';

const BASE_URL = import.meta.env.BASE_URL;
let reqPromise;

/**
 * 获取控件类型
 */
export function getControlTypeData() {
  if (!reqPromise) {
    reqPromise = new Promise((resolve, reject) => {
      request
        .get(BASE_URL + 'json/control-type.json', {
          baseURL: ''
        })
        .then((res) => {
          resolve(res.data ?? []);
        })
        .catch((e) => {
          reject(e);
        });
    });
  }
  return reqPromise;
}
