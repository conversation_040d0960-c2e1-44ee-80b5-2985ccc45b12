import { ref, computed } from 'vue';
import { list } from '@/views/base-code/common/api';

/**
 * 钱币评级相关动态代码表 Composable
 * 替代硬编码的代码表选项
 */
export function useCoinGradeOptions() {
  // 各类代码表数据
  const gradeScoreData = ref([]);
  const specialMarkData = ref([]);
  const authenticityData = ref([]);
  const coinRemarkData = ref([]);
  const starLevelData = ref([]);
  const boxTypeData = ref([]);
  const editionData = ref([]);

  // 数据加载状态
  const loading = ref(false);
  const loaded = ref(false);

  /**
   * 获取代码表数据
   */
  const fetchCodeData = async (codeType) => {
    try {
      const response = await list({ codeType });
      console.log(response);
      return (
        response?.map((item) => ({
          label: item.name,
          value: item.code
        })) || []
      );
    } catch (error) {
      console.error(`获取代码表失败: ${codeType}`, error);
      return [];
    }
  };

  /**
   * 加载所有钱币评级相关代码表
   */
  const loadAllCodeData = async () => {
    if (loaded.value) return;

    loading.value = true;
    try {
      // 并行加载所有代码表
      const [
        gradeScore,
        specialMark,
        authenticity,
        coinRemark,
        starLevel,
        boxType,
        edition
      ] = await Promise.all([
        fetchCodeData('gradeScore'), // 品相分数
        fetchCodeData('specialMark'), // 特殊标记
        fetchCodeData('authenticity'), // 真伪
        fetchCodeData('coinRemark'), // 钱币备注
        fetchCodeData('starLevel'), // 星级
        fetchCodeData('boxType'), // 盒子类型
        fetchCodeData('edition') // 版别
      ]);

      gradeScoreData.value = gradeScore;
      specialMarkData.value = specialMark;
      authenticityData.value = authenticity;
      coinRemarkData.value = coinRemark;
      starLevelData.value = starLevel;
      boxTypeData.value = boxType;
      editionData.value = edition;

      loaded.value = true;
    } catch (error) {
      console.error('加载钱币评级代码表失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 计算属性：转换为Element Plus需要的格式
  const gradeScoreOptions = computed(() => gradeScoreData.value);
  const specialMarkOptions = computed(() => specialMarkData.value);
  const authenticityOptions = computed(() => authenticityData.value);
  const coinRemarkOptions = computed(() => coinRemarkData.value);
  const starLevelOptions = computed(() => starLevelData.value);
  const boxTypeOptions = computed(() => boxTypeData.value);
  const editionOptions = computed(() => editionData.value);

  return {
    // 数据状态
    loading: computed(() => loading.value),
    loaded: computed(() => loaded.value),

    // 代码表选项
    gradeScoreOptions,
    specialMarkOptions,
    authenticityOptions,
    coinRemarkOptions,
    starLevelOptions,
    boxTypeOptions,
    editionOptions,

    // 方法
    loadAllCodeData,
    fetchCodeData
  };
}
