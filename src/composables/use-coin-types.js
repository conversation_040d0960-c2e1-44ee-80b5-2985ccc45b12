import { ref, computed, onMounted } from 'vue';
import codeCommonUtil from '@/utils/code-common-util';

/**
 * 钱币类型相关的组合式函数
 * 提供响应式的钱币类型数据和判断方法，替代硬编码
 */
export function useCoinTypes() {
  // 响应式数据
  const coinTypeOptions = ref([]);
  const coinTypeMap = ref({});
  const loading = ref(false);
  const error = ref(null);

  /**
   * 加载钱币类型数据
   */
  const loadCoinTypes = async () => {
    if (loading.value) return;

    loading.value = true;
    error.value = null;

    try {
      // 并行加载选项和映射表
      const [options, map] = await Promise.all([
        codeCommonUtil.getCoinTypeOptions(),
        codeCommonUtil.getCoinTypeMap()
      ]);

      coinTypeOptions.value = options;
      coinTypeMap.value = map;
    } catch (err) {
      error.value = err.message || '加载钱币类型数据失败';
      console.error('加载钱币类型数据失败:', err);

      // 使用默认数据作为后备
      coinTypeOptions.value = [
        { label: '纸币', value: 'banknote', code: 'banknote', sort: 1 },
        { label: '古钱币', value: 'ancientCoin', code: 'ancientCoin', sort: 2 },
        { label: '机制币', value: 'machineCoin', code: 'machineCoin', sort: 3 },
        { label: '银锭', value: 'silverIngot', code: 'silverIngot', sort: 4 }
      ];
      coinTypeMap.value = {
        banknote: '纸币',
        ancientCoin: '古钱币',
        machineCoin: '机制币',
        silverIngot: '银锭'
      };
    } finally {
      loading.value = false;
    }
  };

  /**
   * 获取钱币类型名称
   */
  const getCoinTypeName = (code) => {
    return coinTypeMap.value[code] || code;
  };

  /**
   * 获取钱币类型代码
   */
  const getCoinTypeCode = (name) => {
    for (const [code, mappedName] of Object.entries(coinTypeMap.value)) {
      if (mappedName === name) {
        return code;
      }
    }
    return name;
  };

  /**
   * 类型判断的计算属性工厂
   */
  const createTypeChecker = (targetType) => {
    return (coinType) =>
      computed(() => {
        if (!coinType) return false;
        const type = typeof coinType === 'string' ? coinType : coinType.value;
        return (
          type === targetType ||
          type === coinTypeMap.value[targetType] ||
          coinTypeMap.value[type] === targetType
        );
      });
  };

  /**
   * 具体的类型判断方法
   */
  const isBanknote = createTypeChecker('banknote');
  const isAncientCoin = createTypeChecker('ancientCoin');
  const isMachineCoin = createTypeChecker('machineCoin');
  const isSilverIngot = createTypeChecker('silverIngot');

  /**
   * 简单版本的类型判断 (非响应式)
   */
  const checkIsBanknote = (coinType) => {
    return (
      coinType === 'banknote' ||
      coinType === '纸币' ||
      coinType === coinTypeMap.value['banknote']
    );
  };

  const checkIsAncientCoin = (coinType) => {
    return (
      coinType === 'ancientCoin' ||
      coinType === '古钱币' ||
      coinType === coinTypeMap.value['ancientCoin']
    );
  };

  const checkIsMachineCoin = (coinType) => {
    return (
      coinType === 'machineCoin' ||
      coinType === '机制币' ||
      coinType === coinTypeMap.value['machineCoin']
    );
  };

  const checkIsSilverIngot = (coinType) => {
    return (
      coinType === 'silverIngot' ||
      coinType === '银锭' ||
      coinType === coinTypeMap.value['silverIngot']
    );
  };

  /**
   * 刷新缓存数据
   */
  const refreshData = async () => {
    codeCommonUtil.clearCache('coinTypeOptions');
    codeCommonUtil.clearCache('coinTypeMap');
    await loadCoinTypes();
  };

  // 组件挂载时自动加载数据
  onMounted(() => {
    loadCoinTypes();
  });

  return {
    // 响应式数据
    coinTypeOptions,
    coinTypeMap,
    loading,
    error,

    // 方法
    loadCoinTypes,
    getCoinTypeName,
    getCoinTypeCode,
    refreshData,

    // 响应式类型判断
    isBanknote,
    isAncientCoin,
    isMachineCoin,
    isSilverIngot,

    // 简单类型判断
    checkIsBanknote,
    checkIsAncientCoin,
    checkIsMachineCoin,
    checkIsSilverIngot
  };
}

/**
 * 专门用于选择器的组合式函数
 */
export function useCoinTypeSelect() {
  const { coinTypeOptions, loading, error, loadCoinTypes } = useCoinTypes();

  // 格式化为 el-select 需要的格式
  const selectOptions = computed(() => {
    return coinTypeOptions.value.map((item) => ({
      label: item.label,
      value: item.value,
      disabled: false
    }));
  });

  return {
    options: selectOptions,
    loading,
    error,
    loadCoinTypes
  };
}

/**
 * 专门用于条件渲染的组合式函数
 */
export function useCoinTypeConditions(coinType) {
  const { coinTypeMap } = useCoinTypes();

  const conditions = computed(() => {
    const type = typeof coinType === 'string' ? coinType : coinType?.value;
    if (!type || !coinTypeMap.value) return {};

    return {
      isBanknote: type === 'banknote' || type === coinTypeMap.value['banknote'],
      isAncientCoin:
        type === 'ancientCoin' || type === coinTypeMap.value['ancientCoin'],
      isMachineCoin:
        type === 'machineCoin' || type === coinTypeMap.value['machineCoin'],
      isSilverIngot:
        type === 'silverIngot' || type === coinTypeMap.value['silverIngot']
    };
  });

  return conditions;
}
