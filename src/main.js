import { createApp } from 'vue';
import App from './App.vue';
import store from './store/index.js';
import { useStore as vuexStore } from './store/store-vuex'; // 假设你的Vuex store文件名为store-vuex.js
import router from './router';

import permission from './utils/permission';
import DictData from '@/components/DictData/index.vue';
import i18n from './i18n';
import installer from './as-needed';
import 'element-plus/theme-chalk/display.css';
import 'ele-admin-plus/es/style/nprogress.scss';
import './styles/themes/rounded.scss';
import './styles/themes/dark.scss';
import './styles/index.scss';
// 如果您正在使用CDN引入，请删除下面一行。
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import nodeWrap from '@/components/DrawFlow/nodeWrap.vue';
import addNode from '@/components/DrawFlow/addNode.vue';

const app = createApp(App);

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(store);
app.use(vuexStore);
app.use(router);
app.use(permission);
app.use(i18n);
app.use(installer);
app.component('DictData', DictData);
app.mount('#app');

app.component('NodeWrap', nodeWrap); //初始化组件
app.component('AddNode', addNode); //初始化组件

app.directive('focus', {
  mounted(el) {
    el.focus();
  }
});

app.directive('enterNumber', {
  mounted(el, { value = 100 }, vnode) {
    el = el.nodeName == 'INPUT' ? el : el.children[0];
    var RegStr =
      value == 0
        ? `^[\\+\\-]?\\d+\\d{0,0}`
        : `^[\\+\\-]?\\d+\\.?\\d{0,${value}}`;
    el.addEventListener('input', function () {
      el.value = el.value.match(new RegExp(RegStr, 'g'));
      el.dispatchEvent(new Event('input'));
    });
  }
});
