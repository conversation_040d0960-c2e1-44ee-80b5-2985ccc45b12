<!-- 切换角色弹框 -->
<template>
  <ele-modal
    form
    :width="300"
    title="切换角色"
    :append-to-body="true"
    :model-value="modelValue"
    @update:modelValue="updateModelValue"
    @closed="onCancel"
  >
    <div
      style="
        min-height: 200px;
        max-height: 500px;
        overflow-y: auto;
        padding: 0px 0 10px 0;
      "
    >
      <el-button
        size="Default"
        class="cus-button"
        v-for="role in props.data"
        :key="role.id"
        @click="changeRoleClick(role)"
        ><span>{{ role.name }} </span></el-button
      >
    </div>
  </ele-modal>
</template>

<script setup>
  import { setCurrentRole } from '@/utils/current-role-util';
  import { switchRole } from '@/api/layout';
  import { EleMessage } from 'ele-admin-plus';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    modelValue: <PERSON>olean,
    /** 角色数据 */
    data: Array
  });
  /** 用户切换角色 */
  const changeRoleClick = (role) => {
    updateModelValue(false);
    let newObj = {
      realName: null,
      roleId: role.id,
      roleName: role.name
    };
    setCurrentRole(newObj);

    /** 获取角色数据 */
    switchRole(role.id)
      .then(() => {})
      .catch((e) => {
        EleMessage.error(e.message);
      });
    //全局刷新
    location.reload();
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
</script>

<style lang="scss" scoped>
  .cus-button {
    display: block;
    width: 100%;
    margin: 5px 0px !important;
  }
</style>
