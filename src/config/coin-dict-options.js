// 钱币管理系统码表选项配置
// 从website_backup/coinsm.html源码中提取的完整码表数据

/**
 * 版别选项 (180+ 个版别)
 */
export const EDITION_OPTIONS = [
  '汽车',
  '南昌',
  '6张纪念钞组合',
  '飞机',
  '轮船',
  '拾圆伍圆贰圆壹圆贰角（荧光钞）',
  '民族人物头像',
  '纺织',
  '女拖拉机手',
  '大团结',
  '长江大桥',
  '毛泽东头像',
  '建国纪念钞',
  '拖拉机',
  '工人农民',
  '龙年纪念钞',
  '大黑拾',
  '红库印',
  '车床工人',
  '币签合一',
  '炼钢工人',
  '工农知识分子头像',
  '教育与生产劳动相结合',
  '各民族大团结',
  '龙钞纪念币',
  '水坝',
  '水电站',
  '建设公债',
  '斐济财神钞',
  '第四套套装 分币组合',
  '银元券',
  '航天钞 2018世界杯 索契冬奥纪念币组合',
  '省立、兑换劵',
  '老假',
  '双马耕地',
  '永泰县',
  '福建',
  '火车',
  '兑换劵',
  '井冈山',
  '天安门',
  '（广东省集藏投资协会监制）',
  '宝塔山',
  '四位领袖浮雕头像',
  '航天纪念钞',
  '迎接新世纪纪念钞',
  '塑料钞',
  '上海',
  '花版',
  '地方流通劵',
  '鸟版',
  '保值公债',
  '黄钥匙',
  '罗马兵',
  '帆船',
  '小棉胎',
  '蓝屋',
  '吉林',
  '寿光',
  '东三省',
  '汇兑券',
  '省立',
  '兑米票',
  '三明市企业内部债券',
  '昌黎',
  '重庆、银元辅币券',
  '琼崖区流通券',
  '银元辅币券',
  '私人印制',
  '大汉四川军政府军用银票',
  '山西',
  '兑换券（纪念券）',
  '左右号不同',
  'Bank of China',
  '预备乘车券',
  '甲辰龙年贺岁纪念钞',
  '船版',
  '纸币套装 伍圆 贰圆 壹圆',
  '本票',
  '益阳二里',
  '太原',
  '革命战争共债劵',
  '2024年斐济龙年塑料钞',
  '四版贰圆贰角/三版贰角/飞机',
  '广州、银元券',
  '合作券',
  '东北九省流通券',
  '天津',
  '三罗码',
  '金圆券',
  '国库券',
  '外汇兑换券',
  '香港',
  '重庆',
  '张家口',
  '捆刀封签',
  '银毫券',
  '北京',
  '厦门',
  '东三省、小银元券',
  '河南',
  '杭州',
  '山东、兑换券',
  '哈尔滨',
  '股权证',
  '股票',
  '兑换券',
  '张家口丰镇',
  '梧州',
  '福州',
  '京兆',
  '节约建国储蓄券',
  '牌坊',
  '北平',
  '济南',
  '第四版人民币 伍圆 贰圆 壹圆',
  '第24届北京冬季奥林匹克运动会纪念钞',
  '奥运会纪念钞',
  '成立一百五十周年纪念',
  '70周年纪念钞',
  '冬奥会纪念钞',
  '关金券',
  '经济建设公债',
  '广东',
  '广州',
  '汽车、飞机、轮船',
  '湘赣省分行',
  '革命战争公债券',
  '票样正面',
  '八同号',
  '票样反面',
  '甘肃',
  '辽东',
  '大洋票',
  '山东',
  '驮运',
  '银毫劵',
  '蛇年纪念钞',
  '纪念中国银行成立一百周年',
  '汕头',
  '地方经济建设公债',
  '支票',
  '第四套套装',
  '七十周年纪念钞',
  '第三套18.88套装',
  '小小三套装 粮票组合',
  '人民代表步出大会堂',
  '521套装',
  '1953年分币套装',
  '冰上运动雪上运动组合',
  '第24届冬奥会纪念钞',
  '退市第四套人民币套装组合',
  '中华民国三十八年',
  '关金伍仟圆',
  '四版贰圆贰角/三版贰角 三枚组合',
  '维吾尔族、彝族 长江大桥组合',
  '第四套套装 十全十美',
  '粮票组合套装',
  '布依族、朝鲜族,长江大桥组合',
  '伍分轮船 贰分飞机 壹分汽车',
  '小小三套装',
  '男皇蓝色版',
  '男皇紫色版',
  '小圣书',
  '大棉胎',
  '七小福套装',
  '第三套18.88套装 粮票组合',
  '东北九省流通券',
  '第四版人民币 壹佰圆 索契冬奥钞',
  '70周年纪念钞 世界杯纪念钞 北京冬奥会纪念钞',
  '四版贰圆贰角/贰分',
  '第三版人民币组合（综合分）',
  '三版伍圆/四版伍圆组合',
  '第三套/第四套拾圆组合',
  '纤云.金光星辉',
  '中华民国三十六年',
  '第四套套装 粮票组合',
  '庆祝中华人民共和国成立50周年',
  '小四套装'
];

/**
 * 品相分数选项 (26个等级)
 */
export const GRADE_SCORE_OPTIONS = [
  { label: 'Superb Gem Unc70', value: 'Superb Gem Unc70' },
  { label: 'Superb Gem Unc69', value: 'Superb Gem Unc69' },
  { label: 'Superb Gem Unc68', value: 'Superb Gem Unc68' },
  { label: 'Superb Gem Unc67', value: 'Superb Gem Unc67' },
  { label: 'Gem Uncirculated66', value: 'Gem Uncirculated66' },
  { label: 'Gem Uncirculated65', value: 'Gem Uncirculated65' },
  { label: 'Choice Uncirculated64', value: 'Choice Uncirculated64' },
  { label: 'Choice Uncirculated63', value: 'Choice Uncirculated63' },
  { label: 'Uncirculated62', value: 'Uncirculated62' },
  { label: 'Uncirculated61', value: 'Uncirculated61' },
  { label: 'Uncirculated60', value: 'Uncirculated60' },
  { label: 'Choice About Unc58', value: 'Choice About Unc58' },
  { label: 'About Uncirculated55', value: 'About Uncirculated55' },
  { label: 'About Uncirculated53', value: 'About Uncirculated53' },
  { label: 'About Uncirculated50', value: 'About Uncirculated50' },
  { label: 'Choice Extremely Fine45', value: 'Choice Extremely Fine45' },
  { label: 'Extremely Fine40', value: 'Extremely Fine40' },
  { label: 'Choice Very Fine35', value: 'Choice Very Fine35' },
  { label: 'Very Fine30', value: 'Very Fine30' },
  { label: 'Very Fine25', value: 'Very Fine25' },
  { label: 'Genuine', value: 'Genuine' },
  { label: 'Genuine真品', value: 'Genuine真品' },
  { label: '极美品', value: '极美品' },
  { label: '精美品', value: '精美品' },
  { label: '美品', value: '美品' },
  { label: '上美品', value: '上美品' }
];

/**
 * 特殊标记选项 (★/EPQ/NET)
 */
export const SPECIAL_MARK_OPTIONS = [
  { label: '★', value: '★' },
  { label: 'EPQ', value: 'EPQ' },
  { label: 'NET', value: 'NET' }
];

/**
 * 真伪选项 (11个编码选项)
 */
export const AUTHENTICITY_OPTIONS = [
  { label: '未鉴定', value: '0', code: '0' },
  { label: '真', value: '1', code: '1' },
  { label: '赝品', value: '2', code: '2' },
  { label: '存疑', value: '3', code: '3' },
  { label: '不提供服务', value: '4', code: '4' },
  { label: '不适合评级(赝品)', value: '5', code: '5' },
  { label: '不适合评级(锭体、铭文修复超出规定)', value: '6', code: '6' },
  { label: '撤评', value: '7', code: '7' },
  { label: '不适合评级', value: '8', code: '8' },
  { label: '不适合评级(老假、老仿、臆造)', value: '9', code: '9' },
  { label: '暂不提供服务(香港)', value: '10', code: '10' }
];

/**
 * 钱币备注选项 (9个价格区间)
 */
export const COIN_REMARK_OPTIONS = [
  { label: '1000以内', value: '1000以内' },
  { label: '2000以内', value: '2000以内' },
  { label: '3000以内', value: '3000以内' },
  { label: '5000以内', value: '5000以内' },
  { label: '8000以内', value: '8000以内' },
  { label: '10000以内', value: '10000以内' },
  { label: '20000以内', value: '20000以内' },
  { label: '30000以内', value: '30000以内' },
  { label: '50000以内', value: '50000以内' }
];

/**
 * 星级选项
 */
export const STAR_LEVEL_OPTIONS = [
  { label: '五星', value: '五星' },
  { label: '四星', value: '四星' },
  { label: '三星', value: '三星' }
];

/**
 * 盒子类型选项
 */
export const BOX_TYPE_OPTIONS = [
  { label: '密封盒', value: '密封盒' },
  { label: '开放式', value: '开放式' },
  { label: '证书', value: '证书' }
];

/**
 * 钱币类型选项 (已重构为动态获取)
 * @deprecated 请使用 getCoinTypeOptions() 从后端动态获取
 * @see /src/api/code-common.js
 */
export const COIN_TYPE_OPTIONS = [
  // 注意: 这里保留作为后备选项，生产环境应使用动态获取的数据
  { label: '纸币', value: 'banknote', code: 'banknote' },
  { label: '古钱币', value: 'ancientCoin', code: 'ancientCoin' },
  { label: '机制币', value: 'machineCoin', code: 'machineCoin' },
  { label: '银锭', value: 'silverIngot', code: 'silverIngot' }
];

/**
 * 获取动态钱币类型选项 (推荐使用)
 * 替代上面的硬编码 COIN_TYPE_OPTIONS
 */
import { getCoinTypeOptions } from '@/api/code-common';
export { getCoinTypeOptions };

/**
 * 格式化版别选项为下拉选择格式
 */
export const getEditionOptions = () => {
  return EDITION_OPTIONS.map((item) => ({
    label: item,
    value: item
  }));
};

/**
 * 格式化真伪选项为下拉选择格式
 */
export const getAuthenticityOptions = () => {
  return AUTHENTICITY_OPTIONS.map((item) => ({
    label: item.label,
    value: item.value,
    code: item.code
  }));
};

/**
 * 根据编码获取真伪标签
 */
export const getAuthenticityLabel = (code) => {
  const option = AUTHENTICITY_OPTIONS.find((item) => item.code === code);
  return option ? option.label : '';
};

/**
 * 根据值获取真伪编码
 */
export const getAuthenticityCode = (value) => {
  const option = AUTHENTICITY_OPTIONS.find((item) => item.value === value);
  return option ? option.code : '';
};
