/*
 * @Date: 2022-08-25 14:13:11
 * @LastEditors: StavinLi <EMAIL>
 * @LastEditTime: 2022-09-21 14:37:09
 * @FilePath: /Workflow-Vue3/src/store/index.js
 */
import { createStore } from 'vuex';
import createPersistedState from 'vuex-persistedstate';

export const useStore = createStore({
  plugins: [
    createPersistedState({
      storage: window.sessionStorage, // 使用 sessionStorage 进行持久化
      paths: ['sharedZizhuData', 'sharedWorkFlowData', 'sharedPersonData'] // 只持久化 sharedZizhuData 这个 state
      // reducer(val) {
      //     return {
      //         sharedZizhuData: val.sharedZizhuData,//只存储state里的sharedZizhuData
      //     }
      // }
    })
    // __DEV__ && createLogger() // 开发环境下使用日志插件
  ],
  state: {
    tableId: '',
    isTried: false,
    promoterDrawer: false,
    flowPermission1: {},
    approverDrawer: false,
    approverConfig1: {},
    copyerDrawer: false,
    copyerConfig1: {},
    conditionDrawer: false,
    conditionsConfig1: {
      conditionNodes: []
    },
    sharedZizhuData: null, //资助详情参数
    sharedWorkFlowData: null, //工作流详情参数
    sharedPersonData: null //人员详情参数
  },
  mutations: {
    setSharedZizhuData(state, payload) {
      state.sharedZizhuData = payload;
      localStorage.setItem('ZIZHU', JSON.stringify(payload));
    },
    setSharedWorkFlowData(state, payload) {
      state.sharedWorkFlowData = payload;
      localStorage.setItem('WORKFLOW', JSON.stringify(payload));
    },
    setSharedPersonData(state, payload) {
      state.sharedPersonData = payload;
      localStorage.setItem('PERSON', JSON.stringify(payload));
    },
    setTableId(status, payload) {
      status.tableId = payload;
    },
    setIsTried(status, payload) {
      status.isTried = payload;
    },
    setPromoter(status, payload) {
      status.promoterDrawer = payload;
    },
    setFlowPermission(status, payload) {
      status.flowPermission1 = payload;
    },
    setApprover(status, payload) {
      status.approverDrawer = payload;
    },
    setApproverConfig(status, payload) {
      status.approverConfig1 = payload;
    },
    setCopyer(status, payload) {
      status.copyerDrawer = payload;
    },
    setCopyerConfig(status, payload) {
      status.copyerConfig1 = payload;
    },
    setCondition(status, payload) {
      status.conditionDrawer = payload;
    },
    setConditionsConfig(status, payload) {
      status.conditionsConfig1 = payload;
    }
  },
  actions: {},
  modules: {}
});
