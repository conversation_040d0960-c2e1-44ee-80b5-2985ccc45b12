<template>
  <el-config-provider :locale="elLocale">
    <ele-config-provider
      :locale="eleLocale"
      :map-key="MAP_KEY"
      :table="tableConfig"
      :license="LICENSE_CODE"
    >
      <ele-app>
        <router-view />
      </ele-app>
    </ele-config-provider>
  </el-config-provider>
</template>

<script setup>
  import { ref } from 'vue';
  import { useThemeStore } from '@/store/modules/theme';
  import { LICENSE_CODE, MAP_KEY } from '@/config/setting';
  import { useLocale } from '@/i18n/use-locale';

  window._AMapSecurityConfig = {
    securityJsCode: '329977d48d064c5483498a44d3618d98'
  };

  // 恢复主题
  const themeStore = useThemeStore();
  themeStore.recoverTheme();

  // 国际化配置
  const { elLocale, eleLocale } = useLocale();

  // 高级表格全局配置
  const tableConfig = ref({
    response: {
      dataName: 'list',
      countName: 'count'
    }
  });
</script>

<style lang="scss">
  /**隐藏高德自带的logo*/
  .amap-logo {
    display: none !important;
  }

  .amap-copyright {
    bottom: -100px;
    display: none !important;
  }

  .ele-drawer-header {
    padding: 10px 16px !important;
  }

  .ele-drawer-body {
    padding: 16px !important;
  }

  .ele-card-header {
    padding: 5px 10px !important;
  }

  .ele-card-body {
    padding: 16px 0 16px 0 !important;
  }

  .el-form-item {
    margin-bottom: 15px !important;
  }
</style>
