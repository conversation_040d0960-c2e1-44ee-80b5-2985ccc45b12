/** 全局样式 */
@use 'element-plus/theme-chalk/src/mixins/function.scss' as *;
@use './transition.scss' as *;

* {
  outline: none;
}

html {
  overflow: auto;
}

body .el-table--default {
  font-size: 12px !important;
}


/* 钉钉进步体*/
// @font-face {
//   font-family: 'DingTalk';
//   src: (url('/font/DingTalkSans.ttf'));
//   font-display: swap;
// }

/* 中文字体-微软雅黑*/
@font-face {
  font-family: 'Microsoft Yahei';
  src: (url('/font/msyh.ttf'));
  font-display: swap;
}

/* 英文字体-Arial*/
@font-face {
  font-family: 'Arial';
  src: (url('/font/arial.ttf'));
  font-display: swap;
}

/* 数字+英文字体-DIN*/
@font-face {
  font-family: 'DIN-Light';
  src: (url('/font/DIN-Light.otf'));
  font-display: swap;
}
@font-face {
  font-family: 'DIN-Bold';
  src: (url('/font/DIN-Bold.otf'));
  font-display: swap;
}

body {
  margin: 0;
  line-height: 1.58;
  color: getCssVar('text-color', 'regular');
  font-size: getCssVar('font-size', 'base');
  //font-family: getCssVar('font-family');
  font-family: 'Microsoft Yahei,Arial,sans-serif!important';
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow-x: hidden;
  overflow-y: auto;
  height: 100vh;
}

/* 关闭响应式 */
body.ele-body-limited {
  min-width: 1200px;
}

/* 色弱模式 */
.ele-admin-weak {
  filter: invert(0.8);
}

/* 按钮加图标减少间距 */
.ele-btn-icon.el-button,
.ele-btn-icon.el-button.is-round {
  padding-left: 10px;
  padding-right: 12px;

  &.el-button--small {
    padding-left: 5px;
    padding-right: 6px;
  }

  &.el-button--large {
    padding-left: 14px;
    padding-right: 16px;
  }
}

/* 级联选择器增加高度 */
.ele-popper-higher .el-cascader-menu__wrap.el-scrollbar__wrap {
  height: 280px;
}

/* 间距组件样式优化 */
.el-space--horizontal > .el-space__item:last-child {
  margin-right: 0 !important;
}

.el-space--vertical > .el-space__item:last-child {
  padding-bottom: 0 !important;
}

/* echarts */
.echarts > div > div {
  max-width: 100%;
  overflow: hidden;
}

/* 小屏幕时分页去掉一些组件 */
@media screen and (max-width: 768px) {
  .ele-pro-table .el-pagination {
    .el-pagination__sizes,
    .el-pagination__jump {
      display: none;
    }
  }
}

/* 暗黑模式切换过渡 */
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

::view-transition-old(root) {
  z-index: 2147483646;
}

::view-transition-new(root) {
  z-index: 1;
}

.dark::view-transition-old(root) {
  z-index: 1;
}

.dark::view-transition-new(root) {
  z-index: 2147483646;
}

/** ele-card 全局样式重写 */
body .ele-card {
  margin-bottom: 8px !important;
  border-radius: 0 !important;
}

body .ele-page:not(.is-plain) {
  padding: 8px 8px 0 8px !important;
  border-radius: 0 !important;
}

////或者加!important
//.ele-card-body {
//  padding: 10px !important;
//}
