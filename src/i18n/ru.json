{"请选择字段": "Пожалуйста, выберите поле", "计数": "Подсчет", "合计": "Сумма", "平均值": "Среднее значение", "最小值": "Мини<PERSON>ум", "最大值": "Максимум", "此格式不支持该文本": "Этот формат не поддерживает такой текст", "二维码生成失败": "Сбой генерации QR-кода", "字体行高": "Высота строки шрифта", "默认": "По умолчанию", "字体": "<PERSON>ри<PERSON><PERSON>", "宋体": "Sim<PERSON>un", "微软雅黑": "Microsoft YaHei", "字体大小": "Размер шрифта", "字体粗细": "Тол<PERSON>ина шрифта", "更细": "Тоньше", "粗体": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "粗体+": "Жир<PERSON><PERSON><PERSON>", "字间距": "Межбуквенный интервал", "左右对齐": "По ширине", "居左": "По левому краю", "居中": "По центру", "居右": "По правому краю", "两端对齐": "По ширине", "标题显示隐藏": "Отображение заголовка", "显示": "Показывать", "隐藏": "Скрыть", "表格边框": "Граница таблицы", "有边框": "Да", "无边框": "Нет", "表头边框": "Граница заголовка", "左边框": "Левая граница", "右边框": "Правая граница", "左右边框": "Левая и правая границы", "上边框": "Верхняя граница", "下边框": "Нижняя граница", "上下边框": "Верхняя и нижняя границы", "表头单元格边框": "Граница ячейки заголовка", "表尾边框": "Граница подвала", "表尾单元格边框": "Граница ячейки подвала", "表头行高": "Высота строки заголовка", "表头字体大小": "Размер шрифта заголовка", "表头字体粗细": "Толщина шрифта заголовка", "表体单元格边框": "Граница ячейки основной части", "表体行高": "Высота строки основной части", "表头背景": "Фон заголовка", "线宽": "Толщина линии", "边框大小": "Размер границы", "条形码格式": "Тип штрихкода", "商品条码": "Штрихкод товара", "条形码": "Штрихкод", "物流": "Логистика", "邮政和快递编码": "Почтовый и курьерский код", "医疗产品编码": "Код медицинского продукта", "不常用编码": "Нестандартный код", "附加组件": "Дополнительный компонент", "实验编码": "Экспериментальный код", "条码类型": "Тип штрихкода", "二维码类型": "Тип QR-кода", "二维码容错率": "Уровень коррекции ошибок QR-кода", "字体颜色": "Цвет шрифта", "文本修饰": "Оформление текста", "下划线": "Подчеркивание", "上划线": "Надчеркивание", "穿梭线": "Перечеркивание", "字段名": "Имя поля", "请输入字段名": "Пожалуйста, введите имя поля", "标题": "Заголовок", "请输入标题": "Пожалуйста, введите заголовок", "测试数据": "Тестовые данные", "仅字段名称存在时有效": "Действительно только если существует имя поля", "位置坐标": "Координаты позиции", "X位置(左)": "Позиция X (слева)", "Y位置(上)": "Позиция Y (сверху)", "同步": "Синхронизировать", "不同步": "Не синхронизировать", "宽高大小": "Ширина и высота", "宽": "Ши<PERSON><PERSON><PERSON>", "高": "Высота", "图片地址": "URL изображения", "请输入图片地址": "Пожалуйста, введите URL изображения", "选择": "Выбрать", "图片缩放": "Масштаб изображения", "等比": "Пропорционально", "裁切": "Обрезать", "填充": "Заполнить", "原始尺寸": "Исходный размер", "颜色": "Цвет", "边框颜色": "Цвет границы", "水印功能": "Водяной знак", "水印内容": "Содержание водяного знака", "旋转角度": "Угол поворота", "水平密度": "Горизонтальная плотность", "垂直密度": "Вертикальная плотность", "水印时间": "Время водяного знака", "时间格式": "Формат времени", "页码格式": "Формат номера страницы", "显示页码": "Показывать номер страницы", "页码续排": "Продолжить нумерацию страниц", "续排": "Продолжить", "重排": "Сбросить", "每行缩进": "Отступ строки", "显示规则": "Правило отображения", "始终隐藏": "Всегда скрывать", "首页": "Первая страница", "奇数页": "Нечетные страницы", "偶数页": "Четные страницы", "尾页": "Последняя страница", "强制分页": "Принудительный разрыв страницы", "是": "Да", "否": "Нет", "打印规则": "Правило печати", "保持奇数": "Сохранять нечетные", "保持偶数": "Сохранять четные", "分页规则": "Правило разрыва страниц", "不分页": "Без разрыва страниц", "移除段落左侧空白": "Убрать левый отступ", "移除": "Удалить", "不移除": "Не удалять", "首页页尾": "Нижний колонтитул первой страницы", "尾页页尾": "Нижний колонтитул последней страницы", "偶数页页尾": "Нижний колонтитул на четных страницах", "奇数页页尾": "Нижний колонтитул на нечетных страницах", "位置固定": "Фиксированная позиция", "拖动方向": "Направление перетаскивания", "横向": "Горизонтально", "竖向": "Вертикально", "左偏移": "Смещение слева", "偏移量": "Смещение", "最低高度": "Минимальная высота", "文本过短或为空时的高度": "Высота при слишком коротком или пустом тексте", "隐藏规则": "Правило скрытия", "表体行边框": "Граница строки основной части", "元素层级": "Z-индекс", "边框设置": "Настройки границы", "实线": "Сплошная линия", "虚线": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "左内边距": "Отступ слева", "上内边距": "Отступ сверху", "右内边距": "Отступ справа", "下内边距": "Отступ снизу", "样式": "Стиль", "边框样式": "Стиль границы", "长虚线": "Длинный пунктир", "短虚线": "Короткий пунктир", "背景颜色": "Цвет фона", "纸张方向(仅自定义纸质有效)": "Ориентация бумаги (только пользовательская)", "纵向": "Книжная", "上下对齐": "Вертикальное выравнивание", "垂直居中": "По центру по вертикали", "底部": "Внизу", "文本换行": "Перенос строк", "不换行": "Без переноса строк", "不换行&隐藏": "Без переноса строк и скрыть", "不换行&省略": "Без переноса строк и многоточие", "打印类型": "Тип печати", "文本": "Текст", "二维码": "QR-код", "字段类型": "Тип поля", "默认(文本)": "По умолчанию (текст)", "序号": "Ин<PERSON><PERSON><PERSON>с", "图片": "Изображение", "单元格高度": "Высота ячейки", "条形码、二维码以及图片有效": "Штрихкод, QR-код и изображение действительны", "底部聚合标题": "Заголовок итогов", "底部聚合文本": "Текст итогов", "聚合类型": "Ти<PERSON> итогов", "底部聚合合并列数": "Количество объединенных столбцов итогов", "合并列数": "Количество объединенных столбцов", "底部聚合类型左右对齐": "Выравнивание итогов", "整数": "Целое число", "保留%s位": "Оставить %s знаков после запятой", "底部聚合小数": "Дробная часть итогов", "转大小写": "Верхний или нижний регистр", "底部聚合类型": "Ти<PERSON> итогов", "不聚合": "Без итогов", "仅文本": "Только текст", "顶部偏移": "Смещение сверху", "一行多组": "Несколько групп в строке", "一行二列": "Одна строка две колонки", "一行三列": "Одна строка три колонки", "一行四列": "Одна строка четыре колонки", "一行多组间隔": "Интервал между группами в строке", "表格头显示": "Отображение заголовка таблицы", "每页显示": "Показывать на странице", "首页显示": "Показывать на первой странице", "不显示": "Не показывать", "数据类型": "Тип данных", "日期时间": "Дата и время", "布尔": "Логический", "格式": "Формат", "格式化函数": "Функция форматирования", "样式函数": "Функция стиля", "行/列合并函数": "Объединение строки/столбца", "跨页合并是否清除": "Очистить объединение перед разрывом страницы", "表格脚函数": "Функция нижнего колонтитула таблицы", "分组字段函数": "Функция поля группировки", "分组头格式化函数": "Функция форматирования заголовка группы", "分组头信息": "Информация заголовка группы", "分组脚格式化函数": "Функция форматирования подвала группы", "分组脚信息": "Информация подвала группы", "多组表格脚函数": "Функция нижнего колонтитула таблицы с несколькими группами", "行样式函数": "Функция стиля строки", "单元格左右对齐": "Выравнивание ячеек по горизонтали", "单元格上下对齐": "Вертикальное выравнивание ячеек", "上": "<PERSON><PERSON>е<PERSON><PERSON><PERSON>", "中": "По центру", "表格头单元格左右对齐": "Горизонтальное выравнивание ячеек заголовка", "单元格样式函数": "Функция стиля ячейки", "表格头样式函数": "Функция стиля заголовка таблицы", "单元格格式化函数": "Функция форматирования ячейки", "单元格渲染函数": "Функция отрисовки ячейки", "自动补全": "Автозаполнение", "每页最大行数": "Макс. строк на странице", "表格脚显示": "Отображение нижнего колонтитула таблицы", "最后显示": "Показать на последней странице", "没有足够空间进行表格分页，请调整页眉/页脚线": "Недостаточно места для постраничного разделения таблицы, отрегулируйте строку заголовка/подвала", "没有足够空间,显示下方内容, 可分页高度": "Недостаточно места, показать содержимое ниже, высоту можно разбить на страницы:", "列属性": "Свойство столбца", "在上方插入行": "Вставить строку выше", "在下方插入行": "Вставить строку ниже", "向左方插入列": "Вставить столбец слева", "向右方插入列": "Вставить столбец справа", "删除行": "Удалить строку", "删除列": "Удалить столбец", "对齐": "Выравнивание", "左": "Слева", "左右居中": "По центру", "右": "Справа", "下": "Внизу", "合并单元格": "Объединить ячейки", "解开单元格": "Разъединить ячейки", "条形码生成失败": "Сбой генерации штрихкода", "请检查 hiprint.init 的 provider 是否配置了": "Проверьте, настроен ли поставщик hiprint.init", "已移除'tableCustom',请替换使用'table'详情见更新记录": "'TableCustom' была удалена, замените ее на 'table', подробности см. в журнале обновлений", "确定": "Подтвердить", "删除": "Удалить", "连接客户端失败": "Не удалось подключиться к клиенту", "基础": "Основные", "边框": "Гра<PERSON><PERSON><PERSON>а", "列": "Столбец", "高级": "Дополнительно", "面板排列": "Расположение панели", "排列方式": "Способ распределения", "垂直间距%s": "Вертикальный интервал %s", "水平间距%s": "Горизонтальный интервал %s", "边框圆角": "Радиус границы", "显示码值": "Отображение кодового значения"}