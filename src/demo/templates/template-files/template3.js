export const name = '合并示例';
export const desc = '首列合并示例';
export const author = 'CcSimple'
export const link = 'https://github.com/CcSimple'
// url 或者 base64 或者 require('../../../assets/logo.png')
export const preview = '/static/template3.png';
export const printData = {
  table: [
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 217001,
      name2: "普通课程名称 1",
      type: "学位课",
      time: 48,
      score: 3,
      score2: 75,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考试"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258905,
      name2: "普通课程名称 2",
      type: "学位课",
      time: 32,
      score: 2,
      score2: 84,
      start: "21年春季",
      tName2: "张丽莎",
      type2: "考试"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 217502,
      name2: "普通课程名称 3",
      type: "学位课",
      time: 16,
      score: 1,
      score2: 91,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考试"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258904,
      name2: "高级不普通的课程名称 1",
      type: "学位课",
      time: 32,
      score: 2,
      score2: 86,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考试"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258903,
      name2: "高级不普通的课程名称 2",
      type: "学位课",
      time: 48,
      score: 3,
      score2: 90,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考试"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 259307,
      name2: "高级不普通的课程名称 3",
      type: "学位课",
      time: 32,
      score: 2,
      score2: 69,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考试"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258906,
      name2: "高级不普通的课程名称 4",
      type: "学位课",
      time: 32,
      score: 2,
      score2: 91,
      start: "21年春季",
      tName2: "张丽莎",
      type2: "考试",
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 217503,
      name2: "普通的课程名称 6",
      type: "学位课",
      time: 32,
      score: 2,
      score2: 80,
      start: "21年春季",
      tName2: "张丽莎",
      type2: "考试"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 216001,
      name2: "普通的课程名称 7",
      type: "必修环节",
      time: 16,
      score: 1,
      score2: 79,
      start: "21年春季",
      tName2: "张丽莎",
      type2: "考查"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258919,
      name2: "普通的课程名称 8",
      type: "必修环节",
      time: 0,
      score: 6,
      score2: 0,
      start: "21年秋季",
      tName2: "张丽莎",
      type2: ""
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258915,
      name2: "高级不普通的课程名称 6",
      type: "选修课",
      time: 16,
      score: 1,
      score2: 87,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考查"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258911,
      name2: "高级不普通的课程名称 7",
      type: "选修课",
      time: 32,
      score: 2,
      score2: 91,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考查"
    },
    {
      code: 2223068061,
      uName: "张三",
      tName: "李丽",
      id: 258907,
      name2: "高级不普通的课程名称 8",
      type: "选修课",
      time: 32,
      score: 2,
      score2: 82,
      start: "20年秋季",
      tName2: "张丽莎",
      type2: "考试"
    },
  ]
}
export const json = {
  "panels": [{
    "index": 0, "name": 1, "height": 296.6, "width": 210, "paperHeader": 24, "paperFooter": 805.5, "printElements": [{
      "options": {
        "left": 15,
        "top": 42,
        "height": 64.5,
        "width": 555,
        "field": "table",
        "coordinateSync": false,
        "widthHeightSync": false,
        "right": 565,
        "bottom": 96,
        "vCenter": 290,
        "hCenter": 69,
        "tableHeaderRowHeight": 30,
        "tableBodyRowHeight": 30,
        "rowsColumnsMerge": "function (data, col, colIndex, rowIndex) {\n  console.log('data', data);\n  console.log('col', col);\n  console.log('colIndex', colIndex);\n  console.log('rowIndex', rowIndex);\n  // 返回一个数组,参数一为行（rowspan）合并数,参数二为列（colspan）合并数, 被合并的行或者列值设为0\n  if (rowIndex >= 0 && rowIndex < 8) {\n    return colIndex == 0 ? [rowIndex == 0 ? 8 - 0 : 0, 1] : [1, 1];\n  } else if (rowIndex >= 8 && rowIndex < 10) {\n    return colIndex == 0 ? [rowIndex == 8 ? 10 - 8 : 0, 1] : [1, 1];\n  } else if (rowIndex >= 10 && rowIndex < 13) {\n    return colIndex == 0 ? [rowIndex == 10 ? 13 - 10 : 0, 1] : [1, 1];\n  } else {\n    return [1, 1]\n  }\n}",
        "rowsColumnsMergeClean": true,
        "tableHeaderRepeat": "first",
        "columns": [[{
          "width": 51.517525000000006,
          "title": "课程<br/>类别",
          "field": "type",
          "checked": true,
          "columnId": "type",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 125.68322499999994,
          "title": "课程编号",
          "field": "id",
          "checked": true,
          "columnId": "id",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 212.86675000000005,
          "title": "课程名称",
          "field": "name2",
          "checked": true,
          "columnId": "name2",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 55.02175000000007,
          "title": "学时",
          "field": "time",
          "checked": true,
          "columnId": "time",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 54.910749999999936,
          "title": "学分",
          "field": "score",
          "checked": true,
          "columnId": "score",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 100,
          "title": "开课时间",
          "field": "start",
          "checked": true,
          "columnId": "start",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 100,
          "title": "任课老师",
          "field": "tName2",
          "checked": true,
          "columnId": "tName2",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 100,
          "title": "考核成绩",
          "field": "score2",
          "checked": true,
          "columnId": "score2",
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }, {
          "width": 100,
          "title": "备注",
          "checked": true,
          "fixed": false,
          "rowspan": 1,
          "colspan": 1,
          "align": "center",
          "tableColumnHeight": "30",
          "tableTextType": "text",
          "tableBarcodeMode": "CODE128A",
          "tableQRCodeLevel": 0,
          "tableSummaryTitle": true,
          "tableSummary": ""
        }]]
      },
      "printElementType": {
        "title": "订单数据",
        "type": "table",
        "editable": true,
        "columnDisplayEditable": true,
        "columnDisplayIndexEditable": true,
        "columnTitleEditable": true,
        "columnResizable": true,
        "columnAlignEditable": true,
        "isEnableEditField": true,
        "isEnableContextMenu": true,
        "isEnableInsertRow": true,
        "isEnableDeleteRow": true,
        "isEnableInsertColumn": true,
        "isEnableDeleteColumn": true,
        "isEnableMergeCell": true
      }
    }], "paperNumberLeft": 565.5, "paperNumberTop": 814.5, "watermarkOptions": {}
  }]
}
export default {
  preview: preview,
  name: name,
  desc: desc,
  author: author,
  link: link,
  printData: printData,
  json: json
}
