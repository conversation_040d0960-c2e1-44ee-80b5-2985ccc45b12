export const name = '默认模板';
export const desc = '这是模板说明'
export const author = 'CcSimple'
export const link = 'https://github.com/CcSimple'
// url 或者 base64 或者 require('../../../assets/logo.png')
export const preview = '/static/template1.png';
export const printData = {
  name: '黄磊',
  logoTest: 'https://portrait.gitee.com/uploads/avatars/user/1800/5400665_CcSimple_1591166830.png!avatar60',
  password: '12346',
  longText: '浙江在线3月29日讯最近，\n一篇小学五年级学生写的作文引起了钱报记者的关注这篇作文的题目叫做《脏话风波》，讲述的是小作者班级里发生的一种不文明现象——讲脏话的同学越来越多，有的人说话甚至句句“带把儿”。班主任为了遏制这种现象，煞费苦心想了很多办法，跟学生斗智斗勇……看到这篇作文，记者突然想到，自己读六年级的儿子有天突然冒出一句脏话。此前，他是从不说脏话的。问他怎么学会的，他也说不出个所以然来。于是，记者做了这个小学生脏话现象调查。经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文《脏话风波》浙江在线3月29日讯最近，一篇小学五年级学生写的作文引起了钱报记者的关注。这篇作文的题目叫做《脏话风波》，讲述的是小作者班级里发生的一种不文明现象——讲脏话的同学越来越多，有的人说话甚至句句“带把儿”。班主任为了遏制这种现象，煞费苦心想了很多办法，跟学生斗智斗勇……看到这篇作文，记者突然想到，自己读六年级的儿子有天突然冒出一句脏话。此前，他是从不说脏话的。问他怎么学会的，他也说不出个所以然来。于是，记者做了这个小学生脏话现象调查。经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文《脏话风波》浙江在线3月29日讯最近，一篇小学五年级学生写的作文引起了钱报记者的关注。这篇作文的题目叫做《脏话风波》，讲述的是小作者班级里发生的一种不文明现象——讲脏话的同学越来越多，有的人说话甚至句句“带把儿”。班主任为了遏制这种现象，煞费苦心想了很多办法，跟学生斗智斗勇……看到这篇作文，记者突然想到，自己读六年级的儿子有天突然冒出一句脏话。此前，他是从不说脏话的。问他怎么学会的，他也说不出个所以然来。于是，记者做了这个小学生脏话现象调查。经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文《脏话风波》浙江在线3月29日讯最近，一篇小学五年级学生写的作文引起了钱报记者的关注。这篇作文的题目叫做《脏话风波》，讲述的是小作者班级里发生的一种不文明现象——讲脏话的同学越来越多，有的人说话甚至句句“带把儿”。班主任为了遏制这种现象，煞费苦心想了很多办法，跟学生斗智斗勇……看到这篇作文，记者突然想到，自己读六年级的儿子有天突然冒出一句脏话。此前，他是从不说脏话的。问他怎么学会的，他也说不出个所以然来。于是，记者做了这个小学生脏话现象调查。经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文《脏话风波》浙江在线3月29日讯最近，一篇小学五年级学生写的作文引起了钱报记者的关注。这篇作文的题目叫做《脏话风波》，讲述的是小作者班级里发生的一种不文明现象——讲脏话的同学越来越多，有的人说话甚至句句“带把儿”。班主任为了遏制这种现象，煞费苦心想了很多办法，跟学生斗智斗勇……看到这篇作文，记者突然想到，自己读六年级的儿子有天突然冒出一句脏话。此前，他是从不说脏话的。问他怎么学会的，他也说不出个所以然来。于是，记者做了这个小学生脏话现象调查。经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文讲述的是小作者班级里发生的一种不文明现象——讲脏话的同学越来越多，有的人说话甚至句句“带把儿”。班主任为了遏制这种现象，煞费苦心想了很多办法，跟学生斗智斗勇……看到这篇作文，记者突然想到，自己读六年级的儿子有天突然冒出一句脏话。此前，他是从不说脏话的。问他怎么学会的，他也说不出个所以然来。于是，记者做了这个小学生脏话现象调查。经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文讲述的是小作者班级里发生的一种不文明现象——讲脏话的同学越来越多，有的人说话甚至句句“带把儿”。班主任为了遏制这种现象，煞费苦心想了很多办法，跟学生斗智斗勇……看到这篇作文，记者突然想到，自己读六年级的儿子有天突然冒出一句脏话。此前，他是从不说脏话的。问他怎么学会的，他也说不出个所以然来。于是，记者做了这个小学生脏话现象调查。经过了解才发现，小学生爱说脏话竟然较为普遍，一般三年级会冒出苗头。无论是学习成绩好的，还是平时不太起眼的，都会说脏话。而且，说脏话会“传染”，一旦冒头不制止，到了五六年级甚至可能在班里大爆发。以下为作文',
  table: [
    {id: '1', name: '王小可', gender: '男', count: '120', amount: '9089元'},
    {id: '2', name: '梦之遥', gender: '女', count: '20', amount: '89元'},
    {id: '3', name: '梦之遥', gender: '女', count: '720', amount: '29089元'},
    {id: '4', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '5', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '6', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '7', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '8', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '9', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '10', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '11', name: '王小可', gender: '男', count: '120', amount: '9089元'},
    {id: '12', name: '梦之遥', gender: '女', count: '20', amount: '89元'},
    {id: '13', name: '梦之遥', gender: '女', count: '720', amount: '29089元'},
    {id: '14', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '15', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '16', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '17', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '18', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '19', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '20', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '21', name: '王小可', gender: '男', count: '120', amount: '9089元'},
    {id: '22', name: '梦之遥', gender: '女', count: '20', amount: '89元'},
    {id: '23', name: '梦之遥', gender: '女', count: '720', amount: '29089元'},
    {id: '24', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '25', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '26', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '27', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '28', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '29', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '21', name: '王小可', gender: '男', count: '120', amount: '9089元'},
    {id: '22', name: '梦之遥', gender: '女', count: '20', amount: '89元'},
    {id: '23', name: '梦之遥', gender: '女', count: '720', amount: '29089元'},
    {id: '24', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '25', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '26', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '27', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '28', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '29', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '21', name: '王小可', gender: '男', count: '120', amount: '9089元'},
    {id: '22', name: '梦之遥', gender: '女', count: '20', amount: '89元'},
    {id: '23', name: '梦之遥', gender: '女', count: '720', amount: '29089元'},

    {id: '29', name: '黄小菊', gender: '女', count: '420', amount: '19089元'},
    {id: '30', name: '黄小菊', gender: '女', count: '420', amount: '19089元'}
  ]
}
export const json = {
  "panels": [{
    "index": 0,
    "height": 297,
    "width": 210,
    "paperHeader": 49.5,
    "paperFooter": 780,
    "printElements": [{
      "options": {
        "left": 175.5,
        "top": 10.5,
        "height": 27,
        "width": 259,
        "title": "HiPrint自定义模块打印插件",
        "fontSize": 19,
        "fontWeight": "600",
        "textAlign": "center",
        "lineHeight": 26,
        "coordinateSync": true,
        "widthHeightSync": true,
        "draggable": false,
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 60, "top": 27, "height": 13, "width": 52, "title": "页眉线", "textAlign": "center"},
      "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 25.5, "top": 57, "height": 705, "width": 9, "fixed": true, "borderStyle": "dotted"},
      "printElementType": {"type": "vline"}
    }, {
      "options": {"left": 60, "top": 61.5, "height": 48, "width": 87, "src": "", "fit": "contain"},
      "printElementType": {"title": "图片", "type": "image"}
    }, {
      "options": {
        "left": 153,
        "top": 64.5,
        "height": 39,
        "width": 276,
        "title": "二维码以及条形码均采用svg格式打印。不同打印机打印不会造成失真。图片打印：不同DPI打印可能会导致失真，",
        "fontFamily": "微软雅黑",
        "textAlign": "center",
        "lineHeight": 18
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 457.5,
        "top": 79.5,
        "height": 13,
        "width": 120,
        "title": "姓名",
        "field": "name",
        "testData": "古力娜扎",
        "color": "#f00808",
        "textDecoration": "underline",
        "textAlign": "center",
        "fields": [{"text": 'id', "field": 'id'}, {"text": '姓名', "field": 'name'}, {
          "text": '性别',
          "field": 'gender'
        }, {"text": '数量', "field": 'count'}],
      }, "printElementType": {"title": "文本", "type": "text"}
    }, {
      "options": {
        "left": 483,
        "top": 124.5,
        "height": 43,
        "width": 51,
        "title": "123456789",
        "textType": "qrcode"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 285,
        "top": 130.5,
        "height": 34,
        "width": 175,
        "title": "123456789",
        "fontFamily": "微软雅黑",
        "textAlign": "center",
        "textType": "barcode"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 60,
        "top": 132,
        "height": 19,
        "width": 213,
        "title": "所有打印元素都可已拖拽的方式来改变元素大小",
        "fontFamily": "微软雅黑",
        "textAlign": "center",
        "lineHeight": 18
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 153,
        "top": 189,
        "height": 13,
        "width": 238,
        "title": "单击元素，右侧可自定义元素属性",
        "textAlign": "center",
        "fontFamily": "微软雅黑"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 60, "top": 190.5, "height": 13, "width": 51, "title": "横线", "textAlign": "center"},
      "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 415.5,
        "top": 190.5,
        "height": 13,
        "width": 164,
        "title": "可以配置各属性的默认值",
        "textAlign": "center",
        "fontFamily": "微软雅黑"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 60, "top": 214.5, "height": 10, "width": 475.5},
      "printElementType": {"title": "横线", "type": "hline"}
    }, {
      "options": {
        "left": 235.5,
        "top": 220.5,
        "height": 32,
        "width": 342,
        "title": "自定义表格：用户可左键选中表头，右键查看可操作项，操作类似Excel，双击表头单元格可进行编辑。内容：title#field",
        "fontFamily": "微软雅黑",
        "textAlign": "center",
        "lineHeight": 15
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 156,
        "top": 265.5,
        "height": 13,
        "width": 94,
        "title": "表头列大小可拖动",
        "fontFamily": "微软雅黑",
        "textAlign": "center"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 60,
        "top": 265.5,
        "height": 13,
        "width": 90,
        "title": "红色区域可拖动",
        "fontFamily": "微软雅黑",
        "textAlign": "center"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 60,
        "top": 285,
        "height": 56,
        "width": 511.5,
        "field": "table",
        "tableFooterRepeat": "",
        "fields": [{"text": 'id', "field": 'id'}, {"text": '姓名', "field": 'name'}, {
          "text": '性别',
          "field": 'gender'
        }, {"text": '数量', "field": 'count'}],
        "columns": [[{"width": 85.25, "colspan": 1, "rowspan": 1, "checked": true}, {
          "title": "性别",
          "field": "gender",
          "width": 85.25,
          "colspan": 1,
          "rowspan": 1,
          "checked": false
        }, {
          "title": "姓名",
          "field": "name",
          "width": 85.25,
          "align": "center",
          "colspan": 1,
          "rowspan": 1,
          "checked": true,
          "tableSummary": "count"
        }, {
          "title": "数量",
          "field": "count",
          "width": 85.25,
          "align": "center",
          "colspan": 1,
          "rowspan": 1,
          "checked": true,
          "tableSummary": "sum"
        }, {
          "width": 85.25,
          "colspan": 1,
          "rowspan": 1,
          "checked": true
        }, {"width": 85.25, "colspan": 1, "rowspan": 1, "checked": true}]]
      }, "printElementType": {
        "title": "表格", "type": "table",
      }
    }, {
      "options": {
        "left": 21,
        "top": 346.5,
        "height": 61.5,
        "width": 15,
        "title": "装订线",
        "lineHeight": 18,
        "fixed": true,
        "contentPaddingTop": 3.75,
        "backgroundColor": "#ffffff"
      }, "printElementType": {"type": "text"}
    }, {
      "options": {
        "left": 225,
        "top": 355,
        "height": 13,
        "width": 346.5,
        "title": "自定义模块：主要为开发人员设计，能够快速，简单，实现自己功能",
        "textAlign": "center"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 60, "top": 370.5, "height": 18, "width": 79, "title": "配置项表格", "textAlign": "center"},
      "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 225,
        "top": 385.5,
        "height": 38,
        "width": 346.5,
        "title": "配置模块：主要为客户使用，开发人员可以配置属性，字段，标题等，客户直接使用，配置模块请参考实例2",
        "fontFamily": "微软雅黑",
        "lineHeight": 15,
        "textAlign": "center",
        "color": "#d93838"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 60,
        "top": 487.5,
        "height": 13,
        "width": 123,
        "title": "长文本会自动分页",
        "textAlign": "center"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 60, "top": 507, "height": 40, "width": 511.5, "field": "longText"},
      "printElementType": {"title": "长文", "type": "longText"}
    }, {
      "options": {"left": 475.5, "top": 565.5, "height": 100, "width": 100},
      "printElementType": {"title": "矩形", "type": "rect"}
    }, {
      "options": {"left": 174, "top": 568.5, "height": 13, "width": 90, "title": "竖线", "textAlign": "center"},
      "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 60, "top": 574.5, "height": 100, "width": 10},
      "printElementType": {"title": "竖线", "type": "vline"}
    }, {
      "options": {"left": 210, "top": 604.5, "height": 13, "width": 120, "title": "横线", "textAlign": "center"},
      "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 130.5, "top": 625.5, "height": 10, "width": 277},
      "printElementType": {"title": "横线", "type": "hline"}
    }, {
      "options": {
        "left": 364.5,
        "top": 649.5,
        "height": 13,
        "width": 101,
        "title": "矩形",
        "textAlign": "center"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 525, "top": 784.5, "height": 13, "width": 63, "title": "页尾线", "textAlign": "center"},
      "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {"left": 12, "top": 786, "height": 49, "width": 49},
      "printElementType": {"title": "html", "type": "html"}
    }, {
      "options": {
        "left": 75,
        "top": 790.5,
        "height": 13,
        "width": 137,
        "title": "红色原型是自动定义的Html",
        "textAlign": "center"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }, {
      "options": {
        "left": 334.5,
        "top": 810,
        "height": 13,
        "width": 205,
        "title": "页眉线已上。页尾下以下每页都会重复打印",
        "textAlign": "center"
      }, "printElementType": {"title": "自定义文本", "type": "text"}
    }],
    "paperNumberLeft": 565.5,
    "paperNumberTop": 819
  }]
}
export default {
  preview: preview,
  name: name,
  desc: desc,
  author: author,
  link: link,
  printData: printData,
  json: json
}
