<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改信息' : '新建信息'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      labelWidth="auto"
      @updateValue="setFieldValue"
    />
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        plain
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch, unref } from 'vue';
  import { useFormData } from '@/utils/use-form-data.js';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api/index.js';
  import ProForm from '@/components/ProForm/index.vue';
  import { useRouter } from 'vue-router';

  const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const { currentRoute } = useRouter();
  const { params, path } = unref(currentRoute);
  const userType = path.split('/')[4];
  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    title: '',
    key: '',
    name: '',
    url: '',
    columns: '',
    paramMode: '',
    role: [],
    sort: '',
    status: '',
    userType: ''
  });

  /** 表单项 */
  const items = ref([
    { prop: 'title', label: '显示名称', type: 'input', required: true },
    { prop: 'key', label: '选项代码', type: 'input', required: true },
    { prop: 'name', label: '选项名称', type: 'input', required: true },
    { prop: 'url', label: '数据接口', type: 'input' },
    { prop: 'columns', label: '字段配置', type: 'textarea' },
    {
      prop: 'paramMode',
      label: '功能业务类型',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'personnelSelector',
        dicQueryParams: {
          valueField: 'code'
        }
      }
    },
    {
      prop: 'role',
      label: '授权角色',
      type: 'dictMultipleSelect',
      // type: 'dictSelect',
      typeKey: 'multipleSelect',
      props: { code: 'listRoles' }
    },
    { prop: 'sort', label: '排序', type: 'inputNumber', required: true },
    {
      prop: 'status',
      label: '状态',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'status',
        dicQueryParams: {
          getValType: 'name'
        }
      },
      required: true
    },
    {
      prop: 'userType',
      label: '用户类别',
      type: 'dictSelect',
      typeKey: 'select',
      props: {
        code: 'userType',
        dicQueryParams: {
          getValType: 'name'
        }
      },
      required: true
    }
  ]);

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // let data = toFormData({...form})
      loading.value = true;
      form.paramMode = form?.paramMode ?? '';
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
