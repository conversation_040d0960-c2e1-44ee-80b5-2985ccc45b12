<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <!--        <search @search="reload"/>-->
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="baseCodeTable"
        tooltip-effect="light"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            v-if="hasPermission('code:codeCommon:save')"
            size="small"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            v-if="hasPermission('code:codeCommon:remove')"
            size="small"
            @click="removeOpt()"
          >
            删除
          </el-button>
          <el-button
            v-if="hasPermission('code:codeCommon:import')"
            class="ele-btn-icon"
            size="small"
            @click="openImport"
          >
            导入
          </el-button>
          <el-button
            v-if="hasPermission('code:codeCommon:export')"
            size="small"
            @click="exportBas()"
          >
            导出
          </el-button>
        </template>
        <template #attachment="{ row }">
          <div v-if="row.attachment">
            <el-link
              v-for="(file, index) in parseAttachments(row.attachment)"
              :key="index"
              type="primary"
              :underline="false"
              @click="downloadFile(file.id, file.originalFilename)"
              style="margin-right: 8px; display: block; margin-bottom: 4px"
            >
              {{ file.originalFilename }}
            </el-link>
          </div>
          <span v-else>-</span>
        </template>
        <template #action="{ row }">
          <el-link
            v-if="hasPermission('code:codeCommon:update')"
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            v-if="hasPermission('code:codeCommon:remove')"
            type="primary"
            :underline="false"
            @click="removeOpt(row)"
          >
            删除
          </el-link>
        </template>
        <!-- 用户名表头 -->
        <template #nameHeader="{ column }">
          <ele-text>{{ column.label }}</ele-text>
          <name-filter @search="onNameFilter" />
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 导入弹窗 -->
    <import v-model="showImport" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import Edit from './components/edit.vue';
  import Import from './components/import.vue';
  import { queryPage, removes } from './api/index';
  import { useRouter } from 'vue-router';
  import { getToken } from '@/utils/token-util';
  import { getCurrentRole } from '@/utils/current-role-util';
  import nameFilter from './components/name-filter.vue';
  import { usePermission } from '@/utils/use-permission';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const { hasPermission } = usePermission();

  const userStore = useUserStore();
  const { codeType } = storeToRefs(userStore);

  let codeTypeFormat = ref(null);
  let routerParams = ref(null);
  const { currentRoute } = useRouter();
  watch(
    currentRoute,
    (route) => {
      const { params } = unref(route);
      routerParams.value = params;
      codeTypeFormat.value = codeType.value[params.codeType];
    },
    { immediate: true }
  );

  const BASE_URL = import.meta.env.BASE_URL;
  const accessToken = getToken();
  /** 当前用户角色*/
  const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'code',
      label: codeTypeFormat.value + '代码',
      minWidth: 110
    },
    {
      prop: 'name',
      label: codeTypeFormat.value + '名称',
      minWidth: 110,
      sortable: 'custom',
      headerSlot: 'nameHeader'
    },
    {
      prop: 'sort',
      label: '排序',
      width: 90,
      sortable: 'custom'
    },
    {
      prop: 'description',
      label: '备注',
      width: 120
    },
    {
      prop: 'attachment',
      label: '附件',
      width: 120,
      slot: 'attachment'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 200,
      slot: 'action'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);

  /** 打开导入弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 导出excel */
  const exportBas = () => {
    window.location.href =
      BASE_URL +
      'api/code/codeCommon/exportData?access_token=' +
      accessToken +
      '&codeType=' +
      routerParams.value.codeType;
  };

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      codeType: routerParams.value.codeType
    });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };

  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        name: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 刷新表格 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      //编辑提交table不全局刷新
      tableRef.value?.reload?.();
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除 */
  const removeOpt = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 解析附件信息 */
  const parseAttachments = (attachmentStr) => {
    if (!attachmentStr) return [];
    try {
      return JSON.parse(attachmentStr);
    } catch (e) {
      console.error('解析附件信息失败:', e);
      return [];
    }
  };

  /** 下载文件 */
  const downloadFile = (fileId, filename) => {
    window.open(BASE_URL + 'api/file/attachment/' + fileId);
  };
</script>

<script>
  export default {
    name: 'BaseCodeCommon'
  };
</script>
