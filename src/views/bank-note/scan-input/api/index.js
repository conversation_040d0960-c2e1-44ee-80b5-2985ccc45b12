import request from '@/utils/request';

/**
 * 扫码查询钱币信息
 * @param {Object} params - 查询参数
 * @param {string} params.nummber - 钱币编号
 * @param {string} params.gm - 钱币类型
 * @param {number} params.addType - 追加类型：0-无，1-追加送评单，2-追加鉴定单
 * @param {Array} params.numbers - 已有钱币编号列表
 */
export async function queryCoinsByScan(params) {
  const res = await request.post('/scan/queryCoinsByScan', params);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量保存扫码录入的钱币
 * @param {Array} coinList - 钱币列表
 */
export async function batchSaveCoins(coinList) {
  const res = await request.post('/scan/batchSave', { coinList });
  if (res.data.code === 0) {
    return res.data.data || res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除钱币
 * @param {string} coinId - 钱币ID
 */
export async function deleteCoin(coinId) {
  const res = await request.delete(`/scan/coin/${coinId}`);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 更新钱币信息
 * @param {Object} coinData - 钱币数据
 */
export async function updateCoin(coinData) {
  const res = await request.put('/scan/coin', coinData);
  if (res.data.code === 0) {
    return res.data.data || res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取钱币类型选项
 */
export async function getCoinTypeOptions() {
  const res = await request.get('/scan/coinTypes');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导出钱币数据
 * @param {Array} coinIds - 钱币ID列表
 */
export async function exportCoins(coinIds) {
  const res = await request.post(
    '/scan/export',
    { coinIds },
    {
      responseType: 'blob'
    }
  );
  return res.data;
}

/**
 * 批量更新钱币字段
 * @param {Array} coinIds - 钱币ID列表
 * @param {Object} updateData - 更新数据
 */
export async function batchUpdateCoins(coinIds, updateData) {
  const res = await request.post('/scan/batchUpdate', {
    coinIds,
    updateData
  });
  if (res.data.code === 0) {
    return res.data.data || res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
