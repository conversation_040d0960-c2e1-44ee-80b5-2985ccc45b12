<template>
  <ele-page flex-table>
    <!-- 搜索区域 -->
    <ele-card :body-style="{ padding: '16px' }">
      <div class="scan-input-header">
        <div class="barcode-input-section">
          <el-input
            v-model="barcodeInput"
            placeholder="请输入或扫描条形码"
            size="large"
            clearable
            @keyup.enter="handleScan"
            @clear="handleClear"
            class="barcode-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleScan" :loading="loading">
                查询
              </el-button>
            </template>
          </el-input>
        </div>

        <div class="options-section">
          <el-radio-group v-model="queryOptions.addType" size="small">
            <el-radio :label="0">无</el-radio>
            <el-radio :label="1">追加该送评单的所有订单</el-radio>
            <el-radio :label="2">追加该鉴定单的所有订单</el-radio>
          </el-radio-group>

          <div class="action-buttons">
            <el-button @click="handleCopy" :disabled="!coinList.length">
              复制条形码
            </el-button>
            <el-button
              type="success"
              @click="handleBatchSave"
              :loading="saving"
              :disabled="!coinList.length"
            >
              批量保存
            </el-button>
          </div>
        </div>
      </div>

      <!-- 快捷批量编辑工具栏 -->
      <div v-if="coinList.length" class="batch-edit-toolbar">
        <div class="toolbar-header" @click="toggleBatchEditExpanded">
          <span class="toolbar-title">
            <el-icon><Edit /></el-icon>
            批量编辑工具
          </span>
          <el-icon
            class="expand-icon"
            :class="{ 'is-expanded': batchEditExpanded }"
          >
            <ArrowDown />
          </el-icon>
        </div>

        <el-collapse-transition>
          <div v-show="batchEditExpanded" class="toolbar-content">
            <!-- 选择状态栏 -->
            <div class="selection-toolbar">
              <div class="selection-info">
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="isAllSelected"
                  @change="handleSelectAll"
                  size="small"
                >
                  全选
                </el-checkbox>
                <span class="selection-count">
                  已选择 {{ selectedRows.length }} /
                  {{ filteredCoinList.length }} 项
                </span>
              </div>
              <div class="selection-actions">
                <el-button
                  size="small"
                  @click="clearSelection"
                  :disabled="!selectedRows.length"
                >
                  清空选择
                </el-button>
                <el-button
                  size="small"
                  @click="selectChangedRows"
                  :disabled="!changedRows.size"
                >
                  选择已修改项
                </el-button>
              </div>
            </div>
            <el-row :gutter="10" class="batch-controls">
              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">真伪:</span>
                  <dict-data
                    v-model="batchEdit.authenticity"
                    code="authenticity"
                    type="select"
                    :dic-query-params="{ getValType: 'name' }"
                    placeholder="选择真伪"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">名称1:</span>
                  <el-input
                    v-model="batchEdit.coinName1"
                    placeholder="名称1"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">名称2:</span>
                  <el-input
                    v-model="batchEdit.coinName2"
                    placeholder="名称2"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">附加:</span>
                  <el-input
                    v-model="batchEdit.coinName3"
                    placeholder="附加"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">年代:</span>
                  <el-input
                    v-model="batchEdit.yearInfo"
                    placeholder="年代"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="10" class="batch-controls">
              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">银行:</span>
                  <el-input
                    v-model="batchEdit.bankName"
                    placeholder="银行"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>
              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">品相分数:</span>
                  <dict-data
                    v-model="batchEdit.gradeScore"
                    code="gradeScore"
                    type="select"
                    :dic-query-params="{ getValType: 'name' }"
                    placeholder="分数"
                    size="small"
                    clearable
                    style="flex: 1"
                    filterable
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">★/EPQ/NET:</span>
                  <el-checkbox-group
                    v-model="batchEdit.specialMarkArray"
                    size="small"
                    style="flex: 1"
                  >
                    <el-checkbox label="★">★</el-checkbox>
                    <el-checkbox label="EPQ">EPQ</el-checkbox>
                    <el-checkbox label="NET">NET</el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">星级:</span>
                  <dict-data
                    v-model="batchEdit.starLevel"
                    code="starLevel"
                    type="select"
                    :dic-query-params="{ getValType: 'name' }"
                    placeholder="星级"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">地区:</span>
                  <el-input
                    v-model="batchEdit.region"
                    placeholder="地区"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">版别:</span>
                  <dict-data
                    v-model="batchEdit.version"
                    code="edition"
                    type="select"
                    :dic-query-params="{ getValType: 'name' }"
                    placeholder="版别"
                    size="small"
                    clearable
                    style="flex: 1"
                    filterable
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">目录:</span>
                  <el-input
                    v-model="batchEdit.catalog"
                    placeholder="目录"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>
              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">钱币备注:</span>
                  <dict-data
                    v-model="batchEdit.inspectionNote"
                    code="coinRemark"
                    type="select"
                    :dic-query-params="{ getValType: 'name' }"
                    placeholder="钱币备注"
                    size="small"
                    clearable
                    style="flex: 1"
                    filterable
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">对内备注:</span>
                  <el-input
                    v-model="batchEdit.internalNote"
                    placeholder="对内备注"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="4">
                <div class="batch-control-item">
                  <span class="label">对外备注:</span>
                  <el-input
                    v-model="batchEdit.externalNote"
                    placeholder="对外备注"
                    size="small"
                    clearable
                    style="flex: 1"
                  />
                </div>
              </el-col>

              <el-col :span="9">
                <div class="batch-action-buttons">
                  <el-button
                    type="primary"
                    size="small"
                    @click="applyBatchEdit"
                    :disabled="!selectedRows.length"
                  >
                    应用到选中项 ({{ selectedRows.length }})
                  </el-button>
                  <el-button size="small" @click="clearBatchEdit">
                    清空
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>

        <!-- 连号生成工具 -->
        <div class="toolbar-header" @click="toggleSerialToolExpanded">
          <span class="toolbar-title">
            <el-icon><Setting /></el-icon>
            连号生成工具
          </span>
          <el-icon
            class="expand-icon"
            :class="{ 'is-expanded': serialToolExpanded }"
          >
            <ArrowDown />
          </el-icon>
        </div>

        <el-collapse-transition>
          <div v-show="serialToolExpanded" class="toolbar-content">
            <div class="serial-generate-section">
              <!-- 连号规则列表 -->
              <div
                v-for="(rule, index) in serialGenerateList"
                :key="index"
                class="serial-rule-item"
              >
                <el-row :gutter="10" class="batch-controls">
                  <el-col :span="3">
                    <div class="batch-control-item">
                      <span class="label">字段选择:</span>
                      <el-select
                        v-model="rule.field"
                        placeholder="选择字段"
                        size="small"
                        clearable
                        style="flex: 1"
                      >
                        <el-option label="编号" value="serialNumber" />
                        <el-option label="名称1" value="coinName1" />
                        <el-option label="名称2" value="coinName2" />
                        <el-option label="附加" value="coinName3" />
                        <el-option label="目录" value="catalog" />
                        <el-option label="对内备注" value="internalNote" />
                      </el-select>
                    </div>
                  </el-col>

                  <el-col :span="3">
                    <div class="batch-control-item">
                      <span class="label">前缀:</span>
                      <el-input
                        v-model="rule.prefix"
                        placeholder="如：FA"
                        size="small"
                      />
                    </div>
                  </el-col>

                  <el-col :span="3">
                    <div class="batch-control-item">
                      <span class="label">起始号:</span>
                      <el-input-number
                        v-model="rule.startNumber"
                        :min="0"
                        placeholder="起始号"
                        size="small"
                        style="width: 100%"
                        :controls="false"
                      />
                    </div>
                  </el-col>

                  <el-col :span="3">
                    <div class="batch-control-item">
                      <span class="label">位数:</span>
                      <el-input-number
                        v-model="rule.digitCount"
                        :min="1"
                        :max="10"
                        placeholder="位数"
                        size="small"
                        style="width: 100%"
                        :controls="false"
                      />
                    </div>
                  </el-col>

                  <el-col :span="3">
                    <div class="batch-control-item">
                      <span class="label">后缀:</span>
                      <el-input
                        v-model="rule.suffix"
                        placeholder="如：-A"
                        size="small"
                      />
                    </div>
                  </el-col>

                  <el-col :span="3">
                    <div class="batch-control-item">
                      <span class="label">生成方式:</span>
                      <el-select
                        v-model="rule.mode"
                        size="small"
                        style="width: 100%"
                      >
                        <el-option label="顺序" value="sequence" />
                        <el-option label="随机" value="random" />
                      </el-select>
                    </div>
                  </el-col>

                  <el-col :span="2">
                    <div class="batch-control-item">
                      <span class="label">启用:</span>
                      <el-switch v-model="rule.enabled" size="small" />
                    </div>
                  </el-col>

                  <el-col :span="2">
                    <div class="batch-action-buttons">
                      <el-button
                        size="small"
                        @click="previewSingleRule(rule)"
                        :disabled="!rule.enabled"
                      >
                        预览
                      </el-button>
                      <el-button
                        type="danger"
                        size="small"
                        @click="removeSerialRule(index)"
                        v-if="serialGenerateList.length > 1"
                      >
                        删除
                      </el-button>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <!-- 添加规则和执行按钮 -->
              <el-row
                :gutter="10"
                class="batch-controls"
                style="margin-top: 10px"
              >
                <el-col :span="2">
                  <el-button type="primary" size="small" @click="addSerialRule">
                    <el-icon><Plus /></el-icon>
                    添加连号规则
                  </el-button>
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="success"
                    size="small"
                    @click="generateAllSerialNumbers"
                  >
                    <el-icon><Setting /></el-icon>
                    生成所有连号
                  </el-button>
                </el-col>
                <el-col :span="2">
                  <el-button size="small" @click="previewAllSerialNumbers">
                    <el-icon><View /></el-icon>
                    预览所有结果
                  </el-button>
                </el-col>
                <el-col :span="2">
                  <el-button size="small" @click="resetSerialRules">
                    <el-icon><Refresh /></el-icon>
                    重置规则
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-collapse-transition>
      </div>
    </ele-card>

    <!-- 钱币信息表格 -->
    <ele-card
      v-if="coinList.length"
      flex-table
      :body-style="{ padding: '0', overflow: 'auto' }"
    >
      <template #header>
        <div class="table-header">
          <span>钱币信息列表 ({{ coinList.length }}项)</span>
          <div class="header-actions">
            <el-button-group size="small">
              <el-button
                v-for="(tab, index) in coinTabs"
                :key="tab.value"
                :type="currentTab === tab.value ? 'primary' : 'default'"
                @click="switchTab(tab.value)"
              >
                {{ tab.label }} ({{ getTabCount(tab.value) }})
              </el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 数据表格 -->
      <div style="width: 100%; overflow-x: auto">
        <el-table
          ref="tableRef"
          :data="filteredCoinList"
          row-key="id"
          border
          stripe
          size="small"
          height="800"
          empty-text="暂无数据"
          :cell-style="getCellStyle"
          @selection-change="handleSelectionChange"
          style="width: 100%; min-width: 1800px"
        >
          <!-- 选择列 -->
          <el-table-column type="selection" width="55" fixed="left" />

          <!-- 订单号 -->
          <!--          <el-table-column prop="nummber" label="订单号" width="130" fixed="left">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ row.nummber }}</el-tag>
            </template>
          </el-table-column>-->

          <!-- 名称1 -->
          <el-table-column
            prop="coinName1"
            label="名称1"
            width="100"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <el-input
                v-model="row.coinName1"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'coinName1')"
                placeholder="名称1"
                :ref="(el) => setInputRef(el, $index, 'coinName1')"
              />
            </template>
          </el-table-column>

          <!-- 名称2 -->
          <el-table-column
            prop="coinName2"
            label="名称2"
            width="100"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <el-input
                v-model="row.coinName2"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'coinName2')"
                placeholder="名称2"
                :ref="(el) => setInputRef(el, $index, 'coinName2')"
              />
            </template>
          </el-table-column>

          <!-- 附加 -->
          <el-table-column
            prop="coinName3"
            label="附加"
            width="100"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <el-input
                v-model="row.coinName3"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'coinName3')"
                placeholder="附加"
                :ref="(el) => setInputRef(el, $index, 'coinName3')"
              />
            </template>
          </el-table-column>

          <!-- 版别 -->
          <el-table-column prop="version" label="版别" width="100">
            <template #default="{ row, $index }">
              <dict-data
                v-model="row.version"
                code="edition"
                type="select"
                :dic-query-params="{ getValType: 'name' }"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'version')"
                style="width: 100%"
                clearable
                filterable
                placeholder="版别"
                :ref="(el) => setInputRef(el, $index, 'version')"
              />
            </template>
          </el-table-column>

          <!-- 年代 -->
          <el-table-column prop="yearInfo" label="年代" width="80">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.yearInfo"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'yearInfo')"
                placeholder="年代"
                :ref="(el) => setInputRef(el, $index, 'yearInfo')"
              />
            </template>
          </el-table-column>

          <!-- 编号（纸币特有） -->
          <el-table-column
            prop="serialNumber"
            label="编号"
            width="100"
            v-if="currentTab === '纸币' || currentTab === 'all'"
          >
            <template #default="{ row, $index }">
              <el-input
                v-model="row.serialNumber"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'serialNumber')"
                placeholder="编号"
                :ref="(el) => setInputRef(el, $index, 'serialNumber')"
              />
            </template>
          </el-table-column>

          <!-- 银行 -->
          <el-table-column prop="bankName" label="银行" width="100">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.bankName"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'bankName')"
                placeholder="银行"
                :ref="(el) => setInputRef(el, $index, 'bankName')"
              />
            </template>
          </el-table-column>

          <!-- 品相分数 -->
          <el-table-column prop="gradeScore" label="品相分数" width="120">
            <template #default="{ row, $index }">
              <dict-data
                v-model="row.gradeScore"
                code="gradeScore"
                type="select"
                :dic-query-params="{ getValType: 'name' }"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'gradeScore')"
                style="width: 100%"
                clearable
                filterable
                placeholder="分数"
                :ref="(el) => setInputRef(el, $index, 'gradeScore')"
              />
            </template>
          </el-table-column>

          <!-- ★/EPQ/NET -->
          <el-table-column prop="specialMark" label="★/EPQ/NET" width="120">
            <template #default="{ row, $index }">
              <el-checkbox-group
                v-model="row.specialMarkArray"
                size="small"
                @change="handlespecialMarkChange(row, $index)"
              >
                <el-checkbox label="★">★</el-checkbox>
                <el-checkbox label="EPQ">EPQ</el-checkbox>
                <el-checkbox label="NET">NET</el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>

          <!-- 星级 -->
          <el-table-column prop="starLevel" label="星级" width="100">
            <template #default="{ row, $index }">
              <dict-data
                v-model="row.starLevel"
                code="starLevel"
                type="select"
                :dic-query-params="{ getValType: 'name' }"
                size="small"
                @change="markRowChanged($index)"
                style="width: 100%"
                clearable
                placeholder="星级"
              />
            </template>
          </el-table-column>

          <!-- 真伪列 -->
          <el-table-column prop="authenticity" label="真伪" width="110">
            <template #default="{ row, $index }">
              <dict-data
                v-model="row.authenticity"
                code="authenticity"
                type="select"
                :dic-query-params="{ getValType: 'name' }"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'authenticity')"
                style="width: 100%"
                :ref="(el) => setInputRef(el, $index, 'authenticity')"
              />
            </template>
          </el-table-column>

          <!-- 地区 -->
          <el-table-column prop="region" label="地区" width="100">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.region"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'region')"
                placeholder="地区"
                :ref="(el) => setInputRef(el, $index, 'region')"
              />
            </template>
          </el-table-column>

          <!-- 目录 -->
          <el-table-column
            prop="catalog"
            label="目录"
            width="100"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <el-input
                v-model="row.catalog"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'catalog')"
                placeholder="目录"
                :ref="(el) => setInputRef(el, $index, 'catalog')"
              />
            </template>
          </el-table-column>

          <!-- 钱币备注 -->
          <el-table-column prop="inspectionNote" label="钱币备注" width="120">
            <template #default="{ row, $index }">
              <dict-data
                v-model="row.inspectionNote"
                code="coinRemark"
                type="select"
                :dic-query-params="{ getValType: 'name' }"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'inspectionNote')"
                style="width: 100%"
                clearable
                filterable
                placeholder="钱币备注"
                :ref="(el) => setInputRef(el, $index, 'inspectionNote')"
              />
            </template>
          </el-table-column>

          <!-- 对内备注 -->
          <el-table-column
            prop="internalNote"
            label="对内备注"
            width="120"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <el-input
                v-model="row.internalNote"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'internalNote')"
                placeholder="对内备注"
                :ref="(el) => setInputRef(el, $index, 'internalNote')"
              />
            </template>
          </el-table-column>

          <!-- 对外备注 -->
          <el-table-column
            prop="externalNote"
            label="对外备注"
            width="120"
            show-overflow-tooltip
          >
            <template #default="{ row, $index }">
              <el-input
                v-model="row.externalNote"
                size="small"
                @change="markRowChanged($index)"
                @keydown="handleKeydown($event, $index, 'externalNote')"
                placeholder="对外备注"
                :ref="(el) => setInputRef(el, $index, 'externalNote')"
              />
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row, $index }">
              <el-button
                type="danger"
                size="small"
                link
                @click="deleteRow($index)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </ele-card>

    <!-- 空状态 -->
    <ele-card v-else>
      <el-empty description="请输入条形码开始扫码录入">
        <template #image>
          <el-icon size="64" color="#dcdfe6">
            <Search />
          </el-icon>
        </template>
      </el-empty>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, nextTick } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus/es';
  import {
    Search,
    CopyDocument,
    Plus,
    Setting,
    View,
    Refresh,
    Edit,
    ArrowDown
  } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useRouter } from 'vue-router';
  import DictData from '@/components/DictData/index.vue';

  // API imports
  import {
    queryCoinsByScan,
    batchUpdateCoins
  } from '@/api/bank-note/scan-input';

  // 路由实例
  const router = useRouter();

  // 响应式数据
  const barcodeInput = ref('');
  const loading = ref(false);
  const saving = ref(false);
  const coinList = ref([]);
  const changedRows = ref(new Set());
  const currentTab = ref('all');
  const tableRef = ref();

  // 折叠状态
  const batchEditExpanded = ref(true); // 默认展开批量编辑工具
  const serialToolExpanded = ref(false);

  // 查询选项
  const queryOptions = reactive({
    addType: 0
  });

  // 批量编辑数据
  const batchEdit = reactive({
    authenticity: '',
    coinName1: '',
    coinName2: '',
    coinName3: '',
    yearInfo: '',
    bankName: '',
    version: '',
    region: '',
    catalog: '',
    gradeScore: '',
    specialMarkArray: [],
    starLevel: '',
    inspectionNote: '',
    internalNote: '',
    externalNote: ''
  });

  // 连号生成配置
  const serialGenerateList = ref([]);

  // 钱币类型标签
  const coinTabs = ref([
    { label: '全部', value: 'all' },
    { label: '纸币', value: '纸币' },
    { label: '古钱币', value: '古钱币' },
    { label: '机制币', value: '机制币' },
    { label: '银锭', value: '银锭' }
  ]);

  // 表格列配置
  const tableColumns = computed(() => [
    {
      prop: 'nummber',
      label: '条形码',
      width: 120,
      slot: 'nummber',
      fixed: 'left'
    },
    {
      prop: 'coinName1',
      label: '名称1',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'coinName2',
      label: '名称2',
      width: 150,
      showOverflowTooltip: true
    },
    {
      prop: 'version',
      label: '版别',
      width: 120
    },
    {
      prop: 'yearInfo',
      label: '年代',
      width: 100
    },
    {
      prop: 'faceValue',
      label: '面值',
      width: 80
    },
    {
      prop: 'authenticity',
      label: '真伪',
      width: 150,
      slot: 'authenticity'
    },
    {
      prop: 'gradeScore',
      label: '等级',
      width: 120,
      slot: 'gradeScore'
    },
    {
      prop: 'region',
      label: '地区',
      width: 120,
      slot: 'region'
    },
    {
      prop: 'boxType',
      label: '盒子类型',
      width: 120,
      slot: 'boxType'
    },
    {
      prop: 'gradeScore',
      label: '品相分数',
      width: 100,
      slot: 'gradeScore'
    },
    {
      prop: 'coinSize',
      label: '尺寸',
      width: 100
    },
    {
      prop: 'coinWeight',
      label: '重量',
      width: 100
    },
    {
      prop: 'material',
      label: '材质',
      width: 100
    },
    {
      prop: 'backweight',
      label: '对内备注',
      width: 150,
      slot: 'backweight'
    },
    {
      prop: 'description',
      label: '对外备注',
      width: 150,
      slot: 'description'
    },
    {
      prop: 'sendnum',
      label: '送评单号',
      width: 140
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 80,
      align: 'center',
      slot: 'action',
      fixed: 'right'
    }
  ]);

  // 过滤后的钱币列表
  const filteredCoinList = computed(() => {
    if (currentTab.value === 'all') {
      return coinList.value;
    }
    return coinList.value.filter((coin) => coin.coinType === currentTab.value);
  });

  // 获取每个标签的数量
  const getTabCount = (tabValue) => {
    if (tabValue === 'all') {
      return coinList.value.length;
    }
    // 修复字段名：coinType 而不是 cointype
    return coinList.value.filter((coin) => coin.coinType === tabValue).length;
  };

  // 获取当前钱币类型
  const getCurrentCoinType = () => {
    if (currentTab.value === 'all') {
      return '01'; // 默认返回纸币类型
    }
    return currentTab.value;
  };

  // 切换标签
  const switchTab = (tabValue) => {
    currentTab.value = tabValue;
  };

  // 标记行已修改
  const markRowChanged = (index) => {
    const actualIndex = coinList.value.findIndex(
      (coin) =>
        filteredCoinList.value[index] &&
        coin.id === filteredCoinList.value[index].id
    );
    if (actualIndex !== -1) {
      changedRows.value.add(actualIndex);
    }
  };

  // 删除行
  const deleteRow = (index) => {
    const coin = filteredCoinList.value[index];
    if (coin) {
      const actualIndex = coinList.value.findIndex((c) => c.id === coin.id);
      if (actualIndex !== -1) {
        coinList.value.splice(actualIndex, 1);
        EleMessage.success('删除成功');
      }
    }
  };

  // 获取单元格样式
  const getCellStyle = ({ row, column, rowIndex }) => {
    const actualIndex = coinList.value.findIndex((coin) => coin.id === row.id);
    if (changedRows.value.has(actualIndex)) {
      return { backgroundColor: '#fff7e6' };
    }
    return {};
  };

  // 处理特殊标签变化
  const handlespecialMarkChange = (row, index) => {
    // 将数组转换为字符串存储
    if (row.specialMarkArray && Array.isArray(row.specialMarkArray)) {
      row.specialMark = row.specialMarkArray.join(',');
    }
    markRowChanged(index);
  };

  // 处理扫码查询
  const handleScan = async () => {
    if (!barcodeInput.value.trim()) {
      EleMessage.warning('请输入条形码');
      return;
    }

    loading.value = true;

    try {
      // 检查是否已存在该条形码
      const existingCoin = coinList.value.find(
        (coin) => coin.serialNumber === barcodeInput.value.trim()
      );
      if (existingCoin) {
        EleMessage.warning('该条形码已存在，无需重复录入');
        barcodeInput.value = '';
        return;
      }

      // 调用API查询钱币信息
      const params = {
        diyCode: barcodeInput.value.trim(),
        gm: getCurrentCoinType(),
        addType: queryOptions.addType,
        serialNumbers: coinList.value.map((coin) => coin.serialNumber)
      };

      const response = await queryCoinsByScan(params);

      // 处理接口返回的数据结构
      let coinData = [];
      if (
        response.data &&
        response.data.data &&
        Array.isArray(response.data.data)
      ) {
        coinData = response.data.data;
      } else if (response.list && Array.isArray(response.list)) {
        coinData = response.list;
      } else if (Array.isArray(response)) {
        coinData = response;
      }

      if (coinData.length > 0) {
        // 为每个钱币添加唯一ID和状态标记
        const newCoins = coinData.map((coin) => ({
          ...coin,
          // 确保所有字段都有默认值
          coinName1: coin.coinName1 || '',
          coinName2: coin.coinName2 || '',
          coinName3: coin.coinName3 || '',
          yearInfo: coin.yearInfo || '',
          bankName: coin.bankName || '',
          version: coin.version || '',
          region: coin.region || '',
          catalog: coin.catalog || '',
          serialNumber: coin.serialNumber || '',
          specialMark: coin.specialMark || '',
          specialMarkArray: coin.specialMark
            ? coin.specialMark
                .split(',')
                .filter((label) => ['★', 'EPQ', 'NET'].includes(label))
            : [], // 三个独立选项
          starLevel: coin.starLevel || '', // 星级单独字段
          inspectionNote: coin.inspectionNote || '',
          internalNote: coin.internalNote || '',
          externalNote: coin.externalNote || '',
          gradeScore: coin.gradeScore || '',
          tempId: `temp_${Date.now()}_${Math.random()}`, // 临时ID用于前端操作
          isNew: true, // 标记为新添加
          isChanged: false // 标记是否已修改
        }));

        coinList.value.push(...newCoins);

        // EleMessage.success(`成功录入 ${coinData.length} 项钱币信息`);
        barcodeInput.value = '';

        // 自动聚焦到输入框
        nextTick(() => {
          const inputEl = document.querySelector('.barcode-input input');
          if (inputEl) inputEl.focus();
        });
      } else {
        EleMessage.warning('未找到相关钱币信息');
      }
    } catch (error) {
      console.error('查询失败:', error);
      EleMessage.error('查询失败，请重试');
    } finally {
      loading.value = false;
    }
  };

  // 清空输入
  const handleClear = () => {
    barcodeInput.value = '';
  };

  // 复制条形码
  const handleCopy = () => {
    if (!coinList.value.length) return;

    const barcodes = coinList.value.map((coin) => coin.serialNumber).join('\n');

    if (navigator.clipboard) {
      navigator.clipboard.writeText(barcodes).then(() => {
        EleMessage.success('条形码列表已复制到剪贴板');
      });
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = barcodes;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      EleMessage.success('条形码列表已复制到剪贴板');
    }
  };

  // 行选择相关状态
  const selectedRows = ref([]);
  const isAllSelected = ref(false);
  const isIndeterminate = ref(false);

  // 处理选择变化
  const handleSelectionChange = (selection) => {
    selectedRows.value = selection;
    updateSelectionState();
  };

  // 更新选择状态
  const updateSelectionState = () => {
    const selectedCount = selectedRows.value.length;
    const totalCount = filteredCoinList.value.length;

    isAllSelected.value = selectedCount === totalCount && totalCount > 0;
    isIndeterminate.value = selectedCount > 0 && selectedCount < totalCount;
  };

  // 处理全选/取消全选
  const handleSelectAll = (checked) => {
    if (checked) {
      // 全选
      nextTick(() => {
        tableRef.value.toggleAllSelection();
      });
    } else {
      // 取消全选
      nextTick(() => {
        tableRef.value.clearSelection();
      });
    }
  };

  // 清空选择
  const clearSelection = () => {
    nextTick(() => {
      tableRef.value.clearSelection();
    });
  };

  // 选择已修改的行
  const selectChangedRows = () => {
    nextTick(() => {
      tableRef.value.clearSelection();

      filteredCoinList.value.forEach((coin, index) => {
        const actualIndex = coinList.value.findIndex((c) => c.id === coin.id);
        if (changedRows.value.has(actualIndex)) {
          tableRef.value.toggleRowSelection(coin, true);
        }
      });
    });
  };

  // 应用批量编辑 - 修改为只对选中的行生效
  const applyBatchEdit = () => {
    if (!selectedRows.value.length) {
      EleMessage.warning('请先选择要编辑的钱币信息');
      return;
    }

    let hasChanges = false;

    selectedRows.value.forEach((coin) => {
      const actualIndex = coinList.value.findIndex((c) => c.id === coin.id);

      // 批量应用每个字段
      const fieldsToUpdate = [
        { key: 'authenticity', value: batchEdit.authenticity },
        { key: 'coinName1', value: batchEdit.coinName1 },
        { key: 'coinName2', value: batchEdit.coinName2 },
        { key: 'coinName3', value: batchEdit.coinName3 },
        { key: 'yearInfo', value: batchEdit.yearInfo },
        { key: 'bankName', value: batchEdit.bankName },
        { key: 'version', value: batchEdit.version },
        { key: 'region', value: batchEdit.region },
        { key: 'catalog', value: batchEdit.catalog },
        { key: 'gradeScore', value: batchEdit.gradeScore },
        { key: 'starLevel', value: batchEdit.starLevel },
        { key: 'inspectionNote', value: batchEdit.inspectionNote },
        { key: 'internalNote', value: batchEdit.internalNote },
        { key: 'externalNote', value: batchEdit.externalNote }
      ];

      fieldsToUpdate.forEach((field) => {
        if (field.value && coin[field.key] !== field.value) {
          coin[field.key] = field.value;
          changedRows.value.add(actualIndex);
          hasChanges = true;
        }
      });

      // 特殊标签需要特殊处理
      if (batchEdit.specialMarkArray && batchEdit.specialMarkArray.length > 0) {
        coin.specialMark = batchEdit.specialMarkArray.join(',');
        coin.specialMarkArray = [...batchEdit.specialMarkArray];
        changedRows.value.add(actualIndex);
        hasChanges = true;
      }
    });

    if (hasChanges) {
      EleMessage.success(
        `已应用批量编辑到 ${selectedRows.value.length} 项钱币`
      );
    } else {
      EleMessage.info('没有需要更新的内容');
    }
  };

  // 清空批量编辑
  const clearBatchEdit = () => {
    Object.keys(batchEdit).forEach((key) => {
      if (typeof batchEdit[key] === 'string') {
        batchEdit[key] = '';
      } else if (Array.isArray(batchEdit[key])) {
        batchEdit[key] = [];
      } else {
        batchEdit[key] = null;
      }
    });
    EleMessage.success('已清空批量编辑设置');
  };

  // 移除钱币
  const removeCoin = (index) => {
    const coin = filteredCoinList.value[index];
    const actualIndex = coinList.value.findIndex((c) => c.id === coin.id);

    coinList.value.splice(actualIndex, 1);
    changedRows.value.delete(actualIndex);

    EleMessage.success('已移除钱币');
  };

  // 批量保存
  const handleBatchSave = async () => {
    if (!changedRows.value.size) {
      EleMessage.warning('没有需要保存的修改');
      return;
    }

    try {
      await ElMessageBox.confirm(
        `确定要保存 ${changedRows.value.size} 项修改吗？`,
        '确认保存',
        { type: 'warning' }
      );

      saving.value = true;

      // 获取已修改的数据
      const changedCoins = Array.from(changedRows.value).map(
        (index) => coinList.value[index]
      );

      // 调用API批量保存
      await batchUpdateCoins(changedCoins);

      changedRows.value.clear();
      EleMessage.success(`成功保存 ${changedCoins.length} 项修改`);

      // 保存成功后，询问是否跳转到送评单管理页面
      try {
        await ElMessageBox.confirm(
          '保存成功！是否跳转到送评单管理页面进行下一步审核？',
          '跳转确认',
          {
            type: 'success',
            confirmButtonText: '立即跳转',
            cancelButtonText: '继续录入',
            distinguishCancelAndClose: true
          }
        );

        // 用户确认跳转，导航到送评单管理页面
        router.push('/bank-note/sendform');
      } catch (action) {
        // 用户选择继续录入或关闭对话框，不做任何操作
        if (action === 'cancel') {
          // 用户选择继续录入，可以在这里添加一些提示或操作
          console.log('用户选择继续录入');
        }
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('保存失败:', error);
        EleMessage.error('保存失败，请重试');
      }
    } finally {
      saving.value = false;
    }
  };

  // 添加连号规则
  const addSerialRule = () => {
    serialGenerateList.value.push({
      field: 'serialNumber',
      prefix: '',
      startNumber: 1,
      digitCount: 8,
      suffix: '',
      mode: 'sequence',
      enabled: true
    });
  };

  // 预览单个连号规则
  const previewSingleRule = (rule) => {
    if (!rule.field) {
      EleMessage.warning('请选择要生成连号的字段');
      return;
    }

    const previewCount = Math.min(5, filteredCoinList.value.length);
    const previews = [];

    for (let i = 0; i < previewCount; i++) {
      if (rule.mode === 'sequence') {
        const number = rule.startNumber + i;
        const paddedNumber = String(number).padStart(rule.digitCount, '0');
        previews.push(`${rule.prefix}${paddedNumber}${rule.suffix}`);
      } else {
        const max = Math.pow(10, rule.digitCount) - 1;
        const randomNumber = Math.floor(Math.random() * max);
        const paddedNumber = String(randomNumber).padStart(
          rule.digitCount,
          '0'
        );
        previews.push(`${rule.prefix}${paddedNumber}${rule.suffix}`);
      }
    }

    ElMessageBox.alert(
      `<div style="line-height: 1.6;">
      <p><strong>字段：</strong>${getFieldLabel(rule.field)}</p>
      <p><strong>生成方式：</strong>${rule.mode === 'sequence' ? '顺序生成' : '随机生成'}</p>
      <p><strong>预览结果：</strong></p>
      <ul style="list-style: none; padding-left: 0;">
        ${previews.map((p) => `<li>${p}</li>`).join('')}
        ${filteredCoinList.value.length > 5 ? '<li>...</li>' : ''}
      </ul>
    </div>`,
      '连号预览',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    );
  };

  // 移除连号规则
  const removeSerialRule = (index) => {
    serialGenerateList.value.splice(index, 1);
  };

  // 生成所有连号
  const generateAllSerialNumbers = () => {
    if (!serialGenerateList.value.length) {
      EleMessage.warning('没有可生成连号的钱币信息');
      return;
    }

    ElMessageBox.confirm(
      `确定要为 ${filteredCoinList.value.length} 项钱币的所有连号规则生成连号吗？`,
      '确认生成',
      { type: 'warning' }
    )
      .then(() => {
        filteredCoinList.value.forEach((coin, index) => {
          serialGenerateList.value.forEach((rule, ruleIndex) => {
            const actualIndex = coinList.value.findIndex(
              (c) => c.id === coin.id
            );

            if (rule.enabled) {
              if (rule.mode === 'sequence') {
                // 顺序生成
                const number = rule.startNumber + index;
                const paddedNumber = String(number).padStart(
                  rule.digitCount,
                  '0'
                );
                coin[rule.field] =
                  `${rule.prefix}${paddedNumber}${rule.suffix}`;
              } else {
                // 随机生成
                const max = Math.pow(10, rule.digitCount) - 1;
                const randomNumber = Math.floor(Math.random() * max);
                const paddedNumber = String(randomNumber).padStart(
                  rule.digitCount,
                  '0'
                );
                coin[rule.field] =
                  `${rule.prefix}${paddedNumber}${rule.suffix}`;
              }

              changedRows.value.add(actualIndex);
            }
          });
        });

        EleMessage.success(`成功生成所有连号`);
      })
      .catch(() => {});
  };

  // 预览所有连号
  const previewAllSerialNumbers = () => {
    if (!serialGenerateList.value.length) {
      EleMessage.warning('没有可预览的连号规则');
      return;
    }

    const previews = [];

    filteredCoinList.value.forEach((coin, index) => {
      serialGenerateList.value.forEach((rule, ruleIndex) => {
        if (rule.enabled) {
          if (rule.mode === 'sequence') {
            const number = rule.startNumber + index;
            const paddedNumber = String(number).padStart(rule.digitCount, '0');
            previews.push(`${rule.prefix}${paddedNumber}${rule.suffix}`);
          } else {
            const max = Math.pow(10, rule.digitCount) - 1;
            const randomNumber = Math.floor(Math.random() * max);
            const paddedNumber = String(randomNumber).padStart(
              rule.digitCount,
              '0'
            );
            previews.push(`${rule.prefix}${paddedNumber}${rule.suffix}`);
          }
        }
      });
    });

    ElMessageBox.alert(
      `<div style="line-height: 1.6;">
      <p><strong>预览结果：</strong></p>
      <ul style="list-style: none; padding-left: 0;">
        ${previews.map((p) => `<li>${p}</li>`).join('')}
        ${filteredCoinList.value.length > 5 ? '<li>...</li>' : ''}
      </ul>
    </div>`,
      '所有连号预览',
      {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定'
      }
    );
  };

  // 重置所有连号规则
  const resetSerialRules = () => {
    serialGenerateList.value = [];
  };

  // 获取字段标签
  const getFieldLabel = (field) => {
    const fieldMap = {
      serialNumber: '编号',
      coinName1: '名称1',
      coinName2: '名称2',
      coinName3: '附加',
      yearInfo: '年代',
      bankName: '银行',
      version: '版别',
      gradeScore: '品相分数',
      authenticity: '真伪',
      region: '地区',
      catalog: '目录',
      starLevel: '星级',
      inspectionNote: '钱币备注',
      internalNote: '对内备注',
      externalNote: '对外备注'
    };
    return fieldMap[field] || field;
  };

  // 切换批量编辑工具展开状态
  const toggleBatchEditExpanded = () => {
    batchEditExpanded.value = !batchEditExpanded.value;
  };

  // 切换连号生成工具展开状态
  const toggleSerialToolExpanded = () => {
    serialToolExpanded.value = !serialToolExpanded.value;
  };

  // 输入框引用管理
  const inputRefs = ref(new Map());

  // 字段顺序定义（用于Tab键导航）
  const fieldOrder = [
    'coinName1',
    'coinName2',
    'coinName3',
    'yearInfo',
    'version',
    'serialNumber',
    'bankName',
    'gradeScore',
    'authenticity',
    'region',
    'catalog',
    'inspectionNote',
    'internalNote',
    'externalNote'
  ];

  // 设置输入框引用
  const setInputRef = (el, rowIndex, field) => {
    if (el) {
      const key = `${rowIndex}-${field}`;
      inputRefs.value.set(key, el);
    }
  };

  // 获取输入框引用
  const getInputRef = (rowIndex, field) => {
    const key = `${rowIndex}-${field}`;
    return inputRefs.value.get(key);
  };

  // 聚焦到指定单元格
  const focusCell = (rowIndex, field) => {
    nextTick(() => {
      const inputRef = getInputRef(rowIndex, field);
      if (inputRef) {
        // 处理不同类型的输入组件
        if (inputRef.$el) {
          // Element Plus 组件
          const input =
            inputRef.$el.querySelector('input') ||
            inputRef.$el.querySelector('textarea');
          if (input) {
            input.focus();
          }
        } else if (inputRef.focus) {
          // 原生输入框
          inputRef.focus();
        }
      }
    });
  };

  // 处理键盘事件
  const handleKeydown = (event, rowIndex, field) => {
    const { key, ctrlKey, metaKey } = event;

    // Enter键：跳到下一行同字段
    if (key === 'Enter') {
      event.preventDefault();
      const nextRowIndex = rowIndex + 1;
      if (nextRowIndex < filteredCoinList.value.length) {
        focusCell(nextRowIndex, field);
      } else {
        EleMessage.success('已到达最后一行');
      }
      return;
    }

    // Tab键：横向切换字段
    if (key === 'Tab') {
      event.preventDefault();
      const currentFieldIndex = fieldOrder.indexOf(field);
      let nextFieldIndex;

      if (event.shiftKey) {
        // Shift+Tab：向前切换
        nextFieldIndex =
          currentFieldIndex > 0 ? currentFieldIndex - 1 : fieldOrder.length - 1;
      } else {
        // Tab：向后切换
        nextFieldIndex =
          currentFieldIndex < fieldOrder.length - 1 ? currentFieldIndex + 1 : 0;
      }

      const nextField = fieldOrder[nextFieldIndex];

      // 检查字段是否在当前视图中可见
      if (
        nextField === 'serialNumber' &&
        currentTab.value !== '纸币' &&
        currentTab.value !== 'all'
      ) {
        // 如果编号字段在当前标签下不可见，跳过
        const skipFieldIndex = event.shiftKey
          ? nextFieldIndex - 1
          : nextFieldIndex + 1;
        const skipField = fieldOrder[skipFieldIndex] || fieldOrder[0];
        focusCell(rowIndex, skipField);
      } else {
        focusCell(rowIndex, nextField);
      }
      return;
    }

    // Ctrl+D 或 Cmd+D：复制上一行同字段的值
    if ((ctrlKey || metaKey) && key === 'd') {
      event.preventDefault();
      if (rowIndex > 0) {
        const prevRowCoin = filteredCoinList.value[rowIndex - 1];
        const currentCoin = filteredCoinList.value[rowIndex];

        if (prevRowCoin && currentCoin && prevRowCoin[field] !== undefined) {
          currentCoin[field] = prevRowCoin[field];

          // 处理特殊字段
          if (field === 'specialMark') {
            currentCoin.specialMarkArray = prevRowCoin.specialMarkArray
              ? [...prevRowCoin.specialMarkArray]
              : [];
          }

          markRowChanged(rowIndex);
          EleMessage.success(`已复制上一行的${getFieldLabel(field)}值`);
        }
      } else {
        EleMessage.warning('没有上一行可复制');
      }
      return;
    }
  };

  // 组件挂载时自动聚焦
  onMounted(() => {
    nextTick(() => {
      const inputEl = document.querySelector('.barcode-input input');
      if (inputEl) inputEl.focus();
    });

    // 初始化默认连号规则
    addSerialRule();
  });
</script>

<script>
  export default {
    name: 'ScanInput'
  };
</script>

<style scoped>
  .scan-input-header {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .barcode-input-section {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .barcode-input {
    flex: 1;
    max-width: 600px;
  }

  .options-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }

  .batch-edit-toolbar {
    margin-top: 8px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: #fff;
  }

  .toolbar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
    transition: background-color 0.2s;
  }

  .toolbar-header:hover {
    background-color: #f1f3f4;
  }

  .toolbar-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    color: #303133;
  }

  .expand-icon {
    transition: transform 0.2s;
    color: #909399;
  }

  .expand-icon.is-expanded {
    transform: rotate(180deg);
  }

  .toolbar-content {
    padding: 16px;
  }

  .toolbar-header:first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }

  .toolbar-header:not(:first-child) {
    border-top: 1px solid #e5e7eb;
    margin-top: 8px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border-bottom: none;
  }

  .batch-controls {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
  }

  .batch-control-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    padding: 4px 6px;
    min-height: 32px;
  }

  .batch-control-item:hover {
    background: rgba(64, 158, 255, 0.05);
    border-radius: 4px;
  }

  .batch-control-item .label {
    font-size: 12px;
    color: #606266;
    white-space: nowrap;
    font-weight: 500;
    line-height: 1;
    min-width: 60px;
    text-align: right;
    margin-right: 4px;
    flex-shrink: 0;
  }

  /* 复选框组样式 */
  .batch-control-item :deep(.el-checkbox-group) {
    display: flex;
    gap: 6px;
    flex-wrap: nowrap;
    align-items: center;
  }

  .batch-control-item :deep(.el-checkbox) {
    margin-right: 0;
    font-size: 12px;
  }

  .batch-control-item :deep(.el-checkbox__label) {
    font-size: 12px;
    padding-left: 4px;
    font-weight: 400;
  }

  .batch-action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
    height: 100%;
  }

  /* 连号生成工具样式 */
  .serial-generate-section {
    background-color: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-top: 0;
  }

  .serial-rule-item {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
    margin-bottom: 8px;
  }

  .serial-rule-item:last-child {
    margin-bottom: 0;
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }

  :deep(.el-card__header) {
    padding: 12px 16px;
    border-bottom: 1px solid #ebeef5;
  }

  :deep(.barcode-input .el-input__prefix) {
    color: #409eff;
  }

  :deep(.batch-edit-toolbar .el-divider__text) {
    font-weight: 500;
    color: #409eff;
  }

  /* 已修改行的样式 */
  :deep(.el-table__row.row-changed) {
    background-color: #fff7e6;
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .options-section {
      flex-direction: column;
      align-items: stretch;
    }

    .batch-controls {
      flex-direction: column;
      align-items: stretch;
    }

    .batch-control-item {
      justify-content: space-between;
    }

    .serial-generate-section {
      padding: 8px;
    }

    .serial-rule-item {
      padding: 8px;
    }
  }

  /* 选择工具栏样式 */
  .selection-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin-bottom: 12px;
  }

  .selection-info {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .selection-count {
    font-size: 12px;
    color: #666;
  }

  .selection-actions {
    display: flex;
    gap: 8px;
  }

  /* 快捷键提示样式 */
  .shortcut-tips {
    margin-bottom: 12px;
  }

  .shortcut-tips :deep(.el-alert) {
    padding: 8px 12px;
  }

  .shortcut-tips :deep(.el-alert__title) {
    font-size: 12px;
    line-height: 1.4;
  }
</style>
