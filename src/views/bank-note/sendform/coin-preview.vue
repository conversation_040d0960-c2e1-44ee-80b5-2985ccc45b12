<template>
  <div class="coin-preview-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">中乾评级证书</h1>
          <p class="page-subtitle">专业钱币评级认证服务</p>
        </div>
        <div class="header-right">
          <el-tag
            v-if="coinData?.coin?.diyCode"
            class="diy-code-tag"
            size="large"
          >
            编码：{{ coinData.coin.diyCode }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 错误信息 -->
    <el-alert
      v-else-if="error"
      :title="error"
      type="error"
      show-icon
      :closable="false"
      class="error-alert"
    />

    <!-- 钱币详情主体 -->
    <div v-else-if="coinData" class="coin-details-main">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧：钱币图片展示 -->
        <div class="image-section">
          <div class="image-container">
            <div v-if="coinImages.length > 0" class="coin-images">
              <div class="image-gallery">
                <div
                  v-for="(image, index) in coinImages"
                  :key="index"
                  class="image-item"
                >
                  <el-image
                    :src="image"
                    :preview-src-list="coinImages"
                    :initial-index="index"
                    fit="cover"
                    class="coin-image"
                    :preview-teleported="true"
                  />
                  <div class="image-label">{{ getImageLabel(index) }}</div>
                </div>
              </div>
            </div>
            <div v-else class="no-image">
              <el-icon class="no-image-icon"><Picture /></el-icon>
              <p>暂无图片</p>
            </div>
          </div>
        </div>

        <!-- 右侧：钱币信息 -->
        <div class="info-section">
          <!-- 评级结果卡片 -->
          <div class="grade-card">
            <div class="grade-header">
              <h3>评级结果</h3>
            </div>
            <div class="grade-content">
              <div class="grade-score">
                {{ formatGradeScore(coinData.coin?.gradeScore, coinData.coin?.gradeScoreValue) }}
              </div>
              <div class="grade-details">
                <div class="grade-item">
                  <span class="label">真伪性：</span>
                  <span class="value">{{
                    coinData.coin?.authenticity || '未鉴定'
                  }}</span>
                </div>
                <div v-if="coinData.coin?.specialMark" class="grade-item">
                  <span class="label">特殊标记：</span>
                  <span class="value">{{ coinData.coin.specialMark }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 基本信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <h3>基本信息</h3>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">名称：</span>
                  <span class="value">{{
                    coinData.display?.coinName || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">版别：</span>
                  <span class="value">{{
                    coinData.display?.serialNumberWithVersion || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">发行银行：</span>
                  <span class="value">{{
                    coinData.coin?.issueBank || '中国人民银行'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">冠号：</span>
                  <span class="value">{{
                    coinData.coin?.serialNumber || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">年代：</span>
                  <span class="value">{{
                    coinData.coin?.yearInfo || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">备注：</span>
                  <span class="value">{{ coinData.coin?.remark || '' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">赔付等级：</span>
                  <span class="value">{{
                    coinData.coin?.compensationLevel || ''
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 送评信息卡片 -->
          <div class="info-card">
            <div class="card-header">
              <h3>送评信息</h3>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">送评单号：</span>
                  <span class="value">{{
                    coinData.coin?.sendnum || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">客户姓名：</span>
                  <span class="value">{{
                    coinData.display?.customerName || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">送评公司：</span>
                  <span class="value">{{
                    coinData.display?.companyName || '未设置'
                  }}</span>
                </div>
                <div class="info-item">
                  <span class="label">提交时间：</span>
                  <span class="value">{{
                    formatDate(coinData.display?.submitDate)
                  }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 评级详情卡片 -->
          <div v-if="hasGradeDetails" class="info-card">
            <div class="card-header">
              <h3>评级详情</h3>
            </div>
            <div class="card-content">
              <div class="info-grid">
                <div
                  v-if="coinData.coin?.scoreRemarks"
                  class="info-item full-width"
                >
                  <span class="label">评分备注：</span>
                  <span class="value">{{ coinData.coin.scoreRemarks }}</span>
                </div>
                <div
                  v-if="coinData.coin?.inspectionNote"
                  class="info-item full-width"
                >
                  <span class="label">验货标注：</span>
                  <span class="value">{{ coinData.coin.inspectionNote }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="action-bar">
        <div class="action-content">
          <div class="action-left">
            <span class="copyright"
              >© 2005-2024 广东宝元鑫泉艺术品评估鉴定有限公司</span
            >
          </div>
          <div class="action-right">
            <el-button @click="goBack" class="action-btn">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <el-button type="primary" @click="refresh" class="action-btn">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
            <el-button type="success" @click="testPublicApi" class="action-btn">
              <el-icon><Connection /></el-icon>
              测试接口
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { ElMessage } from 'element-plus';
  import { getCoinPreviewByDiyCode } from './api';
  import axios from 'axios';
  import { API_BASE_URL } from '@/config/setting';
  import {
    Picture,
    ArrowLeft,
    Refresh,
    Connection
  } from '@element-plus/icons-vue';

  const route = useRoute();
  const router = useRouter();

  // 响应式数据
  const loading = ref(false);
  const error = ref('');
  const coinData = ref(null);

  // 计算属性
  const coinImages = computed(() => {
    if (!coinData.value?.coin?.coinImages) return [];
    try {
      return JSON.parse(coinData.value.coin.coinImages);
    } catch (e) {
      return [];
    }
  });

  // 是否有评级详情
  const hasGradeDetails = computed(() => {
    return (
      coinData.value?.coin?.scoreRemarks || coinData.value?.coin?.inspectionNote
    );
  });

  // 方法
  const loadCoinData = async () => {
    const diyCode = route.params.diyCode;
    if (!diyCode) {
      error.value = '缺少送评条码参数';
      return;
    }

    loading.value = true;
    error.value = '';

    try {
      // 直接调用公开接口，无需 token，使用原生 axios
      const res = await axios.get(
        `${API_BASE_URL}/pjosendform/preview/${diyCode}`
      );
      if (res.data.code === 0) {
        coinData.value = res.data.data;
      } else {
        throw new Error(res.data.message);
      }
    } catch (err) {
      error.value = err.message || '加载钱币详情失败';
      ElMessage.error(error.value);
    } finally {
      loading.value = false;
    }
  };

  const formatDate = (date) => {
    if (!date) return '未设置';
    return new Date(date).toLocaleString('zh-CN');
  };

  // 格式化品相分数显示
  const formatGradeScore = (gradeScore, gradeScoreValue) => {
    if (!gradeScore && !gradeScoreValue) {
      return '未评级';
    }

    // 如果有 gradeScoreValue，从 gradeScore 中移除数字部分，然后组合显示
    if (gradeScoreValue && gradeScore) {
      // 移除 gradeScore 中的数字部分，得到文字部分
      const gradeText = gradeScore.replace(gradeScoreValue, '').trim();
      return `${gradeText} ${gradeScoreValue}`;
    }

    // 如果只有 gradeScore，直接显示
    if (gradeScore) {
      return gradeScore;
    }

    // 如果只有 gradeScoreValue，只显示数字
    if (gradeScoreValue) {
      return gradeScoreValue;
    }

    return '未评级';
  };

  // 获取图片标签
  const getImageLabel = (index) => {
    const labels = ['正面', '背面', '侧面', '细节'];
    return labels[index] || `图片${index + 1}`;
  };

  const goBack = () => {
    router.back();
  };

  const refresh = () => {
    loadCoinData();
  };

  // 测试公开接口
  const testPublicApi = async () => {
    const diyCode = route.params.diyCode;
    if (!diyCode) {
      ElMessage.warning('缺少送评条码参数');
      return;
    }

    try {
      // 直接调用公开接口测试
      const response = await fetch(
        `${API_BASE_URL}/pjosendform/preview/${diyCode}`
      );
      const data = await response.json();

      if (data.code === 0) {
        ElMessage.success('公开接口调用成功！');
        console.log('接口返回数据:', data.data);
      } else {
        ElMessage.error('接口调用失败: ' + data.message);
      }
    } catch (error) {
      ElMessage.error('接口调用出错: ' + error.message);
      console.error('接口调用错误:', error);
    }
  };

  // 生命周期
  onMounted(() => {
    loadCoinData();
  });
</script>

<style scoped>
/* 全局容器 */
.coin-preview-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 0;
  margin: 0;
  font-family: 'PingFang SC', 'Microsoft Yahei', 'Helvetica Neue', Arial, sans-serif;
}

/* 页面头部 - 中式传统风格 */
.page-header {
  background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
  color: white;
  padding: 20px 0;
  box-shadow: 0 2px 8px rgba(139, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  opacity: 0.3;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
  font-family: 'STKaiti', 'KaiTi', 'Microsoft Yahei', serif;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  letter-spacing: 2px;
}

.page-subtitle {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
  letter-spacing: 1px;
}

.header-right {
  flex-shrink: 0;
}

.diy-code-tag {
  background: rgba(255, 215, 0, 0.9);
  border: 2px solid #FFD700;
  color: #8B0000;
  font-size: 14px;
  font-weight: 700;
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

/* 加载状态 */
.loading-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;
}

/* 错误信息 */
.error-alert {
  max-width: 1200px;
  margin: 20px auto;
}

/* 主要内容区域 */
.coin-details-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px 100px;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

/* 图片区域 */
.image-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 2px solid #E8E8E8;
  position: relative;
}

.image-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #8B0000 0%, #DC143C 50%, #8B0000 100%);
  border-radius: 12px 12px 0 0;
}

.image-container {
  width: 100%;
  margin-top: 8px;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  border: 1px solid #E0E0E0;
}

.image-item:hover {
  transform: translateY(-2px);
}

.coin-image {
  width: 100%;
  height: 160px;
  border-radius: 8px;
  cursor: pointer;
}

.image-label {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(139, 0, 0, 0.8));
  color: white;
  padding: 8px 12px 12px;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
  background: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #e4e7ed;
}

.no-image-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-image p {
  margin: 0;
  font-size: 16px;
}

/* 信息区域 */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 评级结果卡片 */
.grade-card {
  background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
  color: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(139, 0, 0, 0.3);
  position: relative;
  border: 2px solid #FFD700;
}

.grade-card::after {
  content: '★';
  position: absolute;
  top: 12px;
  right: 16px;
  font-size: 20px;
  color: #FFD700;
}

.grade-header h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
  letter-spacing: 1px;
  font-family: 'STKaiti', 'KaiTi', 'Microsoft Yahei', serif;
}

.grade-score {
  font-size: 36px;
  font-weight: 700;
  font-family: 'Impact', 'Arial Black', monospace;
  text-align: center;
  margin-bottom: 16px;
  color: #FFD700;
}

.grade-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.grade-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.grade-item:last-child {
  border-bottom: none;
}

.grade-item .label {
  font-weight: 500;
  opacity: 0.9;
}

.grade-item .value {
  font-weight: 600;
}

/* 信息卡片 */
.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #E8E8E8;
  overflow: hidden;
  position: relative;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #8B0000 0%, #DC143C 50%, #8B0000 100%);
}

.card-header {
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
  color: #8B0000;
  font-family: 'STKaiti', 'KaiTi', 'Microsoft Yahei', serif;
  letter-spacing: 1px;
}

.card-content {
  padding: 20px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: stretch;
}

.info-item .label {
  font-weight: 700;
  color: #8B0000;
  min-width: 80px;
  margin-right: 12px;
  font-size: 13px;
}

.info-item.full-width .label {
  margin-bottom: 6px;
  margin-right: 0;
}

.info-item .value {
  color: #333;
  font-weight: 500;
  flex: 1;
  font-size: 14px;
}

/* 底部操作栏 */
.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #fafafa 0%, #f0f0f0 100%);
  border-top: 2px solid #8B0000;
  box-shadow: 0 -2px 12px rgba(139, 0, 0, 0.15);
  z-index: 1000;
}

.action-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-left {
  flex: 1;
}

.copyright {
  color: #8B0000;
  font-size: 11px;
  font-weight: 500;
}

.action-right {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    padding: 0 12px;
  }

  .page-title {
    font-size: 20px;
  }

  .image-gallery {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .coin-image {
    height: 120px;
  }

  .coin-details-main {
    padding: 16px 12px 120px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 18px;
  }

  .image-gallery {
    grid-template-columns: 1fr;
  }

  .coin-image {
    height: 180px;
  }

  .action-right {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .info-item .label {
    min-width: auto;
    margin-right: 0;
    margin-bottom: 2px;
  }
}
</style>
