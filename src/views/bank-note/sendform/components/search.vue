<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form
      size="small"
      label-width="77px"
      @keyup.enter="search"
      @submit.prevent=""
    >
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="送评单号">
            <el-input
              clearable
              v-model.trim="form.sendnum"
              placeholder="请输入送评单号"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="编号">
            <el-input
              clearable
              v-model.trim="form.nummber"
              placeholder="请输入编号"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="姓名">
            <el-input
              clearable
              v-model.trim="form.rname"
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="网名">
            <el-input
              clearable
              v-model.trim="form.nickname"
              placeholder="请输入网名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="24" :md="24" :sm="24" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" size="small" plain @click="search"
              >查询</el-button
            >
            <el-button size="small" @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { EleCard } from 'ele-admin-plus';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    sendnum: '',
    nummber: '',
    rname: '',
    nickname: ''
  });

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
