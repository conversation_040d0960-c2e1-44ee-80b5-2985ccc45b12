<template>
  <div class="editable-template-name">
    <div v-if="!isEditing" class="display-mode" @click="startEdit">
      <span class="template-name">{{ displayName }}</span>
      <el-icon class="edit-icon">
        <EditPen />
      </el-icon>
    </div>

    <div v-else class="edit-mode">
      <el-input
        ref="inputRef"
        v-model="editValue"
        size="small"
        :maxlength="50"
        show-word-limit
        @blur="confirmEdit"
        @keyup.enter="confirmEdit"
        @keyup.esc="cancelEdit"
        placeholder="请输入模板名称"
      />
      <div class="edit-actions">
        <el-button size="small" type="primary" @click="confirmEdit">
          <el-icon><Check /></el-icon>
        </el-button>
        <el-button size="small" @click="cancelEdit">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, computed, nextTick, watch } from 'vue';
  import { EditPen, Check, Close } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus/es';

  const props = defineProps({
    modelValue: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '未命名模板'
    },
    maxLength: {
      type: Number,
      default: 50
    },
    minLength: {
      type: Number,
      default: 2
    },
    disabled: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['update:modelValue', 'change']);

  // 响应式数据
  const isEditing = ref(false);
  const editValue = ref('');
  const inputRef = ref();

  // 计算属性
  const displayName = computed(() => {
    return props.modelValue || props.placeholder;
  });

  // 开始编辑
  const startEdit = () => {
    if (props.disabled) return;

    isEditing.value = true;
    editValue.value = props.modelValue || '';

    nextTick(() => {
      if (inputRef.value) {
        inputRef.value.focus();
        inputRef.value.select();
      }
    });
  };

  // 确认编辑
  const confirmEdit = () => {
    const trimmedValue = editValue.value.trim();

    // 验证输入
    if (!trimmedValue) {
      EleMessage.error('模板名称不能为空');
      return;
    }

    if (trimmedValue.length < props.minLength) {
      EleMessage.error(`模板名称至少需要${props.minLength}个字符`);
      return;
    }

    if (trimmedValue.length > props.maxLength) {
      EleMessage.error(`模板名称不能超过${props.maxLength}个字符`);
      return;
    }

    // 检查是否有变化
    if (trimmedValue !== props.modelValue) {
      emit('update:modelValue', trimmedValue);
      emit('change', trimmedValue);
      EleMessage.success('模板名称已更新');
    }

    isEditing.value = false;
  };

  // 取消编辑
  const cancelEdit = () => {
    editValue.value = props.modelValue || '';
    isEditing.value = false;
  };

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      if (!isEditing.value) {
        editValue.value = newValue || '';
      }
    }
  );

  // 暴露方法
  defineExpose({
    startEdit,
    confirmEdit,
    cancelEdit,
    isEditing: () => isEditing.value
  });
</script>

<style scoped>
  .editable-template-name {
    display: inline-block;
    min-width: 120px;
  }

  .display-mode {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s;
  }

  .display-mode:hover {
    background-color: #f5f7fa;
  }

  .template-name {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    min-width: 100px;
  }

  .edit-icon {
    font-size: 14px;
    color: #909399;
    opacity: 0;
    transition: opacity 0.2s;
  }

  .display-mode:hover .edit-icon {
    opacity: 1;
  }

  .edit-mode {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .edit-actions {
    display: flex;
    gap: 4px;
  }

  /* 输入框样式调整 */
  .edit-mode :deep(.el-input) {
    min-width: 200px;
  }

  .edit-mode :deep(.el-input__wrapper) {
    box-shadow: 0 0 0 1px #409eff inset;
  }

  /* 禁用状态 */
  .editable-template-name.disabled .display-mode {
    cursor: not-allowed;
    opacity: 0.6;
  }

  .editable-template-name.disabled .display-mode:hover {
    background-color: transparent;
  }

  .editable-template-name.disabled .edit-icon {
    display: none;
  }
</style>
