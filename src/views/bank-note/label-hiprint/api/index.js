import request from '@/utils/request';

/**
 * 获取可用字段列表
 */
export async function getAvailableFields() {
  const res = await request.get('/label-design/fields');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 按分类获取字段列表
 */
export async function getFieldsByCategory() {
  const res = await request.get('/label-design/fields/by-category');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存标签模板
 */
export async function saveTemplate(templateData) {
  const res = await request.post('/label-design/template', templateData);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 更新标签模板
 */
export async function updateTemplate(id, templateData) {
  const res = await request.put(`/label-design/template/${id}`, templateData);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取模板列表
 */
export async function getTemplateList() {
  const res = await request.get('/label-design/templates');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取模板详情
 */
export async function getTemplateById(id) {
  const res = await request.get(`/label-design/template/${id}`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除标签模板
 */
export async function deleteTemplate(id) {
  const res = await request.delete(`/label-design/template/${id}`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取默认模板
 */
export async function getDefaultTemplate() {
  const res = await request.get('/label-design/template/default');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 设置默认模板
 */
export async function setDefaultTemplate(id) {
  const res = await request.put(`/label-design/template/${id}/default`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 预览标签效果
 */
export async function previewLabel(templateId, coinIds) {
  const res = await request.post(
    `/label-design/preview?templateId=${templateId}`,
    coinIds
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 复制模板
 */
export async function copyTemplate(id, newName) {
  const res = await request.post(`/label-design/template/${id}/copy`, {
    newName
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 导出模板
 */
export async function exportTemplate(id) {
  const res = await request.get(`/label-design/template/${id}/export`, {
    responseType: 'blob'
  });
  return res.data;
}

/**
 * 导入模板
 */
export async function importTemplate(file) {
  const formData = new FormData();
  formData.append('file', file);

  const res = await request.post('/label-design/template/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });

  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 验证模板数据
 */
export async function validateTemplate(templateData) {
  const res = await request.post(
    '/label-design/template/validate',
    templateData
  );
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取模板使用统计
 */
export async function getTemplateStats(id) {
  const res = await request.get(`/label-design/template/${id}/stats`);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除模板
 */
export async function batchDeleteTemplates(ids) {
  const res = await request.delete('/label-design/templates/batch', {
    data: { ids }
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 搜索模板
 */
export async function searchTemplates(params) {
  const res = await request.get('/label-design/templates/search', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
