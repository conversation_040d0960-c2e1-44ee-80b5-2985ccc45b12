<template>
  <div class="label-designer">
    <!-- 顶部工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button-group>
          <el-button type="primary" :icon="DocumentAdd" @click="newTemplate">
            新建模板
          </el-button>
          <el-button :icon="FolderOpened" @click="loadTemplate">
            加载模板
          </el-button>
          <el-button
            :icon="Document"
            @click="handleSaveTemplate"
            :loading="saving"
          >
            保存模板
          </el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <el-button-group>
          <el-button :icon="RefreshLeft" @click="undo" :disabled="!canUndo">
            撤销
          </el-button>
          <el-button :icon="RefreshRight" @click="redo" :disabled="!canRedo">
            重做
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-center">
        <el-input
          v-model="currentTemplate.templateName"
          placeholder="请输入模板名称"
          style="width: 200px"
        />
      </div>

      <div class="toolbar-right">
        <el-button-group>
          <el-button :icon="View" @click="previewLabel">预览</el-button>
          <el-button :icon="Refresh" @click="resetDesigner">重置</el-button>
        </el-button-group>

        <el-divider direction="vertical" />

        <div class="toolbar-options">
          <el-checkbox v-model="snapToGrid" size="small">网格吸附</el-checkbox>
          <el-checkbox v-model="showGrid" size="small">显示网格</el-checkbox>
        </div>
      </div>
    </div>

    <div class="designer-content">
      <!-- 左侧字段面板 -->
      <div class="field-panel">
        <div class="panel-header">
          <h3>可用字段</h3>
          <el-button size="small" @click="refreshFields">刷新</el-button>
        </div>

        <div class="field-categories" v-loading="loadingFields">
          <el-collapse v-model="activeCategories">
            <el-collapse-item
              v-for="(fields, category) in fieldCategories"
              :key="category"
              :title="getCategoryName(category)"
              :name="category"
            >
              <div class="field-list">
                <div
                  v-for="field in fields"
                  :key="field.fieldName"
                  class="field-item"
                  draggable="true"
                  @dragstart="handleFieldDragStart($event, field)"
                  @dragend="handleFieldDragEnd"
                  :title="field.fieldName"
                >
                  <el-tag
                    :type="getFieldTagType(field.fieldType, field.fieldName)"
                    size="small"
                  >
                    {{ field.displayName }}
                  </el-tag>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <!-- 中间设计区域 -->
      <div class="design-area">
        <div class="design-header">
          <span>标签设计区域 ({{ canvasWidth }}mm × {{ canvasHeight }}mm)</span>
          <el-button-group size="small">
            <el-button @click="zoomOut">缩小</el-button>
            <el-button @click="resetZoom"
              >{{ Math.round(zoomLevel * 100) }}%</el-button
            >
            <el-button @click="zoomIn">放大</el-button>
          </el-button-group>
        </div>

        <div
          class="canvas-container"
          :style="{ transform: `scale(${zoomLevel})` }"
        >
          <div
            class="label-canvas"
            :class="{ 'show-grid': showGrid }"
            :style="{ width: canvasWidth + 'mm', height: canvasHeight + 'mm' }"
            @drop="handleDrop"
            @dragover.prevent
            @click="clearSelection"
            @contextmenu="showContextMenu"
          >
            <!-- 网格背景 -->
            <div class="grid-background" v-if="showGrid"></div>

            <!-- 标签区域 -->
            <div
              v-for="(zone, index) in labelZones"
              :key="zone.id"
              class="label-zone"
              :class="{
                selected: selectedZone?.id === zone.id,
                dragging:
                  dragState.isDraggingZone &&
                  dragState.draggedZone?.id === zone.id
              }"
              :style="getZoneStyle(zone, index)"
              @click.stop="selectZone(zone)"
              @mousedown="startZoneDrag($event, zone)"
              @contextmenu.stop="showZoneContextMenu($event, zone)"
            >
              <div class="zone-content">
                <div class="zone-title">{{ zone.name }}</div>
                <div
                  class="zone-fields"
                  v-if="zone.fields && zone.fields.length"
                >
                  <el-tag
                    v-for="fieldName in zone.fields"
                    :key="fieldName"
                    size="small"
                    closable
                    @close="removeFieldFromZone(zone, fieldName)"
                  >
                    {{ getFieldDisplayName(fieldName) }}
                  </el-tag>
                </div>
              </div>

              <!-- 选中状态的控制点 -->
              <div class="zone-controls" v-if="selectedZone?.id === zone.id">
                <!-- 调整大小手柄 -->
                <div
                  v-for="handle in resizeHandles"
                  :key="handle"
                  :class="`resize-handle resize-${handle}`"
                  @mousedown.stop="startResize($event, zone, handle)"
                ></div>

                <!-- 删除按钮 -->
                <div class="delete-btn" @click.stop="removeZone(zone)">
                  <el-icon><Close /></el-icon>
                </div>
              </div>
            </div>

            <!-- 对齐辅助线 -->
            <div
              v-for="guide in alignmentGuides"
              :key="guide.id"
              class="alignment-guide"
              :class="guide.type"
              :style="guide.style"
            ></div>
          </div>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <PropertyPanel
        :selected-zone="selectedZone"
        :label-zones="labelZones"
        :field-categories="fieldCategories"
        :canvas-width="canvasWidth"
        :canvas-height="canvasHeight"
        @property-change="handlePropertyChange"
        @copy-zone="copyZone"
        @delete-zone="removeZone"
        @move-layer="handleLayerMove"
        @canvas-size-change="handleCanvasSizeChange"
        @template-type-change="handleTemplateTypeChange"
      />
    </div>

    <!-- 右键菜单 -->
    <el-dropdown
      ref="contextMenuRef"
      trigger="manual"
      :virtual-ref="contextMenuTarget"
      virtual-triggering
      @command="handleContextMenuCommand"
    >
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item command="copy" :disabled="!selectedZone">
            <el-icon><DocumentCopy /></el-icon>复制
          </el-dropdown-item>
          <el-dropdown-item command="paste" :disabled="!copiedZone">
            <el-icon><Document /></el-icon>粘贴
          </el-dropdown-item>
          <el-dropdown-item command="delete" :disabled="!selectedZone" divided>
            <el-icon><Delete /></el-icon>删除
          </el-dropdown-item>
          <el-dropdown-item command="selectAll" divided>
            <el-icon><Select /></el-icon>全选
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>

    <!-- 模板加载对话框 -->
    <el-dialog v-model="showTemplateDialog" title="选择模板" width="600px">
      <div v-loading="loadingTemplates">
        <el-table :data="templateList" @row-click="selectTemplate">
          <el-table-column prop="templateName" label="模板名称" />
          <el-table-column prop="templateType" label="类型" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="180" />
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button size="small" @click="loadSelectedTemplate(row)"
                >加载</el-button
              >
              <el-button
                size="small"
                type="danger"
                @click="deleteTemplateConfirm(row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreviewDialog" title="标签预览" width="800px">
      <div class="preview-content" v-html="previewContent"></div>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    ref,
    reactive,
    computed,
    nextTick,
    onMounted,
    onUnmounted,
    watch
  } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import {
    DocumentAdd,
    FolderOpened,
    Document,
    View,
    Refresh,
    Close,
    RefreshLeft,
    RefreshRight,
    DocumentCopy,
    Delete,
    Select
  } from '@element-plus/icons-vue';

  import PropertyPanel from './components/PropertyPanel.vue';
  import { useDragDrop } from './composables/useDragDrop';
  import { useResize } from './composables/useResize';
  import { useHistory } from './composables/useHistory';
  import {
    getFieldsByCategory,
    saveTemplate,
    getTemplateList,
    deleteTemplate,
    previewLabel as previewLabelApi
  } from './api';

  // 组合式函数
  const {
    dragState,
    snapToGrid,
    handleFieldDragStart,
    handleFieldDragEnd,
    startZoneDrag,
    handleCanvasDrop,
    handleDragOver,
    setSnapToGrid: setDragSnapToGrid,
    initDragDrop,
    destroyDragDrop
  } = useDragDrop();

  const {
    isResizing,
    startResize,
    setSnapToGrid: setResizeSnapToGrid,
    setGridSize: setResizeGridSize,
    initResize,
    destroyResize
  } = useResize();

  const labelZones = ref([
    {
      id: 'logo',
      name: '公司Logo位置(预留)',
      x: 0,
      y: 0,
      width: 30,
      height: 26,
      fontSize: 12,
      color: '#999999',
      backgroundColor: '#f0f0f0',
      fields: [] // 公司Logo不要字段，位置预留给底板纸自带的Logo
    },
    {
      id: 'coinInfo',
      name: '钱币信息',
      x: 30,
      y: 0,
      width: 100,
      height: 26,
      fontSize: 10,
      color: '#333333',
      backgroundColor: '#ffffff',
      fields: ['bankName', 'coinName1', 'serialNumber'], // 默认字段，用户可自定义组合
      layout: 'vertical', // 垂直布局：银行名称、钱币名称1、编号-版别
      padding: 3,
      fieldSpacing: 2,
      lineHeight: 1.2
    },
    {
      id: 'gradeInfo',
      name: '评级信息',
      x: 130,
      y: 0,
      width: 62,
      height: 26,
      fontSize: 12,
      color: '#333333',
      backgroundColor: '#ffffff',
      fields: ['gradeScore'], // 使用原始的评级分数字段
      fieldStyles: {
        gradeScore: {
          displayMode: 'grade-layered', // 使用评级分层显示模式
          fontSize: 12, // 基础字体大小
          scoreFontSize: 28, // 数字部分字体大小
          levelFontSize: 12, // 文字部分字体大小
          lineHeight: 1.2
        }
      },
      padding: 4,
      fieldSpacing: 2,
      lineHeight: 1.2
    },
    {
      id: 'qrCodeArea',
      name: '二维码区域',
      x: 160,
      y: 0,
      width: 32,
      height: 26,
      fontSize: 10,
      color: '#333333',
      backgroundColor: '#ffffff',
      fields: ['qrCode'],
      fieldStyles: {
        qrCode: {
          displayMode: 'normal',
          qrSize: 50,
          showText: true,
          textSize: 8
        }
      },
      padding: 2,
      fieldSpacing: 1,
      lineHeight: 1.1
    }
  ]);

  const {
    canUndo,
    canRedo,
    saveState,
    undo,
    redo,
    initHistory,
    initKeyboardShortcuts,
    destroyHistory
  } = useHistory(labelZones);

  // 响应式数据
  const fieldCategories = ref({});
  const loadingFields = ref(false);
  const activeCategories = ref(['BASIC_INFO', 'GRADE_INFO', 'SPECIAL']);
  const selectedZone = ref(null);
  const showGrid = ref(true);
  const alignmentGuides = ref([]);

  // 调整大小手柄
  const resizeHandles = ['nw', 'n', 'ne', 'w', 'e', 'sw', 's', 'se'];

  // 右键菜单
  const contextMenuRef = ref();
  const contextMenuTarget = ref();
  const copiedZone = ref(null);

  // 画布设置 - 默认为大签尺寸
  const canvasWidth = ref(192);
  const canvasHeight = ref(26);
  const zoomLevel = ref(1);

  // 模板相关
  const currentTemplate = reactive({
    id: null,
    templateName: '新建标签模板',
    templateType: 'CUSTOM',
    layoutConfig: '', // JSON: zones配置
    fieldMapping: '' // JSON: 字段映射
  });

  const saving = ref(false);
  const showTemplateDialog = ref(false);
  const templateList = ref([]);
  const loadingTemplates = ref(false);
  const showPreviewDialog = ref(false);
  const previewContent = ref('');

  // 计算样式
  const getZoneStyle = (zone, index) => {
    return {
      left: zone.x + 'mm',
      top: zone.y + 'mm',
      width: zone.width + 'mm',
      height: zone.height + 'mm',
      fontSize: zone.fontSize + 'px',
      color: zone.color,
      backgroundColor: zone.backgroundColor,
      fontWeight: zone.fontWeight || 'normal',
      border: zone.borderWidth
        ? `${zone.borderWidth}px ${zone.borderStyle || 'solid'} ${zone.borderColor || '#ccc'}`
        : '1px dashed #ccc',
      borderRadius: zone.borderRadius ? zone.borderRadius + 'px' : '0',
      padding:
        zone.paddingTop ||
        zone.paddingRight ||
        zone.paddingBottom ||
        zone.paddingLeft
          ? `${zone.paddingTop || 0}px ${zone.paddingRight || 0}px ${zone.paddingBottom || 0}px ${zone.paddingLeft || 0}px`
          : '4px',
      zIndex: 100 + index
    };
  };

  // 工具方法
  const getCategoryName = (category) => {
    const categoryNames = {
      BASIC_INFO: '基础信息',
      GRADE_INFO: '评级信息',
      SPECIAL: '特殊字段'
    };
    return categoryNames[category] || category;
  };

  const getFieldTagType = (fieldType, fieldName) => {
    switch (fieldType) {
      case 'QRCode':
        return 'warning';
      case 'String':
        return '';
      case 'Number':
        return 'success';
      default:
        return 'info';
    }
  };

  const getFieldDisplayName = (fieldName) => {
    for (const fields of Object.values(fieldCategories.value)) {
      const field = fields.find((f) => f.fieldName === fieldName);
      if (field) return field.displayName;
    }
    return fieldName;
  };

  // 区域操作
  const selectZone = (zone) => {
    selectedZone.value = zone;
  };

  const clearSelection = () => {
    selectedZone.value = null;
  };

  const removeZone = (zone) => {
    const index = labelZones.value.findIndex((z) => z.id === zone.id);
    if (index > -1) {
      labelZones.value.splice(index, 1);
      if (selectedZone.value?.id === zone.id) {
        selectedZone.value = null;
      }
      saveState('删除区域');
    }
  };

  const removeFieldFromZone = (zone, fieldName) => {
    const index = zone.fields.indexOf(fieldName);
    if (index > -1) {
      zone.fields.splice(index, 1);
      saveState('移除字段');
    }
  };

  // 拖拽处理
  const handleDrop = (event) => {
    handleCanvasDrop(event, findZoneAtPosition, createNewZone);
  };

  const findZoneAtPosition = (x, y) => {
    return labelZones.value.find(
      (zone) =>
        x >= zone.x &&
        x <= zone.x + zone.width &&
        y >= zone.y &&
        y <= zone.y + zone.height
    );
  };

  const createNewZone = (field, x, y) => {
    const newZone = {
      id: 'zone_' + Date.now(),
      name: field.displayName,
      x: Math.max(0, x - 20),
      y: Math.max(0, y - 10),
      width: 40,
      height: 20,
      fontSize: 12,
      color: '#333333',
      backgroundColor: '#ffffff',
      fields: [field.fieldName],
      // 添加默认间距设置
      padding: 2,
      fieldSpacing: 2,
      lineHeight: 1.2
    };

    labelZones.value.push(newZone);
    selectedZone.value = newZone;
    saveState('创建区域');
  };

  // 属性变更处理
  const handlePropertyChange = ({ zone, type }) => {
    saveState(`修改${type}`);
  };

  // 层级管理
  const handleLayerMove = ({ zone, action }) => {
    const index = labelZones.value.findIndex((z) => z.id === zone.id);
    if (index === -1) return;

    const zones = [...labelZones.value];

    switch (action) {
      case 'top':
        zones.splice(index, 1);
        zones.push(zone);
        break;
      case 'bottom':
        zones.splice(index, 1);
        zones.unshift(zone);
        break;
      case 'up':
        if (index < zones.length - 1) {
          [zones[index], zones[index + 1]] = [zones[index + 1], zones[index]];
        }
        break;
      case 'down':
        if (index > 0) {
          [zones[index], zones[index - 1]] = [zones[index - 1], zones[index]];
        }
        break;
    }

    labelZones.value = zones;
    saveState(`${action}层级`);
  };

  // 右键菜单
  const showContextMenu = (event) => {
    event.preventDefault();
    contextMenuTarget.value = event.target;
    contextMenuRef.value?.handleOpen();
  };

  const showZoneContextMenu = (event, zone) => {
    event.preventDefault();
    selectedZone.value = zone;
    contextMenuTarget.value = event.target;
    contextMenuRef.value?.handleOpen();
  };

  const handleContextMenuCommand = (command) => {
    switch (command) {
      case 'copy':
        copyZone(selectedZone.value);
        break;
      case 'paste':
        pasteZone();
        break;
      case 'delete':
        removeZone(selectedZone.value);
        break;
      case 'selectAll':
        // TODO: 实现全选功能
        break;
    }
  };

  // 复制粘贴功能
  const copyZone = (zone) => {
    if (!zone) return;
    copiedZone.value = JSON.parse(JSON.stringify(zone));
    ElMessage.success('区域已复制');
  };

  const pasteZone = () => {
    if (!copiedZone.value) return;

    const newZone = JSON.parse(JSON.stringify(copiedZone.value));
    newZone.id = 'zone_' + Date.now();
    newZone.name = newZone.name + '_副本';
    newZone.x += 10;
    newZone.y += 10;

    labelZones.value.push(newZone);
    selectedZone.value = newZone;
    saveState('粘贴区域');
    ElMessage.success('区域已粘贴');
  };

  // 缩放功能
  const zoomIn = () => {
    zoomLevel.value = Math.min(zoomLevel.value * 1.2, 3);
  };

  const zoomOut = () => {
    zoomLevel.value = Math.max(zoomLevel.value / 1.2, 0.3);
  };

  const resetZoom = () => {
    zoomLevel.value = 1;
  };

  // 字段加载
  const loadFields = async () => {
    loadingFields.value = true;
    try {
      const fields = await getFieldsByCategory();
      fieldCategories.value = fields;
    } catch (error) {
      ElMessage.error('加载字段失败：' + error.message);
    } finally {
      loadingFields.value = false;
    }
  };

  const refreshFields = () => {
    loadFields();
  };

  // 模板操作
  const handleSaveTemplate = async () => {
    if (!currentTemplate.templateName.trim()) {
      ElMessage.warning('请输入模板名称');
      return;
    }

    saving.value = true;
    try {
      currentTemplate.layoutConfig = JSON.stringify({
        zones: labelZones.value,
        canvas: { width: canvasWidth.value, height: canvasHeight.value }
      });

      const fieldMapping = {};
      labelZones.value.forEach((zone) => {
        if (zone.fields && zone.fields.length > 0) {
          fieldMapping[zone.id] = zone.fields;
        }
      });
      currentTemplate.fieldMapping = JSON.stringify(fieldMapping);

      await saveTemplate(currentTemplate);
      ElMessage.success('模板保存成功');
    } catch (error) {
      ElMessage.error('保存失败：' + error.message);
    } finally {
      saving.value = false;
    }
  };

  const newTemplate = () => {
    currentTemplate.id = null;
    currentTemplate.templateName = '新建标签模板';
    labelZones.value = [];
    selectedZone.value = null;
    saveState('新建模板');
  };

  const loadTemplate = async () => {
    loadingTemplates.value = true;
    try {
      const templates = await getTemplateList();
      templateList.value = templates;
      showTemplateDialog.value = true;
    } catch (error) {
      ElMessage.error('加载模板列表失败：' + error.message);
    } finally {
      loadingTemplates.value = false;
    }
  };

  const loadSelectedTemplate = (template) => {
    try {
      currentTemplate.id = template.id;
      currentTemplate.templateName = template.templateName;
      currentTemplate.templateType = template.templateType;

      if (template.layoutConfig) {
        const layoutConfig = JSON.parse(template.layoutConfig);
        labelZones.value = layoutConfig.zones || [];
        if (layoutConfig.canvas) {
          canvasWidth.value = layoutConfig.canvas.width;
          canvasHeight.value = layoutConfig.canvas.height;
        }
      }

      showTemplateDialog.value = false;
      selectedZone.value = null;
      saveState('加载模板');
      ElMessage.success('模板加载成功');
    } catch (error) {
      ElMessage.error('模板加载失败：' + error.message);
    }
  };

  const deleteTemplateConfirm = (template) => {
    ElMessageBox.confirm(
      `确定要删除模板"${template.templateName}"吗？`,
      '确认删除',
      {
        type: 'warning'
      }
    ).then(async () => {
      try {
        await deleteTemplate(template.id);
        ElMessage.success('删除成功');
        // 重新加载模板列表
        const templates = await getTemplateList();
        templateList.value = templates;
      } catch (error) {
        ElMessage.error('删除失败：' + error.message);
      }
    });
  };

  const resetDesigner = () => {
    ElMessageBox.confirm(
      '确定要重置设计器吗？所有未保存的更改将丢失。',
      '确认重置',
      {
        type: 'warning'
      }
    ).then(() => {
      newTemplate();
    });
  };

  const previewLabel = async () => {
    if (!currentTemplate.id) {
      ElMessage.warning('请先保存模板后再预览');
      return;
    }

    try {
      const preview = await previewLabelApi(currentTemplate.id, [
        'sample-coin-id'
      ]);
      previewContent.value = preview;
      showPreviewDialog.value = true;
    } catch (error) {
      ElMessage.error('预览失败：' + error.message);
    }
  };

  // 处理画布尺寸变更
  const handleCanvasSizeChange = (newSize) => {
    const oldWidth = canvasWidth.value;
    const oldHeight = canvasHeight.value;

    canvasWidth.value = newSize.width;
    canvasHeight.value = newSize.height;

    // 调整超出画布边界的区域
    labelZones.value.forEach((zone) => {
      // 调整位置，确保区域不超出画布
      if (zone.x + zone.width > newSize.width) {
        zone.x = Math.max(0, newSize.width - zone.width);
      }
      if (zone.y + zone.height > newSize.height) {
        zone.y = Math.max(0, newSize.height - zone.height);
      }

      // 调整尺寸，确保区域不超出画布
      if (zone.x + zone.width > newSize.width) {
        zone.width = newSize.width - zone.x;
      }
      if (zone.y + zone.height > newSize.height) {
        zone.height = newSize.height - zone.y;
      }
    });

    // 保存状态到历史记录
    saveState(`画布尺寸变更: ${newSize.width}×${newSize.height}mm`);

    // ElMessage.success(`画布尺寸已更新为 ${newSize.width}×${newSize.height}mm`)
  };

  // 处理模板类型变更
  const handleTemplateTypeChange = (templateType) => {
    console.log('模板类型变更:', templateType);

    // 根据模板类型调整默认区域布局
    if (templateType === 'large') {
      // 大签标签 (192×26mm) - 调整区域布局
      updateZonesForLargeTemplate();
    } else if (templateType === 'small') {
      // 小签标签 (115×21mm) - 调整区域布局
      updateZonesForSmallTemplate();
    }
  };

  // 更新大签模板的区域布局
  const updateZonesForLargeTemplate = () => {
    // 移除公司Logo区域的字段，但保留位置
    const logoZone = labelZones.value.find((zone) => zone.id === 'logo');
    if (logoZone) {
      logoZone.fields = []; // 清空字段，但保留区域
      logoZone.name = '公司Logo位置(预留)';
      logoZone.backgroundColor = '#f0f0f0';
      logoZone.width = 30;
      logoZone.height = 26;
    }

    // 调整其他区域适应大签尺寸
    const coinInfoZone = labelZones.value.find(
      (zone) => zone.id === 'coinInfo'
    );
    if (coinInfoZone) {
      coinInfoZone.x = 30;
      coinInfoZone.width = 100;
      coinInfoZone.height = 26;
      coinInfoZone.fontSize = 10;
      coinInfoZone.fields = ['bankName', 'coinName1', 'serialNumber']; // 默认字段
      coinInfoZone.layout = 'vertical';
    }

    const gradeInfoZone = labelZones.value.find(
      (zone) => zone.id === 'gradeInfo'
    );
    if (gradeInfoZone) {
      gradeInfoZone.x = 130;
      gradeInfoZone.width = 62;
      gradeInfoZone.height = 26;
      gradeInfoZone.fields = ['gradeScore', 'gradeLevel', 'specialMark'];
      gradeInfoZone.fieldStyles = {
        gradeScore: {
          displayMode: 'large',
          fontSize: 24
        },
        gradeLevel: {
          displayMode: 'normal',
          fontSize: 10
        },
        specialMark: {
          displayMode: 'vertical',
          fontSize: 10,
          letterSpacing: 2
        }
      };
    }

    // 调整二维码区域（大签）
    const qrCodeZone = labelZones.value.find(
      (zone) => zone.id === 'qrCodeArea'
    );
    if (qrCodeZone) {
      qrCodeZone.x = 160;
      qrCodeZone.width = 32;
      qrCodeZone.height = 26;
      qrCodeZone.fieldStyles = {
        qrCode: {
          displayMode: 'normal',
          qrSize: 50,
          showText: true,
          textSize: 8
        }
      };
    }
  };

  // 更新小签模板的区域布局
  const updateZonesForSmallTemplate = () => {
    // 移除公司Logo区域的字段，但保留位置
    const logoZone = labelZones.value.find((zone) => zone.id === 'logo');
    if (logoZone) {
      logoZone.fields = []; // 清空字段，但保留区域
      logoZone.name = '公司Logo位置(预留)';
      logoZone.backgroundColor = '#f0f0f0';
      logoZone.width = 20;
      logoZone.height = 21;
    }

    // 调整其他区域适应小签尺寸
    const coinInfoZone = labelZones.value.find(
      (zone) => zone.id === 'coinInfo'
    );
    if (coinInfoZone) {
      coinInfoZone.x = 20;
      coinInfoZone.width = 60;
      coinInfoZone.height = 21;
      coinInfoZone.fontSize = 9;
      coinInfoZone.fields = ['bankName', 'coinName1', 'serialNumber']; // 默认字段
      coinInfoZone.layout = 'vertical';
    }

    const gradeInfoZone = labelZones.value.find(
      (zone) => zone.id === 'gradeInfo'
    );
    if (gradeInfoZone) {
      gradeInfoZone.x = 80;
      gradeInfoZone.width = 35;
      gradeInfoZone.height = 21;
      gradeInfoZone.fields = ['gradeScore', 'gradeLevel', 'specialMark'];
      gradeInfoZone.fieldStyles = {
        gradeScore: {
          displayMode: 'large',
          fontSize: 18
        },
        gradeLevel: {
          displayMode: 'normal',
          fontSize: 8
        },
        specialMark: {
          displayMode: 'vertical',
          fontSize: 8,
          letterSpacing: 1
        }
      };
    }

    // 调整二维码区域（小签）
    const qrCodeZone = labelZones.value.find(
      (zone) => zone.id === 'qrCodeArea'
    );
    if (qrCodeZone) {
      qrCodeZone.x = 85;
      qrCodeZone.width = 30;
      qrCodeZone.height = 21;
      qrCodeZone.fieldStyles = {
        qrCode: {
          displayMode: 'normal',
          qrSize: 40,
          showText: true,
          textSize: 6
        }
      };
    }
  };

  // 键盘快捷键
  const handleKeydown = (event) => {
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 'z':
          event.preventDefault();
          if (event.shiftKey) {
            redo();
          } else {
            undo();
          }
          break;
        case 'c':
          if (selectedZone.value) {
            event.preventDefault();
            copyZone(selectedZone.value);
          }
          break;
        case 'v':
          if (copiedZone.value) {
            event.preventDefault();
            pasteZone();
          }
          break;
        case 'a':
          event.preventDefault();
          // TODO: 实现全选
          break;
        case 's':
          event.preventDefault();
          handleSaveTemplate();
          break;
      }
    } else if (event.key === 'Delete' && selectedZone.value) {
      removeZone(selectedZone.value);
    }
  };

  // 生命周期
  onMounted(() => {
    loadFields();
    initHistory();
    initResize(saveState);
    initDragDrop(saveState);
    initKeyboardShortcuts();
    document.addEventListener('keydown', handleKeydown);
  });

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
    destroyResize();
    destroyDragDrop();
    destroyHistory();
  });
  watch(snapToGrid, (value) => {
    setResizeSnapToGrid(value);
    setDragSnapToGrid(value);
  });
</script>

<style scoped>
  .label-designer {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: #f5f7fa;
  }

  .designer-toolbar {
    height: 60px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .designer-content {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .field-panel {
    width: 280px;
    background: white;
    border-right: 1px solid #e4e7ed;
    display: flex;
    flex-direction: column;
  }

  .panel-header {
    height: 50px;
    padding: 0 16px;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .field-categories {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
  }

  .field-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  .field-item {
    cursor: grab;
    transition: transform 0.2s;
  }

  .field-item:hover {
    transform: translateX(4px);
  }

  .field-item:active {
    cursor: grabbing;
  }

  .design-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
  }

  .design-header {
    height: 50px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
  }

  .canvas-container {
    flex: 1;
    overflow: auto;
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    transform-origin: center top;
  }

  .label-canvas {
    background: white;
    border: 2px solid #409eff;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .grid-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(to right, #f0f0f0 1px, transparent 1px),
      linear-gradient(to bottom, #f0f0f0 1px, transparent 1px);
    background-size: 5mm 5mm;
    pointer-events: none;
    opacity: 0.3;
    transition: opacity 0.2s;
  }

  .show-grid .grid-background {
    opacity: 1;
  }

  .label-zone {
    position: absolute;
    border: 1px dashed #ccc;
    cursor: move;
    transition: all 0.2s;
    min-height: 15px;
  }

  .label-zone:hover {
    border-color: #409eff;
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
  }

  .label-zone.selected {
    border: 2px solid #409eff;
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.5);
  }

  .label-zone.dragging {
    opacity: 0.8;
    transform: rotate(2deg);
  }

  .zone-content {
    padding: 4px;
    height: 100%;
    overflow: hidden;
  }

  .zone-title {
    font-weight: bold;
    font-size: 10px;
    color: #666;
    margin-bottom: 2px;
  }

  .zone-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
  }

  .zone-controls {
    position: absolute;
  }

  .resize-handle {
    position: absolute;
    background: #409eff;
    border: 1px solid white;
    width: 8px;
    height: 8px;
  }

  .resize-nw {
    top: -4px;
    left: -4px;
    cursor: nw-resize;
  }
  .resize-n {
    top: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: n-resize;
  }
  .resize-ne {
    top: -4px;
    right: -4px;
    cursor: ne-resize;
  }
  .resize-w {
    top: 50%;
    left: -4px;
    margin-top: -4px;
    cursor: w-resize;
  }
  .resize-e {
    top: 50%;
    right: -4px;
    margin-top: -4px;
    cursor: e-resize;
  }
  .resize-sw {
    bottom: -4px;
    left: -4px;
    cursor: sw-resize;
  }
  .resize-s {
    bottom: -4px;
    left: 50%;
    margin-left: -4px;
    cursor: s-resize;
  }
  .resize-se {
    bottom: -4px;
    right: -4px;
    cursor: se-resize;
  }

  .delete-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    background: #f56c6c;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    font-size: 10px;
  }

  .alignment-guide {
    position: absolute;
    background: #409eff;
    pointer-events: none;
    z-index: 1000;
  }

  .alignment-guide.horizontal {
    height: 1px;
    width: 100%;
  }

  .alignment-guide.vertical {
    width: 1px;
    height: 100%;
  }

  .toolbar-options {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .dragging-field .label-canvas {
    background-color: rgba(64, 158, 255, 0.05);
  }

  body.dragging-field {
    cursor: grabbing;
  }

  body.resizing {
    user-select: none;
  }

  .preview-content {
    min-height: 200px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 16px;
  }
</style>
