import { ref, reactive } from 'vue';

export function useResize() {
  const isResizing = ref(false);
  const resizeState = reactive({
    isActive: false,
    currentZone: null,
    handle: null,
    startPos: { x: 0, y: 0 },
    startSize: { width: 0, height: 0 },
    startPosition: { x: 0, y: 0 },
    minSize: { width: 10, height: 10 }, // mm
    snapToGrid: true,
    gridSize: 5 // mm
  });

  // 开始调整大小
  const startResize = (event, zone, handle) => {
    event.preventDefault();
    event.stopPropagation();

    isResizing.value = true;
    resizeState.isActive = true;
    resizeState.currentZone = zone;
    resizeState.handle = handle;

    // 记录初始状态
    resizeState.startPos = { x: event.clientX, y: event.clientY };
    resizeState.startSize = { width: zone.width, height: zone.height };
    resizeState.startPosition = { x: zone.x, y: zone.y };

    // 添加全局事件监听
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
    document.body.style.cursor = getCursorForHandle(handle);
    document.body.classList.add('resizing');

    // 禁用文本选择
    document.body.style.userSelect = 'none';
  };

  // 处理调整大小移动
  const handleResizeMove = (event) => {
    if (!resizeState.isActive || !resizeState.currentZone) return;

    const canvas = document.querySelector('.label-canvas');
    if (!canvas) return;

    const canvasRect = canvas.getBoundingClientRect();
    const zoomLevel = parseFloat(
      canvas.style.transform?.match(/scale\(([\d.]+)\)/)?.[1] || 1
    );

    // 计算鼠标移动距离（转换为mm）
    const deltaX =
      ((event.clientX - resizeState.startPos.x) / zoomLevel) * 0.264583; // px to mm
    const deltaY =
      ((event.clientY - resizeState.startPos.y) / zoomLevel) * 0.264583;

    // 获取画布尺寸
    const canvasWidth = parseFloat(canvas.style.width);
    const canvasHeight = parseFloat(canvas.style.height);

    // 根据手柄类型计算新的尺寸和位置
    const newDimensions = calculateNewDimensions(
      resizeState.handle,
      resizeState.startPosition,
      resizeState.startSize,
      deltaX,
      deltaY,
      canvasWidth,
      canvasHeight
    );

    // 应用网格吸附
    if (resizeState.snapToGrid) {
      newDimensions.x =
        Math.round(newDimensions.x / resizeState.gridSize) *
        resizeState.gridSize;
      newDimensions.y =
        Math.round(newDimensions.y / resizeState.gridSize) *
        resizeState.gridSize;
      newDimensions.width =
        Math.round(newDimensions.width / resizeState.gridSize) *
        resizeState.gridSize;
      newDimensions.height =
        Math.round(newDimensions.height / resizeState.gridSize) *
        resizeState.gridSize;
    }

    // 应用约束
    const constrainedDimensions = applyConstraints(
      newDimensions,
      canvasWidth,
      canvasHeight
    );

    // 更新区域
    Object.assign(resizeState.currentZone, constrainedDimensions);
  };

  // 结束调整大小
  const handleResizeEnd = () => {
    if (!resizeState.isActive) return;

    isResizing.value = false;
    resizeState.isActive = false;

    // 清理事件监听和样式
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
    document.body.style.cursor = '';
    document.body.style.userSelect = '';
    document.body.classList.remove('resizing');

    // 触发历史记录保存
    if (resizeState.currentZone && window.labelDesignerSaveState) {
      window.labelDesignerSaveState('调整区域大小');
    }

    // 重置状态
    resizeState.currentZone = null;
    resizeState.handle = null;
  };

  // 计算新的尺寸和位置
  const calculateNewDimensions = (
    handle,
    startPos,
    startSize,
    deltaX,
    deltaY,
    canvasWidth,
    canvasHeight
  ) => {
    let newX = startPos.x;
    let newY = startPos.y;
    let newWidth = startSize.width;
    let newHeight = startSize.height;

    switch (handle) {
      case 'nw': // 西北
        newX = startPos.x + deltaX;
        newY = startPos.y + deltaY;
        newWidth = startSize.width - deltaX;
        newHeight = startSize.height - deltaY;
        break;
      case 'n': // 北
        newY = startPos.y + deltaY;
        newHeight = startSize.height - deltaY;
        break;
      case 'ne': // 东北
        newY = startPos.y + deltaY;
        newWidth = startSize.width + deltaX;
        newHeight = startSize.height - deltaY;
        break;
      case 'w': // 西
        newX = startPos.x + deltaX;
        newWidth = startSize.width - deltaX;
        break;
      case 'e': // 东
        newWidth = startSize.width + deltaX;
        break;
      case 'sw': // 西南
        newX = startPos.x + deltaX;
        newWidth = startSize.width - deltaX;
        newHeight = startSize.height + deltaY;
        break;
      case 's': // 南
        newHeight = startSize.height + deltaY;
        break;
      case 'se': // 东南
        newWidth = startSize.width + deltaX;
        newHeight = startSize.height + deltaY;
        break;
    }

    return { x: newX, y: newY, width: newWidth, height: newHeight };
  };

  // 应用约束条件
  const applyConstraints = (dimensions, canvasWidth, canvasHeight) => {
    let { x, y, width, height } = dimensions;

    // 最小尺寸约束
    width = Math.max(width, resizeState.minSize.width);
    height = Math.max(height, resizeState.minSize.height);

    // 边界约束
    x = Math.max(0, Math.min(x, canvasWidth - width));
    y = Math.max(0, Math.min(y, canvasHeight - height));

    // 如果位置被约束，需要调整尺寸
    if (x === 0) {
      width = Math.min(width, canvasWidth);
    }
    if (y === 0) {
      height = Math.min(height, canvasHeight);
    }
    if (x + width > canvasWidth) {
      width = canvasWidth - x;
    }
    if (y + height > canvasHeight) {
      height = canvasHeight - y;
    }

    return { x, y, width, height };
  };

  // 根据手柄类型获取光标样式
  const getCursorForHandle = (handle) => {
    const cursors = {
      nw: 'nw-resize',
      n: 'n-resize',
      ne: 'ne-resize',
      w: 'w-resize',
      e: 'e-resize',
      sw: 'sw-resize',
      s: 's-resize',
      se: 'se-resize'
    };
    return cursors[handle] || 'default';
  };

  // 设置网格吸附
  const setSnapToGrid = (enabled) => {
    resizeState.snapToGrid = enabled;
  };

  // 设置网格大小
  const setGridSize = (size) => {
    resizeState.gridSize = size;
  };

  // 设置最小尺寸
  const setMinSize = (width, height) => {
    resizeState.minSize = { width, height };
  };

  // 检查是否正在调整大小
  const isResizingZone = (zone) => {
    return resizeState.isActive && resizeState.currentZone?.id === zone?.id;
  };

  // 获取当前调整大小的手柄
  const getCurrentHandle = () => {
    return resizeState.handle;
  };

  // 取消调整大小
  const cancelResize = () => {
    if (resizeState.isActive && resizeState.currentZone) {
      // 恢复到初始状态
      Object.assign(resizeState.currentZone, {
        x: resizeState.startPosition.x,
        y: resizeState.startPosition.y,
        width: resizeState.startSize.width,
        height: resizeState.startSize.height
      });

      handleResizeEnd();
    }
  };

  // 键盘事件处理
  const handleKeydown = (event) => {
    if (resizeState.isActive) {
      if (event.key === 'Escape') {
        event.preventDefault();
        cancelResize();
      }
    }
  };

  // 初始化
  const initResize = (saveStateCallback) => {
    // 保存历史记录回调
    if (saveStateCallback) {
      window.labelDesignerSaveState = saveStateCallback;
    }

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeydown);
  };

  // 清理
  const destroyResize = () => {
    if (resizeState.isActive) {
      handleResizeEnd();
    }
    document.removeEventListener('keydown', handleKeydown);
    delete window.labelDesignerSaveState;
  };

  return {
    isResizing,
    resizeState,
    startResize,
    handleResizeMove,
    handleResizeEnd,
    setSnapToGrid,
    setGridSize,
    setMinSize,
    isResizingZone,
    getCurrentHandle,
    cancelResize,
    initResize,
    destroyResize
  };
}
