import { ref, watch } from 'vue';

export function useHistory(labelZones) {
  const canUndo = ref(false);
  const canRedo = ref(false);
  const history = ref([]);
  const currentIndex = ref(-1);
  const maxHistorySize = ref(50);

  // 保存当前状态到历史记录
  const saveState = (action = '操作') => {
    try {
      // 深拷贝当前状态
      const currentState = {
        zones: JSON.parse(JSON.stringify(labelZones.value)),
        timestamp: Date.now(),
        action: action
      };

      // 如果当前不在历史记录的末尾，删除后面的记录
      if (currentIndex.value < history.value.length - 1) {
        history.value = history.value.slice(0, currentIndex.value + 1);
      }

      // 添加新状态
      history.value.push(currentState);
      currentIndex.value = history.value.length - 1;

      // 限制历史记录大小
      if (history.value.length > maxHistorySize.value) {
        history.value.shift();
        currentIndex.value--;
      }

      // 更新按钮状态
      updateButtonStates();

      console.log(`历史记录已保存: ${action}`, {
        currentIndex: currentIndex.value,
        historyLength: history.value.length
      });
    } catch (error) {
      console.error('保存历史记录失败:', error);
    }
  };

  // 撤销操作
  const undo = () => {
    if (!canUndo.value || currentIndex.value <= 0) return;

    try {
      currentIndex.value--;
      const targetState = history.value[currentIndex.value];

      // 恢复状态
      labelZones.value.splice(
        0,
        labelZones.value.length,
        ...JSON.parse(JSON.stringify(targetState.zones))
      );

      updateButtonStates();

      console.log(`撤销操作: ${targetState.action}`, {
        currentIndex: currentIndex.value,
        historyLength: history.value.length
      });
    } catch (error) {
      console.error('撤销操作失败:', error);
    }
  };

  // 重做操作
  const redo = () => {
    if (!canRedo.value || currentIndex.value >= history.value.length - 1)
      return;

    try {
      currentIndex.value++;
      const targetState = history.value[currentIndex.value];

      // 恢复状态
      labelZones.value.splice(
        0,
        labelZones.value.length,
        ...JSON.parse(JSON.stringify(targetState.zones))
      );

      updateButtonStates();

      console.log(`重做操作: ${targetState.action}`, {
        currentIndex: currentIndex.value,
        historyLength: history.value.length
      });
    } catch (error) {
      console.error('重做操作失败:', error);
    }
  };

  // 更新按钮状态
  const updateButtonStates = () => {
    canUndo.value = currentIndex.value > 0;
    canRedo.value = currentIndex.value < history.value.length - 1;
  };

  // 初始化历史记录
  const initHistory = () => {
    try {
      // 清空历史记录
      history.value = [];
      currentIndex.value = -1;

      // 保存初始状态
      saveState('初始化');

      console.log('历史记录已初始化');
    } catch (error) {
      console.error('初始化历史记录失败:', error);
    }
  };

  // 清空历史记录
  const clearHistory = () => {
    history.value = [];
    currentIndex.value = -1;
    updateButtonStates();
    console.log('历史记录已清空');
  };

  // 获取历史记录信息
  const getHistoryInfo = () => {
    return {
      current: currentIndex.value,
      total: history.value.length,
      canUndo: canUndo.value,
      canRedo: canRedo.value,
      actions: history.value.map((state, index) => ({
        index,
        action: state.action,
        timestamp: state.timestamp,
        isCurrent: index === currentIndex.value
      }))
    };
  };

  // 跳转到指定历史记录
  const jumpToHistory = (index) => {
    if (index < 0 || index >= history.value.length) return;

    try {
      currentIndex.value = index;
      const targetState = history.value[index];

      // 恢复状态
      labelZones.value.splice(
        0,
        labelZones.value.length,
        ...JSON.parse(JSON.stringify(targetState.zones))
      );

      updateButtonStates();

      console.log(`跳转到历史记录: ${targetState.action}`, {
        currentIndex: currentIndex.value,
        historyLength: history.value.length
      });
    } catch (error) {
      console.error('跳转历史记录失败:', error);
    }
  };

  // 设置最大历史记录数量
  const setMaxHistorySize = (size) => {
    maxHistorySize.value = Math.max(1, size);

    // 如果当前历史记录超过限制，删除最旧的记录
    while (history.value.length > maxHistorySize.value) {
      history.value.shift();
      if (currentIndex.value > 0) {
        currentIndex.value--;
      }
    }

    updateButtonStates();
  };

  // 键盘快捷键处理
  const handleKeydown = (event) => {
    // Ctrl+Z 撤销
    if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
      event.preventDefault();
      undo();
    }
    // Ctrl+Shift+Z 或 Ctrl+Y 重做
    else if (
      (event.ctrlKey && event.shiftKey && event.key === 'Z') ||
      (event.ctrlKey && event.key === 'y')
    ) {
      event.preventDefault();
      redo();
    }
  };

  // 监听 labelZones 变化（可选，用于自动保存）
  let autoSaveTimer = null;
  const enableAutoSave = (delay = 1000) => {
    watch(
      labelZones,
      () => {
        if (autoSaveTimer) {
          clearTimeout(autoSaveTimer);
        }
        autoSaveTimer = setTimeout(() => {
          saveState('自动保存');
        }, delay);
      },
      { deep: true }
    );
  };

  // 禁用自动保存
  const disableAutoSave = () => {
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer);
      autoSaveTimer = null;
    }
  };

  // 初始化键盘事件监听
  const initKeyboardShortcuts = () => {
    document.addEventListener('keydown', handleKeydown);
  };

  // 清理
  const destroyHistory = () => {
    disableAutoSave();
    document.removeEventListener('keydown', handleKeydown);
    clearHistory();
  };

  return {
    canUndo,
    canRedo,
    history,
    currentIndex,
    saveState,
    undo,
    redo,
    initHistory,
    clearHistory,
    getHistoryInfo,
    jumpToHistory,
    setMaxHistorySize,
    enableAutoSave,
    disableAutoSave,
    initKeyboardShortcuts,
    destroyHistory
  };
}
