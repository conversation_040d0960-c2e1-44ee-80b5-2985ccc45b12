<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <role-search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemRoleTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <!--                    <ele-tooltip content="编辑" placement="top">-->
          <!--                        <Edit style="width: 1.2em; height: 1.2em; margin-right: 12px;cursor: pointer;"-->
          <!--                              @click="openEdit(row)"/>-->
          <!--                    </ele-tooltip>-->
          <!--                    <ele-tooltip content="删除" placement="top">-->
          <!--                        <Delete style="width: 1.2em; height: 1.2em; margin-right: 12px;cursor: pointer;"-->
          <!--                                @click="remove(row)"/>-->
          <!--                    </ele-tooltip>-->
          <!--                    &lt;!&ndash;                    <el-icon><HelpFilled /></el-icon>&ndash;&gt;-->
          <!--                    <ele-tooltip content="分配权限" placement="top">-->
          <!--                        <ClusterOutlined-->
          <!--                                style="width: 1.2em; height: 1.2em; margin-right: 12px;cursor: pointer;stroke-width: 3;"-->
          <!--                                @click="openAuth(row)"/>-->
          <!--                    </ele-tooltip>-->

          <!--                    <el-link style="width: 1.2em; height: 1.2em;margin-right: 12px;" href="https://element-plus.org" target="_blank">默认</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="primary">主题</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="success">成功</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="warning">警告</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="danger">预警</el-link>-->
          <!--                    <el-link style="margin-right: 12px;" type="info">信息</el-link>-->
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="openAuth(row)">
            分配权限
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <role-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 权限分配弹窗 -->
    <role-auth v-model="showAuth" :data="current" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import RoleSearch from './components/role-search.vue';
  import RoleEdit from './components/role-edit.vue';
  import RoleAuth from './components/role-auth.vue';
  import { pageRoles, removeRoles } from './api/index';
  import { useRouter } from 'vue-router';

  const { currentRoute, push } = useRouter();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '角色名称',
      sortable: 'custom',
      minWidth: 110
    },
    {
      prop: 'roleScope',
      label: '角色标识',
      sortable: 'custom',
      minWidth: 110
    },
    {
      prop: 'remark',
      label: '备注',
      sortable: 'custom',
      minWidth: 110
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示权限分配弹窗 */
  const showAuth = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders }) => {
    return pageRoles({ ...where, ...orders, page, limit });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开权限分配弹窗 */
  const openAuth = (row) => {
    push({
      path: '/system/role/auth',
      query: { id: row.id }
    });
    //
    // current.value = row ?? null;
    // showAuth.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.name).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removeRoles(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };
</script>

<script>
  export default {
    name: 'SystemRole'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
