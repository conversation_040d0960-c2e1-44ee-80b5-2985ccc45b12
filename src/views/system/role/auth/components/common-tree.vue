<template>
  <ele-page flex-table>
    <div style="padding: 10px 16px 12px 0">
      <el-input
        clearable
        size="small"
        :maxlength="20"
        v-model="filterText"
        :placeholder="
          '输入' + (routeType === 'btn' ? '按钮接口权限' : '菜单') + '名称搜索'
        "
        :prefix-icon="SearchOutlined"
        @input="filterTree"
      />
    </div>
    <ele-loading
      :loading="authLoading"
      :spinner-style="{ background: 'transparent' }"
      :style="{
        paddingRight: '20px',
        maxHeight: 'calc(100vh - 162px)',
        minHeight: '100px',
        overflow: 'auto'
      }"
    >
      <el-tree
        ref="treeRef"
        :props="levelProps"
        highlight-current
        node-key="menuId"
        :render-content="renderContent"
        :lazy="true"
        :load="routeType === 'btn' ? getTreeDataBtn : getTreeDataMenu"
        show-checkbox
        @node-expand="keepExpandedNode"
        @check="handleCheck"
        :filter-node-method="filterNode"
        :default-checked-keys="defaultCheckedNodes"
        :default-expanded-keys="defaultExpandedNodes"
        :style="{ '--ele-tree-item-height': '28px' }"
      />
    </ele-loading>
  </ele-page>
</template>

<script setup>
  import { nextTick, ref, unref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  // import {
  //   PlusOutlined,
  //   ColumnHeightOutlined,
  //   VerticalAlignMiddleOutlined
  // } from '@/components/icons';
  // import MenuSearch from './components/menu-search.vue';
  // import MenuEdit from './components/menu-edit.vue';
  // import {listMenus, removeMenu, pageMenus} from '@/api/system/menu';
  // import {queryPage} from "@/views/base-code/dwb/api";
  // import {read} from "xlsx";
  // import Search from "@/views/base-code/dwb/components/search.vue";
  // import {useUserStore} from "@/store/modules/user.js";
  // import {useRouter} from "vue-router";
  import {
    listRoleMenus,
    roleMenusOperation,
    updateRoleMenus
  } from '@/views/system/role/api/index.js';
  import { eachTree } from 'ele-admin-plus';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab.js';

  const props = defineProps({
    /** 数据加载类型 ，menu菜单，按钮接口权限btn */
    routeType: String
  });

  const { currentRoute, push } = useRouter();
  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const { query } = unref(currentRoute);
  const currentId = query.id;
  /** 树组件实例 */
  const treeRef = ref(null);
  /** 输入关键字进行过滤 */
  const filterText = ref(null);

  /** 权限数据请求状态 */
  const authLoading = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 角色权限选中的keys */
  const levelProps = ref({
    label: 'title',
    isLeaf: 'isLeaf'
  });
  const defaultCheckedNodes = ref([]); // 最终要回显的勾选key数组
  const defaultExpandedNodes = ref([]); // 最终要回显的展开过的key数组
  const expandedNodes = ref([]); // 已经展开过的节点（拿到过下级的节点）
  const selectedValue = ref([]); // 已选节点
  const meaningExpandedNum = ref(0); // 标的
  const checkStrictly = ref(false);

  const expandedNodesArray = ref([]); // 已经展开过的子节点

  const canceledList = ref([]); // 取消原本已选择的节点数据
  const choliceList = ref([]); // 新选择的节点数据

  //深度复制对象
  const cloneObj = (obj) => {
    let newObj = {};
    if (typeof obj === 'object') {
      if (obj instanceof Array) {
        newObj = [];
      }
      for (var key in obj) {
        let val = obj[key];
        newObj[key] = typeof val === 'object' ? cloneObj(val) : val;
      }
      return newObj;
    } else {
      return obj;
    }
  };

  //带查重的深复制（仅适用于单层数组）
  const cloneWithCheck = (Arr) => {
    let newArr = [];
    if (Arr.length > 0) {
      newArr.push(cloneObj(Arr[0]));
      for (let i = 1; i < Arr.length; i++) {
        let newFlag = newArr.every((item) => {
          return item.menuId !== Arr[i].menuId;
        });
        if (newFlag) {
          newArr.push(cloneObj(Arr[i]));
        }
      }
    }
    return newArr;
  };
  //保存展开过的节点
  const keepExpandedNode = (nodeData, node, tree) => {
    let newFlag = expandedNodes.value.every((item) => {
      return item.menuId !== nodeData.menuId;
    });
    if (newFlag && !nodeData.isLeaf) {
      expandedNodes.value.push(cloneObj(nodeData));
    }
  };

  const traverseTree = (node) => {
    if (node === null) return; // 当前节点为空，则直接返回
    const queue = [node]; // 使用队列存储待遍历的节点
    while (queue.length > 0) {
      const current = queue.shift(); // 出队一个节点
      for (let i = 0; i < current.children.length; i++) {
        queue.push(current.children[i]); // 将子节点入队
      }
    }
  };

  //点击多选框->整理已选选项并保存
  const handleCheck = async (node, tree) => {
    console.log(node);
    console.log(treeRef.value.getCheckedNodes());

    const isCheck = treeRef.value.getCheckedNodes().indexOf(node) > -1;
    let obj = {
      add: isCheck,
      roleId: currentId,
      menuId: node.menuId
    };
    rmSave(obj);
    let list = cloneObj(tree.checkedNodes); //tree.checkedNodes-未展开过的节点的子节点无法获取到
    list = list.filter((mItem) => {
      return expandedNodes.value.every((item) => {
        return item.menuId !== mItem.menuId;
      });
    });
    selectedValue.value = list;
  };

  /** 角色赋权 */
  const rmSave = (obj) => {
    loading.value = true;
    roleMenusOperation(obj)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  let menuType = ref('00000000000000000000000000000000');

  const getTreeDataMenu = async (node, resolve) => {
    menuType.value = '00000000000000000000000000000000';
    getNodes(node, resolve);
  };
  const getTreeDataBtn = async (node, resolve) => {
    menuType.value = '11111111111111111111111111111111';
    getNodes(node, resolve);
  };

  //获取树节点
  const getNodes = async (node, resolve) => {
    if (0 === node.level) {
      ///如果需要多次回填，该初始化必需
      meaningExpandedNum.value = 0;
      defaultCheckedNodes.value = [];
      ///回填展开节点（这里我略去了从后端拿数据回填到this.expandedNodes里的代码。
      // 实际上这里如果能直接拿数据放到defaultExpandedNodes里也行）
      let expandedNodesSet = cloneWithCheck(expandedNodes.value);
      defaultExpandedNodes.value = expandedNodesSet.map((item) => {
        return item.menuId;
      });
      let list = await getTreeData(node.level);
      const cks = [];
      eachTree(list, (d) => {
        if (d.menuId && d.checked) {
          cks.push(d);
        }
        d.isLeaf = false;
      });
      selectedValue.value = cks;
      resolve(list);
      ///注意回填要在树渲染后才生效
      nextTick(() => {
        changeCss();
        ///没展开过节点，则直接在根节点层级回填
        ///（这里我略去了在页面开始渲染前就从后端拿数据回填到this.value里的代码。实际上这里如果能直接拿数据放到defaultCheckedNodes里也行）
        if (
          0 === defaultExpandedNodes.value.length &&
          selectedValue.value.length > 0
        ) {
          defaultCheckedNodes.value = selectedValue.value.map((item) => {
            return item.menuId;
          });
        }
      });
    } else {
      defaultCheckedNodes.value = [];
      let levelData = await getTreeData(node.level + 1, node.data);
      eachTree(levelData, (d) => {
        //指定节点是否为叶子节点，仅在指定了 lazy 属性的情况下生效
        d.isLeaf = d.menuType === 2 ? true : false;
      });
      resolve(levelData);
      expandedNodesArray.value = levelData.length > 0 ? levelData : [];
      nextTick(() => {
        changeCss();
        ///已选节点变成展开节点时、选值自动替换为下层节点;这是为了下次回填做的准备，回填过程中用不到
        if (node.checked) {
          let list = treeRef.value.getCheckedNodes();
          if (levelData.length > 0) {
            list = list.filter((mItem) => {
              return expandedNodes.value.every((item) => {
                return item.menuId !== mItem.menuId;
              });
            });
            const cks = [];
            eachTree(list, (d) => {
              if (d.menuId && d.checked) {
                cks.push(d);
              }
              d.isLeaf = false;
            });
            selectedValue.value = cks;
            defaultCheckedNodes.value = selectedValue.value.map((item) => {
              return item.menuId;
            });
          }
        }
        ///回填时保证在全部渲染后再回填（选中节点的回填时机是核心难点）
        meaningExpandedNum.value++;
        if (meaningExpandedNum.value === defaultExpandedNodes.value.length) {
          ///（这里我略去了在页面开始渲染前就从后端拿数据回填到this.value里的代码。实际上这里如果能直接拿数据放到defaultCheckedNodes里也行）
          defaultCheckedNodes.value = selectedValue.value.map((item) => {
            return item.menuId;
          });
        }
      });
    }
  };

  /** 保存权限分配 */
  const save = () => {
    const ids =
      (treeRef.value?.getCheckedKeys?.() ?? []).concat(
        treeRef.value?.getHalfCheckedKeys?.() ?? []
      ) ?? [];
    loading.value = true;
    updateRoleMenus(currentId, ids)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        onBack();
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  /** 格式化模版*/
  const renderContent = (h, { node, data, store }) => {
    let classname = '';
    // 由于项目中有三级菜单也有四级级菜单，就要在此做出判断
    if (node.level === 4) {
      classname = 'foo';
    }
    if (node.level === 5) {
      classname = 'foo';
    }
    if (node.level === 3 && node.childNodes.length === 0) {
      classname = 'foo';
    }
    return h(
      'p',
      {
        class: classname
      },
      node.label
    );
  };

  const changeCss = () => {
    var levelName = document.getElementsByClassName('foo'); // levelname是上面的最底层节点的名字
    for (var i = 0; i < levelName.length; i++) {
      // cssFloat 兼容 ie6-8  styleFloat 兼容ie9及标准浏览器
      levelName[i].parentNode.style.cssFloat = 'left'; // 最底层的节点，包括多选框和名字都让他左浮动
      levelName[i].parentNode.style.styleFloat = 'left';
      levelName[i].parentNode.onmouseover = function () {
        this.style.backgroundColor = '#fff';
      };
    }
  };

  /** 获取tree数据 */
  const getTreeData = async (i, p) => {
    const res = await listRoleMenus({
      level: i,
      roleId: currentId,
      parentId: p?.menuId || menuType.value
    });
    console.log(res);
    return res;
  };

  const filterTree = () => {
    treeRef.value.filter(filterText);
  };
  const filterNode = (value, data) => {
    if (!value) return true;
    return data.title.indexOf(value) !== -1;
  };

  watch(
    filterText,
    (text) => {
      treeRef.value.filter(text);
    }
    // {immediate: true}
  );

  watch(
    selectedValue,
    (selectedVal) => {
      if (selectedVal) checkStrictly.value = false;
    },
    { immediate: true }
  );

  watch(
    defaultCheckedNodes,
    (CheckedNodes) => {
      const arr = [];
      nextTick(() => {
        CheckedNodes.forEach((id) => {
          if (
            !treeRef.value.getNode(id)?.data.childPermission ||
            !treeRef.value.getNode(id)?.data.childPermission.length
          ) {
            arr.push(id);
          }
          treeRef.value.setCheckedKeys(arr);
        });
      });
    },
    { immediate: true }
  );
</script>

<script>
  import * as MenuIcons from '@/layout/menu-icons';

  export default {
    name: 'SystemMenu',
    components: MenuIcons
  };
</script>
