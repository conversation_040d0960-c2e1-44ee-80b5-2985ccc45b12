<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card flex-table>
      <ele-pro-table
        ref="tableRef"
        row-key="tableName"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="sytGenerator"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="Plus"
            @click="showEdit()"
            :disabled="!selections.length"
          >
            生成代码
          </el-button>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 生成配置弹窗 -->
    <edit
      v-model="showEditDialog"
      :data-source="dataSource"
      :tables="selections"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { Plus } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import Edit from './components/edit.vue';
  import Search from './components/search.vue';
  import { page } from './api';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前数据源 */
  const dataSource = ref(null);

  /** 是否显示编辑弹窗 */
  const showEditDialog = ref(false);

  /** 查询条件 */
  const where = reactive({
    tableName: ''
  });

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'tableName',
      label: '表名',
      minWidth: 200
    },
    {
      prop: 'tableComment',
      label: '表注释',
      minWidth: 200
    }
  ]);

  /** 表格数据源 */
  const datasource = ({ page: current, limit }) => {
    if (!dataSource.value) {
      return Promise.resolve({ rows: [], total: 0 });
    }
    return page({
      ...dataSource.value,
      tableName: where.tableName,
      page: current,
      limit
    });
  };

  /** 刷新表格 */
  const reload = (params) => {
    if (params) {
      dataSource.value = params.dataSource;
      where.tableName = params.tableName;
    } else {
      dataSource.value = null;
      where.tableName = '';
    }
    selections.value = [];
    tableRef.value?.reload({ page: 1 });
  };

  /** 显示编辑弹窗 */
  const showEdit = () => {
    if (!selections.value.length) {
      EleMessage.error('请至少选择一张表');
      return;
    }
    showEditDialog.value = true;
  };
</script>

<script>
  export default {
    name: 'SystemGenerator'
  };
</script>

<style lang="scss" scoped>
  .search-form {
    margin-bottom: 1rem;
  }
</style>
