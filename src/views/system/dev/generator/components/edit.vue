<template>
  <el-dialog
    title="生成配置"
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    width="500px"
    @closed="resetForm"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <!-- 数据源配置 -->
      <el-form-item label="数据源" prop="dataSource">
        <el-select
          v-model="form.dataSource"
          placeholder="请选择数据源"
          clearable
        >
          <el-option
            v-for="item in dataSources"
            :key="item.id"
            :label="item.name"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <!-- 其他配置保持不变 -->
      <el-form-item label="作者" prop="author">
        <el-input v-model="form.author" placeholder="请输入作者" clearable />
      </el-form-item>
      <el-form-item label="模块名" prop="moduleName">
        <el-input
          v-model="form.moduleName"
          placeholder="请输入模块名"
          clearable
        />
      </el-form-item>
      <el-form-item label="包名" prop="packageName">
        <el-input
          v-model="form.packageName"
          placeholder="请输入包名"
          clearable
        />
      </el-form-item>

      <!-- 添加生成方式选择 -->
      <el-form-item label="生成方式">
        <el-radio-group v-model="form.generateType">
          <el-radio :label="1">生成到指定目录</el-radio>
          <el-radio :label="2">打包下载</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 根据生成方式显示路径输入 -->
      <template v-if="form.generateType === 1">
        <el-form-item label="前端路径" prop="frontPath">
          <el-input
            v-model="form.frontPath"
            placeholder="请输入前端代码生成路径"
            clearable
          />
        </el-form-item>
        <el-form-item label="后端路径" prop="backPath">
          <el-input
            v-model="form.backPath"
            placeholder="请输入后端代码生成路径"
            clearable
          />
        </el-form-item>
      </template>

      <el-form-item label="生成选项">
        <el-checkbox v-model="form.generateBack">生成后端代码</el-checkbox>
        <el-checkbox v-model="form.generateFront">生成前端代码</el-checkbox>
        <el-checkbox v-model="form.generateSql">生成SQL文件</el-checkbox>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue';
  import { ElMessage } from 'element-plus';
  import { generate } from '../api';
  import { list } from '@/views/system/dev/datasource/api';

  const emit = defineEmits(['update:modelValue', 'done']);

  const props = defineProps({
    modelValue: Boolean,
    dataSource: Object, // 当前选中的数据源
    tables: Array // 选中的表
  });

  /** 表单实例 */
  const formRef = ref();

  /** 提交状态 */
  const loading = ref(false);

  /** 数据源列表 */
  const dataSources = ref([]);

  /** 表单数据 */
  const form = reactive({
    author: '',
    moduleName: '',
    packageName: '',
    generateType: 1, // 1:生成到指定目录 2:打包下载
    frontPath: '',
    backPath: '',
    generateBack: true,
    generateFront: true,
    generateSql: true,
    dataSource: null,
    tables: []
  });

  /** 表单验证规则 */
  const rules = reactive({
    dataSource: [
      { required: true, message: '请选择数据源', trigger: 'change' }
    ],
    author: [{ required: true, message: '请输入作者', trigger: 'blur' }],
    moduleName: [{ required: true, message: '请输入模块名', trigger: 'blur' }],
    packageName: [{ required: true, message: '请输入包名', trigger: 'blur' }],
    frontPath: [],
    backPath: []
  });

  /** 监听生成方式变化,更新验证规则 */
  watch(
    () => form.generateType,
    (val) => {
      if (val === 1) {
        // 生成到指定目录时需要验证路径
        rules.frontPath = [
          { required: true, message: '请输入前端代码生成路径', trigger: 'blur' }
        ];
        rules.backPath = [
          { required: true, message: '请输入后端代码生成路径', trigger: 'blur' }
        ];
      } else {
        // 打包下载时不需要验证路径
        rules.frontPath = [];
        rules.backPath = [];
        // 清空路径
        form.frontPath = '';
        form.backPath = '';
      }
    }
  );

  /** 获取数据源列表 */
  const getDataSources = async () => {
    try {
      dataSources.value = await list();
    } catch (e) {
      ElMessage.error(e.message);
    }
  };

  /** 监听数据源变化 */
  watch(
    () => props.dataSource,
    (val) => {
      if (val) {
        form.dataSource = val;
      }
    },
    { immediate: true } // 立即执行一次
  );

  /** 监听选中表变化 */
  watch(
    () => props.tables,
    (val) => {
      if (val) {
        form.tables = val.map((v) => v.tableName);
      }
    },
    { immediate: true } // 立即执行一次
  );

  /** 关闭弹窗 */
  const closeDialog = () => {
    emit('update:modelValue', false);
  };

  /** 重置表单 */
  const resetForm = () => {
    formRef.value?.resetFields();
    // 重置所有表单字段为初始值
    Object.assign(form, {
      author: '',
      moduleName: '',
      packageName: '',
      generateType: 1,
      frontPath: '',
      backPath: '',
      generateBack: true,
      generateFront: true,
      generateSql: true,
      dataSource: null,
      tables: []
    });
  };

  /** 提交表单 */
  const submit = async () => {
    try {
      await formRef.value?.validate();
    } catch (e) {
      return;
    }
    loading.value = true;
    try {
      const res = await generate({
        ...form,
        // 打包下载时不传路径
        frontPath: form.generateType === 2 ? '' : form.frontPath,
        backPath: form.generateType === 2 ? '' : form.backPath
      });

      if (form.generateType === 2 && res instanceof Blob) {
        // 下载文件
        const url = window.URL.createObjectURL(new Blob([res]));
        const link = document.createElement('a');
        link.href = url;
        link.download = 'code.zip';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }

      ElMessage.success('生成成功');
      emit('done');
      closeDialog();
    } catch (e) {
      ElMessage.error(e.message);
    } finally {
      loading.value = false;
    }
  };

  // 初始化时获取数据源列表
  getDataSources();
</script>
