<template>
  <el-form :inline="true" :model="form" class="search-form">
    <el-form-item label="数据源">
      <el-select
        v-model="form.dataSource"
        placeholder="请选择数据源"
        clearable
        @change="handleDataSourceChange"
        style="width: 200px"
        value-key="id"
      >
        <el-option
          v-for="item in dataSources"
          :key="item.id"
          :label="item.name"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="表名">
      <el-input
        v-model="form.tableName"
        placeholder="请输入表名"
        clearable
        @keyup.enter="search"
        style="width: 200px"
      />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="search">查询</el-button>
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { list } from '../../datasource/api';

  const emit = defineEmits(['search']);

  const dataSources = ref([]);

  const form = reactive({
    dataSource: null,
    tableName: ''
  });

  /** 获取数据源列表 */
  const getDataSources = async () => {
    try {
      dataSources.value = await list();
    } catch (e) {
      console.error(e);
    }
  };

  /** 数据源变更 */
  const handleDataSourceChange = () => {
    search();
  };

  /** 搜索 */
  const search = () => {
    emit('search', {
      dataSource: form.dataSource,
      tableName: form.tableName
    });
  };

  /** 重置 */
  const reset = () => {
    form.dataSource = null;
    form.tableName = '';
    emit('search', null);
  };

  onMounted(() => {
    getDataSources();
  });
</script>

<style lang="scss" scoped>
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;

    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-right: 16px;

      .el-input,
      .el-select {
        width: 200px;
      }
    }
  }
</style>
