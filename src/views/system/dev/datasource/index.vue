<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card flex-table>
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="sytDatasource"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="Plus"
            @click="showEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="Delete"
            @click="remove()"
            :disabled="!selections.length"
          >
            删除
          </el-button>
        </template>
        <template #dbType="{ row }">
          <el-tag>{{ row.dbType.toUpperCase() }}</el-tag>
        </template>
        <template #operation="{ row }">
          <el-link type="primary" :underline="false" @click="showEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            type="primary"
            :underline="false"
            @click="testConnection(row)"
          >
            测试连接
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="danger" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 编辑弹窗 -->
    <edit v-model="showEditDialog" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { Delete, Plus } from '@element-plus/icons-vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import Edit from './components/edit.vue';
  import Search from './components/search.vue';
  import {
    page,
    remove as removeApi,
    testConnection as testConnectionApi
  } from './api';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEditDialog = ref(false);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'name',
      label: '数据源名称',
      minWidth: 120
    },
    {
      prop: 'dbType',
      label: '数据库类型',
      minWidth: 120,
      slot: 'dbType'
    },
    {
      prop: 'host',
      label: '主机地址',
      minWidth: 120
    },
    {
      prop: 'port',
      label: '端口号',
      minWidth: 80
    },
    {
      prop: 'dbName',
      label: '数据库名',
      minWidth: 120
    },
    {
      prop: 'username',
      label: '用户名',
      minWidth: 120
    },
    {
      prop: 'operation',
      label: '操作',
      width: 200,
      align: 'center',
      fixed: 'right',
      slot: 'operation'
    }
  ]);

  /** 表格数据源 */
  const datasource = ({ page: current, limit, where }) => {
    return page({ ...where, page: current, limit });
  };

  /** 刷新表格 */
  const reload = () => {
    tableRef.value?.reload({ page: 1 });
  };

  /** 显示编辑弹窗 */
  const showEdit = (row) => {
    current.value = row;
    showEditDialog.value = true;
  };

  /** 删除 */
  const remove = async (row) => {
    const rows = row ? [row] : selections.value;
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    try {
      await ElMessageBox.confirm('确定要删除选中的数据吗?', '提示', {
        type: 'warning'
      });
      const ids = rows.map((d) => d.id);
      await removeApi(ids);
      EleMessage.success('删除成功');
      reload();
    } catch (e) {
      if (e?.message !== 'cancel') {
        EleMessage.error(e.message);
      }
    }
  };

  /** 测试连接 */
  const testConnection = async (row) => {
    try {
      const loading = EleMessage.loading('请求中..');
      await testConnectionApi(row);
      loading.close();
      EleMessage.success('连接成功');
    } catch (e) {
      loading.close();
      EleMessage.error(e.message);
    }
  };
</script>

<script>
  export default {
    name: 'SystemDatasource'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
