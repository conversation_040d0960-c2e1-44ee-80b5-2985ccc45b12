<template>
  <el-dialog
    :title="data ? '修改数据源' : '新增数据源'"
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    width="500px"
    @closed="resetForm"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="数据源名称" prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入数据源名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="数据库类型" prop="dbType">
        <el-select v-model="form.dbType" style="width: 100%" clearable>
          <el-option label="Oracle" value="oracle" />
          <el-option label="MySQL" value="mysql" />
        </el-select>
      </el-form-item>
      <el-form-item label="主机地址" prop="host">
        <el-input v-model="form.host" placeholder="请输入主机地址" clearable />
      </el-form-item>
      <el-form-item label="端口" prop="port">
        <el-input-number
          v-model="form.port"
          :min="1"
          :max="65535"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item
        :label="form.dbType === 'oracle' ? '服务名' : '数据库名'"
        prop="dbName"
      >
        <el-input
          v-model="form.dbName"
          :placeholder="
            form.dbType === 'oracle' ? '请输入服务名' : '请输入数据库名'
          "
          clearable
        />
      </el-form-item>
      <el-form-item label="用户名" prop="username">
        <el-input
          v-model="form.username"
          placeholder="请输入用户名"
          clearable
        />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          v-model="form.password"
          type="password"
          placeholder="请输入密码"
          show-password
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue';
  import { ElMessage } from 'element-plus';
  import { operation } from '../api';

  const emit = defineEmits(['update:modelValue', 'done']);
  const props = defineProps({
    // 是否显示
    modelValue: Boolean,
    // 编辑数据
    data: Object
  });

  // 表单实例
  const formRef = ref(null);

  // 是否加载中
  const loading = ref(false);

  // 表单数据
  const form = reactive({
    id: '',
    name: '',
    dbType: 'oracle',
    host: '',
    port: 1521,
    dbName: '',
    username: '',
    password: ''
  });

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
    dbType: [
      { required: true, message: '请选择数据库类型', trigger: 'change' }
    ],
    host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
    port: [{ required: true, message: '请输入端口', trigger: 'blur' }],
    dbName: [
      { required: true, message: '请输入数据库名/服务名', trigger: 'blur' }
    ],
    username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
    password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
  };

  // 监听编辑数据改变
  watch(
    () => props.data,
    (data) => {
      if (data) {
        Object.assign(form, data);
      } else {
        Object.assign(form, {
          id: '',
          name: '',
          dbType: 'oracle',
          host: '',
          port: 1521,
          dbName: '',
          username: '',
          password: ''
        });
      }
    },
    { immediate: true }
  );

  // 关闭弹窗
  const closeDialog = () => {
    emit('update:modelValue', false);
  };

  // 重置表单
  const resetForm = () => {
    formRef.value?.resetFields();
  };

  // 提交表单
  const submit = async () => {
    // 表单验证
    try {
      await formRef.value?.validate();
    } catch (e) {
      return;
    }
    loading.value = true;
    try {
      await operation(form);
      ElMessage.success(form.id ? '修改成功' : '添加成功');
      emit('done');
      closeDialog();
    } catch (e) {
      ElMessage.error(e.message);
    } finally {
      loading.value = false;
    }
  };
</script>
