<template>
  <el-form :inline="true" :model="form" class="search-form">
    <el-form-item label="数据源名称">
      <el-input
        v-model="form.name"
        placeholder="请输入数据源名称"
        clearable
        style="width: 200px"
      />
    </el-form-item>
    <el-form-item label="数据库类型">
      <el-select
        v-model="form.dbType"
        placeholder="请选择数据库类型"
        clearable
        style="width: 200px"
      >
        <el-option label="MySQL" value="mysql" />
        <el-option label="Oracle" value="oracle" />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="search">查询</el-button>
      <el-button @click="reset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { reactive } from 'vue';

  const emit = defineEmits(['search']);

  const form = reactive({
    name: '',
    dbType: ''
  });

  const search = () => {
    emit('search', form);
  };

  const reset = () => {
    Object.keys(form).forEach((key) => {
      form[key] = '';
    });
    emit('search', form);
  };
</script>

<style lang="scss" scoped>
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fff;
    border-radius: 4px;

    :deep(.el-form-item) {
      margin-bottom: 0;
      margin-right: 16px;

      .el-input,
      .el-select {
        width: 200px;
      }
    }
  }
</style>
