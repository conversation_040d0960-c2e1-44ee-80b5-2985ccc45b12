<!-- 角色选择下拉框 -->
<template>
  <el-select
    clearable
    :model-value="props.modelValue"
    :placeholder="placeholder"
    class="ele-fluid"
    @update:modelValue="updateValue"
  >
    <el-option
      v-for="item in data"
      :key="item.id"
      :value="item.name"
      :label="item.name"
    />
  </el-select>
</template>

<script setup>
  import { ref } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    codeType: String,
    queryUrl: String,
    /** 选中的角色 */
    modelValue: Array,
    /** 提示文本 */
    placeholder: {
      type: String,
      default: '请选择'
    }
  });

  /** 数据 */
  const data = ref([]);

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };

  if (props.codeType) {
    let url = props.queryUrl;
    /** 获取数据 */
    url({ codeType: props.codeType })
      .then((list) => {
        data.value = list;
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  }
</script>
