<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form
      size="small"
      label-width="72px"
      @keyup.enter="search"
      @submit.prevent=""
    >
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="用户账号">
            <el-input
              clearable
              v-model.trim="form.username"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="用户名">
            <el-input
              clearable
              v-model.trim="form.nickname"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <!--                <el-col :lg="6" :md="12" :sm="12" :xs="24">-->
        <!--                    <el-form-item label="性别">-->
        <!--                        <dict-data code="sex" v-model="form.sex" placeholder="请选择"/>-->
        <!--                    </el-form-item>-->
        <!--                </el-col>-->
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { useFormData } from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    username: '',
    nickname: '',
    sex: void 0
  });

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
