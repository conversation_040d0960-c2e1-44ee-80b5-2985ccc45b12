<!-- 用户编辑弹窗 -->
<template>
  <ele-drawer
    :size="660"
    :title="isUpdate ? '修改用户' : '新建用户'"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px', paddingTop: '1px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="用户账号" prop="username">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.username"
              placeholder="请输入用户账号"
              :disabled="isUpdate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户名" prop="realName">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.realName"
              placeholder="请输入用户名"
            />
          </el-form-item> </el-col
        ><el-col :span="12">
          <el-form-item label="网名" prop="onlineName">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.onlineName"
              placeholder="请输入网名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="telMobile">
            <el-input
              clearable
              :maxlength="11"
              v-model="form.telMobile"
              placeholder="请输入手机号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生日期">
            <el-date-picker
              v-model="form.birthday"
              value-format="YYYY-MM-DD"
              placeholder="请选择出生日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">角色设置 [拖拽选择角色]</el-divider>

      <!--                <el-form-item label="角色" prop="roleList">-->
      <!--                    <role-select v-model="form.roleList"/>-->
      <!--                </el-form-item>-->
      <dragsort-list v-model="form.roleList" />

      <el-divider content-position="left">组织关系</el-divider>
      <!--            <edit-table/>-->
      <el-form-item label="所属公司" prop="deptName">
        <common-select
          v-model="form.deptName"
          :queryUrl="queryBaseCom"
          codeType="company"
        />
      </el-form-item>
      <!--      <el-form-item label="所属公司" prop="deptName">
        <ele-table-select
          ref="selectRef"
          clearable
          placeholder="请选择"
          value-key="name"
          label-key="name"
          v-model="form.deptName"
          :table-props="tableProps"
          :cache-data="cacheData"
          :popper-width="480"
        >
          <template #topExtra>
            <advanced-search @search="onSearch" />
          </template>
        </ele-table-select>
      </el-form-item>-->

      <el-divider content-position="left">详细信息</el-divider>

      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="证件类型" prop="idType">
            <common-select
              v-model="form.idType"
              :queryUrl="queryBaseCom"
              codeType="zjlx"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="证件号码" prop="idCode">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.idCode"
              placeholder="请输入证件号码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="gender">
            <el-select
              clearable
              v-model="form.gender"
              placeholder="请选择"
              class="ele-fluid"
            >
              <el-option value="男" label="男" />
              <el-option value="女" label="女" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              clearable
              :maxlength="100"
              v-model="form.email"
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="个人简介">
            <el-input
              type="textarea"
              :rows="5"
              :maxlength="200"
              v-model="form.introduction"
              placeholder="请输入个人简介"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue';
  import { EleMessage, emailReg, phoneReg } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import CommonSelect from './common-select.vue';
  import DragsortList from './dragsort-list.vue';
  import AdvancedSearch from './advanced-search.vue';
  import { checkExistence, operationUser } from '../api/index';
  import { getCodeData as queryBaseCom } from '@/views/base-code/common/api';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表格配置 */
  const tableProps = reactive({
    datasource: ({ page, limit, where, orders }) => {
      return queryBaseCom({
        ...where,
        ...orders,
        page,
        limit,
        codeType: 'company'
      });
    },
    virtual: true,
    height: 288,
    columns: [
      {
        prop: 'name',
        label: '公司名称',
        minWidth: 150
      }
    ],
    showOverflowTooltip: true,
    highlightCurrentRow: true,
    toolbar: false,
    pagination: {
      layout: 'total, prev, pager, next, sizes',
      teleported: false
    },
    rowStyle: { cursor: 'pointer' }
  });
  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    username: '',
    realName: '',
    onlineName: '',
    deptName: '',
    idType: '',
    idCode: '',
    gender: '',
    roleList: [],
    email: '',
    telMobile: '',
    password: '',
    introduction: '',
    birthday: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    username: [
      {
        required: true,
        message: '请输入用户账号',
        type: 'string',
        trigger: 'blur'
      },
      {
        min: 4,
        message: '账号长度最少为4位',
        type: 'string',
        trigger: 'blur'
      },
      {
        type: 'string',
        trigger: 'blur',
        validator: (_rule, value, callback) => {
          checkExistence({ username: value })
            .then(() => {
              if (isUpdate.value) {
                callback();
              } else {
                callback(new Error('账号已经存在'));
              }
            })
            .catch(() => {
              callback();
            });
        }
      }
    ],
    realName: [
      {
        required: true,
        message: '请输入用户名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    onlineName: [
      {
        required: true,
        message: '请输入网名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    gender: [
      {
        required: true,
        message: '请选择性别',
        type: 'string',
        trigger: 'blur'
      }
    ],
    roleList: [
      {
        required: true,
        message: '请选择角色',
        type: 'array',
        trigger: 'blur'
      }
    ],
    email: [
      {
        pattern: emailReg,
        message: '邮箱格式不正确',
        type: 'string',
        trigger: 'blur'
      }
    ],
    password: [
      {
        type: 'string',
        trigger: 'blur',
        validator: (_rule, value, callback) => {
          if (isUpdate.value || /^[\S]{5,18}$/.test(value)) {
            return callback();
          }
          callback(new Error('密码必须为5-18位非空白字符'));
        }
      }
    ],
    telMobile: [
      // {
      //     required: true,
      //     message: '请输入手机号',
      //     type: 'string',
      //     trigger: 'blur'
      // },
      {
        pattern: phoneReg,
        message: '手机号格式不正确',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operationUser(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          checkExistence({ username: props.data.username })
            .then((res) => {
              assignFields({
                ...res,
                password: ''
              });
            })
            .catch(() => {});

          // assignFields({
          //     ...props.data,
          //     password: ''
          // });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
