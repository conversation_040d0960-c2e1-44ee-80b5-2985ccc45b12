<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <search @search="searchReload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="sphfwDictionary"
      >
        <template #toolbar>
          <el-button
            v-if="groupId"
            size="small"
            :icon="ArrowLeftOutlined"
            type="success"
            class="ele-btn-icon"
            @click="onBack"
          >
            返回列表
          </el-button>
          <el-button
            v-if="groupId"
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
          <template
            v-if="
              ['select', 'radio', 'radioButton'].includes(row['controlType'])
            "
          >
            <el-divider direction="vertical" />
            <el-link type="primary" :underline="false" @click="dropClick(row)">
              设置字段显示隐藏
            </el-link>
            <!--                        <el-divider direction="vertical"/>-->
            <!--                        <el-link type="primary" :underline="false" @click="dropClick(row,'valLink')">-->
            <!--                            设置字段值及联-->
            <!--                        </el-link>-->
          </template>
        </template>
        <!-- 表头 -->
        <template #fieldZhHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 字段显示隐藏设置-->
    <field-link
      v-model="showFieldLink"
      :userType="userType"
      :data="currentFieldLink"
      @done="reload"
    />
    <!-- 字段值下拉及联-->
    <field-value-link
      v-model="showFieldValueLink"
      :userType="userType"
      :data="currentFieldLink"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { computed, onActivated, reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    ArrowLeftOutlined,
    DeleteOutlined,
    PlusOutlined
  } from '@/components/icons';
  import Edit from './components/edit.vue';
  import fieldLink from './components/field-link.vue';
  import fieldValueLink from './components/field-value-link.vue';
  import nameFilter from './components/name-filter.vue';
  import Search from './components/search.vue';
  import { queryPage, removes } from './api/index';
  import { useDictData } from '@/utils/use-dict-data';
  import { transformDicDataName } from '@/utils/common';
  import { useRouter } from 'vue-router';
  import { usePageTab } from '@/utils/use-page-tab';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const { currentRoute, push } = useRouter();
  const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
  const ROUTE_PATH = '/system/sphfw/dictionary-field';

  //获取浏览器参数
  const { params, path, query } = unref(currentRoute);
  const userType = path.split('/')[4];
  const groupId = params.groupId ? params.groupId : '';
  const groupName = params.groupName ? params.groupName : '';
  const dictFieldUrl = query.dictFieldUrl;

  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  let dicCodes = [
    'groupType',
    'controlType',
    'listRoles',
    'listGroup',
    'pycc',
    'listDicCode'
  ];
  dicCodes.forEach((code) => {
    if (dicts.value[code]) {
      return;
    } else {
      useDictData(code, { userType: userType });
    }
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = computed(() => {
    return [
      {
        type: 'selection',
        columnKey: 'selection',
        width: 50,
        align: 'center',
        fixed: 'left'
      },
      {
        prop: 'groupId',
        label: '所属信息组',
        // slot: 'groupType',
        formatter: (row) => {
          let newArray = row.groupId ? [row.groupId] : [];
          return transformDicDataName(dicts.value['groupType'], newArray);
        },
        minWidth: 110,
        fixed: 'left'
      },
      {
        prop: 'fieldZh',
        label: '字段中文名',
        headerSlot: 'fieldZhHeader',
        minWidth: 110,
        fixed: 'left'
      },
      {
        prop: 'fieldEn',
        label: '字段英文名',
        minWidth: 110
      },
      {
        prop: 'required',
        label: '是否必填',
        minWidth: 110
      },
      {
        prop: 'showFlag',
        label: '是否显示',
        minWidth: 110
      },
      {
        prop: 'sort',
        label: '排序',
        sortable: 'custom',
        minWidth: 110
      },
      {
        prop: 'startTime',
        label: '开始时间',
        minWidth: 110
      },
      {
        prop: 'endTime',
        label: '截至时间',
        minWidth: 110
      },
      {
        prop: 'controlType',
        label: '控件类型',
        slot: 'controlTypeName',
        formatter: (row) => {
          let newArray = row.controlType ? [row.controlType] : [];
          return transformDicDataName(dicts.value['controlType'], newArray);
        },
        minWidth: 110
      },
      {
        prop: 'regExpression',
        label: '正则验证表达式',
        minWidth: 110
      },
      {
        prop: 'loadDataType',
        label: '加载数据',
        formatter: (row) => {
          let newArray = row.loadDataType ? [row.loadDataType] : [];
          return transformDicDataName(dicts.value['listDicCode'], newArray);
        },
        minWidth: 110
      },
      {
        prop: 'loadDataUrl',
        label: '加载数据URL',
        minWidth: 110
      },
      { prop: 'valueField', label: 'value字段名' },
      { prop: 'textField', label: 'text字段名' },
      {
        prop: 'countFlag',
        label: '是否统计',
        minWidth: 110
      },
      {
        prop: 'tipText',
        label: '温馨提示',
        minWidth: 110
      },
      {
        prop: 'selfModifyFlag',
        label: '是否可修改',
        minWidth: 110
      },
      {
        prop: 'selfModifyVerifyFlag',
        label: '修改是否审核',
        minWidth: 110
      },
      {
        prop: 'modifyRole',
        label: '可修改角色',
        formatter: (row) => {
          let newArray = row.modifyRole ? row.modifyRole.split(',') : [];
          return transformDicDataName(dicts.value['listRoles'], newArray);
        },
        minWidth: 110
      },
      {
        prop: 'hideRoles',
        label: '不可见角色',
        formatter: (row) => {
          let newArray = row.hideRoles;
          return transformDicDataName(
            dicts.value['listRoles'],
            newArray,
            'roleId'
          );
        }
      },
      {
        prop: 'eduLevels',
        label: '培养层次',
        formatter: (row) => {
          let newArray = row.eduLevels;
          return transformDicDataName(
            dicts.value['pycc'],
            newArray,
            'eduLevelId'
          );
        }
      },
      {
        columnKey: 'action',
        label: '操作',
        slot: 'action',
        fixed: 'right',
        minWidth: 215
      }
    ];
  });

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否打开关联关系设置 */
  const showFieldLink = ref(false);
  const showFieldValueLink = ref(false);
  const currentFieldLink = ref(null);

  /** 修改页签标题 */
  const setUserTabTitle = () => {
    if (groupId && unref(currentRoute).path.startsWith(ROUTE_PATH)) {
      setPageTab({
        key: getRouteTabKey(),
        // title: `aaa[${form.nickname}]`
        title: (userType === 'student' ? '学生' : '教师') + groupName
      });
    }
  };

  /** 返回 */
  const onBack = () => {
    removePageTab({ key: getRouteTabKey() });
    push('/system/sphfw/dictionary-group/' + userType);
  };
  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    setUserTabTitle();
    where['groupId'] = where['groupId'] ? where['groupId'] : groupId;
    if (userType) where['userType'] = userType;
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  const searchReload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        fieldZh: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    if (where) {
      tableRef.value?.reload?.({ page: 1, where });
    } else {
      tableRef.value?.reload?.();
    }
  };

  /** 关联关系设置 */
  const dropClick = async (row, valLink) => {
    let newObj = {
      groupId: row.groupId,
      fieldId: row.id,
      fieldEn: row.fieldEn,
      controlType: row.controlType,
      loadDataType: row.loadDataType,
      loadDataUrl: row.loadDataUrl
    };
    currentFieldLink.value = newObj;
    if (valLink) {
      showFieldValueLink.value = true;
    } else {
      showFieldLink.value = true;
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    if (row) {
      //编辑
      current.value = row;
    } else {
      //添加(字段组页面进入用添加功能)
      current.value = {
        userType: userType,
        enterType: 'group',
        dictFieldUrl: dictFieldUrl,
        groupId: groupId
      };
    }
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.fieldZh).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  onActivated(() => {
    setUserTabTitle();
  });
</script>

<script>
  export default {
    name: 'SphfwDictionaryField'
  };
</script>
