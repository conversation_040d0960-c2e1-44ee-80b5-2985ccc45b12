<template>
  <ele-drawer
    :size="930"
    title="设置字段值及联"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form ref="formRef" :model="datasource" @submit.prevent="" size="small">
      <el-form-item label="及联类型">
        <el-radio-group v-model="form.publicType">
          <el-radio-button :label="1">类型及联</el-radio-button>
          <el-radio-button :label="2">值及联</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <ele-data-table
        :border="true"
        row-key="key"
        :columns="columns"
        :data="datasource"
        :span-method="spanMethod"
        @cell-click="cellClick"
        cell-class-name="editable-table-cell"
        class="editable-table"
      >
        <template #showFlagType="{ row, column, $index }">
          <!--                    required: true,-->
          <el-form-item
            v-if="
              tableRowEditId === row.key && tableColumnEditIndex === column.id
            "
            @blur="blurValueInput(row, column)"
            @keyup.enter="blurValueInput(row, column)"
            :prop="$index + '.showFlag'"
            :rules="{
              message: '请选择',
              type: 'string',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <el-select
              clearable
              v-model="row.showFlag"
              placeholder="请选择"
              class="ele-fluid"
            >
              <el-option value="是" label="是" />
              <el-option value="否" label="否" />
            </el-select>
          </el-form-item>
        </template>
        <template #linkFieldDataType="{ row, column, $index }">
          <!--                    required: true,-->
          <el-form-item
            v-if="
              tableRowEditId === row.key && tableColumnEditIndex === column.id
            "
            @blur="blurValueInput(row, column)"
            @keyup.enter="blurValueInput(row, column)"
            :prop="$index + '.linkFieldDataType'"
            :rules="{
              message: '请选择',
              type: 'string',
              trigger: 'change'
            }"
            class="form-error-popper"
            style="margin-bottom: 0"
          >
            <dict-data
              code="listDicCode"
              v-model="row.linkFieldDataType"
              placeholder="请选择"
            />

            <!--                        <el-select clearable-->
            <!--                                   v-model="row.linkedExp"-->
            <!--                                   placeholder="请选择"-->
            <!--                                   class="ele-fluid">-->
            <!--                            <el-option value="是" label="是"/>-->
            <!--                            <el-option value="否" label="否"/>-->
            <!--                        </el-select>-->
            <!--                <div v-else class="editable-cell-text">{{ row.sfbt }}</div>-->
          </el-form-item>
          <el-input
            v-if="row.linkFieldDataType === 'other'"
            clearable
            :maxlength="20"
            v-model="row.linkFieldDataUrl"
            placeholder="请输入"
          />
        </template>
      </ele-data-table>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue';
  import { getCodeData } from '@/views/base-code/dictionary/api/data-index';
  import { getDicFieldValueByUrl, getDictionaryField } from '../api/index';
  import { getDictionaryFieldLink, operation } from '../api/field-link-index';
  import { generateRandomString } from '@/utils/common';
  import { EleMessage } from 'ele-admin-plus';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    userType: String
  });

  /** 表格数据源 */
  const datasource = ref([]);
  /** 表格列配置 */
  const columns = ref([
    {
      columnKey: 'fieldVal',
      prop: 'fieldVal',
      label: '选项',
      width: 120
    },
    {
      prop: 'linkField',
      label: '英文名'
    },
    {
      prop: 'fieldZh',
      label: '中文名'
    },
    {
      prop: 'sfbt',
      label: '原必填值'
    },
    // {
    //     prop: 'showFlag',
    //     label: '关联字段是否显示',
    //     slot: 'showFlagType',
    // },
    {
      prop: 'linkFieldDataType',
      label: '联动表达式',
      slot: 'linkFieldDataType',
      width: 240
    }
  ]);
  /** 提交状态 */
  const loading = ref(false);
  /** 表单数据 */
  const form = reactive({
    publicType: '',
    users: []
  });
  let tableRowEditId = ref(null); // 控制可编辑的每一行
  let tableColumnEditIndex = ref(null); //控制可编辑的每一列

  const cellClick = (row, column) => {
    //赋值给定义的变量
    tableRowEditId.value = row.key; //确定点击的单元格在哪行 如果数据中有ID可以用ID判断，没有可以使用其他值判断，只要能确定是哪一行即可
    tableColumnEditIndex.value = column.id; //确定点击的单元格在哪列
  };
  const blurValueInput = (row, column) => {
    tableRowEditId.value = null;
    tableColumnEditIndex.value = null;
    //在此处调接口传数据blurValueInput
    onSubmit();
  };

  const onSubmit = () => {
    const data = datasource.value;
    loading.value = true;
    operation(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  const getDataSource = async () => {
    let dictionaryFieldLinkData = [];
    await getDictionaryFieldLink({ fieldId: props.data.fieldId }).then(
      (arr) => {
        dictionaryFieldLinkData = arr;
      }
    );
    let newObj = {
      groupId: props.data.groupId,
      required: '否',
      showFlag: '否',
      sort: 'sort',
      order: 'desc'
    };
    let dictionaryField = [];
    let codeData = [];

    await getDictionaryField(newObj).then((arr) => {
      dictionaryField = arr;
    });
    if (props.data.loadDataType === 'other') {
      await getDicFieldValueByUrl(props.data.loadDataUrl).then((arr) => {
        codeData = arr;
      });
    } else {
      await getCodeData({
        sort: 'sort',
        order: 'as',
        codeType: props.data.loadDataType
      }).then((arr) => {
        codeData = arr;
      });
    }
    let fjtjObjs = [];
    for (var i = 0; i < dictionaryField.length; i++) {
      var arr = dictionaryField[i];
      for (var j = 0; j < codeData.length; j++) {
        var row1 = codeData[j];
        fjtjObjs.push({
          key: generateRandomString(10),
          nameRowSpan: i === 0 ? dictionaryField.length : 0,
          fieldId: props.data.fieldId,
          fieldEn: props.data.fieldEn,
          fieldVal: row1.name,
          fieldValId: row1.id,
          showFlag: '否',
          linkFieldDataType: '',
          linkFieldDataUrl: '',
          linkField: arr.fieldEn,
          fieldZh: arr.fieldZh,
          sfbt: arr.required,
          groupId: props.data.groupId
        });
      }
    }
    // 根据group属性进行分组排序，并在每个group内按value升序排序
    const sortedItems = fjtjObjs.sort((a, b) => {
      if (a.fieldVal < b.fieldVal) return -1;
      if (a.fieldVal > b.fieldVal) return 1;
    });

    sortedItems.forEach((e) => {
      e.userType = props.userType;
      let linkData = dictionaryFieldLinkData?.filter?.((d) => {
        return d.fieldVal === e.fieldVal && d.linkField === e.linkField;
      });
      if (linkData.length > 0) {
        e.showFlag = linkData[0].showFlag;
        e.linkFieldDataType = linkData[0].linkFieldDataType;
        e.linkFieldDataUrl = linkData[0].linkFieldDataUrl;
      }
    });
    datasource.value = sortedItems;
  };

  /** 合并表格单元格 */
  const spanMethod = ({ row, column }) => {
    if (column.columnKey === 'fieldVal') {
      return [row.nameRowSpan, 1];
    }
    return [1, 1];
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          getDataSource();
        }
      }
    },
    {
      immediate: true
      // deep: true
    }
  );
</script>

<style lang="scss" scoped>
  .editable-table :deep(.editable-table-cell) {
    position: static;

    & > .cell {
      overflow: visible;
    }
  }

  .editable-cell-text {
    width: 100%;
    min-height: 32px;
    box-sizing: border-box;
  }

  .form-error-popper.is-error .editable-cell-text {
    border: 1px dashed var(--el-color-danger);
    border-radius: var(--el-border-radius-base);
  }

  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }

  .editable-table :deep(tbody > tr:first-child) {
    .el-form-item > .el-form-item__content > .el-form-item__error {
      bottom: calc(0px - 100% - 6px);
      top: auto;

      &:after {
        top: -11px;
        bottom: auto;
        border-bottom-color: var(--el-color-danger);
        border-top-color: transparent;
      }
    }

    &:last-child .el-form-item > .el-form-item__content > .el-form-item__error {
      bottom: calc(0px - 100% - 6px + 32px);
    }
  }
</style>
