<template>
  <ele-drawer
    :size="830"
    :title="isUpdate ? '修改信息字段' : '新建信息字段'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <!--    form==={{ form }}<br/><br/>-->
    <!--        items==={{items}}<br/><br/>-->

    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="items"
      :grid="{ span: 12 }"
      labelWidth="auto"
      @updateValue="setFieldValue"
    >
      <template #fieldStyle="{ item, model, updateValue }">
        <el-input :model-value="model[item.prop]" readonly placeholder="请选择">
          <template #append>
            <div class="setting-wrapper">
              <div class="setting-colors">
                <!-- 颜色选择器 -->
                <el-color-picker
                  ref="colorPickerRef"
                  v-model="colorValue"
                  :predefine="predefineColors"
                  class="setting-color-picker"
                  @change="updateColor"
                  @activeChange="onColorChange"
                />
              </div>
            </div>
          </template>
        </el-input>
      </template>
      <template #regExpression="{ item, model, updateValue }">
        <el-input
          :disabled="
            ['input', 'inputNumber', 'fileUpload', 'imageUpload'].includes(
              model['controlType']
            )
              ? false
              : true
          "
          :model-value="model[item.prop]"
          @update:modelValue="updateValue"
          placeholder="请输入"
        />
      </template>
      <template #loadDataType="{ item, model, updateValue }">
        <dict-data
          type="searchSet"
          filterable
          :disabled="
            [
              'select',
              'multipleSelect',
              'radio',
              'radioButton',
              'checkbox',
              'checkboxButton'
            ].includes(model['controlType'])
              ? false
              : true
          "
          code="listDicCode"
          :model-value="model[item.prop]"
          @update:modelValue="(value) => updateValueDic(value, item.prop)"
        />
      </template>
      <template #loadDataUrl="{ item, model, updateValue }">
        <dict-data
          type="searchSet"
          filterable
          :disabled="model['loadDataType'] === 'other' ? false : true"
          code="listDicUrl"
          :model-value="model[item.prop]"
          @update:modelValue="(value) => updateValueDic(value, item.prop)"
        />
      </template>
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { useI18n } from 'vue-i18n';
  import { computed, nextTick, ref, unref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import { operation } from '../api/index';
  import { useDictData } from '@/utils/use-dict-data';
  import { transformDicDataCode } from '@/utils/common';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { useRouter } from 'vue-router';

  const emit = defineEmits(['done', 'update:modelValue']);
  const { t } = useI18n();

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  //获取浏览器参数
  const { currentRoute, push } = useRouter();
  const { params, path, query } = unref(currentRoute);
  const userType = path.split('/')[4];
  const groupId = params.groupId ? params.groupId : '';
  const groupName = params.groupName ? params.groupName : '';
  const dictFieldUrl = query.dictFieldUrl;

  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  let dicCodes = [
    'groupType',
    'controlType',
    'dictionaryField',
    'listRoles',
    'listGroup',
    'pycc',
    'listDicCode',
    'listDicUrl'
  ];
  dicCodes.forEach((code) => {
    if (dicts.value[code]) {
      return;
    } else {
      useDictData(code, { userType: userType });
    }
  });

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    userType: userType,
    groupId: '',
    fieldEn: '',
    fieldZh: '',
    fieldStyle: void 0,
    sort: void 0,
    required: '否',
    showFlag: '否',
    startTime: '',
    endTime: '',
    controlType: '',
    regExpression: '',
    loadDataType: '',
    loadDataUrlId: '',
    loadDataUrl: '',
    valueField: '',
    textField: '',
    workflowNodeFlowCondition: '否',
    countFlag: '否',
    listShowFlag: '否',
    queryShowFlag: '否',
    tipText: '',
    selfModifyFlag: '否',
    modifyRole: '',
    selfModifyVerifyFlag: '否',
    importFieldFlag: '',
    exportFieldFlag: '否',
    hideRoles: '',
    eduLevels: ''
  });

  /** 是否是修改 */
  const isUpdate = ref(false);
  /** 提交状态 */
  const loading = ref(false);
  /** 表单项 */
  const items = computed(() => {
    return [
      {
        prop: 'groupId',
        label: '所属信息组',
        type: 'dictSelect',
        props: {
          code: 'groupType',
          disabled: groupId ? true : false,
          dicQueryParams: {
            userType: props.data.userType ? props.data.userType : userType
          }
        },
        required: true
      },
      {
        prop: 'fieldEn',
        label: '字段英文名',
        type: 'dictSelect',
        props: {
          code: 'dictionaryField',
          disabled: isUpdate.value,
          dicQueryParams: {
            groupId: props.data.groupId ? props.data.groupId : groupId,
            dictFieldUrl: props.data.dictFieldUrl
              ? props.data.dictFieldUrl
              : dictFieldUrl
          },
          filterable: true
        },
        required: true
      },
      { prop: 'fieldZh', label: '字段中文名', type: 'input', required: true },
      { prop: 'fieldStyle', label: '颜色选择器', type: 'fieldStyle' },
      {
        prop: 'startTime',
        label: '开始时间',
        type: 'datetime',
        required: true
      },
      { prop: 'endTime', label: '截至时间', type: 'datetime', required: true },
      {
        prop: 'sort',
        label: '排序',
        type: 'inputNumber'
      },
      {
        prop: 'controlType',
        label: '控件类型',
        type: 'dictSelect',
        props: { code: 'controlType', filterable: true },
        required: true
      },
      {
        prop: 'regExpression',
        label: '正则验证表达式',
        type: 'regExpression'
      },
      {
        prop: 'loadDataType',
        label: '加载数据',
        type: 'loadDataType'
      },
      {
        prop: 'loadDataUrlId',
        label: '加载数据URL',
        type: 'loadDataUrl'
      },
      { prop: 'valueField', label: 'value字段名', type: 'input' },
      { prop: 'textField', label: 'text字段名', type: 'input' },
      { prop: 'tipText', label: '温馨提示', type: 'input' },
      {
        prop: 'modifyRole',
        label: '可修改角色',
        type: 'dictMultipleSelect',
        props: { code: 'listRoles' }
      },
      {
        prop: 'hideRoles',
        label: '不可见角色',
        type: 'dictMultipleSelect',
        props: { code: 'listRoles' }
      },
      {
        prop: 'eduLevels',
        label: '培养层次',
        type: 'dictMultipleSelect',
        props: { code: 'pycc' }
      },
      {
        prop: 'importFieldFlag',
        label: '导入设置',
        type: 'radioButton',
        options: [
          { label: '必填', value: '必填' },
          { label: '非必填', value: '非必填' }
        ]
      },
      {
        prop: 'exportFieldFlag',
        label: '导出设置',
        type: 'switch',
        colProps: { span: 6 }
      },
      {
        prop: 'required',
        label: '是否必填',
        type: 'switch',
        // props: {
        //   code: 'yesNo',
        //   dicQueryParams: {
        //     getValType: 'name'
        //   },
        // },
        required: true,
        colProps: { span: 6 }
      },
      {
        prop: 'showFlag',
        label: '是否显示',
        type: 'switch',
        required: true,
        colProps: { span: 6 }
      },
      {
        prop: 'workflowNodeFlowCondition',
        label: '工作流节点条件设置',
        type: 'switch',
        colProps: { span: 6 }
      },
      {
        prop: 'countFlag',
        label: '是否统计',
        type: 'switch',
        colProps: { span: 6 }
      },
      {
        prop: 'selfModifyFlag',
        label: '是否可修改',
        type: 'switch',
        colProps: { span: 6 }
      },
      {
        prop: 'selfModifyVerifyFlag',
        label: '修改是否审核',
        type: 'switch',
        colProps: { span: 6 }
      },
      {
        prop: 'listShowFlag',
        label: '列表显示',
        type: 'switch',
        colProps: { span: 6 },
        required: true
      },
      {
        prop: 'queryShowFlag',
        label: '查询条件显示',
        type: 'switch',
        colProps: { span: 6 },
        required: true
      }
    ];
  });

  /** 颜色选择器预设颜色 */
  const predefineColors = ref([
    '#f5222d',
    '#fa541c',
    '#fa8c16',
    '#faad14',
    '#a0d911',
    '#52c41a',
    '#13c2c2',
    '#2f54eb',
    '#722ed1',
    '#eb2f96'
  ]);

  /** 颜色选择器选中颜色 */
  const colorValue = ref(void 0);

  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // const data = {...form, file: form.file.map(item => item.file)}
      const oldEduLevels = form.eduLevels
        ? form.eduLevels.map((d) => {
            return { eduLevelId: d };
          })
        : [];
      const oldHideRoles = form.hideRoles
        ? form.hideRoles.map((d) => {
            return { roleId: d };
          })
        : [];
      const data = {
        ...form,
        // 上传组件的数据格式转为字符串
        modifyRole: form.modifyRole ? form.modifyRole.join() : '',
        eduLevels: oldEduLevels,
        hideRoles: oldHideRoles
      };
      loading.value = true;
      operation(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 手动处理dic值 type="searchSet" value=object*/
  const updateValueDic = (value, prop) => {
    console.log(value);
    if (prop === 'loadDataType') {
      form[prop] = value.dictDataCode;
      form.valueField = value.valueField;
      form.textField = value.textField;
    } else if (prop === 'loadDataUrl') {
      form[prop] = value.dictDataCode;
      form.valueField = value.valueField;
      form.textField = value.textField;
      form.textField = value.url;
    }
    // {
    //   form.loadDataUrlId = value ?? null;
    //   console.log('11111=====', value, dicts.value['listDicUrl'])
    //   let dicUrlData = dicts.value['listDicUrl'];
    //   if (dicUrlData) {
    //     dicUrlData.forEach((item) => {
    //       if (item.id === value) {
    //         form.valueField = item.valueField;
    //         form.textField = item.textField;
    //         form.loadDataUrl = item.url;
    //       }
    //     });
    //   }
    // }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  const updateColor = async (value) => {
    form.fieldStyle = value ?? null;
    initColorValue();
  };

  const resetSetting = async () => {
    initColorValue();
  };

  /** 更新颜色选择器选中 */
  const initColorValue = () => {
    if (form.fieldStyle) {
      colorValue.value = form.fieldStyle;
    } else {
      colorValue.value = void 0;
    }
  };

  initColorValue();

  // 解决ElColorPicker第一次点击预设颜色未选中的问题
  const colorPickerRef = ref(null);
  const onColorChange = (color) => {
    const index = predefineColors.value.indexOf(color.toLowerCase());
    const ins = colorPickerRef.value?.$refs?.predefine;
    if (index != -1 && ins != null && !ins.rgbaColors[index].selected) {
      nextTick(() => {
        ins.rgbaColors[index].selected = true;
      });
    }
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          if (props.data.enterType === 'group') {
            form['groupId'] = groupId ? groupId : props.data.groupId;
            // assignFields({
            //     groupId: groupId ? groupId : props.data.groupId,
            // });
            isUpdate.value = false;
          } else {
            let newArray = props.data?.eduLevels;
            const eduLevels =
              newArray.length > 0
                ? transformDicDataCode(
                    dicts.value['pycc'],
                    newArray,
                    'eduLevelId'
                  )
                : [];
            let newArrayHideRoles = props.data?.hideRoles;
            const hideRoles =
              newArrayHideRoles.length > 0
                ? transformDicDataCode(
                    dicts.value['listRoles'],
                    newArrayHideRoles,
                    'roleId'
                  )
                : [];
            let newArrayModifyRole = props.data.modifyRole
              ? props.data.modifyRole.split(',')
              : [];
            assignFields({
              ...props.data,
              eduLevels: eduLevels,
              hideRoles: hideRoles,
              modifyRole: newArrayModifyRole
            });
            isUpdate.value = true;
          }
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    },
    {
      immediate: true
      // deep: true
    }
  );
</script>

<style lang="scss" scoped>
  .setting-wrapper {
    /* 主题色选择 */
    .setting-colors {
      color: #fff;
      line-height: 0;
    }

    .setting-color-picker {
      background: conic-gradient(
        from 90deg at 50% 50%,
        red -19.41deg,
        red 18.76deg,
        #ff8a00,
        #ffe600 99.87deg,
        #14ff00 141.65deg,
        #00a3ff 177.72deg,
        #0500ff 220.23deg,
        #ad00ff 260.13deg,
        #ff00c7 300.69deg,
        red 340.59deg,
        red 378.76deg
      );
    }

    /* 颜色选择器 */
    .setting-colors :deep(.el-color-picker) {
      line-height: 0;

      .el-color-picker__trigger {
        padding: 0;
        width: 23px;
        height: 23px;
        border: none;
      }

      .el-color-picker__color {
        border: none;
      }

      .el-color-picker__empty {
        background: conic-gradient(
          from 90deg at 50% 50%,
          rgb(255, 0, 0) -19.41deg,
          rgb(255, 0, 0) 18.76deg,
          rgb(255, 138, 0) 59.32deg,
          rgb(255, 230, 0) 99.87deg,
          rgb(20, 255, 0) 141.65deg,
          rgb(0, 163, 255) 177.72deg,
          rgb(5, 0, 255) 220.23deg,
          rgb(173, 0, 255) 260.13deg,
          rgb(255, 0, 199) 300.69deg,
          rgb(255, 0, 0) 340.59deg,
          rgb(255, 0, 0) 378.76deg
        );
        height: 100%;
        width: 100%;

        & > svg {
          display: none;
        }
      }
    }
  }
</style>
