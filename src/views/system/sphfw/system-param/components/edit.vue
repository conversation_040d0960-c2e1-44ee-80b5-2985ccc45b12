<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改参数信息' : '新建参数信息'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      @updateValue="setFieldValue"
    >
      <template #listGroup="{ item, model, updateValue }">
        <dict-data
          :disabled="model['listFlag'] === '是' ? false : true"
          code="listGroup"
          :model-value="model[item.prop]"
          @update:modelValue="updateValue"
        />
      </template>
      <template #attachment="{ item, model, updateValue }">
        <file-upload
          :limit="8"
          :modelValue="model[item.prop]"
          @update:modelValue="updateValue"
          @change="changeDelIds"
        />
        <!--                <advanced-file :modelValue="model[item.prop]" :limit="1" @update:modelValue="updateValue"/>-->
      </template>
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import FileUpload from '@/components/FileUpload/index.vue';
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api';
  import ProForm from '@/components/ProForm/index.vue';
  import { isImageFile, toFormData } from '@/utils/common';

  const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 要删除的附件ID，字符串逗号分割*/
  const deleteFileIds = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    paramName: '',
    paramValue: '',
    paramMode: '',
    description: '',
    attachment: ''
  });

  /** 表单项 */
  const items = ref([
    { prop: 'paramName', label: '参数名称', type: 'input', required: true },
    { prop: 'paramValue', label: '参数值', type: 'input', required: true },
    { prop: 'paramMode', label: '参数模块', type: 'input' },
    { prop: 'description', label: '备注', type: 'input' },
    {
      prop: 'attachment',
      label: '附件',
      type: 'attachment',
      colProps: { span: 24 }
    }
  ]);

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      let fileArray = [];
      if (form.attachment.length > 0) {
        form.attachment.forEach((e) => {
          if (e.status !== 'done') {
            fileArray.push(e);
          }
        });
      }
      let data = toFormData({
        ...form,
        attachment: fileArray,
        deleteFileIds: deleteFileIds.value
      });
      loading.value = true;
      operation(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** changeDelIds */
  const changeDelIds = (value) => {
    deleteFileIds.value = value;
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          const oldFiles = props.data.attachment
            ? JSON.parse(props.data.attachment).map((d, i) => {
                let newObj = {
                  key: d.id,
                  name: d.originalFilename,
                  contentType: d.contentType,
                  fileUrl: BASE_URL + 'api/file/inline/' + d.id,
                  status: 'done'
                };
                let mark = isImageFile(newObj);
                newObj.isImageFile = mark;
                if (mark) newObj.url = newObj.fileUrl;
                return newObj;
              })
            : [];
          assignFields({
            ...props.data,
            attachment: oldFiles
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
