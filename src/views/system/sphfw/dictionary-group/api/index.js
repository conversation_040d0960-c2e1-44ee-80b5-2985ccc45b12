import request from '@/utils/request';

/**
 * 根据id查询用户信息组
 */
export async function getDictionaryGroupById(id) {
  const res = await request.post('/dictionary/dictionaryGroup/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getDictionaryGroup(params) {
  const res = await request.get('/dictionary/dictionaryGroup', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询
 */
export async function queryPage(params) {
  const res = await request.get('/dictionary/dictionaryGroup/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改
 */
export async function operation(params) {
  // let fdata = toFormData({...params, eduLevels: null})
  // // let formData = toFormData({...params})
  // if (params.eduLevels.length > 0) {
  //     params.eduLevels.forEach(((item, index) => {
  //         fdata.append('eduLevels[' + index + '].eduLevelId', item);
  //     }))
  // }
  const res = await request.post(
    '/dictionary/dictionaryGroup/operation',
    params
  );
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
  const res = await request.post('/dictionary/dictionaryGroup/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
