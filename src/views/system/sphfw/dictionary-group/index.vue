<template>
  <ele-page flex-table>
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="sphfwDictionaryGroup"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            class="ele-btn-icon"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #groupName="{ row }">
          <el-link
            type="primary"
            :underline="false"
            @click="dropClick('fieldList', row)"
          >
            {{ row.groupName }}
          </el-link>
        </template>
        <template #action="{ row }">
          <!--                    <el-divider direction="vertical"/>-->
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
          <el-divider direction="vertical" />
          <ele-dropdown
            :items="optItems(row)"
            @command="(key) => dropClick(key, row)"
          >
            <el-link type="primary" :underline="false">
              <span>更多</span>
              <el-icon :size="12">
                <ArrowDown />
              </el-icon>
            </el-link>
          </ele-dropdown>
        </template>
        <!-- 用户名表头 -->
        <template #groupNameHeader="{ column }">
          <div style="display: flex; align-items: center">
            <div style="flex: 1; overflow: hidden; text-overflow: ellipsis">
              {{ column.label }}
            </div>

            <el-icon style="padding: 3px">
              <PieChart />
            </el-icon>
            <name-filter @search="onNameFilter" />
          </div>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 编辑碎片组件弹窗 -->
    <editField v-model="showEditField" :data="currentField" @done="reload" />
  </ele-page>
</template>

<script setup>
  import { reactive, ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from './components/edit.vue';
  import EditField from '@/views/system/sphfw/dictionary-field/components/edit.vue';
  import nameFilter from './components/name-filter.vue';
  import { queryPage, removes } from './api/index';

  import { useRouter } from 'vue-router';
  import { useDictData } from '@/utils/use-dict-data';
  import { insertAtIndex, transformDicDataName } from '@/utils/common';
  import { getListGroupConfigById } from '@/views/system/sphfw/list-group/api';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const { currentRoute, push } = useRouter();
  const { params, path } = unref(currentRoute);
  const userType = path.split('/')[4];
  /** 所属信息组字典数据 */
  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  let dicCodes = [
    'groupType',
    'controlType',
    'listRoles',
    'listGroup',
    'pycc',
    'listDicCode'
  ];
  dicCodes.forEach((code) => {
    if (dicts.value[code]) {
      return;
    } else {
      useDictData(code, { userType: userType });
    }
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'groupName',
      label: '字段组',
      headerSlot: 'groupNameHeader',
      slot: 'groupName',
      fixed: 'left'
    },
    {
      prop: 'showFlag',
      label: '是否显示'
    },
    {
      prop: 'activeFlag',
      label: '是否需要激活'
    },
    {
      prop: 'addFlag',
      label: '是否可添加'
    },
    {
      prop: 'modifyFlag',
      label: '是否可修改'
    },
    {
      prop: 'emptyDataHide',
      label: '空数据隐藏'
    },
    {
      prop: 'listFlag',
      label: '是否列表'
    },
    {
      prop: 'listGroupId',
      label: '数据来源',
      formatter: (row) => {
        let newArray = row.listGroupId ? [row.listGroupId] : [];
        return transformDicDataName(dicts.value['listGroup'], newArray);
      }
      // type: 'listGroup',
    },

    {
      prop: 'sort',
      label: '排序',
      width: 80,
      sortable: 'custom'
    },
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 140,
      fixed: 'right'
    }
  ]);
  let pyccItem = {
    prop: 'eduLevels',
    label: '培养层次',
    formatter: (row) => {
      let newArray = row.eduLevels;
      return transformDicDataName(dicts.value['pycc'], newArray, 'eduLevelId');
    }
  };

  // 在下标为9的位置插入培养层次
  if (userType === 'student') insertAtIndex(columns.value, pyccItem, 9);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 当前字段编辑数据 */
  const currentField = ref({});

  /** 是否显示字段编辑弹窗 */
  const showEditField = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit,
      userType: userType
    });
  };
  /** 用户名筛选值 */
  const nameFilterValue = ref('');

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 用户名筛选事件 */
  const onNameFilter = (name) => {
    nameFilterValue.value = name;
    doReload();
  };
  /** 表格搜索 */
  const doReload = () => {
    if (nameFilterValue.value) {
      reload({
        ...lastWhere,
        groupName: nameFilterValue.value
      });
    } else {
      reload(lastWhere);
    }
  };
  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开组group编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.groupName).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 操作更多*/
  const optItems = () => {
    let itemsArray = [
      { title: '新建字段', command: 'createField' },
      { title: '字段维护', command: 'fieldList' }
    ];
    return itemsArray;
  };

  /** 下拉菜单点击事件 */
  const dropClick = async (key, row) => {
    currentField.value.userType = userType;
    currentField.value.dictFieldUrl = '/dictionary/dictionaryField/userInfo/';
    if (row.listGroupId) {
      const result = await getListGroupConfigById(row.listGroupId).catch((e) =>
        console.error(e)
      );
      currentField.value.dictFieldUrl = result.dictionaryFieldUrl;
    }
    /** 打开字段field编辑弹窗 */
    if (key === 'createField') {
      currentField.value.groupId = row.id;
      currentField.value.enterType = 'group';
      showEditField.value = true;
    } else if (key === 'fieldList') {
      push({
        path:
          '/system/sphfw/dictionary-field/' +
          userType +
          '/' +
          row.id +
          '/' +
          row.groupName,
        query: { dictFieldUrl: currentField.value.dictFieldUrl }
      });
    }
  };
</script>

<script>
  export default {
    name: 'SphfwDictionaryGroup'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>
