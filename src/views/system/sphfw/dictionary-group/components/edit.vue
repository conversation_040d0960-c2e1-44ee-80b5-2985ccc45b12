<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改信息组' : '新建信息组'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      :labelWidth="110"
      @updateValue="setFieldValue"
    >
      <template #listGroup="{ item, model, updateValue }">
        <dict-data
          :disabled="model['listFlag'] === '是' ? false : true"
          code="listGroup"
          :model-value="model[item.prop]"
          @update:modelValue="updateValue"
        />
      </template>
      <template #dicIcon="{ item, model, updateValue }">
        <advanced-file
          :modelValue="model[item.prop]"
          :limit="1"
          @update:modelValue="updateValue"
        />
      </template>
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, unref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api/index';
  import ProForm from '@/components/ProForm/index.vue';
  import AdvancedFile from '../components/advanced-file.vue';
  import { useRouter } from 'vue-router';
  import {
    insertAtIndex,
    toFormData,
    transformDicDataCode
  } from '@/utils/common';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { useDictData } from '@/utils/use-dict-data';

  const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  const { currentRoute } = useRouter();
  const { params, path } = unref(currentRoute);
  const userType = path.split('/')[4];

  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  let dicCodes = [
    'groupType',
    'controlType',
    'listRoles',
    'listGroup',
    'pycc',
    'listDicCode'
  ];
  dicCodes.forEach((code) => {
    if (dicts.value[code]) {
      return;
    } else {
      useDictData(code, { userType: userType });
    }
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    groupName: '',
    eduLevels: '',
    showFlag: '',
    activeFlag: '',
    addFlag: '',
    modifyFlag: '',
    listFlag: '',
    listGroupId: '',
    emptyDataHide: '',
    sort: void 0,
    file_icon: '',
    userType: userType
  });
  const sfOption = ref([
    { label: '是', value: '是' },
    { label: '否', value: '否' }
  ]);

  /** 表单项 */
  const items = ref([
    {
      prop: 'groupName',
      label: '字段名',
      type: 'input',
      required: true
    },
    {
      prop: 'listFlag',
      label: '是否列表',
      type: 'radioButton',
      options: sfOption,
      required: true,
      colProps: { span: 12 }
    },
    {
      prop: 'listGroupId',
      label: '数据来源',
      type: 'listGroup'
    },
    {
      prop: 'sort',
      label: '排序',
      type: 'inputNumber'
    },
    {
      prop: 'showFlag',
      label: '是否显示',
      type: 'radioButton',
      options: sfOption,
      required: true,
      colProps: { span: 12 }
    },
    {
      prop: 'activeFlag',
      label: '是否需要激活',
      type: 'radioButton',
      options: sfOption,
      required: true,
      colProps: { span: 12 }
    },
    {
      prop: 'addFlag',
      label: '是否可添加',
      type: 'radioButton',
      options: sfOption,
      required: true,
      colProps: { span: 12 }
    },
    {
      prop: 'modifyFlag',
      label: '是否可修改',
      type: 'radioButton',
      options: sfOption,
      required: true,
      colProps: { span: 12 }
    },
    {
      prop: 'emptyDataHide',
      label: '空数据隐藏',
      type: 'radioButton',
      options: sfOption,
      required: true,
      colProps: { span: 12 }
    },
    {
      prop: 'file_icon',
      label: '图标',
      type: 'dicIcon',
      colProps: { span: 24 }
    }
  ]);

  let pyccItem = {
    prop: 'eduLevels',
    label: '培养层次',
    type: 'dictMultipleSelect',
    props: { code: 'pycc' },
    required: true
  };
  // 在下标为7的位置插入培养层次
  if (userType === 'student') insertAtIndex(items.value, pyccItem, 1);
  /** 表单验证规则 */
  // const rules = reactive({
  //     listFlag: [
  //         {
  //             type: 'select',
  //             validator: (_rule, value, callback) => {
  //                 if (value && value === '否') {
  //                     form.listGroupId = '';
  //                 }
  //                 callback();
  //             },
  //             trigger: 'change',
  //         }
  //     ],
  //     listGroupId: [
  //         {
  //             type: 'select',
  //             validator: (_rule, value, callback) => {
  //                 if (form.listFlag === '是' && !value) {
  //                     callback(new Error('请选择数据来源'));
  //                 }
  //                 callback();
  //             },
  //             trigger: 'change',
  //         }
  //     ]
  // });

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      let data = toFormData({ ...form, eduLevels: null });
      if (form.eduLevels.length > 0) {
        form.eduLevels.forEach((item, index) => {
          data.append('eduLevels[' + index + '].eduLevelId', item);
        });
      }
      loading.value = true;
      operation(data)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => form.listFlag,
    (listFlag) => {
      if (listFlag === '否') form.listGroupId = null;
    }
  );

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          const oldFiles = props.data.icon
            ? JSON.parse(props.data.icon).map((d, i) => {
                return {
                  key: i,
                  url: BASE_URL + 'api/file/inline/' + d.id,
                  status: 'done'
                };
              })
            : [];

          let newArray = props.data?.eduLevels;
          const eduLevels =
            newArray.length > 0
              ? transformDicDataCode(
                  dicts.value['pycc'] ? dicts.value['pycc'] : [],
                  newArray,
                  'eduLevelId'
                )
              : [];
          assignFields({
            ...props.data,
            file_icon: oldFiles,
            eduLevels: userType === 'student' ? eduLevels : []
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
