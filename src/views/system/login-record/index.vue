<template>
  <ele-page flex-table>
    <login-record-search :where="defaultWhere" @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        highlight-current-row
        :where="defaultWhere"
        cache-key="systemLoginRecordTable"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #loginType="{ row }">
          <el-tag
            v-if="row.loginType === 0"
            size="small"
            type="success"
            :disable-transitions="true"
          >
            登录成功
          </el-tag>
          <el-tag
            v-else-if="row.loginType === 1"
            size="small"
            type="danger"
            :disable-transitions="true"
          >
            登录失败
          </el-tag>
          <el-tag
            v-else-if="row.loginType === 2"
            size="small"
            type="info"
            :disable-transitions="true"
          >
            退出登录
          </el-tag>
          <el-tag
            v-else-if="row.loginType === 3"
            size="small"
            type="warning"
            :disable-transitions="true"
          >
            刷新TOKEN
          </el-tag>
        </template>
      </ele-pro-table>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { reactive, ref } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DownloadOutlined } from '@/components/icons';
  import LoginRecordSearch from './components/login-record-search.vue';
  import { queryPage } from './api';

  /** 默认搜索条件 */
  const defaultWhere = reactive({
    username: '',
    nickname: ''
  });

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'username',
      label: '账号',
      sortable: 'custom',
      minWidth: 110
    },
    // {
    //     prop: 'nickname',
    //     label: '用户名',
    //     sortable: 'custom',
    //     minWidth: 110
    // },
    {
      prop: 'ip',
      label: 'IP地址',
      sortable: 'custom',
      minWidth: 110
    },
    {
      prop: 'device',
      label: '设备型号',
      sortable: 'custom',
      minWidth: 110
    },
    {
      prop: 'os',
      label: '操作系统',
      sortable: 'custom',
      minWidth: 110
    },
    {
      prop: 'browser',
      label: '浏览器',
      sortable: 'custom',
      minWidth: 110
    },
    {
      prop: 'loginType',
      label: '操作类型',
      sortable: 'custom',
      width: 130,
      slot: 'loginType',
      filters: [
        {
          text: '登录成功',
          value: '0'
        },
        {
          text: '登录失败',
          value: '1'
        },
        {
          text: '退出登录',
          value: '2'
        },
        {
          text: '刷新TOKEN',
          value: '3'
        }
      ],
      filterMultiple: false
    },
    {
      prop: 'comments',
      label: '备注',
      sortable: 'custom',
      minWidth: 110
    },
    {
      prop: 'createTime',
      label: '登录时间',
      sortable: 'custom',
      minWidth: 110
    }
  ]);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({
      ...where,
      ...orders,
      ...filters,
      page,
      limit
    });
  };

  /** 刷新表格 */
  const reload = (where) => {
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 导出数据 */
  const exportData = () => {
    const array = [
      [
        '账号',
        '用户名',
        'IP地址',
        '设备型号',
        '操作系统',
        '浏览器',
        '操作类型',
        '备注',
        '登录时间'
      ]
    ];
    // 请求查询全部接口
    const loading = EleMessage.loading('请求中..');
    tableRef.value?.fetch?.(({ where, orders, filters }) => {
      // listLoginRecords({...where, ...orders, ...filters})
      //     .then((data) => {
      //         loading.close();
      //         data.forEach((d) => {
      //             array.push([
      //                 d.username,
      //                 d.nickname,
      //                 d.ip,
      //                 d.device,
      //                 d.os,
      //                 d.browser,
      //                 ['登录成功', '登录失败', '退出登录', '刷新TOKEN'][d.loginType],
      //                 d.comments,
      //                 d.createTime
      //             ]);
      //         });
      //         writeFile(
      //             {
      //                 SheetNames: ['Sheet1'],
      //                 Sheets: {
      //                     Sheet1: utils.aoa_to_sheet(array)
      //                 }
      //             },
      //             '登录日志.xlsx'
      //         );
      //     })
      //     .catch((e) => {
      //         loading.close();
      //         EleMessage.error(e.message);
      //     });
    });
  };
</script>

<script>
  export default {
    name: 'SystemLoginRecord'
  };
</script>
