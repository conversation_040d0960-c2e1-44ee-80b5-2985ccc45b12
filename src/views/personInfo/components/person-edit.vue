<!-- 人员信息编辑弹窗 -->
<template>
  <ele-drawer
    :size="660"
    :title="isUpdate ? `修改${userTypeText}` : `新建${userTypeText}`"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px', paddingTop: '1px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="24">
        <el-col :span="12">
          <el-form-item label="工号" prop="xgh">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.xgh"
              placeholder="请输入工号"
              :disabled="isUpdate"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="姓名" prop="xm">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.xm"
              placeholder="请输入姓名"
            />
          </el-form-item> </el-col
        ><el-col :span="12">
          <el-form-item label="网名" prop="onlineName">
            <el-input
              clearable
              :maxlength="20"
              v-model="form.onlineName"
              placeholder="请输入网名"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="性别" prop="xb">
            <el-select
              clearable
              v-model="form.xb"
              placeholder="请选择性别"
              class="ele-fluid"
            >
              <el-option value="1" label="男" />
              <el-option value="2" label="女" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出生日期" prop="csrq">
            <el-date-picker
              v-model="form.csrq"
              value-format="YYYY-MM-DD"
              placeholder="请选择出生日期"
              class="ele-fluid"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="手机号" prop="sjh">
            <el-input
              clearable
              :maxlength="11"
              v-model="form.sjh"
              placeholder="请输入手机号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="身份证号" prop="zjhm">
            <el-input
              clearable
              :maxlength="18"
              v-model="form.zjhm"
              placeholder="请输入身份证号"
            />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="12">
          <el-form-item label="邮箱" prop="email">
            <el-input
              clearable
              :maxlength="100"
              v-model="form.email"
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>-->
      </el-row>

      <template v-if="userType === 'member'">
        <el-divider content-position="left">人员信息</el-divider>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="公司名称" prop="deptName">
              <common-select
                v-model="form.deptName"
                :queryUrl="queryBaseCom"
                codeType="company"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <el-divider content-position="left">其他信息</el-divider>
      <el-row :gutter="24">
        <el-col :span="24">
          <el-form-item label="备注" prop="bz">
            <el-input
              type="textarea"
              :rows="3"
              :maxlength="200"
              v-model="form.bz"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, watch, computed } from 'vue';
  import { EleMessage, emailReg, phoneReg } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import CommonSelect from '../../system/user/components/common-select.vue';
  import { operationPersonInfo, getPersonInfo } from '../api/index';
  import { getCodeData as queryBaseCom } from '@/views/base-code/common/api';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    /** 用户类型 */
    userType: {
      type: String,
      default: 'member'
    }
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 用户类型文本 */
  const userTypeText = computed(() => {
    return props.userType === 'member' ? '人员' : '用户';
  });

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    xgh: '',
    xm: '',
    onlineName: '',
    xb: '',
    csrq: '',
    sjh: '',
    zjhm: '',
    email: '',
    enabled: true,
    bz: '',
    deptName: ''
  });

  /** 表单验证规则 */
  const rules = reactive({
    xgh: [
      {
        required: true,
        message: '请输入工号',
        type: 'string',
        trigger: 'blur'
      }
    ],
    xm: [
      {
        required: true,
        message: '请输入姓名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    onlineName: [
      {
        required: true,
        message: '请输入网名',
        type: 'string',
        trigger: 'blur'
      }
    ],
    xb: [
      {
        required: true,
        message: '请选择性别',
        type: 'string',
        trigger: 'change'
      }
    ],
    sjh: [
      {
        pattern: phoneReg,
        message: '手机号格式不正确',
        type: 'string',
        trigger: 'blur'
      }
    ],
    email: [
      {
        pattern: emailReg,
        message: '邮箱格式不正确',
        type: 'string',
        trigger: 'blur'
      }
    ],
    zjhm: [
      {
        pattern:
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '身份证号格式不正确',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      operationPersonInfo(props.userType, form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          // 编辑模式，根据工号获取详情
          getPersonInfo(props.userType, props.data.xgh)
            .then((res) => {
              assignFields(res);
            })
            .catch(() => {
              assignFields({
                ...props.data
              });
            });
          isUpdate.value = true;
        } else {
          // 新建模式
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
