import request from '@/utils/request';

/**
 * 获取当前登录用户的个人信息/菜单/权限/角色
 */
export async function getUserInfo() {
  const res = await request.post('/auth/user');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 点击切换角色，获取当前用户角色list
 */
export async function checkSwitchRole() {
  const res = await request.post('/auth/userRoleList');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 切换角色
 * @param roleId
 * @returns {Promise<*>}
 */
export async function switchRole(roleId) {
  const res = await request.post('/auth/switch-role/' + roleId);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改当前登录用户的密码
 */
export async function updatePassword(data) {
  const res = await request.put('/auth/password', data);
  if (res.data.code === 0) {
    return res.data.message ?? '修改成功';
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 修改当前登录用户的个人信息
 */
export async function updateUserInfo(data) {
  const res = await request.put('/auth/user', data);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
