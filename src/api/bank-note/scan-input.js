import request from '@/utils/request';

/**
 * 根据条形码扫描查询钱币信息
 * @param {Object} params 查询参数
 * @param {string} params.nummber 条形码
 * @param {string} params.gm 钱币类型 (01=纸币, 02=古钱币, 03=机制币, 04=银锭)
 * @param {number} params.addType 追加类型 (0=无, 1=追加该送评单所有订单, 2=追加该鉴定单所有订单)
 * @param {Array} params.serialNumber 已存在的条形码数组
 */
export function queryCoinsByScan(params) {
  return request({
    url: '/banknote/scan/query',
    method: 'post',
    data: params
  });
}

/**
 * 批量更新钱币信息
 * @param {Array} coinList 钱币列表
 */
export function batchUpdateCoins(coinList) {
  return request({
    url: '/banknote/scan/batchUpdate',
    method: 'post',
    data: coinList
  });
}

/**
 * 根据送评单号查询钱币列表
 * @param {string} sendnum 送评单号
 */
export function queryCoinsBySendnum(sendnum) {
  return request({
    url: '/banknote/scan/getBySendnum',
    method: 'get',
    params: { sendnum }
  });
}

/**
 * 根据钱币ID删除钱币
 * @param {Array} ids 钱币ID数组
 */
export function removeCoins(ids) {
  return request({
    url: '/banknote/scan/remove',
    method: 'post',
    data: ids
  });
}

/**
 * 获取钱币类型字典
 */
export function getCoinTypes() {
  return request({
    url: '/banknote/scan/coinTypes',
    method: 'get'
  });
}

/**
 * 获取真伪选项字典
 */
export function getResultOptions() {
  return request({
    url: '/banknote/scan/resultOptions',
    method: 'get'
  });
}

/**
 * 获取盒子类型字典
 */
export function getBoxTypes() {
  return request({
    url: '/banknote/scan/boxTypes',
    method: 'get'
  });
}

/**
 * 根据条形码获取钱币详情
 * @param {string} nummber 条形码
 */
export function getCoinDetail(nummber) {
  return request({
    url: '/banknote/scan/detail',
    method: 'get',
    params: { nummber }
  });
}

/**
 * 验证条形码是否存在
 * @param {string} nummber 条形码
 */
export function validateBarcode(nummber) {
  return request({
    url: '/banknote/scan/validate',
    method: 'get',
    params: { nummber }
  });
}

/**
 * 导出扫码录入数据
 * @param {Object} params 导出参数
 */
export function exportScanData(params) {
  return request({
    url: '/banknote/scan/export',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
}
