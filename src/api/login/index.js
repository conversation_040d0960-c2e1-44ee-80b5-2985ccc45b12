import request from '@/utils/request';
import { setToken } from '@/utils/token-util';
import { setCurrentRole } from '@/utils/current-role-util';

/**
 * 登录
 */
export async function login(data) {
  const res = await request.post('/login', data);
  if (res.data.code === 0) {
    setToken(res.data.data?.access_token, data.remember);
    setCurrentRole(res.data.data?.userInfo);
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取验证码
 */
export async function getCaptcha() {
  const res = await request.get('/captcha');
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 退出
 */
export async function checkLogout() {
  const res = await request.get('/logout');
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
