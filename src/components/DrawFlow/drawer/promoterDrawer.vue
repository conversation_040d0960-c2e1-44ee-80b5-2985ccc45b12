<!-- 发起人字段设置-->
<template>
  <ele-drawer
    title="发起人"
    v-model="visible"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    style="max-width: 100%"
    :body-style="{ paddingBottom: '8px' }"
    size="550px"
    :before-close="savePromoter"
  >
    <ele-page>
      <!--      <div class="demo-drawer__content">-->
      <!--        <div class="promoter_content drawer_content">-->
      <!--          <p>{{  }}</p>-->
      <!--          <el-button type="primary" @click="addPromoter">添加/修改发起人</el-button>-->
      <!--        </div>-->
      <el-form
        status-icon
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="auto"
        size="small"
        @submit.prevent=""
      >
        <el-form-item
          :label="$func.arrToStr(flowPermission) || '所有人'"
          prop="selectorDatas"
        >
          <div
            style="margin-bottom: 6px; cursor: pointer"
            @click="openPerSelector()"
          >
            <ele-text
              type="primary"
              style="float: left"
              @click="openPerSelector()"
            >
              <el-icon>
                <CirclePlus />
              </el-icon>
              添加/修改发起人 </ele-text
            >&nbsp;
          </div>
          <el-input
            :rows="4"
            type="textarea"
            readonly
            v-model="qddxData"
            :placeholder="'请选择' + $func.arrToStr(flowPermission) || '所有人'"
          />
        </el-form-item>
      </el-form>
      <!--        <employees-dialog-->
      <!--            :isDepartment="true"-->
      <!--            v-model:visible="promoterVisible"-->
      <!--            v-model:data="checkedList"-->
      <!--            @change="surePromoter"-->
      <!--        />-->
      <!--      </div>-->
    </ele-page>
    <template #footer>
      <el-button @click="closeDrawer">取消</el-button>
      <el-button type="primary" :loading="loading" @click="savePromoter">
        保存
      </el-button>
    </template>
    <PerSelector
      v-model="showPerSelector"
      ref="treeTransferRef"
      node-key="id"
      :perSelectedData="perSelectedData"
      @done="onDoneSelector"
    />
  </ele-drawer>
</template>
<script setup>
  import PerSelector from '@/components/PerSelector/index.vue';
  import $func from '@/plugins/preload';
  import { mapMutations, mapState } from '@/plugins/lib';
  import { computed, ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data.js';

  let flowPermission = ref([]);
  let promoterVisible = ref(false);
  let checkedList = ref([]);
  let { promoterDrawer, flowPermission1 } = mapState();
  let visible = computed({
    get() {
      return promoterDrawer.value;
    },
    set() {
      closeDrawer();
    }
  });
  watch(flowPermission1, (val) => {
    flowPermission.value = val.value;
  });

  let { setPromoter, setFlowPermission } = mapMutations();

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({});

  const showPerSelector = ref(false);
  const perSelectedData = ref([]);

  const openPerSelector = () => {
    showPerSelector.value = true;
  };

  const onDoneSelector = (data) => {
    perSelectedData.value = data;
    form.value.selectorDatas = data;
  };

  const addPromoter = () => {
    checkedList.value = flowPermission.value;
    promoterVisible.value = true;
  };
  const surePromoter = (data) => {
    flowPermission.value = data;
    promoterVisible.value = false;
  };
  const savePromoter = () => {
    setFlowPermission({
      value: flowPermission.value,
      flag: true,
      id: flowPermission1.value.id
    });
    closeDrawer();
  };
  const closeDrawer = () => {
    setPromoter(false);
  };
</script>
