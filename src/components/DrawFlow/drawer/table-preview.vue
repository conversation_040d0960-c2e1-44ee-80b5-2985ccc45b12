<template>
  <ele-page flex-table>
    <ele-card
      :body-style="{ height: 'auto', padding: '5px 0!important' }"
      :header-style="{ padding: '5px 0!important' }"
    >
      <template #header>
        {{ dataName }}
        <ele-tooltip
          v-if="approverType === 'zizhu'"
          placement="top-start"
          :popperOptions="{
            modifiers: [{ name: 'offset', options: { offset: [-8, 10] } }]
          }"
        >
          <el-icon
            :size="18"
            style="align-self: center; margin-right: 4px; cursor: help"
          >
            <QuestionCircleOutlined style="opacity: 0.6" />
          </el-icon>
          <template #content>
            <div>英文名下拉组件的选项数据：</div>
            <div
              >c1...c开始的是系统内置短文本选项，l1...l开始的是系统内置长文本选项</div
            >
          </template>
        </ele-tooltip>
      </template>
      <template #extra>
        <!--      <el-link v-if="isCanShowAddBtn(approverType)" :icon="PlusOutlined" type="primary" :underline="false" @click="add">-->
        <!--        新增-->
        <!--      </el-link>-->
        <el-button
          v-if="isCanShowAddBtn(approverType)"
          type="primary"
          size="small"
          class="ele-btn-icon"
          :icon="PlusOutlined"
          @click="add"
        >
          新建
        </el-button>
      </template>
      <!--    approverType=={{ approverType }}<br/>-->
      <!--    shExtendsFieldEnList=={{ shExtendsFieldEnList }}<br/>-->
      <!--    shExtendsFieldEn=={{ shExtendsFieldEn }}<br/>-->
      <!--    form.users=={{ form.users }}<br/>-->
      <!--    initTableHeader=={{ initTableHeader }}<br/>-->
      <el-form
        ref="formRef"
        size="small"
        :model="form"
        label-width="0px"
        @submit.prevent=""
      >
        <div>
          <ele-table
            size="small"
            border
            style="min-width: 580px; table-layout: fixed"
          >
            <thead>
              <tr>
                <th
                  style="position: sticky; left: 0; z-index: 1; width: 38px"
                ></th>
                <th
                  v-for="header in initTableHeader"
                  :style="
                    header?.hide
                      ? 'display: none;'
                      : 'width:' + header.minWidth + 'px'
                  "
                >
                  {{ header.label }}
                </th>
                <th
                  v-if="approverType !== 'nodeStatus'"
                  :style="{
                    textAlign: 'center',
                    position: 'sticky',
                    right: 0,
                    zIndex: 98,
                    width: approverType === 'zizhu' ? '150px' : '83px'
                  }"
                >
                  操作
                </th>
              </tr>
            </thead>
            <template v-if="approverType === 'nodeStatus'">
              <tr v-for="(row, index) in form.users" :key="row.key">
                <td
                  :style="{
                    textAlign: 'center',
                    position: 'sticky',
                    left: 0,
                    zIndex: 98
                  }"
                >
                  {{ index + 1 }}
                </td>
                <td
                  v-for="item in initTableHeader"
                  :style="item?.hide ? 'display: none;' : ''"
                >
                  <el-form-item
                    v-if="item.type === 'nodeStatus'"
                    label=""
                    class="form-error-popper"
                    style="margin-bottom: 0 !important"
                  >
                    <div class="editable-cell-text">{{ row[item.prop] }}</div>
                  </el-form-item>
                  <el-form-item
                    v-if="item.type === 'glsj'"
                    label=""
                    class="form-error-popper"
                    style="margin-bottom: 0 !important"
                  >
                    <dict-data
                      type="select"
                      filterable
                      :disabled="row['fieldEn'] === 'result' ? true : false"
                      code="listDicCode"
                      :model-value="row[item.prop]"
                      @update:modelValue="
                        (value) => updateValue(index, item.prop, value)
                      "
                    />
                  </el-form-item>
                  <TableFormItem
                    v-if="row.isEdit"
                    :item="item"
                    :index="index"
                    :model="row"
                    @updateValue="
                      (value) => updateValue(index, item.prop, value)
                    "
                  >
                    <template
                      v-for="name in Object.keys($slots).filter(
                        (k) =>
                          ![
                            'default',
                            'footer',
                            'topExtra',
                            'bottomExtra'
                          ].includes(k)
                      )"
                      #[name]="slotProps"
                    >
                      <slot :name="name" v-bind="slotProps || {}"></slot>
                    </template>
                  </TableFormItem>
                  <div v-else class="editable-cell-text">{{
                    row[item.prop]
                  }}</div>
                </td>
              </tr>
            </template>
            <template v-else>
              <vue-draggable
                tag="tbody"
                item-key="fieldEn"
                v-model="form.users"
                handle=".sort-handle"
                :animation="300"
                :set-data="() => void 0"
                :force-fallback="true"
                @update="updateSort"
              >
                <template #item="{ element, index }">
                  <tr :key="element.key">
                    <td
                      :style="{
                        textAlign: 'center',
                        paddingLeft: 0,
                        paddingRight: 0,
                        position: 'sticky',
                        left: 0,
                        zIndex: 1,
                        width: '38px'
                      }"
                    >
                      <ele-text
                        :icon="DragOutlined"
                        :icon-style="{ transform: 'scale(1.15)' }"
                        type="placeholder"
                        class="sort-handle"
                      />
                    </td>
                    <td
                      v-for="item in initTableHeader"
                      :style="item?.hide ? 'display: none;' : ''"
                    >
                      <el-form-item
                        v-if="item.type === 'conditionValue'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <dict-data
                          type="select"
                          filterable
                          :code="element['loadDataType']"
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'extendsField'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <template
                          v-if="
                            approverType === 'zizhu' &&
                            item.prop === 'fieldEn' &&
                            element.isEdit
                          "
                        >
                          <el-select
                            v-model="element[item.prop]"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in zizhuFieldEn"
                              :key="item"
                              :label="item"
                              :value="item"
                              @click="selectChange()"
                            />
                          </el-select>
                        </template>
                        <div v-else class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'regExpression'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <dict-data
                          v-if="element.isEdit"
                          type="select"
                          filterable
                          :disabled="
                            ['input', 'inputNumber'].includes(
                              element['controlType']
                            )
                              ? false
                              : true
                          "
                          code="RegExp"
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                        <div v-else class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <template v-else-if="item.type === 'loadDataType'">
                        <el-form-item
                          v-if="approverType === 'zizhu'"
                          label=""
                          class="form-error-popper"
                          style="margin-bottom: 0 !important"
                        >
                          <dict-data
                            v-if="element.isEdit"
                            type="select"
                            filterable
                            code="listDicCode"
                            :model-value="element[item.prop]"
                            @update:modelValue="
                              (value) => updateValue(index, item.prop, value)
                            "
                          />
                          <div v-else class="editable-cell-text">{{
                            element[item.prop]
                          }}</div>
                        </el-form-item>
                        <el-form-item
                          v-else
                          label=""
                          class="form-error-popper"
                          style="margin-bottom: 0 !important"
                        >
                          <dict-data
                            v-if="element.isEdit"
                            type="select"
                            filterable
                            :disabled="
                              [
                                'select',
                                'multipleSelect',
                                'radio',
                                'radioButton',
                                'checkbox',
                                'checkboxButton'
                              ].includes(element['controlType'])
                                ? false
                                : true
                            "
                            code="listDicCode"
                            :model-value="element[item.prop]"
                            @update:modelValue="
                              (value) => updateValue(index, item.prop, value)
                            "
                          />
                          <div v-else class="editable-cell-text">{{
                            element[item.prop]
                          }}</div>
                        </el-form-item>
                      </template>
                      <el-form-item
                        v-else-if="item.type === 'loadDataUrlId'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <dict-data
                          v-if="element.isEdit"
                          type="select"
                          filterable
                          :disabled="
                            ['other', 'studentXsda'].includes(
                              element['loadDataType']
                            )
                              ? false
                              : true
                          "
                          :code="
                            element['loadDataType'] === 'other'
                              ? 'listDicUrl'
                              : 'studentXsda'
                          "
                          :model-value="element[item.prop]"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                        />
                        <div v-else class="editable-cell-text">{{
                          element[item.prop]
                        }}</div>
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'defaultVal'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <el-input
                          :model-value="element[item.prop]"
                          placeholder="请输入"
                          @update:modelValue="
                            (value) => updateValue(index, item.prop, value)
                          "
                          :disabled="
                            ['studentXsda'].includes(element['loadDataType'])
                              ? true
                              : false
                          "
                        />
                      </el-form-item>
                      <el-form-item
                        v-else-if="item.type === 'nodeStatus'"
                        label=""
                        class="form-error-popper"
                        style="margin-bottom: 0 !important"
                      >
                        <el-input
                          readonly
                          :model-value="element[item.prop]"
                          placeholder="请输入"
                        />
                      </el-form-item>
                      <TableFormItem
                        v-else-if="element.isEdit"
                        :item="item"
                        :index="index"
                        :model="element"
                        @updateValue="
                          (value) => updateValue(index, item.prop, value)
                        "
                      >
                        <template
                          v-for="name in Object.keys($slots).filter(
                            (k) =>
                              ![
                                'default',
                                'footer',
                                'topExtra',
                                'bottomExtra'
                              ].includes(k)
                          )"
                          #[name]="slotProps"
                        >
                          <slot :name="name" v-bind="slotProps || {}"></slot>
                        </template>
                      </TableFormItem>
                      <div v-else class="editable-cell-text">{{
                        element[item.prop]
                      }}</div>
                    </td>
                    <td
                      :style="{
                        textAlign: approverType == 'zizhu' ? 'left' : 'center',
                        position: 'sticky',
                        right: 0,
                        zIndex: 98
                      }"
                    >
                      <div style="display: inline; align-items: center">
                        <el-link
                          v-if="approverType === 'zizhu' && element.isEdit"
                          type="success"
                          :underline="false"
                          @click="done(element)"
                        >
                          完成
                        </el-link>
                        <!--                      <el-link v-else type="primary"-->
                        <!--                               :underline="false"-->
                        <!--                               @click="edit(element)">-->
                        <!--                        编辑-->
                        <!--                      </el-link>-->
                        <el-divider
                          v-if="approverType === 'zizhu'"
                          direction="vertical"
                          style="margin: 0 4px"
                        />
                        <el-link
                          type="danger"
                          :underline="false"
                          @click="remove(element, index)"
                        >
                          删除
                        </el-link>
                        <el-divider
                          v-if="
                            approverType === 'zizhu' &&
                            ['select', 'radio', 'radioButton'].includes(
                              element['controlType']
                            )
                          "
                          direction="vertical"
                          style="margin: 0 4px"
                        />
                        <el-link
                          v-if="
                            approverType === 'zizhu' &&
                            ['select', 'radio', 'radioButton'].includes(
                              element['controlType']
                            )
                          "
                          type="primary"
                          :underline="false"
                          @click="handleSetfield(element)"
                        >
                          附加条件
                        </el-link>
                      </div>
                    </td>
                  </tr>
                </template>
              </vue-draggable>
            </template>
            <tr v-if="!form.users || !form.users.length">
              <td
                :colspan="initTableHeader.length + 2"
                style="text-align: center"
              >
                <ele-text style="padding: 4px 0" type="secondary">
                  暂无数据
                </ele-text>
              </td>
            </tr>
          </ele-table>
        </div>
      </el-form>
    </ele-card>
  </ele-page>
</template>

<script setup>
  import { computed, nextTick, ref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { DragOutlined, PlusOutlined } from '@/components/icons';
  import {
    arrayTypes,
    regionsArrayTypes,
    selectTypes,
    stringTypes,
    uploadTypes
  } from '@/components/ProForm/util';
  import TableFormItem from '@/components/ProForm/components/table-form-item.vue';
  import { useFormData } from '@/utils/use-form-data';
  import {
    getFormExtendsField,
    listStatusInit
  } from '@/views/dingding-flow/api/index.js';
  import { useUserStore } from '@/store/modules/user.js';
  import { storeToRefs } from 'pinia';
  import { useDictData } from '@/utils/use-dict-data.js';
  import VueDraggable from 'vuedraggable';
  import { groupArr, insertAtIndex, removeDuplicates } from '@/utils/common.js';
  import {
    getFormTemplateField,
    getFormTemplateFieldList,
    operation,
    removes
  } from '@/views/zizhu/api/form-template-field-index.js';
  import { ElMessageBox } from 'element-plus';
  import { QuestionCircleOutlined } from '@/components/icons/index.js';

  const props = defineProps({
    dataName: String,
    approverType: String,
    approverId: String,
    currentNode: Object, //资助申请表单，左侧组信息
    selectorData: Object,
    approverData: Array,
    RandomString: String
  });

  // //获取浏览器参数
  // const {params, path, query} = unref(currentRoute);
  // const userType = path.split("/")[4];

  // 定义emit
  const emits = defineEmits(['onDoneGroup', 'child-event', 'FieldLinkEvent']);

  /** 请求状态 */
  const loading = ref(true);

  /** 表单项 */
  const initItems = ref([]);

  /** 表头 */
  const initTableHeader = ref([]);

  /** zizhu表数据 */
  const initTableData = ref([]);

  /** 资助申请字段英文下拉框数据 */
  const allApproverData = ref([]);
  const zizhuFieldEn = ref([]);
  const zizhuFieldEnList = ref([]);
  /** 资助审核字段英文 */
  const shExtendsFieldEn = ref([]);
  const shExtendsFieldEnList = ref([]);

  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);
  let dicCodes = ['listDicUrl'];
  dicCodes.forEach((code) => {
    if (dicts.value[code]) {
      return;
    } else {
      useDictData(code, {});
    }
  });

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    users: []
  });

  /** 删除 */
  const remove = (_row, i) => {
    if (props.approverType === 'zizhu') {
      if (_row?.id) {
        removeOpt([_row], i);
      } else {
        form.users.splice(i, 1);
      }
      selectChange();
      // 找到删除的对象索引
      let index = initTableData.value.findIndex(
        (item) => item.fieldEn === _row.fieldEn
      );
      // 确保对象存在于数组中
      if (index !== -1) {
        // 删除对象
        initTableData.value.splice(index, 1);
      }
      emits('onDoneGroup', initTableData.value);
    } else {
      form.users.splice(i, 1);
      if (props.approverType.includes('workflow_')) {
        selectChangeSh();
      }
      let rData = {
        fieldEn: props.approverType,
        approverId: props.approverId,
        selectorData: props.selectorData,
        workflowNodeForm: form.users
      };
      emits('onDoneGroup', rData);
    }
  };
  /**
   * 资助可配置字段列表
   */
  const queryFormTemplateFieldList = () => {
    getFormTemplateFieldList({ listFlag: false })
      .then((list) => {
        if (list) {
          zizhuFieldEn.value = list;
          zizhuFieldEnList.value = list;
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };
  /**
   * 工作流获取审核设置扩展字段列表
   */
  const queryFormExtendsField = () => {
    getFormExtendsField()
      .then((list) => {
        if (list) {
          shExtendsFieldEn.value = list;
          shExtendsFieldEnList.value = list;
          if (props.approverType.includes('workflow_')) {
            selectChangeSh();
          }
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  /** 获取表单字段 */
  const initModel = computed(() => {
    const fieldFormResult = [];
    initTableHeader.value.forEach((item) => {
      if (item.type) {
        fieldFormResult[item.prop] = null;
        if (
          uploadTypes.includes(item.type) ||
          regionsArrayTypes.includes(item.type) ||
          arrayTypes.includes(item.type)
        ) {
          fieldFormResult[item.prop] = [];
        }
        if (
          stringTypes.includes(item.type) ||
          selectTypes.includes(item.type)
        ) {
          fieldFormResult[item.prop] = '';
        }
      }
    });
    return fieldFormResult;
  });

  /** 关联关系设置 */
  const handleSetfield = async (row) => {
    console.log(row);
    let newObj = {
      infoType: row.infoType,
      projectId: row.projectId,
      groupId: row.groupId,
      fieldId: row.id,
      fieldEn: row.fieldEn,
      controlType: row.controlType,
      loadDataType: row.loadDataType,
      loadDataUrl: row.loadDataUrl
    };
    // currentFieldLink.value = newObj
    // showFieldLink.value = true;
    emits('FieldLinkEvent', newObj);
  };

  // 更新次序
  const updateSort = (evt) => {
    evt.preventDefault();
    if (props.approverType == 'zizhu') {
      const tableDataGroup = groupArr(initTableData.value, 'groupId');
      let newTableData = [];
      tableDataGroup.forEach((item) => {
        if (item.type === props.approverId) {
          form.users.forEach((value, index) => {
            newTableData.push(value);
          });
        } else {
          item.list.forEach((value, index) => {
            newTableData.push(value);
          });
        }
      });
      const sortedButtonList = newTableData.map(function (value, index) {
        value.sort = index + 1;
        return value;
      });
      emits('onDoneGroup', sortedButtonList);
    } else {
      const sortedButtonList = form.users.map(function (value, index) {
        value.sort = index + 1;
        return value;
      });
      let rData = {
        fieldEn: props.approverType,
        approverId: props.approverId,
        selectorData: props.selectorData,
        workflowNodeForm: sortedButtonList
      };
      emits('onDoneGroup', rData);
    }
  };

  /** 添加 */
  const add = () => {
    let obj = {
      ...initModel.value,
      isEdit: true
    };
    let index = 1;
    let arrKeyIndex = [];
    if (props.approverType === 'zizhu') {
      // 使用map()获取每个keyIndex，然后使用 Math.max 求最大值
      arrKeyIndex = initTableData.value.map((e) => e.sort);
      obj.key = Date.now() + '-' + initTableData.value.length;
    } else {
      // 使用map()获取每个keyIndex，然后使用 Math.max 求最大值
      arrKeyIndex = form.users.map((e) => e.sort);
      obj.key = Date.now() + '-' + form.users.length;
    }
    if (arrKeyIndex.length > 0) {
      const maxValue = Math.max(...arrKeyIndex);
      index = maxValue + 1;
    }
    obj.sort = index;
    if (props.approverType === 'zizhu') {
      obj.projectId = props.currentNode.projectId;
      obj.infoType = props.currentNode.infoType;
      obj.type = props.currentNode.type;
      obj.year = props.currentNode.year;
      if (zizhuFieldEn.value.length > 0) {
        obj.fieldEn = zizhuFieldEn.value[0];
        obj.groupId = props.approverId;
        initTableData.value.push(obj);
        form.users.push(obj);
        selectChange();
      } else {
        EleMessage.error(`资助申请字段配置，英文名扩展字段已用完，请先添加`);
      }

      formRef.value?.validate?.((valid, obj) => {
        if (!valid) {
          // const errors = obj ? Object.keys(obj).length : 0;
          // EleMessage.error(`有 ${errors} 项校验不通过1`);
          return;
        }
      });
    } else if (props.approverType.includes('workflow_')) {
      if (shExtendsFieldEn.value.length > 0) {
        obj.fieldEn = shExtendsFieldEn.value[0];
        form.users.push(obj);
        console.log(obj);
        selectChangeSh();
        // initTableData.value.push(obj);
      } else {
        EleMessage.error(`工作流审核配置，英文名扩展字段已用完，请先添加`);
      }
    } else {
      obj.fieldEn = 'bz' + index;
      form.users.push(obj);
    }
  };

  /** 更新值 */
  const updateValue = (index, prop, value) => {
    console.log(index, prop, value);
    if (prop === 'controlType') {
      form.users[index]['regExpression'] = null;
      form.users[index]['loadDataType'] = null;
      form.users[index]['loadDataUrlId'] = null;
    }
    if (prop === 'loadDataUrlId') {
      if (form.users[index]['loadDataType'] === 'studentXsda') {
        form.users[index].defaultValField = value;
      } else {
        /** 加载数据URL 更新listDicUrl*/
        let dicUrlData = dicts.value['listDicUrl'];
        if (dicUrlData) {
          dicUrlData.forEach((item) => {
            if (item.id === value) {
              form.users[index].valueField = item.valueField;
              form.users[index].textField = item.textField;
              form.users[index].loadDataUrl = item.url;
            }
          });
        }
      }
    }
    form.users[index][prop] = value;
    if (props.approverType === 'zizhu') {
      emits('onDoneGroup', initTableData.value);
    } else {
      let rData = {
        fieldEn: props.approverType,
        approverId: props.approverId,
        selectorData:
          props.approverType === 'nodeStatus'
            ? props.approverData
            : props?.selectorData,
        workflowNodeForm: form.users
      };
      emits('onDoneGroup', rData);
    }
  };

  /** 编辑 */
  const edit = (row) => {
    row.isEdit = true;
  };

  /** 完成 */
  const done = (row) => {
    for (let i in row) {
      if (row[i] === true || row[i] === false)
        row[i] = row[i] === true ? '是' : '否';
    }
    submit(row);
  };

  /** 删除 */
  const removeOpt = (rows, index) => {
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.fieldZh).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removes(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            form.users.splice(index, 1);
            emits('child-event', props.currentNode);
            // reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 表单提交 */
  const submit = (row) => {
    formRef.value?.validate?.((valid, obj) => {
      if (!valid) {
        const errors = obj ? Object.keys(obj).length : 0;
        EleMessage.error(`有 ${errors} 项校验不通过`);
        return;
      } else {
        loading.value = true;
        let objArr = [];
        objArr.push(row);
        operation(objArr)
          .then((msg) => {
            loading.value = false;
            EleMessage.success(msg);
            row.isEdit = true;
            emits('child-event', props.currentNode);
          })
          .catch((e) => {
            loading.value = false;
            EleMessage.error(e.message);
          });
      }
    });
  };

  /** table组件是否显示新增按钮 */
  const isCanShowAddBtn = (approverType) => {
    let result = true;
    if (approverType === 'nodeStatus') result = false;
    if (
      approverType.includes('workflow_') &&
      shExtendsFieldEn.value.length === 0
    )
      result = false;
    if (approverType === 'zizhu' && zizhuFieldEn.value.length === 0)
      result = false;
    return result;
  };
  /** 资助申请字段设置英文更新选中数据 */
  const selectChange = () => {
    let newData = [...allApproverData.value, ...form.users];
    if (newData && newData.length > 0) {
      let arraySomeMap = newData.map((item) => {
        return item.fieldEn;
      });
      zizhuFieldEn.value = removeDuplicates(
        zizhuFieldEnList.value,
        arraySomeMap
      );
    }
  };

  /** 工作流审核英文下拉更新选中数据 */
  const selectChangeSh = () => {
    console.log(shExtendsFieldEnList.value);
    let newData = [...shExtendsFieldEnList.value, ...form.users];
    console.log(newData);
    if (newData && newData.length > 0) {
      let arraySomeMap = newData.map((item) => {
        return item.fieldEn;
      });
      console.log(shExtendsFieldEnList.value, arraySomeMap);
      shExtendsFieldEn.value = removeDuplicates(
        shExtendsFieldEnList.value,
        arraySomeMap
      );
      console.log(shExtendsFieldEn.value);
    }
  };

  /** 查询组的申请字段信息 */
  const queryFormTemplateField = () => {
    loading.value = true;
    getFormTemplateField({
      projectId: props.currentNode.projectId,
      type: props.currentNode.type,
      infoType: ['apply', 'list']
    })
      .then((list) => {
        loading.value = false;
        allApproverData.value = list ?? [];
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  watch(
    () => props.approverType,
    (approverType) => {
      if (approverType) {
        if (approverType === 'nodeStatus') {
          initTableHeader.value = [
            {
              prop: 'fieldEn',
              label: '英文名(系统内置不支持编辑)',
              type: 'nodeStatus',
              hide: true
            },
            {
              prop: 'fieldZh',
              label: '中文名',
              type: 'input'
            },
            {
              prop: 'glsj',
              label: '关联事件',
              type: 'glsj'
            }
          ];
          listStatusInit()
            .then((data) => {
              if (JSON.stringify(props.approverData) !== '{}') {
                //页面值回显
                data.forEach((item) => {
                  if (item.fieldEn === 'result') {
                    item.fieldZh = props.approverData['fieldZh'];
                  } else {
                    item.fieldZh = props.approverData[item.fieldEn];
                  }
                });
              }
              form.users = data;
              let rData = {
                fieldEn: props.approverType,
                approverId: props.approverType,
                selectorData: props.approverData,
                workflowNodeForm: form.users
              };
              emits('onDoneGroup', rData);
            })
            .catch((e) => {
              EleMessage.error(e.message);
            });
        } else if (approverType === 'nodeCondition') {
          //工作流条件设置
          initTableHeader.value = [
            {
              prop: 'fieldEn',
              label: '英文名',
              minWidth: 66,
              editable: false,
              type: 'extendsField',
              required: true
            },
            {
              prop: 'fieldZh',
              label: '中文名',
              editable: true,
              type: 'extendsField',
              required: true
            },
            {
              prop: 'value',
              label: '流转条件值',
              editable: true,
              type: 'conditionValue',
              required: true
            },
            {
              prop: 'condition',
              label: '逻辑条件',
              editable: true,
              type: 'select',
              options: [
                { label: '等于', value: 'EQ' },
                { label: '大于', value: 'GT' },
                { label: '小于', value: 'LT' }
              ]
            }
          ];
        } else {
          let baseFields = [
            {
              prop: 'fieldEn',
              label: '英文名',
              type: 'extendsField',
              minWidth: 70,
              required: true,
              editable: true
              // hide: true,
            },
            {
              prop: 'fieldZh',
              label: '中文名',
              editable: true,
              type: 'input',
              required: true
            },
            {
              prop: 'required',
              label: '是否必填',
              required: true,
              type: 'switchSf'
              // props: {
              //   code: 'yesNo',
              //   dicQueryParams: {
              //     getValType: 'name'
              //   },
              //   filterable: true
              // }
            },
            {
              prop: 'controlType',
              label: '字段类型',
              required: true,
              editable: true,
              type: 'dictSelect',
              props: { code: 'controlType', filterable: true }
            },
            {
              prop: 'tipText',
              label: '提示文本',
              editable: true,
              type: 'input'
            },
            {
              prop: 'regExpression',
              label: '正则验证表达式',
              editable: true,
              type: 'regExpression'
            },
            {
              prop: 'loadDataType',
              label: '加载数据',
              type: 'loadDataType'
            },
            {
              prop: 'loadDataUrlId',
              label: '加载数据URL',
              type: 'loadDataUrlId'
            }
          ];
          let zizhuFields = [
            {
              prop: 'defaultVal',
              label: '默认值',
              editable: true,
              type: 'defaultVal'
            }
          ];
          if (approverType === 'zizhu') {
            queryFormTemplateFieldList();
            initTableHeader.value = baseFields.concat(zizhuFields);
            // 在下标为7的位置插入培养层次
            // insertAtIndex(initTableHeader.value, {
            //   prop: 'fztype',
            //   label: '所属分组',
            //   editable: true,
            //   type: 'input',
            // }, 1);
            insertAtIndex(
              initTableHeader.value,
              {
                prop: 'editFlag',
                label: '是否可编辑',
                editable: true,
                type: 'switchSf'
              },
              3
            );
            insertAtIndex(
              initTableHeader.value,
              {
                prop: 'showFlag',
                label: '是否显示',
                editable: true,
                type: 'switchSf'
              },
              4
            );
          } else if (approverType.includes('workflow_')) {
            queryFormExtendsField();
            initTableHeader.value = baseFields;
          } else {
            initTableHeader.value = baseFields;
          }
          nextTick(() => {
            if (props.approverData) {
              //页面值回显
              props.approverData.forEach((item) => {
                item.isEdit = true;
              });
              form.users = props.approverData;

              if (approverType !== 'zizhu') {
                let rData = {
                  fieldEn: props.approverType,
                  approverId: props.approverId,
                  selectorData: props.selectorData,
                  workflowNodeForm: form.users
                };
                emits('onDoneGroup', rData);
              }
            } else {
              //资助申请表单不默认添加行数据
              if (props.approverType !== 'zizhu') add();
            }
          });
        }
      }
    },
    {
      immediate: true
    }
  );

  watch(
    () => props.RandomString,
    (RandomString) => {
      if (RandomString) {
        console.log('RandomString=====', props.approverData);
        //工作流条件参数页面回显
        if (props.approverType === 'nodeCondition') {
          let newData = [];
          if (props.approverData) {
            //页面值回显
            props.approverData.forEach((item, index) => {
              let obj = {
                fieldId: item.id,
                fieldEn: item.fieldEn,
                fieldZh: item.fieldZh,
                loadDataType: item.loadDataType,
                value: item?.value,
                condition: item?.condition,
                sort: index + 1,
                isEdit: true
              };
              newData.push(obj);
            });
          }
          form.users = newData;
          let rData = {
            fieldEn: props.approverType,
            approverId: props.approverId,
            workflowNodeForm: form.users
          };
          emits('onDoneGroup', rData);
        } else {
          if (props.approverType === 'zizhu') queryFormTemplateField();
          setTimeout(() => {
            if (props.approverData.length > 0) {
              //页面值回显
              props.approverData.forEach((item) => {
                item.isEdit = true;
              });
              if (props.approverType === 'zizhu') {
                selectChange();
                initTableData.value = props.approverData;
                form.users = initTableData.value.filter(
                  (obj) => obj.groupId === props.approverId
                );
                emits('onDoneGroup', initTableData.value);
              }
              if (props.approverType.includes('workflow_')) {
                form.users = props.approverData;
                selectChangeSh();
              }
            }
          }, 100);
        }
      }
    },
    {
      immediate: true
    }
  );

  defineExpose({ formRef });
</script>

<style lang="scss" scoped>
  /* 表单验证气泡形式 */
  .form-error-popper.el-form-item > :deep(.el-form-item__content) {
    & > .el-form-item__error {
      position: absolute;
      left: 0;
      top: calc(0px - 100% - 6px);
      width: max-content;
      color: #fff;
      font-size: 12px;
      background: var(--el-color-danger);
      transition: all 0.2s;
      padding: 10px;
      border-radius: 4px;
      z-index: 999;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;

      &:after {
        content: '';
        border: 6px solid transparent;
        border-top-color: var(--el-color-danger);
        position: absolute;
        left: 12px;
        bottom: -11px;
      }
    }

    &:hover > .el-form-item__error {
      opacity: 1;
      visibility: visible;
      pointer-events: all;
    }
  }

  .task-table {
    table-layout: fixed;
    min-width: 300px;

    .sort-handle {
      cursor: move;
    }

    .el-tag {
      width: 20px;
      height: 20px;
      border-radius: 50%;
    }

    td,
    th {
      box-sizing: border-box;
    }

    tr.sortable-ghost {
      opacity: 0;
    }

    tr.sortable-fallback {
      opacity: 1 !important;
      display: table !important;
      table-layout: fixed !important;

      td {
        background: var(--el-color-primary-light-8);
      }
    }
  }
</style>
