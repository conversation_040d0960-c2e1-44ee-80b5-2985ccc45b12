<!-- 审批人设置-->
<template>
  <ele-drawer
    title="审批节点设置"
    v-model="visible"
    direction="rtl"
    custom-class="set_promoter"
    size="65%"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="true"
  >
    <ele-card
      :body-style="{
        height: 'auto',
        padding: '10px 5px 10px 12px!important',
        marginBottom: '-25px!important'
      }"
      header="审核时间设置"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        size="small"
        label-position="left"
        @submit.prevent=""
      >
        <el-row :gutter="16">
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker
                class="ele-fluid"
                value-format="YYYY-MM-DD"
                placeholder="请选择开始时间"
                v-model="form.startTime"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker
                class="ele-fluid"
                value-format="YYYY-MM-DD"
                placeholder="请选择结束时间"
                v-model="form.endTime"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="8" :md="12" :sm="12" :xs="24">
            <el-form-item label="审批类型" prop="reviewType">
              <el-radio-group v-model="form.reviewType">
                <el-radio-button value="或签">或签</el-radio-button>
                <el-radio-button value="会签">会签</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </ele-card>
    <TablePreview
      dataName="节点状态设置"
      approverType="nodeStatus"
      approverId="nodeStatus"
      :approverData="approverDataStatus"
      @onDoneGroup="handleDoneGroup"
    />
    <ele-alert :closable="false">
      <ele-text size="lg">
        <el-button size="small" type="primary" @click="openPerSelector">
          审核人设置
        </el-button>
        <span style="margin-left: 10px; font-size: 12px"
          >审核角色跟审核人可以同时选择，并设置审核表单参数</span
        >
      </ele-text>
    </ele-alert>
    <!--    perSelectedData===={{ perSelectedData }}<br/>-->
    <template
      v-if="perSelectedData"
      v-for="(f2, index) in perSelectedData"
      :key="'formKey' + index"
    >
      <TablePreview
        v-if="f2"
        :ref="(el) => getRiskSpreadRef(el, index)"
        :dataName="f2.dictDataName + ' 审核字段配置'"
        :approverType="'workflow_' + f2.configKey"
        :approverId="f2.dictDataCode"
        :selectorData="f2"
        :approverData="approverData?.[f2.value]"
        @onDoneGroup="handleDoneGroup"
      />
    </template>
    <template #footer>
      <ele-text
        style="float: left"
        v-if="validMsg"
        type="danger"
        :icon="CloseCircleOutlined"
      >
        <span>{{ validMsg }}</span>
      </ele-text>
      <el-button size="small" @click="closeDrawer">取消</el-button>
      <el-button size="small" type="primary" @click="saveApprover">
        保存q2q
      </el-button>
    </template>

    <PerSelector
      v-model="showPerSelector"
      ref="treeTransferRef"
      node-key="id"
      paramMode="zzshlcgn"
      hearder="审核人设置"
      :perSelectedData="perSelectedData"
      @done="onDoneSelector"
    />
  </ele-drawer>
</template>
<script setup>
  import { useFormData } from '@/utils/use-form-data';
  import { mapMutations, mapState } from '@/plugins/lib';
  import { computed, reactive, ref, watch } from 'vue';
  import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';
  import PerSelector from '@/components/PerSelector/index.vue';
  import { CloseCircleOutlined } from '@/components/icons';
  import $func from '@/plugins/preload.js';
  import { EleMessage } from 'ele-admin-plus';

  let props = defineProps({
    directorMaxLevel: {
      type: Number,
      default: 0
    }
  });
  let emits = defineEmits(['update:nodeConfig']);
  let approverConfig = ref({});
  let { approverConfig1, approverDrawer } = mapState();
  let visible = computed({
    get() {
      return approverDrawer.value;
    },
    set() {
      closeDrawer();
    }
  });

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /**节点状态*/
  const approverDataStatus = ref(null);
  /**审核人*/
  const approverData = ref({});
  const approverDataIds = ref({});

  watch(approverConfig1, (val) => {
    // let nodeUserList = val.value.nodeUserList
    let nodeUserList = val.value;
    if (nodeUserList) {
      form.startTime = nodeUserList.startTime;
      form.endTime = nodeUserList.endTime;
      form.reviewType = nodeUserList.reviewType
        ? nodeUserList.reviewType
        : '或签';
      if (nodeUserList.workflowNodeState) {
        approverDataStatus.value = nodeUserList?.workflowNodeState ?? null;
      }
      if (nodeUserList.workflowNodeApprovers.length > 0) {
        nodeUserList.workflowNodeApprovers.forEach((wfa) => {
          approverDataIds.value[wfa.approverId] = wfa.id;
          if (wfa.selectorData)
            perSelectedData.value.push(JSON.parse(wfa.selectorData));
          if (wfa.workflowNodeForm)
            approverData.value[wfa.approverId] = wfa.workflowNodeForm;
        });
      }
    }
    approverConfig.value = val.value;
  });

  let { setApproverConfig, setApprover } = mapMutations();
  /** 表单数据 */
  const [form, resetFields, setFieldValue] = useFormData({
    startTime: '',
    endTime: '',
    reviewType: '或签'
  });

  /** 表单验证规则 */
  const rules = reactive({
    // startTime: [
    //   {
    //     required: true,
    //     message: '请选择开始时间',
    //     type: 'string',
    //     trigger: 'blur'
    //   }
    // ],
    // endTime: [
    //   {
    //     required: true,
    //     message: '请选择结束时间',
    //     type: 'string',
    //     trigger: 'blur'
    //   }
    // ],
    reviewType: [
      {
        required: true,
        message: '请选择审批类型',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** =====人员选择器=====start=*/
  const perSelectedData = ref([]);
  const showPerSelector = ref(false);

  const openPerSelector = () => {
    showPerSelector.value = true;
  };

  const onDoneSelector = (data) => {
    perSelectedData.value = data;
  };

  const riskSpreadRefList = ref([]);
  const getRiskSpreadRef = (el, index) => {
    if (el) {
      riskSpreadRefList.value[index] = el;
    }
  };

  /** 存放form提交字段*/
  const proFormGroup = ref([]);
  const handleDoneGroup = (data) => {
    let isExist = proFormGroup.value.filter(
      (obj) => obj.approverId === data.approverId
    );
    if (isExist.length > 0) {
      proFormGroup.value.filter((obj) => {
        if (obj.approverId === data.approverId)
          obj.workflowNodeForm = data.workflowNodeForm;
      });
    } else {
      proFormGroup.value.push(data);
    }
  };

  /** 表单验证失败提示信息 */
  const validMsg = ref('');
  const validMsgCount = ref(0);
  /** 存放子组件的数组 */
  let resultArr = reactive([]);
  /** 用来创建 Promise 实例，为多个组件校验使用 */
  const checkForm = (formChild) => {
    validMsg.value = null;
    let result = new Promise((resolve, reject) => {
      formChild.formRef?.validate((valid, obj) => {
        if (valid) {
          resolve(true);
        } else {
          const errors = obj ? Object.keys(obj).length : 0;
          validMsgCount.value += errors;
          resolve(false);
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  const saveApprover = async () => {
    await riskSpreadRefList.value?.forEach((child) => {
      checkForm(child);
    });
    try {
      const results = await Promise.all(resultArr);
      // 检查所有结果是否为true
      const allTrue = results.every(Boolean);
      resultArr = []; //每次请求完要清空数组
      if (allTrue) {
        // 执行后续操作
        save();
      } else {
        validMsg.value = ` 共有 ${validMsgCount.value} 项校验不通过`;
        validMsgCount.value = 0;
      }
    } catch (error) {
      console.error('有异步操作失败:', error);
    }
  };

  /** 提交 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      if (perSelectedData.value.length === 0) {
        EleMessage.error('请设置审批人');
        return;
      }
      // loading.value = true;
      let resData = proFormGroup.value;
      let newData = {
        workflowNodeApprovers: [], //节点审核对象
        workflowNodeState: {} //节点状态
      };
      let result = {};
      if (resData) {
        resData.forEach((rd) => {
          if (rd.fieldEn === 'nodeStatus') {
            rd.workflowNodeForm.forEach((wf) => {
              if (wf.fieldEn === 'result') {
                result['fieldEn'] = wf.fieldEn;
                result['fieldZh'] = wf.fieldZh;
              } else {
                result[wf.fieldEn] = wf.fieldZh;
              }
            });
            if (rd.selectorData) {
              result.id = rd.selectorData.id;
              result.nodeId = rd.selectorData.nodeId;
              result.workflowId = rd.selectorData.nodeId;
            }
            newData['workflowNodeState'] = result;
          } else {
            rd.selectorData = JSON.stringify(rd.selectorData);
            rd.id = approverDataIds.value
              ? approverDataIds.value[rd.approverId]
              : '';
            rd.workflowNodeForm.forEach((row) => {
              for (let i in row) {
                if (typeof row[i] === 'boolean') {
                  row[i] = row[i] === true ? '是' : '否';
                }
              }
            });

            newData['workflowNodeApprovers'].push(rd);
          }
        });
      }
      approverConfig.value = { ...approverConfig.value, ...form, ...newData };
      approverConfig.value.error = !$func.setApproverStr(approverConfig.value);
      setApproverConfig({
        value: approverConfig.value,
        flag: true,
        id: approverConfig1.value.id
      });
      emits('update:nodeConfig', approverConfig.value);
      closeDrawer();
    });
  };

  const closeDrawer = () => {
    setApprover(false);
    proFormGroup.value = [];
    perSelectedData.value = [];
    riskSpreadRefList.value = [];
    approverDataStatus.value = null;
    approverData.value = [];
    form.startTime = '';
    form.endTime = '';
    form.reviewType = '';
  };
</script>
<style lang="less">
  .set_promoter {
    .approver_content {
      padding-bottom: 10px;
      border-bottom: 1px solid #f2f2f2;
    }

    .approver_self_select,
    .approver_content {
      .el-button {
        margin-bottom: 20px;
      }
    }

    .approver_content,
    .approver_some,
    .approver_self_select {
      .el-radio-group {
        display: unset;
      }

      .el-radio {
        width: 27%;
        margin-bottom: 20px;
        height: 16px;
      }
    }

    .approver_manager p {
      line-height: 32px;
    }

    .approver_manager select {
      width: 420px;
      height: 32px;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      border: 1px solid rgba(217, 217, 217, 1);
    }

    .approver_manager p.tip {
      margin: 10px 0 22px 0;
      font-size: 12px;
      line-height: 16px;
      color: #f8642d;
    }

    .approver_self {
      padding: 28px 20px;
    }

    .approver_self_select,
    .approver_manager,
    .approver_content,
    .approver_some {
      padding: 20px 20px 0;
    }

    .approver_manager p:first-of-type,
    .approver_some p {
      line-height: 19px;
      font-size: 14px;
      margin-bottom: 14px;
    }

    .approver_self_select h3 {
      margin: 5px 0 20px;
      font-size: 14px;
      font-weight: bold;
      line-height: 19px;
    }
  }
</style>
