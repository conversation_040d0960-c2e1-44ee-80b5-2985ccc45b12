<!--条件设置-->
<template>
  <ele-drawer
    :append-to-body="true"
    title="条件设置"
    v-model="visible"
    direction="rtl"
    custom-class="condition_copyer"
    size="55%"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <ele-page>
      <div class="demo-drawer__content">
        <el-row :gutter="16">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="">
              <el-radio-group
                v-model="userType"
                size="small"
                @change="onChange"
              >
                <el-radio-button value="student">学生</el-radio-button>
                <el-radio-button value="teacher">教师</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :lg="18" :md="12" :sm="12" :xs="24">
            <el-form-item label="选择条件">
              <el-select
                multiple
                size="small"
                clearable
                v-model="fieldData"
                placeholder="请选择条件"
                @remove-tag="removeTag"
                @clear="clearTag"
                class="ele-fluid"
              >
                <el-option
                  v-for="field in dictionaryField"
                  :value="field.id"
                  :label="field.fieldZh"
                  @click="selectChange(field)"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <TablePreview
          v-if="approverData.length > 0"
          dataName="条件设置"
          approverType="nodeCondition"
          approverId="nodeCondition"
          :approverData="approverData"
          :RandomString="RandomString"
          @onDoneGroup="handleDoneGroup"
        />
      </div>
    </ele-page>
    <template #footer>
      <el-button @click="closeDrawer">取消</el-button>
      <el-button type="primary" @click="saveCondition"> 保存aa </el-button>
    </template>
    <employees-role-dialog
      v-model:visible="conditionRoleVisible"
      v-model:data="checkedList"
      @change="sureConditionRole"
      :isDepartment="true"
    />
  </ele-drawer>
</template>
<script setup>
  import employeesRoleDialog from '../dialog/employeesRoleDialog.vue';
  import $func from '@/plugins/preload';
  import { mapMutations, mapState } from '@/plugins/lib.js';
  import { computed, ref, watch } from 'vue';
  import { getDictionaryField } from '@/views/system/sphfw/dictionary-field/api/index.js';
  import { generateRandomString } from '@/utils/common.js';
  import TablePreview from '@/components/DrawFlow/drawer/table-preview.vue';

  let userType = ref('student');
  let fieldData = ref([]);
  let RandomString = ref('');
  let approverData = ref([]);
  let dictionaryField = ref([]);

  let conditionsConfig = ref({
    conditionNodes: []
  });
  let conditionConfig = ref({});
  let PriorityLevel = ref('');
  let checkedList = ref([]);
  let conditionRoleVisible = ref(false);

  let { conditionsConfig1, conditionDrawer } = mapState();

  let visible = computed({
    get() {
      return conditionDrawer.value;
    },
    set() {
      closeDrawer();
    }
  });

  watch(conditionsConfig1, (val) => {
    conditionsConfig.value = val.value;
    PriorityLevel.value = val.priorityLevel;
    conditionConfig.value = val.priorityLevel
      ? conditionsConfig.value.conditionNodes[val.priorityLevel - 1]
      : { nodeUserList: [], conditionDetail: [] };
    if (conditionConfig.value.conditionDetail.length > 0) {
      approverData.value = conditionConfig.value.conditionDetail;
      RandomString.value = generateRandomString(10);
      fieldData.value = conditionConfig.value.conditionDetail.map(
        (item) => item.fieldId
      );
    }
  });

  let { setCondition, setConditionsConfig } = mapMutations();

  const closeDrawer = () => {
    setCondition(false);
    proFormGroup.value = [];
    approverData.value = [];
    fieldData.value = [];
  };

  const onChange = async (event) => {
    getDataSource();
  };

  const getDataSource = async () => {
    let newObj = {
      userType: userType.value,
      workflowNodeFlowCondition: '是'
    };
    await getDictionaryField(newObj).then((arr) => {
      dictionaryField.value = arr;
    });
  };

  /** 可清空的单选模式下用户点击清空按钮时触发*/
  const clearTag = () => {
    RandomString.value = generateRandomString(10);
    approverData.value = [];
  };

  /** 多选模式下移除tag时触发 */
  const removeTag = (data) => {
    RandomString.value = generateRandomString(10);
    if (data) {
      let isExist = approverData.value.filter((obj) => obj.id === data);
      if (isExist.length > 0) {
        approverData.value.forEach((v) => {
          if (v.id === data) {
            approverData.value.splice(approverData.value.indexOf(v), 1);
          }
        });
      }
    }
  };

  /** 更新选中数据 */
  const selectChange = (data) => {
    RandomString.value = generateRandomString(10);
    let isExist = approverData.value.filter((obj) => obj.id === data.id);
    if (isExist.length > 0) {
      approverData.value.forEach((v) => {
        if (v.id === data.id) {
          approverData.value.splice(approverData.value.indexOf(v), 1);
        }
      });
    } else {
      approverData.value.push(data);
    }
  };

  /** 存放form提交字段*/
  const proFormGroup = ref([]);
  const handleDoneGroup = (data) => {
    let isExist = proFormGroup.value.filter(
      (obj) => obj.approverId === data.approverId
    );
    if (isExist.length > 0) {
      proFormGroup.value.filter((obj) => {
        if (obj.approverId === data.approverId)
          obj.workflowNodeForm = data.workflowNodeForm;
      });
    } else {
      proFormGroup.value.push(data);
    }
  };

  const saveCondition = () => {
    let resData = proFormGroup.value;
    if (resData) {
      conditionConfig.value.conditionDetail = [];
      resData.forEach((rd) => {
        rd.workflowNodeForm.forEach((item) => {
          conditionConfig.value.conditionDetail.push(item);
        });
      });
    }
    var a = conditionsConfig.value.conditionNodes.splice(
      PriorityLevel.value - 1,
      1
    ); //截取旧下标
    conditionsConfig.value.conditionNodes.splice(
      conditionConfig.value.priorityLevel - 1,
      0,
      a[0]
    ); //填充新下标
    conditionsConfig.value.conditionNodes.map((item, index) => {
      item.priorityLevel = index + 1;
    });
    for (var i = 0; i < conditionsConfig.value.conditionNodes.length; i++) {
      conditionsConfig.value.conditionNodes[i].error =
        $func.conditionStr(conditionsConfig.value, i) == '请设置条件' &&
        i != conditionsConfig.value.conditionNodes.length - 1;
    }
    setConditionsConfig({
      value: conditionsConfig.value,
      flag: true,
      id: conditionsConfig1.value.id
    });
    closeDrawer();
  };

  if (userType.value) getDataSource();
</script>
<style lang="less">
  .condition_copyer {
    .priority_level {
      position: absolute;
      top: 11px;
      right: 30px;
      width: 100px;
      height: 32px;
      background: rgba(255, 255, 255, 1);
      border-radius: 4px;
      border: 1px solid rgba(217, 217, 217, 1);
      font-size: 12px;
    }

    .condition_content {
      padding: 20px 20px 0;

      p.tip {
        margin: 20px 0;
        width: 510px;
        text-indent: 17px;
        line-height: 45px;
        background: rgba(241, 249, 255, 1);
        border: 1px solid rgba(64, 163, 247, 1);
        color: #46a6fe;
        font-size: 14px;
      }

      ul {
        max-height: 500px;
        overflow-y: scroll;
        margin-bottom: 20px;

        li {
          & > span {
            float: left;
            margin-right: 8px;
            width: 70px;
            line-height: 32px;
            text-align: right;
          }

          & > div {
            display: inline-block;
            width: 370px;

            & > p:not(:last-child) {
              margin-bottom: 10px;
            }
          }

          &:not(:last-child) > div > p {
            margin-bottom: 20px;
          }

          & > a {
            float: right;
            margin-right: 10px;
            margin-top: 7px;
          }

          select,
          input {
            width: 100%;
            height: 32px;
            background: rgba(255, 255, 255, 1);
            border-radius: 4px;
            border: 1px solid rgba(217, 217, 217, 1);
          }

          select + input {
            width: 260px;
          }

          select {
            margin-right: 10px;
            width: 100px;
          }

          p.selected_list {
            padding-left: 10px;
            border-radius: 4px;
            min-height: 32px;
            border: 1px solid rgba(217, 217, 217, 1);
            word-break: break-word;
          }

          p.check_box {
            line-height: 32px;
          }
        }
      }

      .el-button {
        margin-bottom: 20px;
      }
    }
  }

  .condition_list {
    .el-dialog__body {
      padding: 16px 26px;
    }

    p {
      color: #666666;
      margin-bottom: 10px;

      & > .check_box {
        margin-bottom: 0;
        line-height: 36px;
      }
    }
  }
</style>
