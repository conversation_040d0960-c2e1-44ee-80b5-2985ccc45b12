<template>
  <el-form
    ref="proFormRef"
    :model="initModel"
    label-position="left"
    size="small"
    @submit.prevent=""
  >
    <el-row :gutter="4">
      <el-col
        v-for="item in initItems"
        :lg="12"
        :md="12"
        :sm="12"
        :xs="24"
        v-show="item.showFlag === '是'"
      >
        <ProFormItem
          :item="item"
          :model="initModel"
          :rules="rules"
          @updateItemValue="(prop, value) => updateFormValue(item, prop, value)"
        >
          <template
            v-for="name in Object.keys($slots).filter(
              (k) =>
                !['default', 'footer', 'topExtra', 'bottomExtra'].includes(k)
            )"
            #[name]="slotProps"
          >
            <slot :name="name" v-bind="slotProps || {}"></slot>
          </template>
        </ProFormItem>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
  import { ref, nextTick, reactive } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import ProForm from '@/components/ProForm/index.vue';
  import ProFormItem from '@/components/ProForm/components/pro-form-item.vue';
  import { EleMessage } from 'ele-admin-plus';

  const emit = defineEmits(['onDone']);

  const props = defineProps({
    /** 表单标题宽度 */
    labelWidth: Number,
    /** 表单显示列数 */
    grid: Number,
    currentGroup: Object,
    userType: String,
    data: Object
  });

  /** 请求状态 */
  const loading = ref(true);

  const proFormRef = ref();

  /** 表单项 */
  const initItems = ref([
    {
      key: 'fieldKey_1',
      label: '学院',
      prop: 'xyid',
      type: 'dictSelect',
      required: false,
      showFlag: '是',
      fieldLinks: null,
      selfFieldLink: 'dwmc',
      nextField: 'zymc',
      props: {
        code: 'other',
        filterable: true,
        dicQueryParams: {
          dictFieldUrl: '/code/codeDwb',
          valueField: 'id',
          textField: 'name'
        }
      }
    },
    {
      key: 'fieldKey_2',
      label: '专业',
      prop: 'zyid',
      type: 'dictSelect',
      required: false,
      showFlag: '否',
      fieldLinks: null,
      selfFieldLink: 'zymc',
      nextField: 'bjmc',
      props: {
        code: 'other',
        filterable: true,
        dicQueryParams: {
          dictFieldUrl: '/code/codeZyb',
          valueField: 'id',
          textField: 'name'
        }
      }
    },
    {
      key: 'fieldKey_3',
      label: '班级',
      prop: 'bjid',
      type: 'dictSelect',
      required: false,
      showFlag: '否',
      fieldLinks: null,
      selfFieldLink: 'bjmc',
      nextField: '',
      props: {
        code: 'other',
        filterable: true,
        dicQueryParams: {
          dictFieldUrl: '/code/codeBjb',
          valueField: 'id',
          textField: 'name'
        }
      }
    }
  ]);
  /** 获取表单字段 */
  const initModel = ref({ xqmc: '', xyid: '', zyid: '', bjid: '' });
  const rules = reactive({});
  /** 表单数据 */
  const [form, resetFields, setFieldValue] = useFormData(initModel);

  /** 更新表单数据 */
  const updateFormValue = (item, prop, value) => {
    initModel.value[item.prop] = value;

    if (item.selfFieldLink) {
      //院系专业班级三个字段页面逻辑特殊处理
      nextTick(() => {
        initItems.value.filter((f) => {
          if (f.selfFieldLink === item.nextField) {
            f.showFlag = '是';
            f.props['refresh'] = value; //watch每次刷新，重新请求
            if (item.nextField === 'zymc') {
              f.props.dicQueryParams['params'] = { dwid: value };
            }
            if (item.nextField === 'bjmc') {
              f.props.dicQueryParams['params'] = { zyid: value };
            }
          }
        });
      });
    }
    if (item.fieldLinks) {
      //获取选择值下面所有的显示字段信息
      let showfields = item.fieldLinks.filter((f) => {
        // return f.fieldValId === value
        return f.fieldVal === value;
      });
      if (showfields.length > 0) {
        showfields.forEach((sf) => {
          nextTick(() => {
            //切换选择值，清空之前选项所赋值
            initModel.value[sf.linkField] = '';
            //动态切换选择值，关联具体字段的显示隐藏
            initItems.value.filter((init) => {
              init.showFlag =
                sf.linkField === init.prop ? sf.showFlag : init.showFlag;
              if (sf.linkFieldDataType && sf.linkField === init.prop) {
                init.props = {
                  code: sf.linkFieldDataType,
                  filterable: true
                };
              }
            });
          });
        });
      }
    }
    emit('onDone', initModel.value);
  };

  defineExpose({ proFormRef });
</script>
