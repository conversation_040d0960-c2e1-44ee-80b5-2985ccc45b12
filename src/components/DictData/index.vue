<!-- 字典组件 -->
<template>
  <!--        code===== {{type}}/{{code}}////{{dicQueryParams}}<br/><br/>-->
  <!--    code===== {{code}}/{{dicQueryParams}}<br/><br/>-->
  <!--          data===== {{data}}<br/><br/>-->
  <!--    valueDataTest===== {{valueDataTest}}<br/><br/>-->
  <template v-if="type === 'text'">
    <span v-for="item in valueData" :key="item.dictDataCode">
      {{ item.dictDataName }}
    </span>
  </template>
  <template v-else-if="type === 'tag'">
    <el-tag
      v-for="item in valueData"
      :key="item.dictDataCode"
      :disable-transitions="true"
      size="small"
    >
      {{ item.dictDataName }}
    </el-tag>
  </template>
  <el-radio-group
    v-else-if="type === 'radio'"
    :disabled="disabled"
    @update:modelValue="updateValue"
    :model-value="modelValue"
  >
    <el-radio
      v-for="item in data"
      :key="
        dicQueryParams && dicQueryParams.getValType === 'name'
          ? item.dictDataName
          : item.dictDataCode
      "
      :label="item.dictDataName"
    >
      {{ item.dictDataName }}
    </el-radio>
  </el-radio-group>
  <el-radio-group
    v-else-if="type === 'radioButton'"
    :disabled="disabled"
    @update:modelValue="updateValue"
    :model-value="modelValue"
  >
    <el-radio-button
      v-for="item in data"
      :key="
        dicQueryParams && dicQueryParams.getValType === 'name'
          ? item.dictDataName
          : item.dictDataCode
      "
      :label="item.dictDataName"
    >
      {{ item.dictDataName }}
    </el-radio-button>
  </el-radio-group>
  <el-checkbox-group
    v-else-if="type === 'checkboxButton'"
    :disabled="disabled"
    @update:modelValue="updateValue"
    :model-value="modelValue"
  >
    <el-checkbox
      v-for="item in data"
      border
      style="margin-bottom: 8px"
      :key="item.dictDataCode"
      :label="item.dictDataCode"
    >
      {{ item.dictDataName }}
    </el-checkbox>
  </el-checkbox-group>
  <el-checkbox-group
    v-else-if="type === 'checkbox'"
    :disabled="disabled"
    @update:modelValue="updateValue"
    :model-value="modelValue"
  >
    <el-checkbox
      v-for="item in data"
      :key="item.dictDataCode"
      :label="item.dictDataCode"
    >
      {{ item.dictDataName }}
    </el-checkbox>
  </el-checkbox-group>
  <el-select
    v-else-if="type === 'searchSet'"
    @update:modelValue="updateValue"
    :model-value="modelValue"
    :clearable="true"
    :disabled="disabled"
    :placeholder="placeholder"
    :teleported="teleported"
    :filterable="filterable"
    class="ele-fluid"
  >
    <!--        码表获取数据，根据getValType设置类型取code还是name-->
    <el-option
      v-for="item in data"
      @click="selectChange(item)"
      :key="item.dictDataCode"
      :value="item.dictDataCode"
      :label="item.dictDataName"
    />
  </el-select>
  <el-select
    v-else
    @update:modelValue="updateValue"
    :model-value="modelValue"
    :clearable="true"
    :disabled="disabled"
    :placeholder="placeholder"
    :multiple="type === 'multipleSelect'"
    :teleported="teleported"
    :filterable="filterable"
    class="ele-fluid"
  >
    <!--        码表获取数据，根据getValType设置类型取code还是name-->
    <el-option
      v-for="item in data"
      :key="
        dicQueryParams && dicQueryParams.getValType === 'name'
          ? item.dictDataName
          : item.dictDataCode
      "
      :value="
        dicQueryParams && dicQueryParams.getValType === 'name'
          ? item.dictDataName
          : item.dictDataCode
      "
      :label="item.dictDataName"
    />
  </el-select>
</template>

<script setup>
  import { computed, ref, watch } from 'vue';
  import { useUserStore } from '@/store/modules/user';
  import { EleMessage } from 'ele-admin-plus';
  import { listRoles } from '@/views/system/role/api';
  import {
    getDicFieldValueByUrl,
    getDictionaryFieldByGroupId
  } from '@/views/system/sphfw/dictionary-field/api';
  import { getControlTypeData } from '@/utils/control-type-util';
  import { getCodeType } from '@/views/base-code/dictionary/api';
  import { getSelectControlApi } from '@/views/base-code/selectDictionary/api';
  import { getDictionaryGroup } from '@/views/system/sphfw/dictionary-group/api';
  import { getListGroupConfig } from '@/views/system/sphfw/list-group/api';
  import { getCodeData } from '@/views/base-code/dictionary/api/data-index';

  const emit = defineEmits(['update:modelValue']);

  const props = defineProps({
    /** 字典值 */
    modelValue: [String, Number, Boolean, Array],
    /** 字典类型 */
    code: String,
    /** 每次刷新，重新请求 */
    refresh: String,
    // /** 动态获取接口参数配置 */
    dicQueryParams: Object,
    /** 组件类型 */
    type: String,
    /** 是否禁用 */
    disabled: Boolean,
    /** 提示文本 */
    placeholder: String,
    /** select的下拉是否插入到body下 */
    teleported: {
      type: Boolean,
      default: true
    },
    /** select 是否可搜索 */
    filterable: Boolean
  });

  /** 字典数据 */
  const data = ref([]);

  // 已缓存的字典
  const userStore = useUserStore();

  /** 绑定值对应的字典数据 */
  const valueData = computed(() => {
    const result = [];
    const val = props.modelValue;
    if (val == null || val === '') {
      return result;
    }
    const values = Array.isArray(val) ? val : [val];
    values.forEach((v) => {
      const temp = dicData.value.find((d) => d.dictDataCode == v);
      if (temp != null) {
        result.push(temp);
      } else {
        result.push({ dictDataCode: v, dictDataName: v });
      }
    });
    return result;
  });

  /** 更新选中数据 searchSet特殊处理 获取el-select，value=object */
  const selectChange = (value) => {
    emit('update:modelValue', value);
  };

  /** 更新选中数据 */
  const updateValue = (value) => {
    emit('update:modelValue', value);
  };

  const getUseDictData = (code, dicQueryParams) => {
    let groupId = dicQueryParams?.groupId;
    let dictionaryFieldUrl = dicQueryParams?.dictFieldUrl;
    let userType = dicQueryParams?.userType;
    let params = dicQueryParams?.params;
    let result = [];
    userStore.setDicts([], code);
    if (code === 'listRoles') {
      //角色下拉
      /** 获取角色数据 */
      listRoles()
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.id,
                dictDataName: resData.name
              });
            });
            userStore.setDicts(result, code);
            data.value = result;
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else if (code === 'studentXsda') {
      //学生档案-学生字段查询
      getFieldList('student', { listShowFlag: '是' })
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.fieldEn,
                dictDataName: resData.fieldZh,
                ...resData
              });
            });
            userStore.setDicts(result, code);
            data.value = result;
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else if (code === 'searchSet') {
      //学生高级查询设置
      getFieldList(userType, { tempField: 'no' })
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.fieldEn,
                dictDataName: resData.fieldZh,
                ...resData
              });
            });
            userStore.setDicts(result, code);
            data.value = result;
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else if (dictionaryFieldUrl) {
      //其他类型
      if (code === 'dictionaryField' && groupId && dictionaryFieldUrl) {
        //字段英文名
        getDictionaryFieldByGroupId(groupId, dictionaryFieldUrl)
          .then((list) => {
            if (list) {
              list.forEach((resData) => {
                result.push({ dictDataCode: resData, dictDataName: resData });
              });
              userStore.setDicts(result, code);
              data.value = result;
            }
          })
          .catch((e) => {
            EleMessage.error(e.message);
          });
      } else {
        getDicFieldValueByUrl(dictionaryFieldUrl, params)
          .then((list) => {
            let valueField = dicQueryParams?.valueField ?? 'id';
            let textField = dicQueryParams?.textField ?? 'name';
            if (list) {
              list.forEach((resData) => {
                result.push({
                  dictDataCode: resData[valueField],
                  dictDataName: resData[textField]
                });
              });
              userStore.setDicts(result, code);
              data.value = result;
            }
          })
          .catch((e) => {
            EleMessage.error(e.message);
          });
      }
    } else if (code === 'controlType') {
      //获取控件类型
      getControlTypeData()
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.value,
                dictDataName: resData.label
              });
            });
            data.value = result;
            userStore.setDicts(result, code);
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else if (code === 'listDicCode') {
      //加载数据
      getCodeType()
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.code,
                dictDataName: resData.name,
                ...resData
              });
            });
            result.push({ dictDataCode: 'other', dictDataName: '其他' });
            data.value = result;
            userStore.setDicts(result, code);
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else if (code === 'extendsField') {
    } else if (code === 'listDicUrl') {
      //加载数据URL
      getSelectControlApi()
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.id,
                dictDataName: resData.name,
                ...resData
              });
            });
            data.value = result;
            userStore.setDicts(result, code);
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else if (code === 'groupType') {
      //信息字段，所属字段组
      let obj = {};
      if (userType)
        obj = {
          userType: userType
        };
      getDictionaryGroup(obj)
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.id,
                dictDataName: resData.groupName
              });
            });
            data.value = result;
            userStore.setDicts(result, code);
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else if (code === 'listGroup') {
      //信息字段，所属字段组
      getListGroupConfig()
        .then((list) => {
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData.id,
                dictDataName: resData.groupName
              });
            });
            data.value = result;
            userStore.setDicts(result, code);
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    } else {
      //公共码表获取
      getCodeData({ codeType: code })
        .then((list) => {
          let valueField = dicQueryParams?.valueField ?? 'id';
          let textField = dicQueryParams?.textField ?? 'name';
          if (list) {
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData[valueField],
                dictDataName: resData[textField],
                ...resData
              });
            });
            userStore.setDicts(result, code);
            data.value = result;
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    }
  };

  watch(
    () => props.code,
    (code) => {
      if (code) {
        getUseDictData(code, props.dicQueryParams);
      }
    },
    {
      immediate: true
    }
  );
  watch(
    () => props.refresh,
    (refresh) => {
      if (refresh) {
        console.log(refresh);
        getUseDictData(props.code, props.dicQueryParams);
      }
    },
    {
      immediate: true
    }
  );
</script>
