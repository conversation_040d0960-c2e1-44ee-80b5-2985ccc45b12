<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <!-- hiprint 拖拽图标需要 -->
    <link href="<%= BASE_URL %>css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" media="print" href="<%= BASE_URL %>css/print-lock.css">
    <!-- 初次加载  -->
    <style>#loading-mask{position:fixed;left:0;top:0;height:100%;width:100%;background:#fff;user-select:none;z-index:9999;overflow:hidden}.loading-wrapper{position:absolute;top:50%;left:50%;text-align:center;transform:translate(-50%,-100%)}.loading-tips{margin-top: 10px;color: grey;font-size: 14px}.loading-dot{animation:antRotate 1.2s infinite linear;transform:rotate(45deg);position:relative;display:inline-block;font-size:40px;width:40px;height:40px;box-sizing:border-box}.loading-dot i{width:20px;height:20px;position:absolute;display:block;background-color:#1890ff;border-radius:100%;transform:scale(.75);transform-origin:50% 50%;opacity:.3;animation:antSpinMove 1s infinite linear alternate}.loading-dot i:nth-child(1){top:0;left:0}.loading-dot i:nth-child(2){top:0;right:0;-webkit-animation-delay:.4s;animation-delay:.4s}.loading-dot i:nth-child(3){right:0;bottom:0;-webkit-animation-delay:.8s;animation-delay:.8s}.loading-dot i:nth-child(4){bottom:0;left:0;-webkit-animation-delay:1.2s;animation-delay:1.2s}@keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@-webkit-keyframes antRotate{to{-webkit-transform:rotate(405deg);transform:rotate(405deg)}}@keyframes antSpinMove{to{opacity:1}}@-webkit-keyframes antSpinMove{to{opacity:1}}</style>
    <!-- source  -->
    <style>#source{position:fixed;top:0px;right:0px;width:149px;height:149px;z-index:999;}.link{position:relative;right:-37px;top:-22px;display:block;width:190px;padding:4px 16px;text-align:center;color:rgb(255, 255, 255);font-size:15px;background: rgb(101, 24, 255);text-decoration:none;text-shadow:rgb(0 0 0 / 15%) 0px -1px 0px;transform-origin:0% 0%;transform:rotate(45deg);cursor:pointer;}.link:hover{color:white;font-size:large;transition:ease-in;transition-duration:0.3s;top:-24px}</style>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div id="loading-mask">
        <div class="loading-wrapper">
          <span class="loading-dot loading-dot-spin"><i></i><i></i><i></i><i></i></span>
          <div class="loading-tips">初次加载较慢，请耐心等待...</div>
        </div>
      </div>
    </div>
    <div id="source">
      <a href="https://gitee.com/CcSimple/vue-plugin-hiprint" class="link">查 看 源 代 码</a>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
