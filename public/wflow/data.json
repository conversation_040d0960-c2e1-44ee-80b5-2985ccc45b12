{"code": "200", "msg": "success", "data": {"tableId": 1, "nodeConfig": {"nodeName": "", "type": 0, "settype": "", "conditionList": [], "startTime": "", "endTime": "", "flowNode": "", "reviewType": "", "sort": "", "workflowId": "", "workflowNodeApprovers": [], "workflowNodeEvents": [], "workflowNodeState": {}, "nodeUserList": null, "childNode": {"nodeName": "", "error": true, "type": 1, "settype": 2, "childNode": {}, "startTime": "", "endTime": "", "flowNode": "", "reviewType": "", "sort": "", "workflowId": "", "workflowNodeApprovers": [], "workflowNodeEvents": [], "workflowNodeState": {}, "nodeUserList": null}, "conditionNodes": []}}}