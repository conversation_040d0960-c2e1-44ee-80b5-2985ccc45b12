{"name": "ele-admin-plus-js", "version": "1.1.9", "type": "module", "private": true, "scripts": {"dev": "vite --host", "serve": "vite build && vite preview --host", "build": "vite build", "serve:staging": "vite build --mode staging && vite preview --host", "build:staging": "vite build --mode staging", "lint:eslint": "eslint --cache --max-warnings 0  \"src/**/*.{vue,js}\" --fix", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite/", "clean:lib": "rimraf node_modules"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@ant-design/colors": "^7.0.2", "@ant-design/icons-vue": "^7.0.1", "@bytemd/plugin-gfm": "^1.21.0", "@bytemd/plugin-highlight": "^1.21.0", "@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue-jsx": "^4.0.0", "axios": "^1.6.8", "bytemd": "^1.21.0", "countup.js": "^2.8.0", "cropperjs": "^1.6.1", "dayjs": "^1.11.10", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "ele-admin-plus": "^1.3.0", "element-plus": "^2.8.0", "fabric": "^5.3.0", "github-markdown-css": "^5.5.1", "highlight.js": "^11.11.1", "jsbarcode": "^3.11.6", "markdown-it": "^14.1.0", "nprogress": "^0.2.0", "pinia": "^2.1.7", "rollup-plugin-visualizer": "^5.12.0", "sortablejs": "^1.15.2", "tinymce": "^5.10.9", "unplugin-auto-import": "^0.18.2", "vue": "^3.4.21", "vue-draggable-next": "^2.2.1", "vue-echarts": "^6.6.9", "vue-i18n": "^9.12.0", "vue-plugin-hiprint": "^0.0.60", "vue-router": "^4.3.0", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0", "vuex-persistedstate": "^4.1.0", "xgplayer": "^3.0.16", "xgplayer-hls": "^3.0.16", "xgplayer-music": "^3.0.16", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/babel-plugin-jsx": "^1.2.2", "@vue/compiler-sfc": "^3.4.21", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.24.1", "less": "^2.7.2", "less-loader": "^5.0.0", "postcss": "^8.4.38", "prettier": "^3.2.5", "rimraf": "^5.0.5", "sass": "^1.75.0", "unplugin-vue-components": "^0.27.4", "vite": "^5.2.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-lang-jsx": "^1.5.3", "vue-eslint-parser": "^9.4.2"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}