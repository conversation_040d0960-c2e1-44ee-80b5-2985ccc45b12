#!/bin/bash

# 定义远程服务器信息
remote_host="************"
remote_port="22"
remote_user="root"
remote_password="Pjk18300709626"

# 本地构建项目
echo "开始构建项目..."
npm run build

# 检查构建是否成功
if [ $? -ne 0 ]; then
    echo "构建失败，脚本终止"
    exit 1
fi

echo "构建成功，开始部署..."

sshpass -p "$remote_password" ssh -p $remote_port $remote_user@$remote_host << EOF
if [ ! -d "/app/server/frontend" ]; then
  mkdir -p /app/server/frontend
  echo "Directory /app/server/frontend created."
else
  echo "Directory /app/server/frontend already exists."
fi
EOF

echo "Transferring files to remote server..."
# 先清理远程目录
sshpass -p "$remote_password" ssh -p $remote_port $remote_user@$remote_host << EOF
rm -rf /app/server/frontend/dist
rm -f /app/server/frontend/nginx.conf
rm -f /app/server/frontend/docker-compose.yml
EOF

# 打包并传输 dist 目录
echo "Transferring dist directory..."
tar -czf - ./dist | sshpass -p "$remote_password" ssh -p $remote_port $remote_user@$remote_host "cd /app/server/frontend && tar -xzf -"

# 传输配置文件
echo "Transferring configuration files..."
sshpass -p "$remote_password" scp -P $remote_port -o ConnectTimeout=30 -o ServerAliveInterval=60 ./nginx.conf $remote_user@$remote_host:/app/server/frontend/
sshpass -p "$remote_password" scp -P $remote_port -o ConnectTimeout=30 -o ServerAliveInterval=60 ./docker-compose.yml $remote_user@$remote_host:/app/server/frontend/

echo "File transfer completed."

# 登录远程服务器并进入项目目录, 指定端口, 并使用 sshpass 输入密码
sshpass -p "$remote_password" ssh -p $remote_port $remote_user@$remote_host << EOF
cd /app/server/frontend

# 检查 docker-compose.yml 文件是否存在
if [ ! -f "docker-compose.yml" ]; then
  echo "Error: docker-compose.yml file not found in /app/server/frontend"
  exit 1
fi

# 停止并删除现有的容器 (如果容器名已存在)
docker ps -a --filter "name=server-frontend" -q | xargs -r docker rm -f

# 执行 docker compose 命令
echo "Starting docker compose..."
docker compose up -d

# 检查容器状态
echo "Checking container status..."
docker compose ps
EOF

echo "Deployment completed!"
