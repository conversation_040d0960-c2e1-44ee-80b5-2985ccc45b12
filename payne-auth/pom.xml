<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.payne</groupId>
        <artifactId>dev-platform</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>payne-auth</artifactId>

    <properties>
        <java.version>17</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.payne</groupId>
            <artifactId>payne-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payne</groupId>
            <artifactId>payne-upms</artifactId>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*Mapper.xml</include>
                </includes>
            </resource>
<!--            <resource>-->
<!--                <directory>src/main/resources</directory>-->
<!--                <includes>-->
<!--                    <include>*.yml</include>-->
<!--                </includes>-->
<!--            </resource>-->
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>  <!-- 递归包含所有子目录 -->
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>