package com.payne.auth.security;

import cn.hutool.core.codec.Base64;
import com.payne.auth.entity.OauthClientDetails;
import com.payne.core.config.ConfigProperties;
import com.payne.core.constant.Constants;
import com.payne.core.utils.CommonUtil;
import com.payne.core.utils.JwtSubject;
import com.payne.core.utils.JwtUtil;
import com.payne.upms.system.entity.LoginRecord;
import com.payne.upms.system.entity.Menu;
import com.payne.upms.system.entity.SysAccount;
import com.payne.upms.system.service.LoginRecordService;
import com.payne.upms.system.service.SysAccountService;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 处理携带token的请求过滤器
 *
 * 
 * @since 2020-03-30 20:48:05
 */
@Slf4j
//@Component
@AllArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {
//    @Resource
    private final ConfigProperties configProperties;
//    @Resource
    private final LoginRecordService loginRecordService;
//    @Resource
    private final SysAccountService sysAccountService;
//    @Resource
    private final RedisTemplate<String, Object> redisTemplate;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
            throws ServletException, IOException {
        String requestURI = request.getRequestURI();

        // 白名单路径直接放行
        if (requestURI.startsWith("/api/sec_js") || requestURI.startsWith("/api/login")|| requestURI.startsWith("/api/file/")
                || requestURI.startsWith("/api/captcha") || requestURI.startsWith("/api/cus-auth")
                || requestURI.startsWith("/unified-auth/open") || requestURI.startsWith("/api/pjosendform/preview/")) {
            chain.doFilter(request, response);
            return;
        }
        String accessToken = JwtUtil.getAccessToken();
        if (StringUtils.hasLength(accessToken)) {
            if (requestURI.startsWith("/api/msg") || requestURI.startsWith("/v1")) {
                try {
                    if (processClientToken(accessToken, request, response)) return;
                } catch (ExpiredJwtException e) {
                    log.error(e.getMessage(), e);
                    CommonUtil.responseError(response, Constants.TOKEN_EXPIRED_CODE, Constants.TOKEN_EXPIRED_MSG,
                            null);
                    return;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    CommonUtil.responseError(response, Constants.BAD_CREDENTIALS_CODE, Constants.BAD_CREDENTIALS_MSG,
                            null);
                    return;
                }
            } else {
                try {
                    processUserToken(accessToken, request, response);
                } catch (ExpiredJwtException e) {
                    log.error(e.getMessage(), e);
                    CommonUtil.responseError(response, Constants.TOKEN_EXPIRED_CODE, Constants.TOKEN_EXPIRED_MSG,
                            null);
                    return;
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    CommonUtil.responseError(response, Constants.BAD_CREDENTIALS_CODE, Constants.BAD_CREDENTIALS_MSG,
                            null);
                    return;
                }
            }
        } else {
            CommonUtil.responseError(response, Constants.UNAUTHORIZED_CODE, Constants.UNAUTHORIZED_MSG, null);
            return;
        }

        chain.doFilter(request, response);
    }

    private boolean processClientToken(String accessToken, HttpServletRequest request, HttpServletResponse response) {
        Claims claims = JwtUtil.parseToken(accessToken, configProperties.getTokenKey());
        JwtSubject jwtSubject = JwtUtil.getJwtSubject(claims);
        String basicKey = jwtSubject.getUsername();
        OauthClientDetails oauthClientDetails = (OauthClientDetails) redisTemplate.opsForValue().get(accessToken);
        if (oauthClientDetails == null) {
            CommonUtil.responseError(response, Constants.UNAUTHORIZED_CODE, Constants.UNAUTHORIZED_MSG, null);
            return true;
        } else {
            String decodeStr = Base64.decodeStr(basicKey);
            String[] split = decodeStr.split(":");
            String userName = "";
            if (split.length == 3) {
                userName = split[2];
                SysAccount account = getAccount(userName);
                List<Menu> authorities = account.getAuthorities().stream()
                        .filter(m -> StringUtils.hasLength(m.getAuthority())).collect(Collectors.toList());
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        account, null, authorities);
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
            String encode = Base64.encode(oauthClientDetails.getClientId() + ":" + oauthClientDetails.getClientSecret()
                    + (StringUtils.hasLength(userName) ? ":" + userName : ""));
            if (!Objects.equals(basicKey, encode) || !Constants.ENABLE.equals(oauthClientDetails.getStatus())) {
                CommonUtil.responseError(response, Constants.UNAUTHORIZED_CODE, Constants.UNAUTHORIZED_MSG, null);
                return true;
            }
        }
        return false;
    }

    /**
     * 处理用户令牌
     */
    private void processUserToken(String token, HttpServletRequest request, HttpServletResponse response) {
        Claims claims = JwtUtil.parseToken(token, configProperties.getTokenKey());
        JwtSubject jwtSubject = JwtUtil.getJwtSubject(claims);
        SysAccount account = getAccount(jwtSubject.getUsername());
        List<Menu> authorities = account.getAuthorities().stream()
                .filter(m -> StringUtils.hasLength(m.getAuthority())).collect(Collectors.toList());
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                account, null, authorities);
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // token将要过期签发新token, 防止突然退出登录
        long expiration = (claims.getExpiration().getTime() - new Date().getTime()) / 1000 / 60;
        if (expiration < configProperties.getTokenRefreshTime()) {
            String newToken = JwtUtil.buildToken(jwtSubject, configProperties.getTokenExpireTime(),
                    configProperties.getTokenKey());
            response.addHeader(Constants.TOKEN_HEADER_NAME, newToken);
            loginRecordService.saveAsync(account.getUsername(), LoginRecord.TYPE_REFRESH, null,
                    request);
        }
    }

    private SysAccount getAccount(String username) {
        SysAccount account = (SysAccount) redisTemplate.opsForValue().get(username);
        if (account == null) {
            account = sysAccountService.getByUsername(username);
            if (account == null) {
                throw new UsernameNotFoundException("Username not found");
            }
            redisTemplate.opsForValue().set(username, account, configProperties.getTokenExpireTime(), TimeUnit.SECONDS);
        }
        return account;
    }
}
