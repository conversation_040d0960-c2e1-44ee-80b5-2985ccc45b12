package com.payne.auth.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.auth.entity.OauthClientDetails;
import com.payne.auth.param.OauthClientDetailsParam;
import com.payne.core.web.PageResult;

/**
 *  服务接口
 *  2020-04-07.
 */
public interface OauthClientDetailsService extends IService<OauthClientDetails> {
    PageResult<OauthClientDetails> queryPage(OauthClientDetailsParam param);
}
