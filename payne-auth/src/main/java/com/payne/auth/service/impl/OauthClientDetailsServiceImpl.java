package com.payne.auth.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.auth.entity.OauthClientDetails;
import com.payne.auth.mapper.OauthClientDetailsMapper;
import com.payne.auth.param.OauthClientDetailsParam;
import com.payne.auth.service.OauthClientDetailsService;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import org.springframework.stereotype.Service;

@Service
public class OauthClientDetailsServiceImpl extends ServiceImpl<OauthClientDetailsMapper, OauthClientDetails> implements OauthClientDetailsService {


    @Override
    public PageResult<OauthClientDetails> queryPage(OauthClientDetailsParam param) {
        PageParam<OauthClientDetails, OauthClientDetailsParam> page = new PageParam<>(param);
        page = baseMapper.selectPage(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }
}
