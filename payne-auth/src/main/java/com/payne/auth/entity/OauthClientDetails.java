package com.payne.auth.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *  2020/04/07.
 */
@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("OAUTH_CLIENT_DETAILS")
@Table(name = "OAUTH_CLIENT_DETAILS")
public class OauthClientDetails implements Serializable{

    private static final long serialVersionUID = 1L;

	@Id
	@Column(name = "ID", columnDefinition = "VARCHAR2(50)")
	@TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
	@TableField("client_id")
	private String clientId;
	@TableField("resource_ids")
	private String resourceIds;
	@TableField("client_secret")
	private String clientSecret;
	@TableField("scope")
	@Column(name = "scope", columnDefinition = "VARCHAR2(255)")
	private String scope;
	@TableField("authorized_grant_types")
	private String authorizedGrantTypes;
	@TableField("web_server_redirect_uri")
	private String webServerRedirectUri;
	private String authorities;
	@TableField("access_token_validity")
	private Integer accessTokenValidity;
	@TableField("refresh_token_validity")
	private Integer refreshTokenValidity;
	@TableField("additional_information")
	private String additionalInformation;
	private String autoapprove;
	@TableField("remark")
	private String remark;
	@TableField("client_index")
	private String clientIndex;

	// 新增业务字段
	@TableField("client_name")
	private String clientName;
	@TableField("create_time")
	private Date createTime;
	@TableField("STATUS")
	private String status;
	@TableField("SORT")
	private Integer sort;
	@TableField("CLIENT_ICON")
	private String clientIcon;


}
