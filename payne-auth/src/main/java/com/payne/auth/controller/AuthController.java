package com.payne.auth.controller;

import cn.hutool.core.codec.Base64;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.payne.auth.entity.OauthClientDetails;
import com.payne.auth.service.OauthClientDetailsService;
import com.payne.core.config.ConfigProperties;
import com.payne.core.constant.Constants;
import com.payne.core.utils.JwtSubject;
import com.payne.core.utils.JwtUtil;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.core.web.Resp;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * 身份验证控制器
 *
 * <AUTHOR>
 * @date 2024/08/28
 */
@RestController
@RequestMapping("/api/cus-auth")
public class AuthController extends BaseController {

    @Resource
    private ConfigProperties configProperties;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private OauthClientDetailsService oauthClientDetailsService;


    /**
     * 获取令牌
     *
     * @param clientId     客户端id
     * @param clientSecret 客户机密
     * @param username     用户名（可选）
     * @return {@link Resp }
     */
    @PostMapping("/token")
    public ApiResult getToken(String clientId, String clientSecret, String username) {
        if (StringUtils.isEmpty(clientId) || StringUtils.isEmpty(clientSecret)) {
            return fail("clientId和clientSecret不能为空");
        }
        
        // 根据是否有 username 构建不同的 basicKey
        String basicKey;
        if (StringUtils.isNotEmpty(username)) {
            basicKey = Base64.encode(clientId + ":" + clientSecret + ":" + username);
        } else {
            basicKey = Base64.encode(clientId + ":" + clientSecret);
        }
        
        return getOrCreateToken(basicKey, clientId, clientSecret);
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return {@link ApiResult }
     */
    @PostMapping("/refresh")
    public ApiResult refreshToken(@RequestParam String refreshToken) {
        if (StringUtils.isEmpty(refreshToken)) {
            return fail("refreshToken不能为空");
        }

        String basicKey = (String) redisTemplate.opsForValue().get(refreshToken);
        if (StringUtils.isEmpty(basicKey)) {
            return fail("无效的refreshToken");
        }

        String[] credentials = new String(Base64.decode(basicKey)).split(":");
        if (credentials.length < 2) {  // 修改判断条件，因为现在可能有3个部分
            return fail("无效的refreshToken");
        }

        return getOrCreateToken(basicKey, credentials[0], credentials[1]);
    }

    private ApiResult getOrCreateToken(String basicKey, String clientId, String clientSecret) {
        Map<String, Object> map = new HashMap<>();
        String accessToken = (String) redisTemplate.opsForValue().get(basicKey);

        if (StringUtils.isNotBlank(accessToken)) {
            Long expireTimes = redisTemplate.getExpire(basicKey);
            if (expireTimes != null && expireTimes > 600) {
                return buildResponse(map, accessToken, expireTimes);
            }
        }

        return createNewToken(clientId, clientSecret, basicKey, map);
    }

    private ApiResult createNewToken(String clientId, String clientSecret, String basicKey, Map<String, Object> map) {
        OauthClientDetails oauthClientDetails = getOauthClientDetails(clientId, clientSecret);
        if (oauthClientDetails == null) {
            return fail("clientId和clientSecret不匹配");
        }

        Long expireTime = configProperties.getClientTokenExpireTime();
        String accessToken = JwtUtil.buildToken(new JwtSubject(basicKey), expireTime, configProperties.getTokenKey());
        String refreshToken = UUID.randomUUID().toString();

        redisTemplate.opsForValue().set(basicKey, accessToken, expireTime, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(accessToken, oauthClientDetails, expireTime, TimeUnit.SECONDS);
        redisTemplate.opsForValue().set(refreshToken, basicKey, expireTime * 2, TimeUnit.SECONDS);

        return buildResponse(map, accessToken, expireTime, refreshToken);
    }

    private OauthClientDetails getOauthClientDetails(String clientId, String clientSecret) {
        LambdaQueryWrapper<OauthClientDetails> wrapper = new LambdaQueryWrapper<OauthClientDetails>()
                .eq(OauthClientDetails::getStatus, Constants.ENABLE)
                .eq(OauthClientDetails::getClientId, clientId)
                .eq(OauthClientDetails::getClientSecret, clientSecret);
        return oauthClientDetailsService.getOne(wrapper);
    }

    private ApiResult buildResponse(Map<String, Object> map, String accessToken, Long expiresIn) {
        map.put("access_token", accessToken);
        map.put("expires_in", expiresIn);
        return success(map);
    }

    private ApiResult buildResponse(Map<String, Object> map, String accessToken, Long expiresIn, String refreshToken) {
        map.put("access_token", accessToken);
        map.put("expires_in", expiresIn);
        map.put("refresh_token", refreshToken);
        return success(map);
    }

}
