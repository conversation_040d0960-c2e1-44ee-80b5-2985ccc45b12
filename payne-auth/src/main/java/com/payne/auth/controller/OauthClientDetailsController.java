package com.payne.auth.controller;

import com.payne.auth.entity.OauthClientDetails;
import com.payne.auth.mapper.OauthClientDetailsMapper;
import com.payne.auth.param.OauthClientDetailsParam;
import com.payne.auth.service.OauthClientDetailsService;
import com.payne.core.annotation.OperationLog;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import jakarta.annotation.Resource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;


/**
 * oauth客户端管理
 *
 * <AUTHOR>
 * @date 2024/08/28
 */
@RestController
@RequestMapping("/api/oauth-client-details")
public class OauthClientDetailsController extends BaseController {

    @Resource
    private OauthClientDetailsService oauthClientDetailsService;
    @Resource
    private OauthClientDetailsMapper oauthClientDetailsMapper;

    @GetMapping("/page")
    public PageResult<OauthClientDetails> page(OauthClientDetailsParam param) {
        PageParam<OauthClientDetails, OauthClientDetailsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = oauthClientDetailsService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部
     */
    @GetMapping()
    public List<OauthClientDetails> list(OauthClientDetailsParam param) {
        PageParam<OauthClientDetails, OauthClientDetailsParam> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return oauthClientDetailsService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询渠道账号信息
     */
    @GetMapping("/{id}")
    public OauthClientDetails get(@PathVariable("id") String id) {
        return oauthClientDetailsService.getById(id);
    }

    /**
     * 添加或修改
     */
    @OperationLog(module = "认证应用管理", comments = "保存认证应用")
    @PostMapping("/operation")
    public void save(@RequestBody OauthClientDetails client) {
        if (StringUtils.hasLength(client.getId())) {
            OauthClientDetails oauthClientDetails = oauthClientDetailsService.getById(client.getId());
            oauthClientDetails.setClientName(client.getClientName());
            oauthClientDetails.setWebServerRedirectUri(client.getWebServerRedirectUri());
            oauthClientDetails.setRemark(client.getRemark());
            oauthClientDetails.setClientIndex(client.getClientIndex());
            oauthClientDetailsService.updateById(oauthClientDetails);
        } else {
            client.setClientId(CommonUtil.randomCode(16));
            client.setClientSecret(CommonUtil.randomCode(32));
            client.setCreateTime(new Date());
            client.setAuthorizedGrantTypes("authorization_code,password");   // 默认authorization_code
            client.setScope("web");                                 // 默认web
            client.setAutoapprove(Boolean.TRUE.toString());
            oauthClientDetailsService.save(client);
        }
    }

    /**
     * 批量删除
     */
    @OperationLog(module = "认证应用管理", comments = "批量删除认证应用")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        oauthClientDetailsService.removeByIds(ids);
    }

}
