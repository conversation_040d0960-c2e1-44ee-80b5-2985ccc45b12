<!doctype html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta
      http-equiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <link rel="icon" href="/favicon.ico" />
    <!-- hiprint 打印样式 - 必须添加 -->
    <link
      rel="stylesheet"
      type="text/css"
      media="print"
      href="https://cdn.jsdelivr.net/npm/vue-plugin-hiprint@latest/dist/print-lock.css"
    />
    <script>
      (function () {
        var metaEl = document.createElement('meta');
        metaEl.setAttribute('name', 'viewport');
        if (
          JSON.parse(localStorage.getItem('theme') || '{}').responsive === false
        ) {
          metaEl.setAttribute('content', 'width=1200, initial-scale=1.0');
        } else {
          metaEl.setAttribute(
            'content',
            'width=device-width, initial-scale=1.0'
          );
          document.documentElement.style.overflow = 'hidden';
        }
        document.head.appendChild(metaEl);
        if (navigator.userAgent.includes('WebKit')) {
          document.documentElement.classList.add('is-webkit');
        }
      })();
    </script>
    <title>管理系统</title>
    <style>
      .ele-admin-loading {
        width: 28px;
        height: 28px;
        display: grid;
        grid-gap: 8px;
        grid-template-rows: repeat(2, 1fr);
        grid-template-columns: repeat(2, 1fr);
        transform: rotate(45deg);
        animation: loadingRotate 1.2s infinite linear;
        position: fixed;
        top: calc(50% - 14px);
        left: calc(50% - 14px);
      }

      .ele-admin-loading > i {
        border-radius: 50%;
        background: #1677ff;
      }

      .ele-admin-loading > i:nth-child(2) {
        opacity: 0.7;
      }

      .ele-admin-loading > i:nth-child(3) {
        opacity: 0.5;
      }

      .ele-admin-loading > i:nth-child(4) {
        opacity: 0.3;
      }

      @keyframes loadingRotate {
        to {
          transform: rotate(405deg);
        }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="ele-admin-loading">
        <i></i>
        <i></i>
        <i></i>
        <i></i>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script type="text/javascript" src="/security.js"></script>
    <script type="text/javascript" src="/api/sec_js"></script>
  </body>
</html>
