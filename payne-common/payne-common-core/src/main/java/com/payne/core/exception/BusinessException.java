package com.payne.core.exception;

import com.payne.core.constant.Constants;
import lombok.Data;

/**
 * 自定义业务异常
 *
 */
@Data
public class BusinessException extends RuntimeException {
    private static final long serialVersionUID = 1L;
    private Integer code;
    private String data;

    public BusinessException() {
        this(Constants.RESULT_ERROR_MSG);
    }

    public BusinessException(String message) {
        this(Constants.RESULT_ERROR_CODE, message);
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(Integer code, String message, String data) {
        super(message);
        this.code = code;
        this.data = data;
    }

    public BusinessException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public BusinessException(Integer code, String message, Throwable cause,
                             boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
    }
}
