package com.payne.core.web;

import com.baomidou.mybatisplus.annotation.TableField;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.utils.CommonUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询参数基本字段
 *
 * 
 * @since 2021-08-26 22:14:43
 */
@Data
public class BaseParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分页查询页码
     */
    @TableField(exist = false)
    private Long page;

    /**
     * 分页查询每页数量
     */
    @TableField(exist = false)
    private Long limit;
    /**
     * 排序字段, 排序字段或sql, 如果是sql则order字段无用, 如: `id asc, name desc`
     */
    @TableField(exist = false)
    private String sort;

    /**
     * 排序方式, sort是字段名称时对应的排序方式, asc升序, desc降序
     */
    @TableField(exist = false)
    private String order;

    /**
     * 创建时间起始值
     */
    @QueryField(value = "create_time", type = QueryType.GE)
    @TableField(exist = false)
    private String createTimeStart;
    /**
     * 创建时间结束值
     */
    @QueryField(value = "create_time", type = QueryType.LE)
    @TableField(exist = false)
    private String createTimeEnd;

    /**
     * 获取集合中的第一条数据
     *
     * @param records 集合
     * @return 第一条数据
     */
    public <T> T getOne(List<T> records) {
        return CommonUtil.listGetOne(records);
    }
}
