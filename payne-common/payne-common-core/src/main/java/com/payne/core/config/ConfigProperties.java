package com.payne.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * 系统配置属性
 *
 */
@Data
@ConfigurationProperties(prefix = "config")
public class ConfigProperties {
    /**
     * 文件上传磁盘位置
     */
    private Integer uploadLocation = 0;

    /**
     * 文件上传是否使用uuid命名
     */
    private Boolean uploadUuidName = true;

    /**
     * 文件上传生成缩略图的大小(kb)
     */
    private Integer thumbnailSize = 60;

    /**
     * OpenOffice的安装目录
     */
    private String openOfficeHome;

    /**
     * client token过期时间, 单位秒
     */
    private Long clientTokenExpireTime = 60 * 60 * 24 * 7L;
    /**
     * token过期时间, 单位秒
     */
    private Long tokenExpireTime = 60 * 60 * 24L;

    /**
     * token快要过期自动刷新时间, 单位分钟
     */
    private int tokenRefreshTime = 30;

    /**
     * 生成token的密钥Key的base64字符
     */
    private String tokenKey;
    /**
     * 临时文件目录
     */
    private String tempFileDir;
}
