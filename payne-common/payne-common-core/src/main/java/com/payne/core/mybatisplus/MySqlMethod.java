package com.payne.core.mybatisplus;

import lombok.Getter;

/**
 *  2024/8/30.
 */
@Getter
public enum MySqlMethod {

    SELECT_GROUP_COUNT("selectGroupCount", "分组查询满足条件总记录数", "<script>%s SELECT %s ,COUNT(*) AS total FROM %s %s %s\n</script>"),
    SELECT_GROUP_COUNT_WITH_PERMISSION("selectGroupCountWithPermission", "分组查询满足条件总记录数", "<script>%s SELECT %s ,COUNT(*) AS total FROM %s %s %s\n</script>"),
    SELECT_COUNT_WITH_PERMISSION("selectCountWithPermission", "查询满足条件总记录数", "<script>%s SELECT COUNT(%s) AS total FROM %s %s %s\n</script>"),
    SELECT_LIST_WITH_PERMISSION("selectListWithPermission", "查询满足条件所有数据", "<script>%s SELECT %s FROM %s %s %s\n</script>"),
    SELECT_PAGE_WITH_PERMISSION("selectPageWithPermission", "查询满足条件所有数据（并翻页）", "<script>%s SELECT %s FROM %s %s %s\n</script>"),

    SELECT_JOIN_COUNT_WITH_PERMISSION("selectJoinCountWithPermission", "查询满足条件总记录数",
            "<script>\n%s SELECT COUNT(%s) FROM %s %s %s %s %s\n</script>"),
    SELECT_JOIN_LIST_WITH_PERMISSION("selectJoinListWithPermission", "返回List集合",
            "<script>\n%s SELECT %s %s FROM %s %s %s %s %s %s\n</script>"),
    SELECT_JOIN_ONE_WITH_PERMISSION("selectJoinOneWithPermission", "返回一条记录",
            "<script>\n%s SELECT %s %s FROM %s %s %s %s %s\n</script>"),
    SELECT_JOIN_MAP_WITH_PERMISSION("selectJoinMapWithPermission", "返回一个Map",
            "<script>\n%s SELECT %s %s FROM %s %s %s %s %s\n</script>"),
    SELECT_JOIN_MAPS_WITH_PERMISSION("selectJoinMapsWithPermission", "返回Map集合",
            "<script>\n%s SELECT %s %s FROM %s %s %s %s %s %s\n</script>"),
    SELECT_JOIN_MAPS_PAGE_WITH_PERMISSION("selectJoinMapsPageWithPermission", "返回Map集合并分页",
            "<script>\n%s SELECT %s %s FROM %s %s %s %s %s %s\n</script>"),
    SELECT_JOIN_PAGE_WITH_PERMISSION("selectJoinPageWithPermission", "连表查询并分页",
            "<script>\n%s SELECT %s %s FROM %s %s %s %s %s %s\n</script>");

    private final String method;
    private final String desc;
    private final String sql;

    MySqlMethod(String method, String desc, String sql) {
        this.method = method;
        this.desc = desc;
        this.sql = sql;
    }
}
