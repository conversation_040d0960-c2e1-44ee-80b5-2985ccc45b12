package com.payne.core.mybatisplus.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.interfaces.MPJBaseJoin;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 *  2024/8/29.
 */
public interface MyMPJBaseMapper<T> extends MPJBaseMapper<T> {
    List<Map<String, Object>> selectGroupCount(@Param("ew") Wrapper<T> queryWrapper);

    List<Map<String, Object>> selectGroupCountWithPermission(@Param("ew") Wrapper<T> queryWrapper, @Param("params") MyMPQueryParams params);

    Long selectCountWithPermission(@Param("ew") Wrapper<T> queryWrapper, @Param("params") MyMPQueryParams params);

    List<T> selectListWithPermission(@Param("ew") Wrapper<T> queryWrapper);

    List<T> selectListWithPermission(@Param("ew") Wrapper<T> queryWrapper, @Param("params") MyMPQueryParams params);

    <P extends IPage<T>> P selectPageWithPermission(P page, @Param("ew") Wrapper<T> queryWrapper);

    <P extends IPage<T>> P selectPageWithPermission(P page, @Param("ew") Wrapper<T> queryWrapper, @Param("params") MyMPQueryParams params);

    Long selectJoinCountWithPermission(@Param("ew") MPJBaseJoin<T> var1);

    <DTO> List<DTO> selectJoinListWithPermission(@Param("resultTypeClass_Eg1sG") Class<DTO> var1, @Param("ew") MPJBaseJoin<T> var2);

    <P extends IPage<Map<String, Object>>> P selectJoinMapsPageWithPermission(P var1, @Param("ew") MPJBaseJoin<T> var2);

    List<Map<String, Object>> selectJoinMapsWithPermission(@Param("ew") MPJBaseJoin<T> var1);

    Map<String, Object> selectJoinMapWithPermission(@Param("ew") MPJBaseJoin<T> var1);

    <DTO> DTO selectJoinOneWithPermission(@Param("resultTypeClass_Eg1sG") Class<DTO> var1, @Param("ew") MPJBaseJoin<T> var2);

    <DTO, P extends IPage<DTO>> P selectJoinPageWithPermission(P var1, @Param("resultTypeClass_Eg1sG") Class<DTO> var2, @Param("ew") MPJBaseJoin<T> var3);

    //传递表别名供CustomDataPermissionHandlerImpl里动态拼接条件的别名
    Long selectJoinCountWithPermission(@Param("ew") MPJBaseJoin<T> var1, @Param("params") MyMPQueryParams params);

    <DTO> List<DTO> selectJoinListWithPermission(@Param("resultTypeClass_Eg1sG") Class<DTO> var1, @Param("ew") MPJBaseJoin<T> var2, @Param("params") MyMPQueryParams params);

    <P extends IPage<Map<String, Object>>> P selectJoinMapsPageWithPermission(P var1, @Param("ew") MPJBaseJoin<T> var2, @Param("params") MyMPQueryParams params);

    List<Map<String, Object>> selectJoinMapsWithPermission(@Param("ew") MPJBaseJoin<T> var1, @Param("params") MyMPQueryParams params);

    Map<String, Object> selectJoinMapWithPermission(@Param("ew") MPJBaseJoin<T> var1, @Param("params") MyMPQueryParams params);

    <DTO> DTO selectJoinOneWithPermission(@Param("resultTypeClass_Eg1sG") Class<DTO> var1, @Param("ew") MPJBaseJoin<T> var2, @Param("params") MyMPQueryParams params);

    <DTO, P extends IPage<DTO>> P selectJoinPageWithPermission(P var1, @Param("resultTypeClass_Eg1sG") Class<DTO> var2, @Param("ew") MPJBaseJoin<T> var3, @Param("params") MyMPQueryParams params);
}
