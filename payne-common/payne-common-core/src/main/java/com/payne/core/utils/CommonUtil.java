package com.payne.core.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.payne.core.constant.Constants;
import com.payne.core.web.ApiResult;
import com.payne.core.web.ExcelImportError;
import jakarta.activation.MimetypesFileTypeMap;
import jakarta.persistence.Id;
import jakarta.persistence.Transient;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.tomcat.util.http.fileupload.servlet.ServletRequestContext;
import org.springframework.http.HttpMethod;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import java.io.*;
import java.lang.invoke.CallSite;
import java.lang.invoke.LambdaMetafactory;
import java.lang.invoke.MethodHandles;
import java.lang.invoke.MethodType;
import java.lang.reflect.Field;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import static java.lang.invoke.LambdaMetafactory.FLAG_SERIALIZABLE;

/**
 * 常用工具方法
 *
 * 
 * @since 2017-06-10 10:10:22
 */
@Slf4j
public class CommonUtil {
    // 生成uuid的字符
    private static final String[] chars = new String[]{
            "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m",
            "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
            "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
            "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M",
            "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"
    };

    /**
     * 同步获取uuid
     *
     * @return String
     */
    public static synchronized String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成8位uuid
     *
     * @return String
     */
    public static String randomUUID8() {
        StringBuilder sb = new StringBuilder();
        String uuid = getUUID();
        for (int i = 0; i < 8; i++) {
            String str = uuid.substring(i * 4, i * 4 + 4);
            int x = Integer.parseInt(str, 16);
            sb.append(chars[x % 0x3E]);
        }
        return sb.toString();
    }

    /**
     * 生成16位uuid
     *
     * @return String
     */
    public static String randomUUID16() {
        StringBuilder sb = new StringBuilder();
        String uuid = getUUID();
        for (int i = 0; i < 16; i++) {
            String str = uuid.substring(i * 2, i * 2 + 2);
            int x = Integer.parseInt(str, 16);
            sb.append(chars[x % 0x3E]);
        }
        return sb.toString();
    }

    /**
     * 检查List是否有重复元素
     *
     * @param list   List
     * @param mapper 获取需要检查的字段的Function
     * @param <T>    数据的类型
     * @param <R>    需要检查的字段的类型
     * @return boolean
     */
    public static <T, R> boolean checkRepeat(List<T> list, Function<? super T, ? extends R> mapper) {
        for (int i = 0; i < list.size(); i++) {
            for (int j = 0; j < list.size(); j++) {
                if (i != j && mapper.apply(list.get(i)).equals(mapper.apply(list.get(j)))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * List转为树形结构
     *
     * @param data           List
     * @param parentId       顶级的parentId
     * @param parentIdMapper 获取parentId的Function
     * @param idMapper       获取id的Function
     * @param consumer       赋值children的Consumer
     * @param <T>            数据的类型
     * @param <R>            parentId的类型
     * @return List<T>
     */
    public static <T, R> List<T> toTreeData(List<T> data, R parentId,
                                            Function<? super T, ? extends R> parentIdMapper,
                                            Function<? super T, ? extends R> idMapper,
                                            BiConsumer<T, List<T>> consumer) {
        List<T> result = new ArrayList<>();
        for (T d : data) {
            R dParentId = parentIdMapper.apply(d);
            if (ObjectUtil.equals(parentId, dParentId)) {
                R dId = idMapper.apply(d);
                List<T> children = toTreeData(data, dId, parentIdMapper, idMapper, consumer);
                consumer.accept(d, children);
                result.add(d);
            }
        }
        return result;
    }

    /**
     * 遍历树形结构数据
     *
     * @param data     List
     * @param consumer 回调
     * @param mapper   获取children的Function
     * @param <T>      数据的类型
     */
    public static <T> void eachTreeData(List<T> data, Consumer<T> consumer, Function<T, List<T>> mapper) {
        for (T d : data) {
            consumer.accept(d);
            List<T> children = mapper.apply(d);
            if (children != null && children.size() > 0) {
                eachTreeData(children, consumer, mapper);
            }
        }
    }

    /**
     * 获取集合中的第一条数据
     *
     * @param records 集合
     * @return 第一条数据
     */
    public static <T> T listGetOne(List<T> records) {
        return records == null || records.size() == 0 ? null : records.get(0);
    }

    /**
     * 支持跨域
     *
     * @param response HttpServletResponse
     */
    public static void addCrossHeaders(HttpServletResponse response) {
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setHeader("Access-Control-Expose-Headers", Constants.TOKEN_HEADER_NAME);
    }

    /**
     * 输出错误信息
     *
     * @param response HttpServletResponse
     * @param code     错误码
     * @param message  提示信息
     * @param error    错误信息
     */
    public static void responseError(HttpServletResponse response, Integer code, String message, String error) {
        response.setContentType("application/json;charset=UTF-8");
        try {
            PrintWriter out = response.getWriter();
            out.write(JSONUtil.toJSONString(new ApiResult<>(code, message, null, error)));
            out.flush();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void responseError(HttpServletResponse response, String error) {
        response.setContentType("application/json;charset=UTF-8");
        try {
            PrintWriter out = response.getWriter();
            out.write(JSONUtil.toJSONString(new ApiResult<>(Constants.RESULT_ERROR_CODE, Constants.RESULT_ERROR_MSG, null, error)));
            out.flush();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static void responseSuccess(HttpServletResponse response) {
        response.setContentType("application/json;charset=UTF-8");
        try {
            PrintWriter out = response.getWriter();
            out.write(JSONUtil.toJSONString(new ApiResult<>(Constants.RESULT_OK_CODE, Constants.RESULT_OK_MSG, null, null)));
            out.flush();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
    }

    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip) || "127.0.0.1".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip) || "127.0.0.1".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip) || "127.0.0.1".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (StrUtil.isBlank(ip) || "unknown".equalsIgnoreCase(ip) || "127.0.0.1".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (StrUtil.isBlank(ip) || "127.0.0.1".equals(ip) || ip.indexOf(":") > -1) {
            try {
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                ip = null;
            }
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        return ip;
    }

    public static String md5encrypt(String text) {
        try {
            MessageDigest alg = MessageDigest.getInstance("MD5");
            alg.update(text.getBytes());
            byte[] digesta = alg.digest();
            return byte2hex(digesta);
        } catch (NoSuchAlgorithmException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static String byte2hex(byte[] bytes) {
        StringBuffer hs = new StringBuffer();
        String stmp = "";
        for (int n = 0; n < bytes.length; ++n) {
            stmp = Integer.toHexString(bytes[n] & 0xFF);
            if (stmp.length() == 1) {
                hs.append("0");
                hs.append(stmp);
            } else {
                hs.append(stmp);
            }
        }
        return hs.toString();
    }

    /**
     * 随机码
     *
     * @param length 随机码长度
     * @return
     */
    public synchronized static String randomCode(int length) {
        String val = "";
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            String str = random.nextInt(2) % 2 == 0 ? "num" : "char";
            if ("char".equalsIgnoreCase(str)) { // 产生字母
                int nextInt = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (nextInt + random.nextInt(26));
            } else if ("num".equalsIgnoreCase(str)) { // 产生数字
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }

    public static Field[] getAllFields(Class<?> clazz) {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        return fieldList.toArray(fields);
    }

    public static Field getField(Class<?> clazz, String name) {
        return getField(getAllFields(clazz), name);
    }

    public static Field getField(Field[] allFields, String name) {
        for (Field field : allFields) {
            if (name.equals(field.getName()))
                return field;
        }
        return null;
    }

    public static void setValue(@NotNull Object target, @NotEmpty String name, Object value) {
        Field field = ReflectUtil.getField(target.getClass(), name);
        setValue(target, field, value);
    }

    public static void setValue(@NotNull Object target, @NotNull Field field, Object value) {
        if (field == null)
            return;
        ReflectUtil.setFieldValue(target, field, value);
    }

    public static Object getValue(@NotNull String name, @NotNull Object target) {
        try {
            Field field = ReflectUtil.getField(target.getClass(), name);
            field.setAccessible(true);
            return field.get(target);
        } catch (IllegalAccessException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static void setValueFromRequest(@NotNull Object target, @NotNull HttpServletRequest request) {
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String name = parameterNames.nextElement();
            String value = request.getParameter(name);
            setValue(target, name, value);
        }
    }

    public static Map<String, String> getParamMap(@NotNull HttpServletRequest request) {
        return JakartaServletUtil.getParamMap(request);
    }

    public static <T> T toBean(Object source, Class<T> clazz) {
        return BeanUtil.toBean(source, clazz);
    }

    public static boolean isMultipartContent(HttpServletRequest request) {
        if (!HttpMethod.POST.name().equalsIgnoreCase(request.getMethod()))
            return false;
        ServletRequestContext ctx = new ServletRequestContext(request);
        String contentType = ctx.getContentType();
        if (contentType == null) {
            return false;
        } else {
            return contentType.toLowerCase(Locale.ENGLISH).startsWith("multipart/");
        }
    }

    public static Map<String, List<MultipartFile>> getFileFromRequest(@NotNull HttpServletRequest request) {
        boolean multipartContent = isMultipartContent(request);
        Map<String, List<MultipartFile>> fileMap = new HashMap<>();
        if (multipartContent) {
            MultipartHttpServletRequest multipartHttpServletRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
            if (multipartHttpServletRequest != null)
                fileMap = getFileFromRequest(multipartHttpServletRequest);
        }
        return fileMap;
    }

    public static Map<String, List<MultipartFile>> getFileFromRequest(@NotNull MultipartHttpServletRequest request) {
        Map<String, List<MultipartFile>> files = new HashMap<>();
        Iterator<String> fileNames = request.getFileNames();
        while (fileNames.hasNext()) {
            String fileName = fileNames.next();
            files.put(fileName, request.getFiles(fileName));
        }
        return files;
    }

    public static String getPrimaryKey(Class<?> clazz) {
        Field[] fields = CommonUtil.getAllFields(clazz);
        for (Field field : fields) {
            if (field.isAnnotationPresent(Id.class)) {
                return field.getName();
            }
        }
        return null;
    }

    public static boolean isPrimaryKey(Field field) {
        return field.isAnnotationPresent(Id.class);
    }

    /**
     * 导入错误信息生成本地临时文件
     *
     * @param errors
     * @return
     */
    public static String writeErrorInfoExcel(List<ExcelImportError> errors) {
        String id = CommonUtil.randomUUID16() + "_error";
        File tempDirectory = FileUtils.getTempDirectory();
        File filePath = new File(tempDirectory, id + ".xlsx");
        //生成本地文件
        EasyExcel.write(filePath.getAbsolutePath(), ExcelImportError.class).sheet("sheet1").doWrite(errors);
        return id;
    }

    /**
     * 生成本地临时文件
     *
     * @param data
     * @return
     */
    public static String writeDataExcel(List<?> data) {
        String id = CommonUtil.randomUUID16() + "_data";
        File tempDirectory = FileUtils.getTempDirectory();
        File filePath = new File(tempDirectory, id + ".xlsx");
        //生成本地文件
        EasyExcel.write(filePath.getAbsolutePath(), Object.class).sheet("sheet1").doWrite(data);
        return id;
    }

    /**
     * 逗号分割
     *
     * @param str
     * @return
     */
    public static String[] split(String str) {
        return str.split(",");
    }

    public static String[] split(String str, String separator) {
        return str.split(separator);
    }

    public static String strJoin(Collection<String> strList) {
        return strJoin(strList, ",");
    }

    public static String strJoin(Collection<String> strList, String separator) {
        if (CollectionUtils.isEmpty(strList)) return null;
        return String.join(separator, strList);
    }

    public static Set<Integer> toIntSet(String str) {
        return Arrays.stream(str.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toSet());
    }

    public static List<Integer> toIntList(String str) {
        return Arrays.stream(str.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    //Set转逗号分割String
    public static String setToStr(Set<String> strSet) {
        return strJoin(strSet, ",");
    }

    /**
     * zip包解压到指定目录
     *
     * @param zipFilePath   zip包文件路径
     * @param destDirectory 解压目录
     * @throws IOException
     */
    public static void unzip(String zipFilePath, String destDirectory) throws IOException {
        File dir = new File(destDirectory);
        if (!dir.exists()) dir.mkdirs();
        FileInputStream fis;
        ZipInputStream zipIs;
        ZipEntry zEntry;
        fis = new FileInputStream(zipFilePath);
        zipIs = new ZipInputStream(fis);
        while ((zEntry = zipIs.getNextEntry()) != null) {
            String string = destDirectory + File.separator + zEntry.getName();
            if (zEntry.isDirectory()) {
                new File(string).mkdirs();
                continue;
            }
            byte[] buffer = new byte[1024];
            FileOutputStream fos = new FileOutputStream(string);
            int len;
            while ((len = zipIs.read(buffer)) > 0) {
                fos.write(buffer, 0, len);
            }
            fos.close();
        }
        zipIs.close();
        fis.close();
    }

    /**
     * 读取目录下所有文件路径
     *
     * @param destDirectory
     * @return
     */
    public static List<String> readFilePath(String destDirectory) {
        List<String> list = new ArrayList<>();
        readFilePath(destDirectory, list);
        return list;
    }

    public static void readFilePath(String destDirectory, List<String> list) {
        File file = new File(destDirectory);
        if (!file.exists()) {
            return;
        }
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                readFilePath(f.getAbsolutePath(), list);
            }
        } else {
            list.add(file.getAbsolutePath());
        }
    }

    public static boolean isImage(File file) {
        if (file == null || !file.exists()) {
            return false;
        }

        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            byte[] byteArray = new byte[8];
            fileInputStream.read(byteArray, 0, 8);

            String mimeType = new MimetypesFileTypeMap().getContentType(file);
            return mimeType.toLowerCase(Locale.ROOT).startsWith("image/");
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            return false;
        }
    }

    public static boolean isZipFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        String contentType = file.getContentType();
        if (StringUtils.hasLength(contentType) && contentType.startsWith("application/zip"))
            return true;
        return false;
    }

    public static boolean isImageFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }
        String contentType = file.getContentType();
        if (StringUtils.hasLength(contentType) && contentType.startsWith("image/"))
            return true;
        return false;
    }

    /**
     * 构造一个 SFunction 对象
     *
     * @param entityClass
     * @param fieldName
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> SFunction<T, R> getSFunction(Class<T> entityClass, String fieldName) {
        SFunction<T, R> func = null;
        // 获取 MethodHandles.Lookup 实例，用于反射操作。
        final MethodHandles.Lookup lookup = MethodHandles.lookup();
        // 定义方法类型，表示实体类的实例方法，该方法返回字段的类型。
        Field field = getField(entityClass, fieldName);
        MethodType methodType = MethodType.methodType(field.getType(), entityClass);
        // 用于存储 LambdaMetafactory 创建的 CallSite 对象。
        final CallSite site;
        // 构造标准的 Java getter 方法名。
        String getFunName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
        // 使用 LambdaMetafactory 创建一个动态的 SFunction 实例。
        try {
            site = LambdaMetafactory.altMetafactory(
                    lookup,
                    "invoke",
                    MethodType.methodType(SFunction.class),
                    methodType,
                    lookup.findVirtual(entityClass, getFunName, MethodType.methodType(field.getType())),
                    methodType,
                    FLAG_SERIALIZABLE
            );

            // 使用 CallSite 来获取 SFunction 实例。
            func = (SFunction<T, R>) site.getTarget().invokeExact();
        } catch (Throwable e) {
            // 如果在创建 SFunction 过程中发生异常，抛出异常，指出实体类中没有找到对应的 getter 方法。
            log.error(e.getMessage(), e);
        }
        return func;
    }

    /**
     * 根据实体字段构造 SFunction 数组对象
     *
     * @param aClass            对象class
     * @param fields            字段组
     * @param includePrimaryKey 是否包含主键
     * @param <T>
     * @return
     */
    public static <T> SFunction[] buildSFunctions(Class<T> aClass, Field[] fields, boolean includePrimaryKey) {
        List<SFunction<T, Object>> list = new ArrayList<>();
        List<String> ignoreField = List.of("serialVersionUID");
        for (Field field : fields) {
            Transient annotation = field.getAnnotation(Transient.class);
            if (annotation != null || (!includePrimaryKey && isPrimaryKey(field)) || ignoreField.contains(field.getName()))
                continue;
            SFunction<T, Object> sFunction = getSFunction(aClass, field.getName());
            if (sFunction != null)
                list.add(sFunction);
        }
        return list.toArray(new SFunction[]{});
    }

    /**
     * 根据实体字段构造 SFunction 数组对象
     *
     * @param aClass            对象class
     * @param includePrimaryKey 是否包含主键
     * @param <T>
     * @return
     */
    public static <T> SFunction[] buildSFunctions(Class<T> aClass, boolean includePrimaryKey) {
        return buildSFunctions(aClass, getAllFields(aClass), includePrimaryKey);
    }

    public static String str2SqlInCondition(String[] str) {
        if (str == null || str.length == 0)
            return null;

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length; i++) {
            sb.append("'");
            sb.append(str[i]);
            sb.append("'");
            if (i < str.length - 1) {
                sb.append(",");
            }
        }
        return sb.toString();
    }

    public static String str2SqlInCondition(String str) {
        if (!StringUtils.hasText(str)) return null;
        return str2SqlInCondition(split(str));
    }

    public static String appendComma(String... str) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < str.length; i++) {
            stringBuilder.append(str[i]);
            if (i < str.length - 1) {
                stringBuilder.append(",");
            }
        }
        return stringBuilder.toString();
    }

    public static String[] strArray(String... str) {
        return str;
    }

    public static <T> List<T> pageList(List<T> list, int page, int pageSize) {
        List<T> lists = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            int startIndex;
            int endIndex;
            int total = list.size();
            int num = total % pageSize;
            startIndex = (page - 1) * pageSize;
            if (startIndex > total) {
                return new ArrayList<>();
            }
            endIndex = page * pageSize;
            if (endIndex > total) {
                endIndex = (page - 1) * pageSize + num;
            }
            lists = list.subList(startIndex, endIndex);
        }
        return lists;
    }

    /**
     * 转义JSON字符串中的特殊字符
     *
     * @param input 原始字符串
     * @return 转义后的字符串
     */
    public static String escapeJsonString(String input) {
        if (input == null) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            switch (c) {
                case '"':
                    result.append("\\\"");
                    break;
                case '\\':
                    result.append("\\\\");
                    break;
                case '/':
                    result.append("\\/");
                    break;
                case '\b':
                    result.append("\\b");
                    break;
                case '\f':
                    result.append("\\f");
                    break;
                case '\n':
                    result.append("\\n");
                    break;
                case '\r':
                    result.append("\\r");
                    break;
                case '\t':
                    result.append("\\t");
                    break;
                default:
                    // 处理其他控制字符
                    if (c < ' ') {
                        String hex = Integer.toHexString(c);
                        result.append("\\u");
                        for (int j = 0; j < 4 - hex.length(); j++) {
                            result.append('0');
                        }
                        result.append(hex);
                    } else {
                        result.append(c);
                    }
                    break;
            }
        }
        return result.toString();
    }
}
