package com.payne.core.mybatisplus.base;

import com.payne.core.enums.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MyMPJBaseMapper查询参数
 *
 * <AUTHOR>
 * @date 2024/10/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MyMPQueryParams {

    public MyMPQueryParams(String tableAlias) {
        this.tableAlias = tableAlias;
    }

    public MyMPQueryParams(UserType userType) {
        this.userType = userType;
    }

    public MyMPQueryParams(Class<?> aClass) {
        this.aClass = aClass;
    }

    /**
     * 表别名,多表联合查询时指定权限过滤的表别名
     */
    private String tableAlias;
    /**
     * 权限过滤查询USER_INFO时,指定用户类型
     */
    private UserType userType;
    /**
     * 追加数据权限范围业务表class, 根据业务表XGH,XYID,ZYID,BJID,PYCCID,NJID等字段进行过滤
     */
    private Class<?> aClass;

}
