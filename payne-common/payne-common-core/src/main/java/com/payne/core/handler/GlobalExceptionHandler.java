package com.payne.core.handler;

import com.payne.core.constant.Constants;
import com.payne.core.exception.BusinessException;
import com.payne.core.utils.CommonUtil;
import com.payne.core.web.ApiResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.nio.file.AccessDeniedException;


/**
 * 全局异常处理器
 *
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    @ResponseBody
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ApiResult<?> methodNotSupportedExceptionHandler(HttpRequestMethodNotSupportedException e,
                                                           HttpServletResponse response) {
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, "请求方式不正确").setError(e.toString());
    }

    @ResponseBody
    @ExceptionHandler(AccessDeniedException.class)
    public ApiResult<?> accessDeniedExceptionHandler(AccessDeniedException e, HttpServletResponse response) {
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.UNAUTHORIZED_CODE, Constants.UNAUTHORIZED_MSG).setError(Constants.UNAUTHORIZED_MSG);
    }

    @ResponseBody
    @ExceptionHandler(BindException.class)
    public ApiResult<?> methodArgumentNotValidExceptionHandler(BindException e, HttpServletRequest request, HttpServletResponse response) {
        BindingResult bindingResult = e.getBindingResult();
        log.error("请求[ {} ] {} 的参数校验发生错误", request.getMethod(), request.getRequestURL());
//        for (ObjectError objectError : bindingResult.getAllErrors()) {
//            FieldError fieldError = (FieldError) objectError;
//            log.error("参数 {} = {} 校验错误：{}", fieldError.getField(), fieldError.getRejectedValue(), fieldError.getDefaultMessage());
//            result.put(fieldError.getField(), fieldError.getDefaultMessage());
//        }
        String defaultMessage = bindingResult.getAllErrors().get(0).getDefaultMessage();
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, defaultMessage).setError(defaultMessage);
    }

    @ResponseBody
    @ExceptionHandler(BusinessException.class)
    public ApiResult<?> businessExceptionHandler(BusinessException e, HttpServletResponse response) {
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        int errorCode = Constants.RESULT_ERROR_CODE;
        if (e.getCode() != null)
            errorCode = e.getCode();
        return new ApiResult<>(errorCode, e.getMessage()).setData(e.getData()).setError(Constants.RESULT_ERROR_MSG);
    }

    @ResponseBody
    @ExceptionHandler(Throwable.class)
    public ApiResult<?> exceptionHandler(Throwable e, HttpServletResponse response) {
        log.error(e.getMessage(), e);
        CommonUtil.addCrossHeaders(response);
        return new ApiResult<>(Constants.RESULT_ERROR_CODE, Constants.RESULT_ERROR_MSG).setError(Constants.RESULT_ERROR_MSG);
    }

}
