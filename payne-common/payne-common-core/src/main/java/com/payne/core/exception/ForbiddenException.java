package com.payne.core.exception;

import com.payne.core.constant.Constants;

/**
 * 权限不足异常
 * 通常在用户尝试访问未授权的资源或执行未授权的操作时抛出。
 * 建议HTTP状态码映射为 403 Forbidden.
 */
public class ForbiddenException extends BusinessException { // 或者继承 RuntimeException

     private static Integer CODE = 403;

    public ForbiddenException() {
        super("权限不足，禁止访问"); // 提供一个默认消息
    }

    public ForbiddenException(String message) {
        super(message);
    }

    public ForbiddenException(String message, Throwable cause) {
        super(Constants.UNAUTHORIZED_CODE,message, cause);
    }

}