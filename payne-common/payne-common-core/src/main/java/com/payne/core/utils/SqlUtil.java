package com.payne.core.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.payne.core.enums.UserType;

import java.util.Map;

public class SqlUtil {

    public static String getRealSql(Object wrapper) {
        String sqlSegment;
        Map<String, Object> paramNameValuePairs;

        if (wrapper instanceof QueryWrapper) {
            QueryWrapper<?> queryWrapper = (QueryWrapper<?>) wrapper;
            sqlSegment = queryWrapper.getSqlSegment();
            paramNameValuePairs = queryWrapper.getParamNameValuePairs();
        } else {
            throw new IllegalArgumentException("Unsupported wrapper type");
        }
        for (Map.Entry<String, Object> entry : paramNameValuePairs.entrySet()) {
            String placeholder = "#{ew.paramNameValuePairs." + entry.getKey() + "}";
            Object value = entry.getValue();
            String realValue = "";
            if (value instanceof UserType) {
                realValue = String.valueOf(((UserType) value).getValue());
            } else if (value instanceof String) {
                realValue = "'" + value + "'";
            } else {
                realValue = value.toString();
            }
            sqlSegment = sqlSegment.replace(placeholder, realValue);
        }
        return sqlSegment;
    }

}
