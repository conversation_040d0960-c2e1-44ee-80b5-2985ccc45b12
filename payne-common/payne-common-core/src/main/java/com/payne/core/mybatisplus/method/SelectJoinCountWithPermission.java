package com.payne.core.mybatisplus.method;

import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.github.yulichang.method.MPJAbstractMethod;
import com.payne.core.mybatisplus.MySqlMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * copy {@link com.baomidou.mybatisplus.core.injector.methods.SelectCount}
 *
 * <AUTHOR>
 * @since 1.1.8
 */
public class SelectJoinCountWithPermission extends MPJAbstractMethod {
    protected static final MySqlMethod sqlMethod = MySqlMethod.SELECT_JOIN_COUNT_WITH_PERMISSION;

    public SelectJoinCountWithPermission() {
        super(sqlMethod.getMethod());
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = String.format(sqlMethod.getSql(), sqlFirst(), sqlCount(),
                mpjTableName(tableInfo), sqlAlias(), sqlFrom(), sqlWhereEntityWrapper(true, tableInfo), sqlComment());
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, removeExtraWhitespaces(sql), modelClass);
        return this.addSelectMappedStatementForOther(mapperClass, sqlMethod.getMethod(), sqlSource, Long.class);
    }
}
