package com.payne.core.mybatisplus.method;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.payne.core.mybatisplus.MySqlMethod;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 *  2024/12/2.
 */
public class SelectCountWithPermission extends AbstractMethod {
    protected static final MySqlMethod sqlMethod = MySqlMethod.SELECT_COUNT_WITH_PERMISSION;

    public SelectCountWithPermission() {
        super(sqlMethod.getMethod());
    }

    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        String sql = String.format(sqlMethod.getSql(), this.sqlFirst(), this.sqlCount(), tableInfo.getTableName(),
                this.sqlWhereEntityWrapper(true, tableInfo), this.sqlComment());
        SqlSource sqlSource = super.createSqlSource(this.configuration, sql, modelClass);
        return this.addSelectMappedStatementForOther(mapperClass, this.methodName, sqlSource, Long.class);
    }
}
