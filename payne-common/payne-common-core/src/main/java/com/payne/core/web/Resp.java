package com.payne.core.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.payne.core.constant.Constants;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

/**
 *  2020/4/7.
 */
public class Resp<T> implements java.io.Serializable {

    private int code;
    private String msg;
    private T info;

    public Resp() {
        this.code = Constants.RESULT_OK_CODE;
    }

    public Resp(T info) {
        this.code = Constants.RESULT_OK_CODE;
        this.info = info;
    }

    public Resp(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    public Resp(int code, T info) {
        this.code = code;
        this.info = info;
    }
    public Resp(int code,String msg, T info) {
        this.code = code;
        this.msg = msg;
        this.info = info;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getInfo() {
        return info;
    }

    public void setInfo(T info) {
        this.info = info;
    }

    public static Resp<String> success() {
        return new Resp(Constants.RESULT_OK_CODE, Constants.RESULT_OK_MSG);
    }

    public static <T> Resp<T> success(T info) {
        return new Resp(Constants.RESULT_OK_CODE, info);
    }

    public static <T> Resp<T> success(int code, T info) {
        return new Resp(code, info);
    }
    public static <T> Resp<T> success(int code,String msg, T info) {
        return new Resp(code,msg, info);
    }

    public static Resp<String> error(int errorCode,String errorMsg) {
        return new Resp(errorCode, errorMsg);
    }
    public static Resp<String> error(String errorMsg) {
        return new Resp(Constants.RESULT_ERROR_CODE, errorMsg);
    }

    public static Resp<String> error() {
        return new Resp(Constants.RESULT_ERROR_CODE, Constants.RESULT_ERROR_MSG);
    }

    public static <T> void render(HttpServletResponse response, int code, T info) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            response.setContentType("text/json;charset=utf-8");
            String s = objectMapper.writeValueAsString(new Resp<T>(code, info));
            response.getWriter().println(s);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
