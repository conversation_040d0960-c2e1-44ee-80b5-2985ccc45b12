package com.payne.core.utils;

import com.payne.core.constant.Constants;
import com.payne.core.exception.BusinessException;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collection;

public class AssertUtil {

    public static void throwMessage(@NotNull BusinessException e) {
        throw e;
    }

    public static void throwMessage(@NotEmpty String message) {
        throw new BusinessException(message);
    }

    public static void throwImportError(@NotEmpty String errorFileId) {
        throw new BusinessException(Constants.RESULT_ERROR_CODE_IMPORT_DATA, "导入失败, 请下载错误信息", errorFileId);
    }

    public static void isTrue(boolean expression, @NotEmpty String message) {
        if (!expression) {
            throw new BusinessException(message);
        }
    }

    public static void hasLength(@NotEmpty String text, @NotEmpty String message) {
        if (!StringUtils.hasLength(text)) {
            throw new BusinessException(message);
        }
    }

    public static void notNull(@NotNull Collection<?> collection, @NotEmpty String message) {
        if (CollectionUtils.isEmpty(collection)) {
            throw new BusinessException(message);
        }
    }
}
