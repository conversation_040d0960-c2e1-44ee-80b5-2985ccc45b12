package com.payne.core.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.payne.core.constant.Constants;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Getter
public class FormDataWrapper<T> {
    private final T object;
    private final String deleteFileIds;
    private final List<String> fields;
    private final Map<String, Object> textFields = new HashMap<>();
    private final Map<String, List<MultipartFile>> fileFields = new HashMap<>();

    public FormDataWrapper(HttpServletRequest request, Class<T> clazz) {
        Field[] fieldArray = CommonUtil.getAllFields(clazz);
        fields = Arrays.stream(fieldArray).map(Field::getName).toList();
        if (request instanceof MultipartHttpServletRequest multipartRequest) {
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
            for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                String fieldName = entry.getKey(); // 完整字段名
                MultipartFile file = entry.getValue();
                String groupKey = extractBaseFieldName(fieldName);
                fileFields.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(file);
            }
        }

        Map<String, String[]> parameterMap = request.getParameterMap();
        if (!CollectionUtils.isEmpty(parameterMap)) {
            Map<String, Object> tempMap = new HashMap<>();
            parameterMap.forEach((key, values) -> {
                if (values != null && values.length > 0)
                    tempMap.put(key, values[0]);
            });

            for (Map.Entry<String, Object> entry : tempMap.entrySet()) {
                List<String> keys = parseKey(entry.getKey());
                insertValue(textFields, keys, entry.getValue());
            }
        }

        fileFields.forEach((key, files) -> {
           textFields.remove(key);
        });
        try {
            ObjectMapper objectMapper = getObjectMapper();
            String string = objectMapper.writeValueAsString(textFields);
            object = objectMapper.readValue(string, clazz);
            deleteFileIds = request.getParameter(Constants.DELETE_FILE_IDS);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private static ObjectMapper getObjectMapper() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtil.DEFAULT_DATETIME_FORMAT);
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);    // 忽略实体中没有的字段
        objectMapper.registerModule(javaTimeModule);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
        return objectMapper;
    }

    private String extractBaseFieldName(String fieldName) {
        // 匹配以某个单词开头，后面可能跟着中括号：attachments[0][file] -> attachments
        Matcher matcher = Pattern.compile("^(.*?)(?=\\[\\d+\\])").matcher(fieldName);
        return matcher.find() ? matcher.group(1) : fieldName;
    }

    // 解析如 "a[0][b][1][c]" 为 ["a", "0", "b", "1", "c"]
    private static List<String> parseKey(String rawKey) {
        List<String> parts = new ArrayList<>();
        Matcher m = Pattern.compile("([a-zA-Z0-9_]+)|\\[([^\\]]+)\\]").matcher(rawKey);
        while (m.find()) {
            if (m.group(1) != null) {
                parts.add(m.group(1));
            } else if (m.group(2) != null) {
                parts.add(m.group(2));
            }
        }
        return parts;
    }

    // 递归插入值
    private static void insertValue(Map<String, Object> root, List<String> keys, Object value) {
        Object current = root;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            boolean isLast = (i == keys.size() - 1);
            boolean isIndex = key.matches("\\d+");

            if (isLast) {
                if (current instanceof Map) {
                    ((Map<String, Object>) current).put(key, value);
                } else if (current instanceof List) {
                    ensureListSize((List<Object>) current, Integer.parseInt(key));
                    ((List<Object>) current).set(Integer.parseInt(key), value);
                }
            } else {
                Object next;
                if (current instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) current;
                    next = map.get(key);
                    if (next == null) {
                        next = keys.get(i + 1).matches("\\d+") ? new ArrayList<>() : new HashMap<>();
                        map.put(key, next);
                    }
                } else if (current instanceof List) {
                    int idx = Integer.parseInt(key);
                    List<Object> list = (List<Object>) current;
                    ensureListSize(list, idx);
                    next = list.get(idx);
                    if (next == null) {
                        next = keys.get(i + 1).matches("\\d+") ? new ArrayList<>() : new HashMap<>();
                        list.set(idx, next);
                    }
                } else {
                    throw new IllegalStateException("Unexpected type during traversal");
                }
                current = next;
            }
        }
    }

    private static void ensureListSize(List<Object> list, int index) {
        while (list.size() <= index) {
            list.add(null);
        }
    }

    public Map<String, Object> getTextFields() {
        Map<String, Object> tempMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : textFields.entrySet()) {
            if (fields.contains(entry.getKey()))
                tempMap.put(entry.getKey(), entry.getValue());
        }
        return tempMap;
    }

    public Map<String, Object> getTextFields(boolean includeClassUnknownProperties) {
        return includeClassUnknownProperties ? textFields : getTextFields();
    }
}
