package com.payne.core.web;

import com.alibaba.fastjson.JSON;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.payne.core.utils.AssertUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * GridFsTemplate 文件操作工具类
 */
@Slf4j
@Service
public class GridFsService {
    @Resource
    private GridFsTemplate gridFsTemplate;
    @Resource
    private GridFSBucket gridFSBucket;


    /**
     * 保存文件
     *
     * @param username       文件拥有者（用户名）
     * @param multipartFiles
     * @return
     */
    public String save(String username, MultipartFile... multipartFiles) {
        List<FileInfo> list = saveFiles(username, multipartFiles);
        if (!CollectionUtils.isEmpty(list))
            return JSON.toJSONString(list);
        return null;
    }

    public List<FileInfo> saveFiles(String username, MultipartFile... multipartFiles) {
        List<FileInfo> list = new ArrayList<>();
        try {
            for (MultipartFile file : multipartFiles) {
                List<FileInfo> fileInfos = save(username, file);
                list.addAll(fileInfos);
            }
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            AssertUtil.throwMessage("操作失败, 文件上传异常");
        }
        return list;
    }

    /**
     * 保存文件
     *
     * @param username
//     * @param originalFilename
//     * @param contentType
//     * @param inputStream
     * @return
     */
    public List<FileInfo> save(String username, MultipartFile file) throws IOException {
        List<FileInfo> list = new ArrayList<>();
        Document metaData = new Document();
        if (StringUtils.hasLength(username))
            metaData.append("_username", username);
        ObjectId objectId = gridFsTemplate.store(file.getInputStream(), file.getOriginalFilename(), file.getContentType(), metaData);
        FileInfo fileInfo = new FileInfo();
        fileInfo.setId(objectId.toString());
        fileInfo.setContentType(file.getContentType());
        fileInfo.setOriginalFilename(file.getOriginalFilename());
        fileInfo.setSize(file.getSize());
        list.add(fileInfo);
        return list;
    }

    /**
     * 获取文件
     *
     * @param id
     * @return
     */
    public GridFSFile getGridFSFile(String id) {
        return gridFsTemplate.findOne(new Query().addCriteria(Criteria.where("_id").is(id)));
    }

    public GridFsResource getGridFsResource(GridFSFile file) {
        if (file != null) {
            GridFSDownloadStream gridFS = gridFSBucket.openDownloadStream(file.getObjectId());
            return new GridFsResource(file, gridFS);
        }
        return null;
    }

    /**
     * 根据ID获取文件
     *
     * @param id
     * @return
     */
    public GridFsResource get(String id) {
        GridFSFile data = getGridFSFile(id);
        if (data != null) {
            GridFSDownloadStream gridFS = gridFSBucket.openDownloadStream(data.getObjectId());
            return new GridFsResource(data, gridFS);
        }
        return null;
    }

    /**
     * 删除文件
     *
     * @param id
     */
    public void remove(String... id) {
        Query query = new Query();
        Criteria criteria = Criteria.where("_id").in(id);
        query.addCriteria(criteria);
        gridFsTemplate.delete(query);
    }

    /**
     * 删除文件
     *
     * @param fileInfos
     */
    public void remove(FileInfo... fileInfos) {
        for (FileInfo fileInfo : fileInfos) {
            remove(fileInfo.getId());
        }
    }

    /**
     * 删除文件
     *
     * @param fileInfos
     */
    public void remove(List<FileInfo> fileInfos) {
        if (CollectionUtils.isEmpty(fileInfos)) return;
        remove(fileInfos.toArray(new FileInfo[]{}));
    }

    /**
     * 删除文件
     *
     * @param fileInfoStr
     */
    public void removeByFileInfoStr(String fileInfoStr) {
        if (!StringUtils.hasLength(fileInfoStr)) return;
        List<FileInfo> list = new ArrayList<>();
        if (JSON.isValidArray(fileInfoStr)) {
            list = JSON.parseArray(fileInfoStr, FileInfo.class);
        } else if (JSON.isValidObject(fileInfoStr)) {
            list.add(JSON.parseObject(fileInfoStr, FileInfo.class));
        }
        remove(list);
    }

    /**
     * 获取文件内容
     *
     * @param id 文件ID
     * @return 文件字节数组
     */
    public byte[] getFileContent(String id) {
        try {
            GridFsResource resource = get(id);
            if (resource != null && resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    return inputStream.readAllBytes();
                }
            }
            throw new RuntimeException("文件不存在: " + id);
        } catch (IOException e) {
            log.error("获取文件内容失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取文件内容失败: " + e.getMessage());
        }
    }
}
