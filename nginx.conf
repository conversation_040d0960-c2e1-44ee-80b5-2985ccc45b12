server {
    listen 80;
    server_name localhost;
    client_max_body_size 2048m;  # 允许最大上传为 2G
    # 前端静态文件目录
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files $uri $uri/ /index.html;  # 支持前端路由
    }

    # 后端接口代理
    location /api {
        proxy_pass http://127.0.0.1:7070;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
