<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.5.3</version>
    </parent>

    <groupId>com.payne</groupId>
    <artifactId>dev-platform</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <modules>
        <module>payne-auth</module>
        <module>payne-upms</module>
        <module>payne-common</module>
        <module>payne-server</module>
        <module>payne-generator</module>
    </modules>

    <properties>
        <syt.dev.version>1.0-SNAPSHOT</syt.dev.version>
        <java.version>17</java.version>
        <hutool.version>5.8.26</hutool.version>
        <mybatis-plus.version>3.5.5</mybatis-plus.version>
        <mybatis-plus-join.version>1.4.12</mybatis-plus-join.version>
        <druid.version>1.2.21</druid.version>
        <fastjson.version>1.2.83</fastjson.version>
        <snakeyaml.version>2.1</snakeyaml.version>
        <ojdbc8.version>23.2.0.0.0</ojdbc8.version>
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <dameng.version>8.1.2.79</dameng.version>
        <easy-captcha.version>1.6.2</easy-captcha.version>
        <jjwt.version>0.11.2</jjwt.version>
        <bcprov-jdk18on.version>1.76</bcprov-jdk18on.version>
        <cglib.version>3.3.0</cglib.version>
        <commons-text.version>1.13.1</commons-text.version>
        <commons-io.version>2.16.1</commons-io.version>
        <janino.version>3.1.12</janino.version>
        <redisson.version>3.36.0</redisson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.payne</groupId>
                <artifactId>payne-common-core</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.payne</groupId>
                <artifactId>payne-auth</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.payne</groupId>
                <artifactId>payne-upms</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.payne</groupId>
                <artifactId>payne-generator</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${ojdbc8.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmJdbcDriver18</artifactId>
                <version>${dameng.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dameng</groupId>
                <artifactId>DmDialect-for-hibernate6.2</artifactId>
                <version>${dameng.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk18on</artifactId>
                <version>${bcprov-jdk18on.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.janino</groupId>
                <artifactId>janino</artifactId>
                <version>${janino.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

    </dependencies>
</project>