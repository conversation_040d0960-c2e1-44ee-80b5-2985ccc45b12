<html><head>
    <meta charset="utf-8">
    <title>彩色标签打印(批量)</title>
    <style media="print">
        @page {
            size: auto;
            margin: 0mm;
        }
    </style>
    <style type="text/css">
        body {
            padding: 0;
            margin: 0;
        }

        /* 首张div */
        .first {
            width: 19cm;
            height: 3.7cm;
            border: 1px solid cornflowerblue;
        }

        /* 其他div */
        .other {
            width: 19cm;
            height: 2.1cm;
            border-left: 1px solid grey;
            border-bottom: 1px solid grey;
            border-right: 1px solid grey;
        }

        /* 首张div */
        .first1 {
            width: 19cm;
            height: 2.1cm;
            border: 1px solid cornflowerblue;
        }

        /* 首张div */
        .firstJb {
            width: 19cm;
            height: 4.3cm;
            border: 1px solid cornflowerblue;
        }

        /* 其他div */
        .other1 {
            width: 19cm;
            height: 2.6cm;
            border-left: 1px solid grey;
            border-bottom: 1px solid grey;
            border-right: 1px solid grey;
        }

        /* 图片位置框 */
        .imgBoxA {
            position: relative;
            top: 0px;
            left: 180px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxB {
            position: relative;
            top: 0px;
            left: 180px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1 {
            position: relative;
            top: 77px;
            left: 375px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxAJB {
            position: relative;
            top: 13px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxAJB1 {
            position: relative;
            top: 82px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1M {
            position: relative;
            top: 77px;
            left: 265px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1M1 {
            position: relative;
            top: 20px;
            left: 265px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxB1 {
            position: relative;
            top: 15px;
            left: 375px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxBao {
            position: relative;
            top: 5px;
            left: 380px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoY {
            position: relative;
            top: 5px;
            left: 355px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxBaoA {
            position: relative;
            top: 5px;
            left: 380px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            font-weight: normal;
        }

        .imgBoxBaoB {
            position: relative;
            top: 1px;
            left: 285px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            font-weight: normal;
        }


        .imgBoxBaoYX {
            position: relative;
            top: 77px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoYX1 {
            position: relative;
            top: 20px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .picker {
            width: 150px;
            height: 28px;
            border-radius: 4px;
            cursor: pointer;
        }

        #choose {
            position: absolute;
            top: 10px;
            left: 750px;
        }

        h1 {
            text-align: center;
            line-height: 22px;
            font-family: 青鸟华光简行楷;
        }

        #tips {
            position: absolute;
            top: 10px;
            left: 1000px;
        }

        /* 鼠标右键 */
        .shade {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0px;
            left: 0px;

        }

        .wrap-ms-right {
            list-style: none;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 9999;
            padding: 5px 0;
            min-width: 80px;
            margin: 0;
            display: none;
            font-family: "微软雅黑";
            font-size: 14px;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, .15);
            box-sizing: border-box;
            border-radius: 4px;
            -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -ms-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -o-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ms-item {
            height: 30px;
            line-height: 30px;
            text-align: center;
            cursor: pointer;
        }

        .ms-item:hover {
            background-color: #343a40;
            color: #FFFFFF;
        }

        #live {
            display: inline-block;
            width: 100%;
            height: 22px;
            line-height: 1.5;
            padding: 4px 7px;
            font-size: 14px;
            border: 1px solid #dddee1;
            border-radius: 4px;
            background-color: #fff;
            background-image: none;
            position: relative;
            cursor: text;
            transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
        }

        .imgBoxBao111 {
            position: relative;
            top: 5px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoB2222 {
            position: relative;
            top: 7px;
            left: 350px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            position: relative;
        }

        .imgBoxA2222 {
            position: relative;
            top: 82px;
            left: 350px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .banbie2222 {
            margin-top: 15px;
            margin-left: 15px;
        }

        .test_top {
            margin-top: 3px;
            margin-left: 15px;
        }
    </style>
</head>
<body><div class="shade"></div><div class="wrap-ms-right"><li class="ms-item" data-item="0"><i data-item="0"></i>&nbsp; 中乾850机-13行小标</li><li class="ms-item" data-item="1"><i data-item="1"></i>&nbsp; 中乾8160机-13行小标</li><li class="ms-item" data-item="2"><i data-item="2"></i>&nbsp; 宝鑫模板-粮票模板</li><li class="ms-item" data-item="3"><i data-item="3"></i>&nbsp; 重来</li><li class="ms-item" data-item="4"><i data-item="4"></i>&nbsp; 打印</li><li class="ms-item" data-item="5"><i data-item="5"></i>&nbsp; 适应七字</li><li class="ms-item" data-item="6"><i data-item="6"></i>&nbsp; 适应八字</li></div>
<!-- 操作栏 -->
<div id="choose">
    <span style="color: Crimson;">[谷歌浏览器下使用]</span>
    <br><br>
    <!-- 左右箭头按钮 -->
    <button onclick="moveLeft()" class="arrow-button">←</button>
    <button onclick="moveRight()" class="arrow-button">→</button>
    <button onclick="moveUp()" class="arrow-button">↑</button>
    <button onclick="moveDown()" class="arrow-button">↓</button>
    <script>
        function ck1() {
            $("#live").val("<span style=\"font-family:宋体;\">18k</span><br><span>纸黄金</span>");
            $("#live")[0].dispatchEvent(new Event('input'));
        }

        function ck2() {
            $("#live").val("<span>黄金双冠</span><br><span></span>");
            $("#live")[0].dispatchEvent(new Event('input'));
        }

        function ck3() {
            $("#live").val("<span >金砖九冠</span><br><span></span>");
            $("#live")[0].dispatchEvent(new Event('input'));
        }

        function blockFont() {
            //  $("h1").css("font-weight","bold");
            // $("h1").css("font-family","青鸟华光简行楷");
            // $("h1").css("font-size","32px");
            // $("#switchGrade").attr("class", "other");
            let blockFont = $("#switchGrade h1");//首个

            let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
            let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

            if (ok != 0) {

                let blockFontAll = $(".other h1");//
                let weight = blockFont.css("font-weight");
                if ("600" > weight) {
                    blockFont.css("font-weight", "700");
                    blockFontAll.css("font-weight", "700");
                } else {
                    blockFont.css("font-weight", "100");
                    blockFontAll.css("font-weight", "100");
                }
            }

            if (ok1 != 0) {

                let blockFontAll = $(".other1 h1");//
                let weight = blockFont.css("font-weight");
                if ("600" > weight) {
                    blockFont.css("font-weight", "700");
                    blockFontAll.css("font-weight", "700");
                } else {
                    blockFont.css("font-weight", "100");
                    blockFontAll.css("font-weight", "100");
                }


            }

        }


        function minFont() {
            //  $("h1").css("font-weight","bold");
            // $("h1").css("font-family","青鸟华光简行楷");
            // $("h1").css("font-size","32px");
            // $("#switchGrade").attr("class", "other");
            // let minFont=$("#switchGrade h1");//首个


            for (let i = 1; i < 11; i++) {
                let thisFont = $("#switchImgBox" + i + " h1");

                let fontSize = parseInt(thisFont.css("font-size").substring(0, (thisFont.css("font-size").length - 2))) - 1;

                // minFont.css("font-size",fontSize+"px");
                // minFontAll.css("font-size",fontSize+"px");
                thisFont.css("font-size", fontSize + "px");

            }


        }


        function maxFont() {
            //  $("h1").css("font-weight","bold");
            // $("h1").css("font-family","青鸟华光简行楷");
            // $("h1").css("font-size","32px");
            // $("#switchGrade").attr("class", "other");
            // let maxFont=$("#switchGrade h1");//首个

            for (let i = 1; i < 11; i++) {
                let thisFont = $("#switchImgBox" + i + " h1");

                let fontSize = parseInt(thisFont.css("font-size").substring(0, (thisFont.css("font-size").length - 2))) + 1;

                // minFont.css("font-size",fontSize+"px");
                // minFontAll.css("font-size",fontSize+"px");
                thisFont.css("font-size", fontSize + "px");

            }


        }


        function familyFont() {
            //  $("h1").css("font-weight","bold");
            // $("h1").css("font-family","青鸟华光简行楷");
            // $("h1").css("font-size","32px");
            // $("#switchGrade").attr("class", "other");


            for (let i = 1; i < 11; i++) {
                let thisFont = $("#switchImgBox" + i + " h1");

                let family = "青鸟华光简行楷";//字体
                family = $("#familyFont").val();//字体
                // family=thisFont.val();//字体
                thisFont.css("font-family", family);
            }

        }


        function adaptiveFont() {
            //  $("h1").css("font-weight","bold");
            // $("h1").css("font-family","青鸟华光简行楷");
            // $("h1").css("font-size","32px");
            // $("#switchGrade").attr("class", "other");
            let adaptiveFont = $("#switchGrade h1");//首个

            let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
            let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

            if (ok != 0) {

                let adaptiveFontAll = $(".other h1");//

                let nameLen = $("#live").val();

                let thisFont = $("#banbie");//

                nameLen = nameLen.length;
                if (nameLen <= 3) {
                    adaptiveFont.css("font-size", "40px");
                    adaptiveFontAll.css("font-size", "40px");
                    // thisFont.css("font-size","40px");
                } else if (nameLen == 6) {
                    adaptiveFont.css("font-size", "28px");
                    adaptiveFontAll.css("font-size", "28px");
                    // thisFont.css("font-size","32px");
                } else if (nameLen == 7) {
                    adaptiveFont.css("font-size", "24px");
                    adaptiveFontAll.css("font-size", "24px");
                    // thisFont.css("font-size","23px");
                } else if (nameLen == 8) {
                    adaptiveFont.css("font-size", "21px");
                    adaptiveFontAll.css("font-size", "21px");
                    // thisFont.css("font-size","20px");
                } else if (nameLen <= 10) {
                    adaptiveFont.css("font-size", "18px");
                    adaptiveFontAll.css("font-size", "18px");
                    // thisFont.css("font-size","18px");
                }


            }


            for (let i = 1; i < 11; i++) {
                let thisFont = $("#switchImgBox" + i + " h1");


                let nameLen = $("#live").val();


                nameLen = nameLen.length;
                if (nameLen <= 3) {
                    // adaptiveFont.css("font-size","40px");
                    // adaptiveFontAll.css("font-size","40px");
                    thisFont.css("font-size", "40px");
                } else if (nameLen == 6) {
                    thisFont.css("font-size", "28px");
                    // thisFont.css("font-size","32px");
                } else if (nameLen == 7) {
                    // adaptiveFont.css("font-size","23px");
                    // adaptiveFontAll.css("font-size","23px");
                    thisFont.css("font-size", "24px");
                } else if (nameLen == 8) {
                    // adaptiveFont.css("font-size","20px");
                    // adaptiveFontAll.css("font-size","20px");
                    thisFont.css("font-size", "21px");
                } else if (nameLen <= 10) {
                    // adaptiveFont.css("font-size","18px");
                    // adaptiveFontAll.css("font-size","18px");
                    thisFont.css("font-size", "18px");
                } else if (nameLen <= 11) {
                    // adaptiveFont.css("font-size","18px");
                    // adaptiveFontAll.css("font-size","18px");
                    thisFont.css("font-size", "25px");
                }


            }


        }

        function changeTop() {
            var thisFontSize = $(".banbie").css("font-size");
            var nameLen = $("#live").val();
            nameLen = nameLen.length;


            if (nameLen == 6 && parseInt(thisFontSize) == 28) {
                $(".banbie").css("margin-top", "18px");

            } else if (nameLen == 7 && parseInt(thisFontSize) == 24) {
                $(".banbie").css("margin-top", "18px");

            } else if (nameLen == 8 && parseInt(thisFontSize) == 21) {
                $(".banbie").css("margin-top", "18px");
            } else if (nameLen == 9 && parseInt(thisFontSize) == 18) {
                $(".banbie").css("margin-top", "18px");
            } else {
                if (parseInt(thisFontSize) == 32) {
                    $(".banbie").css("margin-top", "8px");
                }
            }

        }

    </script>
    <!--    <input type="text" id="live" v-model="firstname" placeholder="请输入名称" @input="getFullName" autocomplete="off"/>-->
    <input type="text" id="live" v-model="firstname" placeholder="请输入名称" @input="getFullName" autocomplete="off" style="color: rgb(0, 0, 0);">
    <div id="demos" style="display: none;"><section><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div> <div class="input-value">当前值：<span class="description"></span></div></div></section></div>


    <div style="padding-top: 10px;">
        <div class="picker" id="color-picker" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont()">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont()">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont()" id="familyFont">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont()">
    <input type="button" value="调节" onclick="changeTop()">

    <p></p>
    <p></p>
    <p></p>
    <button onclick="ck1()">18k纸黄金</button>
    <button onclick="ck2()">黄金双冠</button>
    <button onclick="ck3()">金砖九冠</button>


</div>


<!--  -->

<div id="tips">
    <p>
        <label style="color:crimson;font-size: 18px;">调</label>
        <label style="color: green;font-size: 18px;">色</label>
        <label style="color: mediumpurple;font-size: 18px;">表</label>
        Ctrl+F 可搜索
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(255,0,0);">1.大红色</span>
        （纵二横三）：{
        <br>
        <code>&lt;p style='font-size: 12px;'&gt;&lt;/p&gt;</code>
        <br>
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;补号，幼线体，数字冠，大王冠，首发冠，平水，凸版，渡水，中水，小圆水，
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;大圆水，爱情号，长号，宽水红，爱情号，豹子号，老虎号，中国梦，红二平，
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;生日快乐，爱版，窄水红，红光蓝鹤，
            <br>
            &nbsp;
            }
        </a>
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(152,0,0);">2.深红色</span>
        （纵二横二）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;浴火凤凰
            <br>
            }
        </a>
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: black;">3.黑色</span>
        （纵一)：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            满堂彩，炭黑，深版
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(0,255,0,1);">4.绿色</span>
        （纵三）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            绿幽灵，绿钻
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(0,255,0,1);">5.背绿</span>
        （#95db95）
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(185,215,168,1);">6.深青色</span>
        （纵七）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            青绿美翠，五彩苍松，苍松翠鹤
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #0096db;">7.浅蓝色</span>
        （#95db95）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            玉钩国
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(0,255,255);">8.天蓝色</span>
        （纵三横二）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            天蓝色，蓝凤朝阳
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(74,134,232);">9.蓝色</span>
        （纵三横三）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            中密丝
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(0,0,255);">10.深蓝色</span>
        （纵三横四）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            高密丝
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(204,204,204);">11.水墨色</span>
        （纵一横四）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            古币水印，古币墨水印，海鸥水印
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #f05799;">12.深粉色</span>
        （#f05799）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            满天星桃花红
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #f27e7e;">13.香槟粉</span>
        （#f27e7e）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            金杯桃花红
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(255,153,0);">14.橙色</span>
        （纵二横四）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            金牡丹，红金龙，金龙王，金光蓝鹤
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #e68e09;">15.金星绿波</span>
        （#e68e09）
    </p>
</div>


<!-- 首张 -->
<div class="first first1" id="switchGrade">
    <div class="imgBoxA" id="switchImgBox1">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>

<!-- 2 -->
<div class="other">
    <div class="imgBoxB" id="switchImgBox2">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox3">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox4">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox5">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox6">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox7">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox8">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox9">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox10">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox11">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox12">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other">
    <div class="imgBoxB" id="switchImgBox13">
        <h1 class="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>

<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<script src="/platformFramework/js/smile/color.js?"></script>
<script src="/platformFramework/js/smile/mouseRight.min.js" type="text/javascript" charset="utf-8"></script>
<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<!--<script src="../..//platformFramework/statics/libs/input.js" type="text/javascript" charset="utf-8"></script>-->


<script src="/platformFramework/statics/libs/smartInput1.js"></script>
<script src="/platformFramework/statics/libs/input.min.js" type="text/javascript" charset="UTF-8"></script>
<link rel="stylesheet" href="/platformFramework/statics/css/inputq.css">
<link rel="stylesheet" href="/platformFramework/statics/css/smartInput1.css">

<!--<link rel="stylesheet" href="statics/css/input.css">-->
<script>


    let obj = document.getElementById("live");
    let banbie = document.getElementsByClassName("banbie")
    let a = Colorpicker.create({
        el: "color-picker",
        color: "blue",
        change: function (elem, hex) {
            console.log(elem)
            elem.style.backgroundColor = hex;
            obj.style.color = hex;
            for (var i = 0; i < banbie.length; i++) {
                banbie[i].style.color = hex;
            }
        }
    });

    /**
     * 监听输入框
     */
    $("#live").bind("input propertychange", function () {

        let banbie = document.getElementsByClassName("banbie");

        let live = $("#live").val();
        if (live.length <= 4) {
            for (var i = 0; i < banbie.length; i++) {
                banbie[i].innerHTML = $("#live").val();
                $('.banbie').css("font-size", "40px");
                banbie[i].style.paddingTop = '5px';
            }
        }
        if (live.length == 5) {
            for (var i = 0; i < banbie.length; i++) {
                banbie[i].innerHTML = $("#live").val();
                $('.banbie').css("font-size", "32px");
                banbie[i].style.paddingTop = '8px';
            }
        }
        if (live.length >= 6) {
            for (var i = 0; i < banbie.length; i++) {
                banbie[i].innerHTML = $("#live").val();
                $('.banbie').css("font-size", "27px");
                banbie[i].style.paddingTop = '10px';
            }
        }

    });
    $('body').mouseRight({
        menu: [
            {
                itemName: "中乾850机-13行小标",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("#switchGrade").attr("class", "first1");
                    $(".other").attr("class", "other1");
                    $("#switchImgBox1").attr("class", "imgBoxA");
                    $("#switchImgBox2").attr("class", "imgBoxA");
                    $("#switchImgBox3").attr("class", "imgBoxA");
                    $("#switchImgBox4").attr("class", "imgBoxA");
                    $("#switchImgBox5").attr("class", "imgBoxA");
                    $("#switchImgBox6").attr("class", "imgBoxA");
                    $("#switchImgBox7").attr("class", "imgBoxA");
                    $("#switchImgBox8").attr("class", "imgBoxA");
                    $("#switchImgBox9").attr("class", "imgBoxA");
                    $("#switchImgBox10").attr("class", "imgBoxA");
                    $("#switchImgBox11").attr("class", "imgBoxA");
                    $("#switchImgBox12").attr("class", "imgBoxA");
                    $("#switchImgBox13").attr("class", "imgBoxA");
                    blockFont();
                    let banbie = document.getElementsByClassName("banbie");
                    let other1 = document.getElementsByClassName("other1");

                    for (var i = 0; i < 13; i++) {
                        banbie[i].innerHTML = $("#live").val();
                        $('.banbie').css("font-size", "18px");
                        banbie[i].style.paddingTop = '15px';
                    }

                    for (let i = 0; i < other1.length; i++) {
                        other1[i].style.height = "2.1cm"
                    }


                }
            }, {
                itemName: "中乾8160机-13行小标",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "19px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox2").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox3").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox4").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox5").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox6").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox7").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox8").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox9").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox10").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox11").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox12").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox13").attr("class", "imgBoxBaoB2222");
                    var lives = $("#live").val();
                    if (lives.length > 5) {
                        var sss = $(".banbie").html();
                        var newStr = sss.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                        // $('.banbie').html(newStr);

                        $('.banbie').css("fontSize", '20px');
                        $('.banbie').attr("class", "test_top");
                    } else {
                        $('.banbie').attr("class", "banbie2222");
                    }
                    blockFont();
                }
            },
            {
                itemName: "宝鑫模板-粮票模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "19px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox2").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox3").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox4").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox5").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox6").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox7").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox8").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox9").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox10").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox11").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox12").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox13").attr("class", "imgBoxBaoB2222");
                    $("#banbie").attr("class", "banbie2222");
                    $("#banbie2").attr("class", "banbie2222");
                    $("#banbie3").attr("class", "banbie2222");
                    $("#banbie4").attr("class", "banbie2222");
                    $("#banbie5").attr("class", "banbie2222");
                    $("#banbie6").attr("class", "banbie2222");
                    $("#banbie7").attr("class", "banbie2222");
                    $("#banbie8").attr("class", "banbie2222");
                    $("#banbie9").attr("class", "banbie2222");
                    $("#banbie10").attr("class", "banbie2222");
                    $("#banbie11").attr("class", "banbie2222");
                    $("#banbie12").attr("class", "banbie2222");
                    $("#banbie13").attr("class", "banbie2222");
                    blockFont();

                }
            },

            {
                itemName: "重来",
                callback: function () {
                    $("#print-btn").css("display", "block");
                    $("#choose").css("display", "block");
                    $("#tips").css("display", "block");
                    $(".first").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".first1").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".firstJb").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".other").css({
                        "border-left": '1px solid grey',
                        "border-bottom": '1px solid grey',
                        "border-right": '1px solid grey'
                    });
                    $(".other1").css({
                        "border-left": '1px solid grey',
                        "border-bottom": '1px solid grey',
                        "border-right": '1px solid grey'
                    });

                    $(".imgBoxA").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxB1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBao").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoA").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoY").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1M").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1M1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoYX").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoYX1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxAJB1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxAJB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoB2222").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBao111").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA2222").css({
                        border: "1px solid lightseagreen"
                    });

                }
            },
            {
                itemName: "打印",
                callback: function () {
                    $("#print-btn").css("display", "none");
                    $("#choose").css("display", "none");
                    $("#tips").css("display", "none");
                    $(".first").css({
                        border: "none"
                    });
                    $(".first1").css({
                        border: "none"
                    });
                    $(".firstJb").css({
                        border: "none"
                    });

                    $(".imgBoxA1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxB1").css({
                        border: "1px solid transparent"
                    });
                    $(".other1").css({
                        border: "none"
                    });
                    $(".other").css({
                        border: "none"
                    });
                    $(".imgBoxA").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBao").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoA").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoY").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1M").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1M1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoYX").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoYX1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxAJB1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxAJB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoB2222").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBao111").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA2222").css({
                        border: "1px solid transparent"
                    });
                    setTimeout(function () {
                        window.print();
                    }, 100);

                }
            },
            {
                itemName: "适应七字",
                callback: function () {
                    let banbie = document.getElementsByClassName("banbie");
                    let live = $("#live").val();
                    for (var i = 0; i < 10; i++) {
                        banbie[i].innerHTML = $("#live").val();
                        $('.banbie').css("font-size", "23px");
                        banbie[i].style.paddingTop = '10px';
                    }
                }
            },
            {
                itemName: "适应八字",
                callback: function () {
                    let banbie = document.getElementsByClassName("banbie");
                    let live = $("#live").val();
                    for (var i = 0; i < 10; i++) {
                        banbie[i].innerHTML = $("#live").val();
                        $('.banbie').css("font-size", "20px");
                        banbie[i].style.paddingTop = '14px';
                    }
                }
            },

        ]
    });
    let initialMarginRight = 0; // 初始 margin-right 值
    function moveRight() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");

            if (initialMarginRight === 0) {
                // 记录初始 margin-right 值
                initialMarginRight = parseInt(thisFont.css("margin-right").substring(0, (thisFont.css("margin-right").length - 2)));
            }

            // 累积增加 margin-right 值
            initialMarginRight -= 1;

            thisFont.css("margin-right", initialMarginRight + "px");
        }
    }


    function moveLeft() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");
            let marginLeft = parseInt(thisFont.css("margin-left").substring(0, (thisFont.css("margin-left").length -
                2))) - 5;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("margin-left", marginLeft + "px");

        }
    }
    function moveDown() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");
            let marginLeft = parseInt(thisFont.css("padding-top").substring(0, (thisFont.css("padding-top").length -
                2))) + 5;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("padding-top", marginLeft + "px");

        }
    }

    function moveUp() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");
            let marginLeft = parseInt(thisFont.css("padding-top").substring(0, (thisFont.css("padding-top").length -
                2))) - 5;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("padding-top", marginLeft + "px");

        }
    }
</script><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 149px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div>


<!--<script>-->


<!--    let live = new Vue({-->
<!--        el: "#live",-->
<!--        data: {-->
<!--            firstname: ''-->
<!--        },-->

<!--        methods: {-->
<!--            getFullName: function() {-->
<!--                let numbers=[ "1", "2", "3", "4", "5" ];-->
<!--                console.log(this.firstname);-->
<!--                console.log("-&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;");-->
<!--              //  $("#div").load(location.href+" #div>*","");-->
<!--                updedsearch(this.firstname,numbers);-->

<!--       /*         varvm=new Vue({-->
<!--                    el:"#div",-->
<!--                    data:{-->
<!--                        numbers: [ 1, 2, 3, 4, 5 ]-->
<!--                    },-->
<!--                    computed:{-->
<!--                        evenNumbers: function () {-->
<!--                            return this.numbers.filter(function (number)-->
<!--                            {-->
<!--                                let ok=this.firstname;-->
<!--                                ok=ok==null?4:ok;-->
<!--                                return number<ok;-->
<!--                            })-->
<!--                        }-->
<!--                    }-->
<!--                })*/-->


<!--            }-->
<!--        }-->

<!--    });-->


<!--</script>-->


<!--<script>-->


<!--    function updedsearch(search,numbers){-->

<!--        varvm=new Vue({-->
<!--            el:"#div",-->

<!--            computed  :{-->
<!--                evenNumbers: function () {-->


<!--                    return numbers.filter(function (number)-->
<!--                    {-->

<!--                        console.log("============")-->
<!--                        console.log(search)-->
<!--                        console.log(number)-->

<!--                        return  (number.indexOf(search) != -1)-->
<!--                    })-->
<!--                }-->
<!--            },-->


<!--        })-->

<!--    }-->


<!--</script>-->

<script>


</script>


</body></html>