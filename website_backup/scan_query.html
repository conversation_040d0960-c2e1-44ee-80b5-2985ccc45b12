<html><head>
	<title>扫码查询</title>
	<link rel="stylesheet" href="/platformFramework/statics/css/bootstrap.min.css">
</head>
<body>
<div style="width: 1170px;padding:40px 0 0 40px;">
	<!-- 显示的表格数据 -->
	<div class="form-group">
		<label>评单号</label>
		<input type="email" class="form-control" id="tt" placeholder="请拿起你的扫码器" autofocus="autofocus" style="width: 350px;">
	</div>
	<div class="row">
		<div class="col-md-12">
			<table class="table table-hover " id="scan_table">
				<!-- 表头 -->
				<thead>
				<tr>
					<!-- 全选 ↓ -->
					<th style="text-align: left">送评单号</th>
					<th style="text-align: left">网名</th>
					<th style="text-align: left">真实姓名</th>
					<th style="text-align: left">项目数量</th>
					<th style="text-align: left">总费用</th>
					<th style="text-align: left">创建时间</th>
					<th style="text-align: left">操作</th>
				</tr>
				</thead>
				<!-- tbody 表内容,使用js填充 -->
				<tbody>
				</tbody>
			</table>
		</div>
	</div>
</div>
<script src="/platformFramework/statics/libs/jquery.min.js?_1749996689880"></script>
<script src="/platformFramework/statics/libs/vue.min.js?_1749996689880"></script>
<script src="/platformFramework/statics/plugins/jqgrid/grid.locale-cn.js?_1749996689880"></script>
<script src="/platformFramework/statics/plugins/jqgrid/jquery.jqGrid.min.js?_1749996689880"></script>
<script>
	$(function(){
		/**
		 *  扫码获取评单号
		 */
		var tt = $("#tt");
		var show = "";

		$("#tt").keypress(function(e) {
			if (e.keyCode == 13) {
				if (tt.val().length < 8) {
					alert("评单号格式不正确")
					return;
				}
				if (tt.val().length > 10&&tt.val().length < 39) {
					alert("评单号格式不正确")
					return;
				}

				if (tt.val().length > 39) {
					show = tt.val().substring(39, tt.val().length);
				}else{
					show = tt.val();
				}
				tt.attr('placeholder', show);
				tt.val('');

				//发送ajax
				//console.log(tt.attr('placeholder')); //值

				$.ajax({
					url: "../pjosendform/scan",
					data:{"nummber":tt.attr('placeholder')},
					dataType: "json",
					type:"post",
					success: function (data){
						if(data.code==500){
							alert("没有此送评单信息")
							return;
						}
						build_table(data);
					},
					error:function (XMLHttpRequest, textStatus, errorThrown) {
						alert("请求失败！");
					}
				});


			}
		});

		//1.解析显示表格信息 ↑
		function build_table(result){
			//清空表格
			$("#scan_table tbody").empty();
			//1).接收所有员工数据
			var list=result.send;
			//2).使用JS函数进行遍历,index:索引,item:对象
			$.each(list,function(index,item){
				//3).将每一条数据以 <tr>节点填充到表格中显示,
				//   首先得把每一个元素接收再append到父元素 <tr>中
				var sendnum=$("<td style='color: #CD950C;'></td>").append(item.sendnum);
				var nickname=$("<td style='color: #CD950C;'></td>").append(item.nickname);
				var rname=$("<td style='color: #CD950C;'></td>").append(item.rname);
				var coinCount=$("<td></td>").append(item.coinCount+"个");
				var sumfeetw=$("<td></td>").append("￥"+item.sumfeetw+"元");
				console.log(item);
				var inupttime=$("<td></td>").append(getMyDate(item.inupttime));
				var btn_edit=$("<button @click='update'></button>").addClass("btn btn-primary   btn-sm edit_btn index")//给修改按钮添加 Class标识,
						.append($("<span><span>").addClass("glyphicon glyphicon-pencil")).append(" 修改");
				//为编辑按钮添加一个自定义属性 ,表示当前员工ID ++++++++
				btn_edit.attr("edit-id",item.id);

				//4).将两个按钮拼到一个<td>里 ↓
				var btn=$("<td></td>").append(btn_edit);

				//5).把所有的<td>m节点append到 <tr>中,最后把<tr>节点appendTo到指定的位置
				$("<tr></tr>").append(sendnum).append(nickname).append(rname).append(coinCount).append(sumfeetw).append(inupttime).append(btn)
						.appendTo("#scan_table tbody");
			});
		}

		function getMyDate(str) {
			function getzf(num) {
				if (parseInt(num) < 10) {
					num = '0' + num;
				}
				return num;
			}
			var oDate = new Date(str),
					oYear = oDate.getFullYear(),
					oMonth = oDate.getMonth() + 1,
					oDay = oDate.getDate(),
					oHour = oDate.getHours(),
					oMin = oDate.getMinutes(),
					oTime = oYear + '-' + getzf(oMonth) + '-' + getzf(oDay) + ' ' + getzf(oHour) + ':' + getzf(oMin); //最后拼接时间
			return oTime;
		};

		//给编辑按钮绑定单击事件
		$(document).on("click",".index",function(){
			var id=$(".index").attr("edit-id");
			window.open("/platformFramework/scan/addsendform.html?id="+id);
		});

	});
</script>

</body></html>