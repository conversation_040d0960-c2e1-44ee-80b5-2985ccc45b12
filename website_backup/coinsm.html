<html style=""><head>
	<title></title>
	<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<!--bootstrap-->
<link rel="stylesheet" href="/platformFramework/statics/css/bootstrap.min.css">
<link rel="stylesheet" href="/platformFramework/statics/css/font-awesome.min.css">
<!--jqgrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="/platformFramework/statics/plugins/ztree/css/metroStyle/metroStyle.css">
<!--main-->
<link rel="stylesheet" href="/platformFramework/statics/css/main.css">
<link rel="stylesheet" href="/platformFramework/statics/css/iview.css">
<link rel="stylesheet" href="/platformFramework/statics/css/style.css">
<!--treegrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/treegrid/jquery.treegrid.css">
<!--富文本-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/froala_editor/css/froala_editor.min.css">

<!--jquery-->
<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<!--layer-->
<script src="/platformFramework/statics/plugins/layer/layer.js"></script><link rel="stylesheet" href="http://**************/platformFramework/statics/plugins/layer/skin/default/layer.css?v=3.0.3303" id="layuicss-skinlayercss">
<!--bootstrap-->
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<!--vue-->
<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/iview.min.js"></script>
<!--jqgrid-->
<script src="/platformFramework/statics/plugins/jqgrid/grid.locale-cn.js"></script>
<script src="/platformFramework/statics/plugins/jqgrid/jquery.jqGrid.min.js"></script>
<!--ztree-->
<script src="/platformFramework/statics/plugins/ztree/jquery.ztree.all.min.js"></script>
<!--treegrid-->
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.extension.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.min.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.bootstrap3.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/tree.table.js"></script>

<!--simplemde富文本-->
<script src="/platformFramework/statics/plugins/froala_editor/js/froala_editor.min.js"></script>
<!--[if lt IE 9]>
<script src="/platformFramework/statics/plugins/froala_editor/js/froala_editor_ie8.min.js"></script>
<![endif]-->
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/tables.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/lists.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/colors.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/media_manager.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_family.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_size.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/block_styles.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/video.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/langs/zh_cn.js"></script>

<script src="/platformFramework/statics/libs/jquery-extend.js"></script>
<script src="/platformFramework/js/common.js"></script>


<!--exce-->
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jexcel.css">
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jsuites.css">
<link rel="stylesheet" href="/platformFramework/js/v4/jquery-clockpicker.min.css">
<link rel="stylesheet" href="/platformFramework/js/v4/happy-scroll.css">
<!--<link rel="stylesheet" href="/platformFramework/statics/css/v8/jspreadsheet.css">-->
<script src="/platformFramework/js/v4/jexcel.js"></script>
<script src="/platformFramework/js/v4/jsuites.js"></script>
<script src="/platformFramework/js/v4/jquery-clockpicker.min.js"></script>
<script src="/platformFramework/js/v4/happy-scroll.min.js"></script>
<script src="/platformFramework/js/v4/jquery.cookie.min.js"></script>
<!--<script src="/platformFramework/js/v8/jspreadsheet.js"></script>-->	<style>
		.upload-list {
			display: inline-block;
			width: 60px;
			height: 60px;
			text-align: center;
			line-height: 60px;
			border: 1px solid transparent;
			border-radius: 4px;
			overflow: hidden;
			background: #fff;
			position: relative;
			box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
			margin-right: 4px;
		}

		.upload-list img {
			width: 100%;
			height: 100%;
		}

		.upload-list-cover {
			display: none;
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			background: rgba(0, 0, 0, .6);
		}

		.upload-list:hover .upload-list-cover {
			display: block;
		}

		.upload-list-cover i {
			color: #fff;
			font-size: 20px;
			cursor: pointer;
			margin: 0 2px;
		}
		.ivu-tabs .ivu-tabs-tabpane {
			-ms-flex-negative: 0;
			flex-shrink: 0;
			width: 100%;
			transition: opacity .3s;
			opacity: 1;
			/*min-height: 1000px;*/
			min-height: 700px;
		}
	</style>
</head>
<body>
<div id="rrapp"><div><div class="ivu-row" style="margin-left: -8px; margin-right: -8px;"><div class="search-group"><div class="ivu-col ivu-col-span-3" style="padding-left: 8px; padding-right: 8px;"><textarea id="nummbers" placeholder="条码（换行隔开）" rows="2" class="ivu-input" style="display: none;"></textarea></div> <div class="ivu-col ivu-col-span-4" style="padding-left: 8px; padding-right: 8px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="条形码" class="ivu-input"> <!----></div></div> <div class="ivu-radio-group"><label class="ivu-radio-wrapper ivu-radio-group-item ivu-radio-wrapper-checked"><span class="ivu-radio ivu-radio-checked"><span class="ivu-radio-inner"></span> <input type="radio" class="ivu-radio-input"></span><span>无</span></label> <label class="ivu-radio-wrapper ivu-radio-group-item"><span class="ivu-radio"><span class="ivu-radio-inner"></span> <input type="radio" class="ivu-radio-input"></span><span>追加该送评单的所有订单</span></label> <label class="ivu-radio-wrapper ivu-radio-group-item"><span class="ivu-radio"><span class="ivu-radio-inner"></span> <input type="radio" class="ivu-radio-input"></span><span>追加该鉴定单的所有订单</span></label></div> <button type="button" class="ivu-btn"><!----> <!----> <span>查询</span></button> <button type="button" class="ivu-btn"><!----> <!----> <span>复制</span></button> <button type="button" class="ivu-btn"><!----> <!----> <span>保存</span></button> <span style="color: red; font-size: 25px;">(录入过程请使用谷歌64位(Chrome)浏览器)</span></div></div> <div class="ivu-tabs"><div class="ivu-tabs-bar"><!----> <div class="ivu-tabs-nav-container"><div class="ivu-tabs-nav-wrap" style="position: relative;"><span class="ivu-tabs-nav-prev ivu-tabs-nav-scroll-disabled"><i class="ivu-icon ivu-icon-chevron-left"></i></span> <span class="ivu-tabs-nav-next ivu-tabs-nav-scroll-disabled"><i class="ivu-icon ivu-icon-chevron-right"></i></span> <div class="ivu-tabs-nav-scroll"><div class="nav-text ivu-tabs-nav"><div class="ivu-tabs-ink-bar ivu-tabs-ink-bar-animated" style="display: block; width: 74px; transform: translate3d(0px, 0px, 0px);"></div> <div class="ivu-tabs-tab ivu-tabs-tab-active"><!----> 古钱币 <!----></div><div class="ivu-tabs-tab"><!----> 银锭 <!----></div><div class="ivu-tabs-tab"><!----> 机制币 <!----></div><div class="ivu-tabs-tab"><!----> 纸币 <!----></div><div class="ivu-tabs-tab"><!----> 纸币excel <!----></div></div></div><object tabindex="-1" type="text/html" data="about:blank" style="display: block; position: absolute; top: 0px; left: 0px; width: 100%; height: 100%; border: none; padding: 0px; margin: 0px; opacity: 0; z-index: -1000; pointer-events: none;"></object></div></div></div> <div class="ivu-tabs-content ivu-tabs-content-animated" style="transform: translateX(0%) translateZ(0px);"><div class="ivu-tabs-tabpane"><table class="ui-jqgrid-htable ui-common-table table table-bordered"><thead><tr><th class="ui-th-column ui-th-ltr" style="width: 7%;">订单号</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称1</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称2</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">版别</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">年代</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">年份</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">等级</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">尺寸</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">重量</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">材质</th> <th class="ui-th-column ui-th-ltr">盒子类型</th> <th class="ui-th-column ui-th-ltr">对内备注</th> <th class="ui-th-column ui-th-ltr">对外备注</th> <th class="ui-th-column ui-th-ltr">操作</th></tr></thead> <tbody><tr><td></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称1" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称2" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="版别" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年代" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年份" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="等级" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="尺寸" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="重量" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="材质" class="ivu-input"> <!----></div></td> <td><div class="ivu-select ivu-select-single" style="width: 100px;"><div class="ivu-select-selection"><input type="hidden">  <span class="ivu-select-placeholder" style="display: none;">请选择</span> <span class="ivu-select-selected-value" style="display: none;"></span> <input type="text" placeholder="请选择" class="ivu-select-input"> <i class="ivu-icon ivu-icon-ios-close ivu-select-arrow" style="display: none;"></i> <i class="ivu-icon ivu-icon-arrow-down-b ivu-select-arrow"></i></div> <div class="ivu-select-dropdown" style="display: none;"><ul class="ivu-select-not-found" style="display: none;"><li>无匹配数据</li></ul> <ul class="ivu-select-dropdown-list"><li class="ivu-select-item">密封盒
								</li><li class="ivu-select-item">开放式
								</li><li class="ivu-select-item">证书
								</li></ul> <ul class="ivu-select-loading" style="display: none;">加载中</ul></div></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对内备注" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对外备注" class="ivu-input"> <!----></div></td> <td><!----></td></tr></tbody></table></div> <div class="ivu-tabs-tabpane"><table class="ui-jqgrid-htable ui-common-table table table-bordered"><thead><tr><th class="ui-th-column ui-th-ltr" style="width: 7%;">订单号</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称1</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称2</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">版别</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">年代</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">年份</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">地区</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">尺寸</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">重量</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">材质</th> <th class="ui-th-column ui-th-ltr">盒子类型</th> <th class="ui-th-column ui-th-ltr">对内备注</th> <th class="ui-th-column ui-th-ltr">对外备注</th> <th class="ui-th-column ui-th-ltr">操作</th></tr></thead> <tbody><tr><td></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称1" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称2" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="版别" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年代" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年份" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="地区" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="尺寸" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="重量" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="材质" class="ivu-input"> <!----></div></td> <td><div class="ivu-select ivu-select-single" style="width: 100px;"><div class="ivu-select-selection"><input type="hidden">  <span class="ivu-select-placeholder" style="display: none;">请选择</span> <span class="ivu-select-selected-value" style="display: none;"></span> <input type="text" placeholder="请选择" class="ivu-select-input"> <i class="ivu-icon ivu-icon-ios-close ivu-select-arrow" style="display: none;"></i> <i class="ivu-icon ivu-icon-arrow-down-b ivu-select-arrow"></i></div> <div class="ivu-select-dropdown" style="display: none;"><ul class="ivu-select-not-found" style="display: none;"><li>无匹配数据</li></ul> <ul class="ivu-select-dropdown-list"><li class="ivu-select-item">密封盒
								</li><li class="ivu-select-item">开放式
								</li><li class="ivu-select-item">证书
								</li></ul> <ul class="ivu-select-loading" style="display: none;">加载中</ul></div></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对内备注" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对外备注" class="ivu-input"> <!----></div></td> <td><!----></td></tr></tbody></table></div> <div class="ivu-tabs-tabpane"><table class="ui-jqgrid-htable ui-common-table table table-bordered"><thead><tr><th class="ui-th-column ui-th-ltr" style="width: 7%;">订单号</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称1</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称2</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">版别</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">年代</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">面值</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">尺寸</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">重量</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">材质</th> <th class="ui-th-column ui-th-ltr">盒子类型</th> <th class="ui-th-column ui-th-ltr">对内备注</th> <th class="ui-th-column ui-th-ltr">对外备注</th> <th class="ui-th-column ui-th-ltr">操作</th></tr></thead> <tbody><tr><td></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称1" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称2" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="版别" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年代" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="面值" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="尺寸" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="重量" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="材质" class="ivu-input"> <!----></div></td> <td><div class="ivu-select ivu-select-single" style="width: 100px;"><div class="ivu-select-selection"><input type="hidden">  <span class="ivu-select-placeholder" style="display: none;">请选择</span> <span class="ivu-select-selected-value" style="display: none;"></span> <input type="text" placeholder="请选择" class="ivu-select-input"> <i class="ivu-icon ivu-icon-ios-close ivu-select-arrow" style="display: none;"></i> <i class="ivu-icon ivu-icon-arrow-down-b ivu-select-arrow"></i></div> <div class="ivu-select-dropdown" style="display: none;"><ul class="ivu-select-not-found" style="display: none;"><li>无匹配数据</li></ul> <ul class="ivu-select-dropdown-list"><li class="ivu-select-item">密封盒
								</li><li class="ivu-select-item">开放式
								</li><li class="ivu-select-item">证书
								</li></ul> <ul class="ivu-select-loading" style="display: none;">加载中</ul></div></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对内备注" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对外备注" class="ivu-input"> <!----></div></td> <td><!----></td></tr></tbody></table></div> <div class="ivu-tabs-tabpane"><div class="ivu-row" style="margin-left: -8px; margin-right: -8px; float: right;"><div class="search-group"><div class="ivu-col ivu-col-span-3" style="padding-left: 8px; padding-right: 8px;"></div> <button type="button" class="ivu-btn"><span id="spanNumBack">上一页</span></button> <button type="button" class="ivu-btn"><span id="spanNum">0</span></button> <button type="button" class="ivu-btn"><span>/</span></button> <button type="button" class="ivu-btn"><span id="spanNum2">0</span></button> <button type="button" class="ivu-btn"><span id="spanNumNext">下一页</span></button></div></div> <table class="ui-jqgrid-htable ui-common-table table table-bordered"><thead><tr><th class="ui-th-column ui-th-ltr" style="width: 7%;">订单号</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称1</th> <th class="ui-th-column ui-th-ltr" style="width: 7%;">名称2</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">版别</th> <th class="ui-th-column ui-th-ltr" style="width: 4%;">年代</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">冠号</th> <th class="ui-th-column ui-th-ltr" style="width: 5%;">银行</th> <th class="ui-th-column ui-th-ltr" style="width: 10%;">品相分数</th> <th class="ui-th-column ui-th-ltr" style="width: 10%;"> ★ /EPQ/NET</th> <th class="ui-th-column ui-th-ltr" style="width: 10%;">真伪</th> <th class="ui-th-column ui-th-ltr">地区</th> <th class="ui-th-column ui-th-ltr">目录</th> <th class="ui-th-column ui-th-ltr">钱币备注</th> <th class="ui-th-column ui-th-ltr">盒子类型</th> <th class="ui-th-column ui-th-ltr">对内备注</th> <th class="ui-th-column ui-th-ltr">对外备注</th> <th class="ui-th-column ui-th-ltr">操作</th></tr></thead> <tbody><tr><td></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称1" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称2" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="版别" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年代" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="冠号" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="银行" class="ivu-input"> <!----></div></td> <td><div class="ivu-select ivu-select-single"><div class="ivu-select-selection"><input type="hidden">  <span class="ivu-select-placeholder" style="display: none;">请选择</span> <span class="ivu-select-selected-value" style="display: none;"></span> <input type="text" placeholder="请选择" class="ivu-select-input"> <i class="ivu-icon ivu-icon-ios-close ivu-select-arrow" style="display: none;"></i> <i class="ivu-icon ivu-icon-arrow-down-b ivu-select-arrow"></i></div> <div class="ivu-select-dropdown" style="display: none;"><ul class="ivu-select-not-found" style="display: none;"><li>无匹配数据</li></ul> <ul class="ivu-select-dropdown-list"><li class="ivu-select-item">Superb Gem Unc70
								</li><li class="ivu-select-item">Superb Gem Unc69
								</li><li class="ivu-select-item">Superb Gem Unc68
								</li><li class="ivu-select-item">Superb Gem Unc67
								</li><li class="ivu-select-item">Gem Uncirculated66
								</li><li class="ivu-select-item">Gem Uncirculated65
								</li><li class="ivu-select-item">Choice Uncirculated64
								</li><li class="ivu-select-item">Choice Uncirculated63
								</li><li class="ivu-select-item">Uncirculated62
								</li><li class="ivu-select-item">Uncirculated61
								</li><li class="ivu-select-item">Uncirculated60
								</li><li class="ivu-select-item">Choice About Unc58
								</li><li class="ivu-select-item">About Uncirculated55
								</li><li class="ivu-select-item">About Uncirculated53
								</li><li class="ivu-select-item">About Uncirculated50
								</li><li class="ivu-select-item">Choice Extremely Fine45
								</li><li class="ivu-select-item">Extremely Fine40
								</li><li class="ivu-select-item">Choice Very Fine35
								</li><li class="ivu-select-item">Very Fine30
								</li><li class="ivu-select-item">Very Fine25
								</li><li class="ivu-select-item">Genuine
								</li><li class="ivu-select-item">Genuine真品
								</li><li class="ivu-select-item">极美品
								</li><li class="ivu-select-item">精美品
								</li><li class="ivu-select-item">美品
								</li><li class="ivu-select-item">上美品
								</li></ul> <ul class="ivu-select-loading" style="display: none;">加载中</ul></div></div></td> <td><div class="ivu-checkbox-group"><label class="ivu-checkbox-wrapper ivu-checkbox-group-item"><span class="ivu-checkbox"><span class="ivu-checkbox-inner"></span> <input type="checkbox" class="ivu-checkbox-input" value="11"> <!----></span> ★</label><label class="ivu-checkbox-wrapper ivu-checkbox-group-item"><span class="ivu-checkbox"><span class="ivu-checkbox-inner"></span> <input type="checkbox" class="ivu-checkbox-input" value="12"> <!----></span> EPQ</label><label class="ivu-checkbox-wrapper ivu-checkbox-group-item"><span class="ivu-checkbox"><span class="ivu-checkbox-inner"></span> <input type="checkbox" class="ivu-checkbox-input" value="13"> <!----></span> NET</label></div></td> <td><div class="ivu-select ivu-select-single"><div class="ivu-select-selection"><input type="hidden">  <span class="ivu-select-placeholder" style="display: none;">请选择</span> <span class="ivu-select-selected-value" style="display: none;"></span> <input type="text" placeholder="请选择" class="ivu-select-input"> <i class="ivu-icon ivu-icon-ios-close ivu-select-arrow" style="display: none;"></i> <i class="ivu-icon ivu-icon-arrow-down-b ivu-select-arrow"></i></div> <div class="ivu-select-dropdown" style="display: none;"><ul class="ivu-select-not-found" style="display: none;"><li>无匹配数据</li></ul> <ul class="ivu-select-dropdown-list"><li class="ivu-select-item">未鉴定
								</li><li class="ivu-select-item">真
								</li><li class="ivu-select-item">赝品
								</li><li class="ivu-select-item">存疑
								</li><li class="ivu-select-item">不提供服务
								</li><li class="ivu-select-item">不适合评级(赝品)
								</li><li class="ivu-select-item">不适合评级(锭体、铭文修复超出规定)
								</li><li class="ivu-select-item">不适合评级(老假、老仿、臆造)
								</li><li class="ivu-select-item">不适合评级
								</li><li class="ivu-select-item">撤评
								</li><li class="ivu-select-item">暂不提供服务(香港)
								</li></ul> <ul class="ivu-select-loading" style="display: none;">加载中</ul></div></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="地区" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="目录" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="钱币备注" class="ivu-input"> <!----></div></td> <td><div class="ivu-select ivu-select-single" style="width: 100px;"><div class="ivu-select-selection"><input type="hidden">  <span class="ivu-select-placeholder" style="display: none;">请选择</span> <span class="ivu-select-selected-value" style="display: none;"></span> <input type="text" placeholder="请选择" class="ivu-select-input"> <i class="ivu-icon ivu-icon-ios-close ivu-select-arrow" style="display: none;"></i> <i class="ivu-icon ivu-icon-arrow-down-b ivu-select-arrow"></i></div> <div class="ivu-select-dropdown" style="display: none;"><ul class="ivu-select-not-found" style="display: none;"><li>无匹配数据</li></ul> <ul class="ivu-select-dropdown-list"><li class="ivu-select-item">密封盒
								</li><li class="ivu-select-item">开放式
								</li><li class="ivu-select-item">证书
								</li></ul> <ul class="ivu-select-loading" style="display: none;">加载中</ul></div></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对内备注" class="ivu-input"> <!----></div></td> <td><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对外备注" class="ivu-input"> <!----></div></td> <td><!----></td></tr></tbody></table></div> <div class="ivu-tabs-tabpane"><div style="height: 700px; width: 100%;"><div class="happy-scroll"><div class="happy-scroll-container" style="width: 1214px; height: 720px;"><div class="happy-scroll-content"><div class="con"><div id="spreadsheet1" tabindex="1" class="jexcel_container" style="width: 100%;"><div class="jexcel_toolbar"></div><div class="jexcel_content"><table class="jexcel jexcel_overflow" cellpadding="0" cellspacing="0" unselectable="yes"><colgroup><col width="50"><col width="82px"><col width="100px"><col width="80px"><col width="80px"><col width="150px"><col width="50px"><col width="120px"><col width="100px"><col width="150px"><col width="110px"><col width="150px"><col width="50px"><col width="100px"><col width="100px"><col width="70px"><col width="100px"><col width="100px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"><col width="0px"></colgroup><thead class="draggable resizable"><tr><td class="jexcel_selectall"></td><td data-x="0" title="订单号" style="text-align: center;">订单号</td><td data-x="1" title="名称1" style="text-align: center;">名称1</td><td data-x="2" title="名称2" style="text-align: center;">名称2</td><td data-x="3" title="附加" style="text-align: center;">附加</td><td data-x="4" title="版别" style="text-align: left;">版别</td><td data-x="5" title="年代" style="text-align: center;">年代</td><td data-x="6" title="冠号" style="text-align: center;">冠号</td><td data-x="7" title="银行" style="text-align: center;">银行</td><td data-x="8" title="品相分数" style="text-align: center;">品相分数</td><td data-x="9" title="★ /EPQ/NET" style="text-align: left;">★ /EPQ/NET</td><td data-x="10" title="真伪" style="text-align: left;">真伪</td><td data-x="11" title="地区" style="text-align: center;">地区</td><td data-x="12" title="目录" style="text-align: center;">目录</td><td data-x="13" title="钱币备注" style="text-align: center;">钱币备注</td><td data-x="14" title="星级" style="text-align: left;">星级</td><td data-x="15" title="对内备注" style="text-align: center;">对内备注</td><td data-x="16" title="对外备注" style="text-align: center;">对外备注</td><td data-x="17" title="addType" style="text-align: center;">addType</td><td data-x="18" title="amount" style="text-align: center;">amount</td><td data-x="19" title="boxfee" style="text-align: center;">boxfee</td><td data-x="20" title="bzfee" style="text-align: center;">bzfee</td><td data-x="21" title="classid" style="text-align: center;">classid</td><td data-x="22" title="cldef" style="text-align: center;">cldef</td><td data-x="23" title="cldefNum" style="text-align: center;">cldefNum</td><td data-x="24" title="cldefname" style="text-align: center;">cldefname</td><td data-x="25" title="cointype" style="text-align: center;">cointype</td><td data-x="26" title="corder" style="text-align: center;">corder</td><td data-x="27" title="createTime" style="text-align: center;">createTime</td><td data-x="28" title="createUser" style="text-align: center;">createUser</td><td data-x="29" title="ctCaizhi" style="text-align: center;">ctCaizhi</td><td data-x="30" title="ctJiyuan" style="text-align: center;">ctJiyuan</td><td data-x="31" title="ctPrint" style="text-align: center;">ctPrint</td><td data-x="32" title="ctValue" style="text-align: center;">ctValue</td><td data-x="33" title="ctZhuzb" style="text-align: center;">ctZhuzb</td><td data-x="34" title="extbqs" style="text-align: center;">extbqs</td><td data-x="35" title="extbqs1" style="text-align: center;">extbqs1</td><td data-x="36" title="extbqs2" style="text-align: center;">extbqs2</td><td data-x="37" title="feeex" style="text-align: center;">feeex</td><td data-x="38" title="finalfee" style="text-align: center;">finalfee</td><td data-x="39" title="gjfee" style="text-align: center;">gjfee</td><td data-x="40" title="gm" style="text-align: center;">gm</td><td data-x="41" title="gmname" style="text-align: center;">gmname</td><td data-x="42" title="grade" style="text-align: center;">grade</td><td data-x="43" title="id" style="text-align: center;">id</td><td data-x="44" title="ids" style="text-align: center;">ids</td><td data-x="45" title="imgList" style="text-align: center;">imgList</td><td data-x="46" title="indate" style="text-align: center;">indate</td><td data-x="47" title="intro" style="text-align: center;">intro</td><td data-x="48" title="ispack" style="text-align: center;">ispack</td><td data-x="49" title="issm" style="text-align: center;">issm</td><td data-x="50" title="jiaji" style="text-align: center;">jiaji</td><td data-x="51" title="jiajifee" style="text-align: center;">jiajifee</td><td data-x="52" title="marker" style="text-align: center;">marker</td><td data-x="53" title="mianzhi" style="text-align: center;">mianzhi</td><td data-x="54" title="name3" style="text-align: center;">name3</td><td data-x="55" title="nameex" style="text-align: center;">nameex</td><td data-x="56" title="nickname" style="text-align: center;">nickname</td><td data-x="57" title="numbers" style="text-align: center;">numbers</td><td data-x="58" title="pic" style="text-align: center;">pic</td><td data-x="59" title="qtfee" style="text-align: center;">qtfee</td><td data-x="60" title="qxshow" style="text-align: center;">qxshow</td><td data-x="61" title="rank" style="text-align: center;">rank</td><td data-x="62" title="rname" style="text-align: center;">rname</td><td data-x="63" title="rnameex" style="text-align: center;">rnameex</td><td data-x="64" title="score" style="text-align: center;">score</td><td data-x="65" title="scoreDesc" style="text-align: center;">scoreDesc</td><td data-x="66" title="scoreName" style="text-align: center;">scoreName</td><td data-x="67" title="scoreNum" style="text-align: center;">scoreNum</td><td data-x="68" title="scoreex" style="text-align: center;">scoreex</td><td data-x="69" title="scoreshow" style="text-align: center;">scoreshow</td><td data-x="70" title="sendnum" style="text-align: center;">sendnum</td><td data-x="71" title="shape" style="text-align: center;">shape</td><td data-x="72" title="shuizhong" style="text-align: center;">shuizhong</td><td data-x="73" title="size" style="text-align: center;">size</td><td data-x="74" title="smtime" style="text-align: center;">smtime</td><td data-x="75" title="status" style="text-align: center;">status</td><td data-x="76" title="subclassid" style="text-align: center;">subclassid</td><td data-x="77" title="thirdclassid" style="text-align: center;">thirdclassid</td><td data-x="78" title="thumb" style="text-align: center;">thumb</td><td data-x="79" title="updateTime" style="text-align: center;">updateTime</td><td data-x="80" title="updateUser" style="text-align: center;">updateUser</td><td data-x="81" title="weight" style="text-align: center;">weight</td><td data-x="82" title="year" style="text-align: center;">year</td><td data-x="83" title="yearex" style="text-align: center;">yearex</td><td data-x="84" title="zk" style="text-align: center;">zk</td></tr></thead><tbody class="draggable resizable"></tbody></table><div class="jexcel_corner" unselectable="on" onselectstart="return false" style="top: -2000px; left: -2000px;"></div><textarea class="jexcel_textarea" id="jexcel_textarea" tabindex="-1"></textarea></div><div class="jexcel_pagination" style="display: none;"><div></div><div></div></div><div class="jexcel_contextmenu jcontextmenu" tabindex="900"></div><div class="jexcel_about"><a href="https://bossanova.uk/jspreadsheet/"><span>Jspreadsheet CE</span></a></div></div></div></div></div><div class="happy-scroll-strip happy-scroll-strip--vertical"><div class="happy-scroll-bar" style="height: 0px; width: 5px; background: rgba(0, 0, 0, 0.5); opacity: 0;"></div></div><div class="happy-scroll-strip happy-scroll-strip--horizontal"><div class="happy-scroll-bar" style="width: 0px; height: 5px; background: rgba(0, 0, 0, 0.5); opacity: 0;"></div></div></div></div></div></div></div></div> <div class="ivu-card ivu-card-bordered" style="display: none;"><div class="ivu-card-head"><p></p></div> <!----> <div class="ivu-card-body"> <form class="ivu-form ivu-form-label-right"><div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">编号</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="number" placeholder="不填写时则自动生成" readonly="readonly" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">纲目</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <i class="ivu-icon ivu-icon-eye ivu-input-icon ivu-input-icon-normal"></i> <!----> <input autocomplete="off" type="text" placeholder="纲目" readonly="readonly" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item ivu-form-item-required"><label class="ivu-form-item-label" style="width: 80px;">名称</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="名称" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">年代</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年代" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">尺寸</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="尺寸" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">重量</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="重量" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">材质</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="材质" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">缺陷代码</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="缺陷代码" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">分数代码</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="分数代码" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">分数名称</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="分数名称" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">分数</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="分数" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">分数备注</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="分数备注" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item" style="width: 268px;"><label class="ivu-form-item-label" style="width: 80px;">鉴定结果</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-select ivu-select-single"><div class="ivu-select-selection"><input type="hidden">  <span class="ivu-select-placeholder" style="display: none;">请选择</span> <span class="ivu-select-selected-value" style="display: none;"></span> <input type="text" placeholder="请选择" class="ivu-select-input"> <i class="ivu-icon ivu-icon-ios-close ivu-select-arrow" style="display: none;"></i> <i class="ivu-icon ivu-icon-arrow-down-b ivu-select-arrow"></i></div> <div class="ivu-select-dropdown" style="display: none;"><ul class="ivu-select-not-found" style="display: none;"><li>无匹配数据</li></ul> <ul class="ivu-select-dropdown-list"><li class="ivu-select-item">未鉴定
					</li><li class="ivu-select-item">真
					</li><li class="ivu-select-item">赝品
					</li><li class="ivu-select-item">存疑
					</li><li class="ivu-select-item">不提供服务
					</li><li class="ivu-select-item">不适合评级(赝品)
					</li><li class="ivu-select-item">不适合评级(锭体、铭文修复超出规定)
					</li><li class="ivu-select-item">不适合评级(老假、老仿、臆造)
					</li><li class="ivu-select-item">不适合评级
					</li><li class="ivu-select-item">撤评
					</li><li class="ivu-select-item">暂不提供服务(香港)
					</li></ul> <ul class="ivu-select-loading" style="display: none;">加载中</ul></div></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">年份</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="年份" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">地区</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="地区" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">银行名称</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="地区" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">纸币编号</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="纸币编号" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">预估价</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="预估价" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">对内备注</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="ivu-input-wrapper ivu-input-type"><!----> <!----> <i class="ivu-icon ivu-icon-load-c ivu-load-loop ivu-input-icon ivu-input-icon-validate"></i> <input autocomplete="off" type="text" placeholder="对内备注" class="ivu-input"> <!----></div> <!----></div></div> <div class="ivu-form-item"><label class="ivu-form-item-label" style="width: 80px;">图片</label> <div class="ivu-form-item-content" style="margin-left: 80px;"><div class="upload-list"><img> <div class="upload-list-cover"><i class="ivu-icon ivu-icon-ios-eye-outline"></i> <i class="ivu-icon ivu-icon-ios-trash-outline"></i></div></div> <div class="ivu-upload" style="display: inline-block; width: 58px;"><div class="ivu-upload ivu-upload-drag"><input type="file" multiple="multiple" class="ivu-upload-input"> <div style="width: 58px; height: 58px; line-height: 58px;"><i class="ivu-icon ivu-icon-camera" style="font-size: 20px;"></i></div></div>  <!----></div> <!----> <!----></div></div> <button type="button" class="ivu-btn ivu-btn-primary"><!----> <!----> <span>提交</span></button> <button type="button" class="ivu-btn ivu-btn-warning" style="margin-left: 8px;"><!----> <!----> <span>返回</span></button> <button type="button" class="ivu-btn ivu-btn-ghost" style="margin-left: 8px;"><!----> <!----> <span>重置</span></button></form></div></div></div>
<!-- 选择纲目 -->
<div id="deptLayer" style="display: none;padding:10px;">
	<ul id="deptTree" class="ztree"></ul>
</div>




<script src="/platformFramework/js/sys/coinsm.js?_1749996659431"></script><div data-transfer="true" class="v-transfer-dom"><div class="ivu-modal-mask" style="display: none;"></div> <div class="ivu-modal-wrap ivu-modal-hidden"><div class="ivu-modal" style="width: 520px; display: none;"><div class="ivu-modal-content"><a class="ivu-modal-close"><i class="ivu-icon ivu-icon-ios-close-empty"></i></a> <div class="ivu-modal-header"><div class="ivu-modal-header-inner">查看图片</div></div> <div class="ivu-modal-body"></div> <div class="ivu-modal-footer"><button type="button" class="ivu-btn ivu-btn-text ivu-btn-large"><!----> <!----> <span>取消</span></button> <button type="button" class="ivu-btn ivu-btn-primary ivu-btn-large"><!----> <!----> <span>确定</span></button></div></div></div></div></div>
<!--<script src="js/v4/jexcel.js"></script>-->
<!--<script src="js/v4/jsuites.js"></script>-->
<!--<link rel="stylesheet" href="css/v4/jexcel.css" type="text/css" />-->
<!--<link rel="stylesheet" href="css/v4/jsuites.css" type="text/css" />-->
<script>
	var usernames =null;
	var numberRow;
	var numberRowOk=0;

	var userIdssss = null;
	// function getIntArr(str){
	// 	return  str.replace(/[^0-9]/ig, ' ').trim().split(/\s+/);
	// }

	var onpa = function(el, data) {//粘贴数据后
		// console.log("onpa");
		// console.log( el );
		// console.log( data );


		console.log("改变后");


	}


	var onbefor = function(el, data, x, y) {//它发生在将数据粘贴到电子表格之前，可用于拦截、更改或取消用户操作。



		 let arrdata=data.split('\n');//行数
		arrdata=arrdata[0].trim().split('\t');
		let col=table1.getSelectedColumns();//x
		let row=table1.getSelectedRows();//y
		if(arrdata.length>1){

			if((arrdata.length==1&&arrdata.length==col.length)||row.length>1){

				for (let i = 0; i < arrdata.length; i++) {
					for (let j = 0; j < row.length; j++) {
						table1.setValueFromCoords(col[i],row[j].rowIndex-1,arrdata[i]);
					}

				}
				return false;
			}


		}


		//table1.setValueFromCoords(table1.getSelectedColumns()[0],table1.getSelectedRows()[0].rowIndex-1,"111111111");
		//console.log(table1.getSelectedColumns());//列
		//console.log(table1.getSelectedRows());//行
		// console.log(table1.getSelectedColumns());//x坐标取值
		// console.log(table1.getSelectedRows());//y坐标取值




	}



	var onediti = function(el, cell, x, y) {//选择改变

		// console.log("onediti");
		// console.log( el );
		// console.log( cell );
		// console.log( x );
		// console.log( y );
		// console.log("onediti");


	}


	var changed1 = function(el, records) {//单元格改变事件

		// console.log("改变后");
		// console.log( el );
		// console.log(records);
		// for (let i = 0; i < records.length; i++) {
		//
		// 	console.log(records[i].x);
		// 	console.log(records[i].y);
		//
		//
		// }

	}


	var changed = function(instance, cell, x, y, value) {
		var cellName = jspreadsheet.getColumnNameFromId([x,y]);
		 //console.log('单元格内容' + cellName + ' 更新为: ' + value );
		// table1.setValueFromCoords(x,y,"111111111");
		let  projectData = [];

		let data=table1.getData();
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据


		for (let i = 0; i < data.length; i++) {
			let json={};

			let data1=data[i];

			json.addr=(data1[11]);//addr
			json.bank=(data1[7]); //bank
			json.bzfee=(data1[20]);//bzfee
			json.catalog=(data1[12]);//catalog
			json.classid=(data1[21]);//classid
			json.cldef=(data1[22]);//cldef
			json.cldefNum=(data1[23]);//cldefNum
			json.cldefname=(data1[24]);//cldefname
			json.cointype=(data1[25]);//cointype
			json.createTime=(data1[27]);//createTime
			json.createUser=(data1[28]);//createUser
			json.edition=(data1[4]); //edition
			json.extbq=(extbqPackDe(data1[9])); //extbq
			json.finalfee=(data1[38]);//finalfee
			json.gjfee=(data1[39]);//gjfee
			json.gm=(data1[40]);//gm
			json.gmname=(data1[41]);//gmname
			json.id=(data1[43]);//id
			json.jiaji=(data1[50]);//jiaji
			json.live=(data1[3]);//live
			json.name=(data1[1]); //name
			json.niandai=(data1[5]); //niandai
			json.nummber=(data1[0]); //nummber
			json.pic=(data1[58]);//pic
			json.qxshow=(data1[60]);//qxshow
			json.result=(resultPackDe(data1[10])); //result
			json.score=(scoreshow2Pack2(data1[8]));//score 分数data1[64]
			json.scoreName=(data1[66]);//scoreName
			json.scoreNum=(scoreshow2Pack1(data1[8]));//scoreNum 分数表现data1[67]
			json.scoreshow=(data1[69]);//scoreshow
			json.scoreshow2=(data1[8]); //scoreshow2
			json.sendnum=(data1[70]);//sendnum
			json.subclassid=(data1[76]);//subclassid
			json.thumb=(data1[78]);//thumb
			json.updateTime=(data1[79]);//updateTime
			json.updateUser=(data1[80]);//updateUser
			json.zbnum=(data1[6]); //zbnum
			json.zk=(data1[84]);//zk
			json.boxtype=(boxtypePackDe(data1[14]));//boxtype
			json.backweight=(data1[15]);//backweight
			json.description=(data1[16]);//description
			json.coindesc=(data1[13]);//coindesc
			json.name2=(data1[2]);//name2

			userIdssss=json.createUser
			projectData.push(json);//boxtypePackDe



		}



		// let url = "../pjclcoin/bufferAll";
		// Ajax.request({
		// 	url: url,
		// 	params: JSON.stringify(projectData),
		// 	type: "POST",
		// 	contentType: "application/json",
		// 	successCallback: function (r) {
		//
		// 		// console.log(r);
		//
		// 	}
		// });




	}

	var beforeChange =  function(instance, cell, x, y, value) {
		/*// 检查是否满足序列化的条件
		if (/^[A-Z]+\d+$/.test(value)) { // 正则表达式匹配以字母开头并且带数字结尾的字符串
			var match = value.match(/([A-Z]+)(\d+)$/);
			var prefix = match[1]; // 字母部分
			var number = parseInt(match[2]); // 数字部分
			var colIndex = x; // 当前列
			var rowIndex = y; // 当前行

			// 获取下拉的范围
			var nextRows = instance.getSelectedRows();

			for (var i = 0; i < nextRows.length; i++) {
				// 增加序号
				var newValue = prefix + (number + i + 1);
				instance.setValueFromCoords(colIndex, nextRows[i], newValue);
			}

			// 阻止默认的值更改
			return false;
		}*/
	}


	var insertedRow = function(instance) {
		console.log('Row added');
	}

	var insertedColumn = function(instance) {
		console.log('Column added');
	}

	var deletedRow = function(instance) {
		console.log('Row deleted');
	}

	var deletedColumn = function(instance) {
		console.log('Column deleted');
	}

	var sort = function(instance, cellNum, order) {
		var order = (order) ? 'desc' : 'asc';
		//console.log('The column  ' + cellNum + ' sorted by ' + order + '');
	}


	var beforesort = function(el, column,  direction,  newOrderValues) {
		//console.log('排序前');
		// return false;
		// var order = (order) ? 'desc' : 'asc';
		//console.log('The column  ' + cellNum + ' sorted by ' + order + '');
	}

	var resizeColumn = function(instance, cell, width) {
		//console.log('The column  ' + cell + ' resized to width ' + width + ' px');
	}

	var resizeRow = function(instance, cell, height) {
		//console.log('The row  ' + cell + ' resized to height ' + height + ' px');
	}

	var selectionActive = function(instance, x1, y1, x2, y2, origin) {
		var cellName1 = jspreadsheet.getColumnNameFromId([x1, y1]);
		var cellName2 = jspreadsheet.getColumnNameFromId([x2, y2]);

		 numberRow=table1.getValue(cellName1);
		 numberRowOk=0;


		// console.log(x1);

		// console.log(table1.getValue(cellName1));


		 //console.log('实时选择区 ' + cellName1 + ' 到 ' + cellName2 + '');
	}

	var loaded = function(instance) {
		console.log('New data is loaded');
	}

	var moveRow = function(instance, from, to) {
		console.log('The row ' + from + ' was move to the position of ' + to + ' ');
	}

	var moveColumn = function(instance, from, to) {
		console.log('The col ' + from + ' was move to the position of ' + to + ' ');
	}

	var blur = function(instance) {
		console.log('The table ' + $(instance).prop('id') + ' is blur');
	}

	var focus = function(instance) {
		console.log('The table ' + $(instance).prop('id') + ' is focus');
	}




	var dataauto = ['1|汽车','2|飞机','3|轮船','4|民族人物头像','5|纺织','6|女拖拉机手','7|大团结','8|长江大桥','9|毛泽东头像','10|建国纪念钞','11|拖拉机','12|车床工人','13|炼钢工人','14|工农知识分子头像','15|教育与生产劳动相结合','16|各民族大团结','17|龙钞纪念币','18|水电站','19|火车','20|井冈山','21|天安门','22|宝塔山','23|四位领袖浮雕头像','24|航天纪念钞','25|迎接新世纪纪念钞','26|塑料钞'];
	var data1 = [
		// [ '名称1','名称2','年代','目录','银行名称','盒子类型','数量','标准价','国际价','费用','折扣','盒子费','加急','版别','特殊标签','归属公司'],
		// [ '订单号','名称1','名称2','版别','年代','冠号','银行','品相分数','★ /EPQ/NET','真伪','地区','目录','钱币备注','盒子类型','对内备注','对外备注'],
		// [ '订单号','名称1','名称2','版别','年代','冠号','银行','品相分数','★ /EPQ/NET','真伪','地区','目录','钱币备注','盒子类型','对内备注','对外备注'],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
	];

	// console.log(data1);
	var table1 = jspreadsheet(document.getElementById('spreadsheet1'), {
		// data:data1,
		columns: [

			{
				title: '订单号',
				type: 'text',
				width:'82px',
				readOnly:true,

			},
			{
				title: '名称1',
				type: 'text',
				width:'100px',
			},
			{
				title: '名称2',
				type: 'text',
				width:'80px',
			},
			{
				title: '附加',
				type: 'text',
				width:'80px',
			},

			{
				title: '版别',
				type: 'autocomplete',//autocomplete

				source:['汽车','南昌','6张纪念钞组合','飞机','轮船','拾圆伍圆贰圆壹圆贰角（荧光钞）','民族人物头像','纺织','女拖拉机手','大团结','长江大桥','毛泽东头像','建国纪念钞',
					    '拖拉机','工人农民','龙年纪念钞','大黑拾','红库印','车床工人','币签合一','炼钢工人','工农知识分子头像','教育与生产劳动相结合','各民族大团结','龙钞纪念币','水坝',
					    '水电站','建设公债','斐济财神钞','第四套套装 分币组合','银元券','航天钞 2018世界杯 索契冬奥纪念币组合','省立、兑换劵','老假','双马耕地','永泰县','福建','火车','兑换劵','井冈山','天安门','（广东省集藏投资协会监制）','宝塔山','四位领袖浮雕头像','航天纪念钞','迎接新世纪纪念钞','塑料钞',
					    '上海','花版','地方流通劵','鸟版','保值公债','黄钥匙','罗马兵','帆船','小棉胎','蓝屋','吉林','寿光','东三省','汇兑券','省立','兑米票','三明市企业内部债券','昌黎','重庆、银元辅币券','琼崖区流通券','银元辅币券','私人印制','大汉四川军政府军用银票','山西','兑换券（纪念券）','左右号不同','Bank of China','预备乘车券','甲辰龙年贺岁纪念钞','船版','纸币套装 伍圆 贰圆 壹圆','本票','益阳二里','太原','革命战争共债劵','2024年斐济龙年塑料钞','四版贰圆贰角/三版贰角/飞机','广州、银元券','合作券','东北九省流通券','天津','三罗码','金圆券','国库券','外汇兑换券','香港','重庆','张家口','捆刀封签','银毫券','北京',
					    '厦门','东三省、小银元券','河南','杭州','山东、兑换券','哈尔滨','股权证','股票','兑换券','张家口丰镇','梧州','福州','京兆','节约建国储蓄券','牌坊','北平','济南','第四版人民币 伍圆 贰圆 壹圆','第24届北京冬季奥林匹克运动会纪念钞','奥运会纪念钞','成立一百五十周年纪念','70周年纪念钞','冬奥会纪念钞','关金券','经济建设公债','广东','广州',
					    '汽车、飞机、轮船','湘赣省分行','革命战争公债券','票样正面','八同号','票样反面','甘肃','辽东','大洋票','山东','驮运','银毫劵',"蛇年纪念钞",
					    '纪念中国银行成立一百周年','汕头','地方经济建设公债','支票','第四套套装','七十周年纪念钞','第三套18.88套装','小小三套装 粮票组合',
				        '人民代表步出大会堂','521套装','1953年分币套装',  '冰上运动雪上运动组合',
						'第24届冬奥会纪念钞',  '退市第四套人民币套装组合','中华民国三十八年','关金伍仟圆', '四版贰圆贰角/三版贰角 三枚组合', '维吾尔族、彝族 长江大桥组合',
						'第四套套装 十全十美',  '粮票组合套装',  '布依族、朝鲜族,长江大桥组合',  '伍分轮船 贰分飞机 壹分汽车',
						'小小三套装','男皇蓝色版','男皇紫色版','小圣书','大棉胎','七小福套装','第三套18.88套装 粮票组合','东北九省流通券','第四版人民币 壹佰圆 索契冬奥钞','70周年纪念钞 世界杯纪念钞 北京冬奥会纪念钞','四版贰圆贰角/贰分','第三版人民币组合（综合分）','三版伍圆/四版伍圆组合','第三套/第四套拾圆组合','纤云.金光星辉','中华民国三十六年','第四套套装 粮票组合','庆祝中华人民共和国成立50周年','小四套装'],
				width:'150px',
				align: 'left',

			},
			{
				title: '年代',
				type: 'text',
				width:'50px',
			},
			{
				title: '冠号',
				type: 'text',
				width:'120px',
			},
			{
				title: '银行',
				type: 'text',
				width:'100px',
			},
			{
				title: '品相分数',
				type: 'autocomplete',
				// source: qualityOptions,
				source:[ 'Superb Gem Unc70','Superb Gem Unc69','Superb Gem Unc68','Superb Gem Unc67','Gem Uncirculated66','Gem Uncirculated65','Choice Uncirculated64','Choice Uncirculated63','Uncirculated62','Uncirculated61','Uncirculated60','Choice About Unc58','About Uncirculated55','About Uncirculated53','About Uncirculated50','Choice Extremely Fine45','Extremely Fine40','Choice Very Fine35','Very Fine30','Very Fine25','Genuine','Genuine真品','极美品','精美品','美品','上美品'],
				width:'150px',
			},
			{
				title: '★ /EPQ/NET',
				autocomplete:true,
				multiple:true,
				type: 'dropdown',
				source:[ '★','EPQ','NET'],
				width:'110px',
				align: 'left',
			},

			{
				title: '真伪',
				type: 'autocomplete',
				source:[ '0|未鉴定','1|真','2|赝品','3|存疑','4|不提供服务','5|不适合评级(赝品)','6|不适合评级(锭体、铭文修复超出规定)','9|不适合评级(老假、老仿、臆造)','8|不适合评级','7|撤评'],
				width:'150px',
				align: 'left',
			},{
				title: '地区',
				type: 'text',
				width:'50px',
			},{
				title: '目录',
				type: 'text',
				width:'100px',
			},
			{
				title: '钱币备注',
				type: 'autocomplete',
				source:['1000以内' ,
				'2000以内' ,
				'3000以内' ,
				'5000以内' ,
				'8000以内' ,
				'10000以内',
				'20000以内',
				'30000以内',
				'50000以内'],
				width:'100px',
			},
			{
				title: '星级',
				type: 'autocomplete',
				source:[ '五星','四星','三星'],
				width:'70px',
				align: 'left',
			},
			{
				title: '对内备注',
				type: 'text',
				width:'100px',
			},
			{
				title: '对外备注',
				type: 'text',
				width:'100px',
			},


			//	------------------


			{
				title: 'addType',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//11
			// 	title: 'addr',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'amount',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//15
			// 	title: 'backweight',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {//7
			// 	title: 'bank',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'boxfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//14
			// 	title: 'boxtype',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'bzfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//12
			// 	title: 'catalog',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'classid',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'cldef',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'cldefNum',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'cldefname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//13
			// 	title: 'coindesc',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'cointype',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'corder',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'createTime',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'createUser',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctCaizhi',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctJiyuan',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctPrint',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctValue',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctZhuzb',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//16
			// 	title: 'description',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {//4
			// 	title: 'edition',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {//9
			// 	title: 'extbq',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'extbqs',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'extbqs1',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'extbqs2',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'feeex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'finalfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'gjfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'gm',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'gmname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'grade',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'id',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ids',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'imgList',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'indate',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'intro',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ispack',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'issm',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'jiaji',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'jiajifee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {
			// 	title: 'live',
			// 	type: 'text',
			// 	width:'0px',
			// 	height:'0px',
			// 	readOnly:true,
			// },
			{
				title: 'marker',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'mianzhi',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {
			// 	title: 'name',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {
			// 	title: 'name2',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'name3',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'nameex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//5
			// 	title: 'niandai',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'nickname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'numbers',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {
			// 	title: 'nummber',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'pic',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'qtfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'qxshow',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'rank',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//10
			// 	title: 'result',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'rname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'rnameex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'score',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreDesc',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreName',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreNum',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreshow',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//8
			// 	title: 'scoreshow2',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'sendnum',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'shape',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'shuizhong',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'size',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'smtime',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'status',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'subclassid',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'thirdclassid',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'thumb',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'updateTime',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'updateUser',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'weight',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'year',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'yearex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//6
			// 	title: 'zbnum',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'zk',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},

		],
		rowResize: true,
		columnDrag: true,
		allowInsertColumn:false,//插入列
		allowManualInsertColumn:false,
		allowManualInsertRow:false,
		allowManualInsertRow:false,
		tableOverflowResizable:true,
		tableOverflow:false,//有滚轮
		// tableHeight:'100%',
		// tableWidth:"100%",
		autoCasting:false,//
		editorFormulas:false,
		loadingSpin:true,

		autoIncrement:false,//自增

		//parseFormulas	Enable execution of formulas inside the table
		//autoIncrement	Auto increment actions when using the dragging corner
		//autoCastings	Convert strings into numbers when is possible


		// autoIncrement：
		onpaste:onpa,
		onbeforepaste:onbefor,
		oneditionstart:onediti,
				onafterchanges:changed1,
				onchange: changed,
				onbeforechange: beforeChange,
				oninsertrow: insertedRow,
				oninsertcolumn: insertedColumn,
				ondeleterow: deletedRow,
				ondeletecolumn: deletedColumn,
				onselection: selectionActive,
				onsort: sort,
				onbeforesort:beforesort,
				onresizerow: resizeRow,
				onresizecolumn: resizeColumn,
				onmoverow: moveRow,
				onmovecolumn: moveColumn,
				onload: loaded,
				onblur: blur,
				onfocus: focus,
		        license: 'MWEzMTE4MGFkNWY5YzQzNjE4NjZiNmE1NThhM2M0Yjc1NmUyNGM2N2YzZjU2NDQ5ZjM1MGFiYWNmOTFkNTkwODFiYmYwNDE1YjhhM2ViNGUyMzM2YjYzY2Q4NTcyMWE4MGQ4YjVjNjI2NWY4NWYyMTBjMWU5M2ZmNTU4OGI1MDQsZXlKdVlXMWxJam9pY0dGMWJDNW9iMlJsYkNJc0ltUmhkR1VpT2pFMk5UZzVOakk0TURBc0ltUnZiV0ZwYmlJNld5SnFjM0J5WldGa2MyaGxaWFF1WTI5dElpd2lZM05pTG1Gd2NDSXNJbXB6YUdWc2JDNXVaWFFpTENKc2IyTmhiR2h2YzNRaVhTd2ljR3hoYmlJNklqSWlMQ0p6WTI5d1pTSTZXeUoyTnlJc0luWTRJaXdpY0dGeWMyVnlJaXdpYzJobFpYUnpJaXdpWm05eWJYTWlMQ0p5Wlc1a1pYSWlMQ0ptYjNKdGRXeGhJbDE5',



	});

	function updateTab(y1Tab) {
		let value01 = $("#value01").val(); // x
		let value02 = $("#value02").val(); // 开始 y
		let value03 = $("#value03").val(); // 末尾 y
		let value1 = $("#value1").val(); // 末尾 y 用户自选
		value1 = Number(value1) + Number(y1Tab) - 1;
		if (value1 !== "") {
			value1 = value1 > table1.rows.length ? table1.rows.length : value1;
		} else {
			value1 = value03;
			value1 = value1 > table1.rows.length ? table1.rows.length : value1;
		}
		let value2 = $("#value2").val(); // 固定文本

		// Extract the numeric part from value2
		let reg = /\d+/g;
		let result = value2.match(reg);

		// Define a regex to remove leading zeros
		let reg1 = new RegExp("([0]*)([1-9]+[0-9]+)", "g");

		let num = ""; // Prepare to handle leading zeros
		if (result !== null && result.length > 0) {
			// Remove leading zeros
			let result1 = result[0].replace(reg1, "$2");
			let result1Length = result1.length;
			for (let i = 0; i < result[0].length - result1Length; i++) {
				num += "0";
			}
			console.log(num);
		} else {
			console.error("没有匹配到任何内容");
		}

		let value3 = $("#value3").val(); // 自增位置
		if (value3 === "") {
			value3 = 0;
		}

	/*	if (result != null) {
			value3 = result[0];
			value2 = value2.replace(value3, '');
		}*/

		let prefix = ""  // "HY"
		let number = ""  // "99987378"
		let suffix = ""  // "（豹子头）"
			// 使用正则表达式捕获各部分
		let regs = /^(.*?)(\d+)(.*)$/;
		let matches = value2.match(regs);

		if (matches) {
		prefix = matches[1];   // "HY"
		number = matches[2];   // "99987378"
		suffix = matches[3];   // "（豹子头）"
		}



		if (value01 > 0 && value02 > 0 && value03 > 0 && value1 > 0) {
			let numberLength = result[0].length; // 假设 result[0] 是从 value2 中提取的数字部分

			for (let i = value02 - 1; i < value1; i++) {
				let newNumber = String(parseInt(number, 10) + 1).padStart(numberLength, '0');
				let newValue = prefix + newNumber + suffix;
				table1.setValueFromCoords((value01 - 1), i, newValue);
				number = newNumber; // 更新 number 以供下一次迭代使用
			}
		} else {
			alert("输入索引异常");
		}
	}




	function updateTab1(y1Tab){


		let value01=$("#value01").val();//x
		let value02=$("#value02").val();//开始y
		let value03=$("#value03").val();//末尾y
		let value1=$("#tab2Val1").val();//末尾y用户自选
		value1 = Number(value1)+Number(y1Tab)-1;
		if(value1 != ""){
			value1=value1>table1.rows.length?table1.rows.length:value1;
		}else {
			value1=value03;
			value1=value1>table1.rows.length?table1.rows.length:value1;
		}
		let value2=$("#value2").val();//固定文本
		let reg = /\d+\.*\d*$/g;

		let result = value2.match(reg);



		let value3=$("#value3").val();//自增位置
		if(value3==""){
			value3=0;
		}

		if(result!=null){
			value3=result[0];
		}


		if(value01>0&&value02>0&&(value03>0)&&value1>0){

			for (let i = value02-1; i < value1; i++) {

					table1.setValueFromCoords((value01 - 1), i, value2);

			}

		}else {

			alert("输入索引异常");

		}





	};







</script>


<script>
	$(function (){

		let url = "../pjclcoin/queryAll";
		Ajax.request({
			url: url,
			type: "POST",
			contentType: "application/json",
			successCallback: function (r) {


				if(r.list!=null){
					//console.log(r.list);

					$.each(r.list, function(i, item){

							let data1T =[item.nummber,
								item.name,
								item.name2,
								item.live,
								item.edition,
								//editionPack(item.edition),
								item.niandai,
								item.zbnum,
								item.bank,
								item.scoreshow2,
								extbqPack(item.extbq),
								resultPack(item.result),
								item.addr,
								item.catalog,
								item.coindesc,
								boxtypePack(item.boxtype),
								item.backweight,
								item.description];//
							data1T.push(item.addType);
							data1T.push(item.amount);
							data1T.push(item.boxfee);
							data1T.push(item.bzfee);
							data1T.push(item.classid);
							data1T.push(item.cldef);
							data1T.push(item.cldefNum);
							data1T.push(item.cldefname);
							data1T.push(item.cointype);
							data1T.push(item.corder);
							data1T.push(item.createTime);
							data1T.push(item.createUser);
							data1T.push(item.ctCaizhi);
							data1T.push(item.ctJiyuan);
							data1T.push(item.ctPrint);
							data1T.push(item.ctValue);
							data1T.push(item.ctZhuzb);
							data1T.push(item.extbqs);
							data1T.push(item.extbqs1);
							data1T.push(item.extbqs2);
							data1T.push(item.feeex);
							data1T.push(item.finalfee);
							data1T.push(item.gjfee);
							data1T.push(item.gm);
							data1T.push(item.gmname);
							data1T.push(item.grade);
							data1T.push(item.id);
							data1T.push(item.ids);
							data1T.push(item.imgList);
							data1T.push(item.indate);
							data1T.push(item.intro);
							data1T.push(item.ispack);
							data1T.push(item.issm);
							data1T.push(item.jiaji);
							data1T.push(item.jiajifee);

							data1T.push(item.marker);
							data1T.push(item.mianzhi);
							data1T.push(item.name3);
							data1T.push(item.nameex);
							data1T.push(item.nickname);
							data1T.push(item.numbers);
							data1T.push(item.pic);
							data1T.push(item.qtfee);
							data1T.push(item.qxshow);
							data1T.push(item.rank);
							data1T.push(item.rname);
							data1T.push(item.rnameex);
							data1T.push(item.score);
							data1T.push(item.scoreDesc);
							data1T.push(item.scoreName);
							data1T.push(item.scoreNum);
							data1T.push(item.scoreex);
							data1T.push(item.scoreshow);
							data1T.push(item.sendnum);
							data1T.push(item.shape);
							data1T.push(item.shuizhong);
							data1T.push(item.size);
							data1T.push(item.smtime);
							data1T.push(item.status);
							data1T.push(item.subclassid);
							data1T.push(item.thirdclassid);
							data1T.push(item.thumb);
							data1T.push(item.updateTime);
							data1T.push(item.updateUser);
							data1T.push(item.weight);
							data1T.push(item.year);
							data1T.push(item.yearex);
							data1T.push(item.zk);
							vm.projects5Ve.push(data1T);

						    vm.projects4Ve1.push(item);





					});



					if (vm.projects5Ve.length>1){
						vm.projects5Ve.pop();//删掉最后一行空数据
					}

					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);

					table1.setData(vm.projects5Ve);


				}



			}
		});

	})
</script>





<div class="layui-layer-move"></div></body></html>