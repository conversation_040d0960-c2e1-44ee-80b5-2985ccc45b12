<html><head>
    <meta charset="utf-8">
    <title>彩色标签打印（单个）</title>
    <style media="print">
        @page {
            size: auto;
            margin: 0mm;
        }

    </style>
    <style>
        body {
            padding: 0;
            margin: 0;
        }

        /* 首张div */
        .first {
            width: 19cm;
            height: 3.7cm;
            margin-top: 0;
            border: 1px solid cornflowerblue;
        }

        /* 其他div */
        .other {
            width: 19cm;
            height: 2.5cm;
            border-left: 1px solid grey;
            border-bottom: 1px solid grey;
            border-right: 1px solid grey;
        }

        /* 首张div */
        .first1 {
            margin-top: 0;
            width: 19cm;
            height: 4.1cm;
            border: 1px solid cornflowerblue;
        }

        /* 首张div */
        .firstJb {
            width: 19cm;
            height: 4.3cm;
            border: 1px solid cornflowerblue;
        }

        .first1mini {
            top: 4.1cm;
            width: 19cm;
            border: 1px solid cornflowerblue;
        }


        /* 其他div */
        .other1 {
            width: 19cm;
            height: 2.6cm;
            border-left: 1px solid grey;
            border-bottom: 1px solid grey;
            border-right: 1px solid grey;
        }

        /* 其他div */
        .otherHN {
            width: 19cm;
            height: 2.6cm;
            border-left: 1px solid grey;
            border-bottom: 1px solid grey;
            border-right: 1px solid grey;
        }


        /* 图片位置框 */
        .imgBoxA {
            position: relative;
            top: 62px;
            left: 350px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxB {
            position: relative;
            top: 15px;
            left: 350px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBHn {
            position: relative;
            top: 8px;
            left: 390px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1HN {
            position: relative;
            top: 77px;
            left: 390px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxAJB {
            position: relative;
            top: 13px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxAJB1 {
            position: relative;
            top: 82px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1 {
            position: relative;
            top: 77px;
            left: 375px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxA1M {
            position: relative;
            top: 77px;
            left: 265px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1M1 {
            position: relative;
            top: 20px;
            left: 265px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxBaoYX {
            position: relative;
            top: 77px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoYX1 {
            position: relative;
            top: 20px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxB1 {
            position: relative;
            top: 15px;
            left: 375px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxBao {
            position: relative;
            top: 5px;
            left: 380px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBao111 {
            position: relative;
            top: 5px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoY {
            position: relative;
            top: 5px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoA {
            position: relative;
            top: 5px;
            left: 380px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            font-weight: normal;
        }

        .imgBoxBaoB {
            position: relative;
            top: 1px;
            left: 285px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            font-weight: normal;
        }


        .picker {
            width: 50px;
            height: 21px;
            border-radius: 4px;
            cursor: pointer;
        }

        .abc {
            /*padding-top: 2px;*/
            float: left;
        }

        #choose {
            position: absolute;
            top: 10px;
            left: 750px;
        }

        #tips {
            position: absolute;
            top: 10px;
            left: 1100px;
        }

        h1 {
            text-align: center;
            line-height: 22px;
            font-family: 青鸟华光简行楷;
        }

        #live, #live2, #live3, #live4, #live5,
        #live6, #live7, #live8, #live9, #live10 {
            display: inline-block;
            width: 60%;
            height: 20px;
            line-height: 1.5;
            padding: 4px 7px;
            font-size: 14px;
            border: 1px solid #dddee1;
            border-radius: 4px;
            background-color: #d1deef;
            background-image: none;
            position: relative;
            cursor: text;
            transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
        }

        /* 鼠标右键 */
        .shade {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0px;
            left: 0px;

        }

        .wrap-ms-right {
            list-style: none;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 9999;
            padding: 5px 0;
            min-width: 80px;
            margin: 0;
            display: none;
            font-family: "微软雅黑";
            font-size: 14px;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, .15);
            box-sizing: border-box;
            border-radius: 4px;
            -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -ms-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -o-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ms-item {
            height: 30px;
            line-height: 30px;
            text-align: center;
            cursor: pointer;
        }

        .ms-item:hover {
            background-color: #343a40;
            color: #FFFFFF;
        }

        .up3 {
            margin-top: 1px;
        }

        .styleTop {
            top: 5px;
        }

        .styleHeight {
            height: 2.5cm;
        }

        .imgBoxA1MAbc {
            position: relative;
            top: 70px;
            left: 260px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1MAbc1 {
            position: relative;
            top: 7px;
            left: 260px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1MAbc2 {
            position: relative;
            margin-top: -3px;
            left: 260px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1Mdcv {
            position: relative;
            left: 260px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .banbie2222 {
            margin-top: 23px;
        }

        .test_top {
            margin-top: 8px;
        }

        .test_topHN {
            margin-top: 40px;

        }

        .ZhongQianMIin6 {
            margin-top: 6px;
        }

        .banbie3333 {
            margin-top: 18px;
        }

        .test_top222 {
            margin-top: 5px;
        }

    </style>
</head>
<body><div class="shade"></div><div class="wrap-ms-right"><li class="ms-item" data-item="0"><i data-item="0"></i>&nbsp; 中乾模板</li><li class="ms-item" data-item="1"><i data-item="1"></i>&nbsp; 中乾模板-新</li><li class="ms-item" data-item="2"><i data-item="2"></i>&nbsp; 纸币中乾-2022（金标大）</li><li class="ms-item" data-item="3"><i data-item="3"></i>&nbsp; 纸币中乾-2022（金标大－去头）</li><li class="ms-item" data-item="4"><i data-item="4"></i>&nbsp; 中乾模板-新mini模板</li><li class="ms-item" data-item="5"><i data-item="5"></i>&nbsp; 中乾模板-新元模板</li><li class="ms-item" data-item="6"><i data-item="6"></i>&nbsp; 宝鑫模板</li><li class="ms-item" data-item="7"><i data-item="7"></i>&nbsp; 宝鑫模板细体</li><li class="ms-item" data-item="8"><i data-item="8"></i>&nbsp; mini模板</li><li class="ms-item" data-item="9"><i data-item="9"></i>&nbsp; 元模板</li><li class="ms-item" data-item="10"><i data-item="10"></i>&nbsp; 中乾模板-粮票模板</li><li class="ms-item" data-item="11"><i data-item="11"></i>&nbsp; 宝鑫模板-粮票模板</li><li class="ms-item" data-item="12"><i data-item="12"></i>&nbsp; 中乾模板-新(湖南)</li><li class="ms-item" data-item="13"><i data-item="13"></i>&nbsp; 下移</li><li class="ms-item" data-item="14"><i data-item="14"></i>&nbsp; 上移</li><li class="ms-item" data-item="15"><i data-item="15"></i>&nbsp; 重来</li><li class="ms-item" data-item="16"><i data-item="16"></i>&nbsp; 打印</li></div>

<!-- 操作栏 -->
<div id="choose">
<!--    <span style="color: Crimson;">[谷歌浏览器下使用]</span>-->
<!--    <br/><br/>-->
    <button onclick="increaseFontSize()">一键增大字体</button>
    <button onclick="decreaseFontSize()">一键减小字体</button>
    <!-- 左右箭头按钮 -->
    <button onclick="moveLeft()" class="arrow-button">←</button>
    <button onclick="moveRight()" class="arrow-button">→</button>
    <br><br>

    <label id="lab1" onclick="allFont(0)"><span>1</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(0)" id="allFont1">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>

</select>
    <input type="text" id="live" placeholder="第一条" style="margin-top: 10px; color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect" onclick="fontFamilySelect('live')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
	    <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(1)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(1)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(0)" id="familyFont0">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(1)">
    <input type="button" value="调节" onclick="changeTop(1)">
    <br>


    <br>

    <label id="lab2" onclick="allFont(1)"><span>2</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(1)" id="allFont2">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live2" placeholder="第二条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect2" onclick="fontFamilySelect('live2')">
    <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos1"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker2" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(2)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(2)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(1)" id="familyFont1">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(2)">
    <input type="button" value="调节" onclick="changeTop(2)">
    <br>
    <br>
    <br>
    <label id="lab3" onclick="allFont(2)"><span>3</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(2)" id="allFont3">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live3" placeholder="第三条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect3" onclick="fontFamilySelect('live3')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos2"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker3" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(3)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(3)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(2)" id="familyFont2">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(3)">
    <input type="button" value="调节" onclick="changeTop(3)">

    <br><br>
    <br>

    <label id="lab4" onclick="allFont(3)"><span>4</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(3)" id="allFont4">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live4" placeholder="第四条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect4" onclick="fontFamilySelect('live4')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos3"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker4" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(4)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(4)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(3)" id="familyFont3">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(4)">
    <input type="button" value="调节" onclick="changeTop(4)">

    <br> <br> <br>

    <label id="lab5" onclick="allFont(4)"><span>5</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(4)" id="allFont5">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live5" placeholder="第五条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect5" onclick="fontFamilySelect('live5')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos4"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker5" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(5)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(5)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(4)" id="familyFont4">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(5)">
    <input type="button" value="调节" onclick="changeTop(5)">

    <br>
    <br>
    <label id="lab6" onclick="allFont(5)"><span>6</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(5)" id="allFont6">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live6" placeholder="第六条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect6" onclick="fontFamilySelect('live6')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos5"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker6" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(6)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(6)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(5)" id="familyFont5">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(6)">
    <input type="button" value="调节" onclick="changeTop(6)">

    <br><br><br>
    <label id="lab7" onclick="allFont(6)"><span>7</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(6)" id="allFont7">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live7" placeholder="第七条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect7" onclick="fontFamilySelect('live7')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos6"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker7" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(7)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(7)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(6)" id="familyFont6">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(7)">
    <input type="button" value="调节" onclick="changeTop(7)">

    <br>
    <br><br>
    <label id="lab8" onclick="allFont(7)"><span>8</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(7)" id="allFont8">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live8" placeholder="第八条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect8" onclick="fontFamilySelect('live8')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos7"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker8" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(8)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(8)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(7)" id="familyFont7">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(8)">
    <input type="button" value="调节" onclick="changeTop(8)">

    <br>
    <br><br>
    <label id="lab9" onclick="allFont(8)"><span>9</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(8)" id="allFont9">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live9" placeholder="第九条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect9" onclick="fontFamilySelect('live9')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos8"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker9" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(9)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(9)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(8)" id="familyFont8">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(9)">
    <input type="button" value="调节" onclick="changeTop(9)">

    <br>
    <br><br>
    <label id="lab10" onclick="allFont(9)"><span>10</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(9)" id="allFont10">
    <option value=""></option>
    <option value="全选" onclick="allFont(11)">全选</option>
    <option value="反选" onclick="allFont(12)">反选</option>
    <option value="取消" onclick="allFont(13)">取消</option>
</select>
    <input type="text" id="live10" placeholder="第十条" style="color: rgb(0, 0, 0);">
    <select style="height: 23px; width: 50px;" id="fontFamilySelect10" onclick="fontFamilySelect('live10')">
        <option selected="selected">请选择模板</option>
        <option>人物1</option>
        <option>人物2</option>
        <option>人物3</option>
        <option>人物4</option>
        <option>人物5</option>
        <option>人物6</option>
        <option>人物7</option>
        <option>钛白·纤云</option>
        <option>黄金圣龙</option>
        <option>匠心独运</option>
        <option>金玉良缘</option>
        <option>开门红</option>
        <option>好运右绿9</option>
        <option>好运右红9</option>
        <option>好运叠色9</option>
        <option>好运红黄蓝9</option>
        <option>好运三红9</option>
        <option>蓝妖王</option>
        <option>长长久久</option>
        <option>红双喜</option>
        <option>吉祥开门钞绿</option>
        <option>绿钞王</option>
        <option>同号绿钞王</option>
        <option>吉祥开门钞三色</option>
        <option>靓号绿钞王</option>
        <option>幼线体</option>
        <option>盛世向上</option>
        <option>四季有钱</option>
        <option>金龙钞王</option>
        <option>真·金龙钞王</option>
        <option>龙·金龙钞王</option>
        <option>天蓝之冠</option>
        <option>粉红佳墨</option>
        <option>红色光辉</option>
        <option>长码红光</option>
        <option>盛世同胞</option>
        <option>一帆风顺</option>
        <option>九九连心</option>
        <option>豹王</option>
        <option>紫金铃</option>
        <option>中国红</option>
        <option>中国梦</option>
        <option>开门绿钞</option>
        <option>披霞带珠</option>
        <option>龙鳞彩鹤</option>
        <option>一心一意红</option>
        <option>蓝冠之星</option>
        <option>一心一意绿</option>
        <option>二龙腾飞</option>
        <option>浅版桃花</option>
        <option>九九同心</option>
        <option>开门中国梦</option>
        <option>黑珍珠长长久久</option>
        <option>雪龙王长长久久</option>
        <option>开门桃花红</option>
        <option>开门幼线体</option>
        <option>幼线体开门小号</option>
        <option>桃花红开门小号</option>
        <option>爱版黑美人</option>
        <option>开门绿晶灵</option>
        <option>开门绿水晶</option>
        <option>蓝冠五彩星</option>
        <option>龙鳞之星</option>
        <option>大团结</option>
        <option>粉红翡翠</option>
        <option>青绿荧光美翠</option>
        <option>帆风顺</option>
        <option>蛇年纪念钞</option>
    </select>
    <div id="demos9"><section style="display: none;"><div class="section-demo"><div class="friendSearchContainer"><input placeholder="输入文本自动检索，上下键选取，回车选中，可点选" class="smartInput-input smartInput"> <!----> <ul class="friendSearchList" style="display: none;"><li class="">霸王花
                </li><li class="">霸王云
                </li><li class="">白钻
                </li><li class="">背红
                </li><li class="">背金沙
                </li><li class="">背绿沙
                </li><li class="">背绿星星黄金甲
                </li><li class="">背祥云
                </li><li class="">苍松翠鹤
                </li><li class="">穿越时空
                </li><li class="">大双边
                </li><li class="">蝶中彩
                </li><li class="">东方红
                </li><li class="">枫叶红
                </li><li class="">高密丝
                </li><li class="">红光蓝鹤
                </li><li class="">红金龙
                </li><li class="">红口5
                </li><li class="">红麒麟
                </li><li class="">红太阳
                </li><li class="">红霞鹤影
                </li><li class="">红钻之光
                </li><li class="">黄金背红
                </li><li class="">黄金甲
                </li><li class="">黄金甲纤云
                </li><li class="">黄牡丹
                </li><li class="">姐妹花
                </li><li class="">金杯桃花红
                </li><li class="">金粉桃花红
                </li><li class="">金观音
                </li><li class="">金光国徽
                </li><li class="">金光神鹰
                </li><li class="">金光星辉
                </li><li class="">金龙王
                </li><li class="">金满堂
                </li><li class="">金满堂·背红
                </li><li class="">金满堂·黄金甲
                </li><li class="">金满堂·金牡丹
                </li><li class="">金牡丹（只标注DQ）
                </li><li class="">金网鹤王
                </li><li class="">金星绿波
                </li><li class="">开门红
                </li><li class="">开门红纤云
                </li><li class="">蓝凤朝阳
                </li><li class="">蓝天瑞云
                </li><li class="">两边荧光
                </li><li class="">两边荧光
                </li><li class="">流浪地球（只标注强荧光）
                </li><li class="">绿翡翠
                </li><li class="">绿美人
                </li><li class="">绿牡丹
                </li><li class="">绿幽灵
                </li><li class="">绿钻
                </li><li class="">绿钻关门冠
                </li><li class="">绿钻首发冠
                </li><li class="">满版开门红
                </li><li class="">满版中国红
                </li><li class="">满堂彩
                </li><li class="">满堂红(只标注PA)
                </li><li class="">满天星桃花红
                </li><li class="">青绿美翠
                </li><li class="">青天白日背祥
                </li><li class="">青天白日青丝
                </li><li class="">青天白日正祥
                </li><li class="">清荷绿
                </li><li class="">日月双蝶
                </li><li class="">三色彩蝶
                </li><li class="">双边金牡丹（只标注DQ）
                </li><li class="">太白金星
                </li><li class="">钛白纤云
                </li><li class="">天地绿
                </li><li class="">万紫千红
                </li><li class="">五彩苍松
                </li><li class="">五彩金花
                </li><li class="">五星光辉
                </li><li class="">纤丝
                </li><li class="">纤云.红光蓝鹤
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云
                </li><li class="">小纤云.苍松翠鹤
                </li><li class="">幸运树
                </li><li class="">燕子桃花红
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">荧光版
                </li><li class="">右单边荧光
                </li><li class="">宇宙之眼
                </li><li class="">浴火凤凰
                </li><li class="">正红背绿
                </li><li class="">正祥云
                </li><li class="">中国红
                </li><li class="">中国龙
                </li><li class="">中国梦
                </li><li class="">间荧光
                </li><li class="">左单边荧光
                </li></ul> <div class="friendSearchModal" style="display: none;"></div></div></div></section></div>
    <div class="abc">
        <div class="picker" id="color-picker10" style="background-color: rgb(0, 0, 0);"></div>
    </div>
    <input type="button" value="小" onclick="minFont(10)">
    <input type="button" value="B" onclick="blockFont()">
    <input type="button" value="大" onclick="maxFont(10)">
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(9)" id="familyFont9">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="Aa剑豪体">Aa剑豪体</option>
        <option value="云峰飞云体">云峰飞云体</option>
        <option value="仓耳周珂正大榜书">仓耳周珂正大榜书</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(10)">
    <input type="button" value="调节" onclick="changeTop(10)">
</div>


<div id="tips">
    <p>
        <label style="color:crimson;font-size: 18px;">调</label>
        <label style="color: green;font-size: 18px;">色</label>
        <label style="color: mediumpurple;font-size: 18px;">表</label>
        Ctrl+F 可搜索
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(255,0,0);">1.大红色</span>
        （纵二横三）：{
        <br>
        <code>&lt;p style='font-size: 12px;'&gt;&lt;/p&gt;</code>
        <br>
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;补号，幼线体，数字冠，大王冠，首发冠，平水，凸版，渡水，中水，小圆水，
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;大圆水，爱情号，长号，宽水红，爱情号，豹子号，老虎号，中国梦，红二平，
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;生日快乐，爱版，窄水红，红光蓝鹤，
            <br>
            &nbsp;
            }
        </a>
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(152,0,0);">2.深红色</span>
        （纵二横二）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;浴火凤凰
            }
        </a>
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: black;">3.黑色</span>
        （纵一)：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            满堂彩，炭黑，深版
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(0,255,0);">4.绿色</span>
        （纵三）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            绿幽灵，绿钻
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(0,255,0);">5.背绿</span>
        （#95db95）
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(185,215,168);">6.深青色</span>
        （纵七）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            青绿美翠，五彩苍松，苍松翠鹤
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #0096db;">7.浅蓝色</span>
        （#95db95）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            玉钩国
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(0,255,255);">8.天蓝色</span>
        （纵三横二）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            天蓝，蓝凤朝阳
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(74,134,232);">9.蓝色</span>
        （纵三横三）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            中密丝
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(0,0,255);">10.深蓝色</span>
        （纵三横四）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            高密丝
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(204,204,204);">11.水墨色</span>
        （纵一横四）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            古币水印，古币墨水印，海鸥水印
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #f05799;">12.深粉色</span>
        （#f05799）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            满天星桃花红
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #f27e7e;">13.香槟粉</span>
        （#f27e7e）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            金杯桃花红
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(255,153,0);">14.橙色</span>
        （纵二横四）：{
        <a>
            <br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            金牡丹，红金龙，金龙王，金光蓝鹤
            <br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #e68e09;">15.金星绿波</span>
        （#e68e09）
    </p>
</div>

<!-- 首张 -->
<div class="first first1" id="switchGrade" onclick="allFont(0)">
    <div class="imgBoxA" id="switchImgBox1">
        <h1 class="applyFontSize" id="banbie" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>

<!-- 2 -->
<div class="other" onclick="allFont(1)">
    <div class="imgBoxB" id="switchImgBox2">
        <h1 class="applyFontSize" id="banbie2" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(2)">
    <div class="imgBoxB" id="switchImgBox3">
        <h1 class="applyFontSize" id="banbie3" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(3)">
    <div class="imgBoxB" id="switchImgBox4">
        <h1 class="applyFontSize" id="banbie4" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(4)">
    <div class="imgBoxB" id="switchImgBox5">
        <h1 class="applyFontSize" id="banbie5" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(5)">
    <div class="imgBoxB" id="switchImgBox6">
        <h1 class="applyFontSize" id="banbie6" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(6)">
    <div class="imgBoxB" id="switchImgBox7">
        <h1 class="applyFontSize" id="banbie7" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(7)">
    <div class="imgBoxB" id="switchImgBox8">
        <h1 class="applyFontSize" id="banbie8" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(8)">
    <div class="imgBoxB" id="switchImgBox9">
        <h1 class="applyFontSize" id="banbie9" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>
<div class="other" onclick="allFont(9)">
    <div class="imgBoxB" id="switchImgBox10">
        <h1 class="applyFontSize" id="banbie10" style="color: rgb(0, 0, 0);"></h1>
    </div>
</div>

<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<script src="/platformFramework/js/smile/color.js?"></script>
<script src="/platformFramework/js/smile/mouseRight.min.js" type="text/javascript" charset="utf-8"></script>


<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<script src="/platformFramework/statics/libs/smartInput1.js"></script>
<script src="/platformFramework/statics/libs/input.min2.js" type="text/javascript" charset="UTF-8"></script>
<link rel="stylesheet" href="/platformFramework/statics/css/inputq2.css">
<link rel="stylesheet" href="/platformFramework/statics/css/smartInput2.css">


<script>
    var banbie = document.getElementById("banbie");
    var banbie2 = document.getElementById("banbie2");
    var banbie3 = document.getElementById("banbie3");
    var banbie4 = document.getElementById("banbie4");
    var banbie5 = document.getElementById("banbie5");
    var banbie6 = document.getElementById("banbie6");
    var banbie7 = document.getElementById("banbie7");
    var banbie8 = document.getElementById("banbie8");
    var banbie9 = document.getElementById("banbie9");
    var banbie10 = document.getElementById("banbie10");

    let obj = document.getElementById("live");
    let obj2 = document.getElementById("live2");
    let obj3 = document.getElementById("live3");
    let obj4 = document.getElementById("live4");
    let obj5 = document.getElementById("live5");
    let obj6 = document.getElementById("live6");
    let obj7 = document.getElementById("live7");
    let obj8 = document.getElementById("live8");
    let obj9 = document.getElementById("live9");
    let obj10 = document.getElementById("live10");

    let a = Colorpicker.create({
        el: "color-picker",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj.style.color = hex;
            banbie.style.color = hex;

            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }

        }
    });


    let b = Colorpicker.create({
        el: "color-picker2",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj2.style.color = hex;
            banbie2.style.color = hex;

            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let c = Colorpicker.create({
        el: "color-picker3",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj3.style.color = hex;
            banbie3.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let d = Colorpicker.create({
        el: "color-picker4",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj4.style.color = hex;
            banbie4.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let e = Colorpicker.create({
        el: "color-picker5",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj5.style.color = hex;
            banbie5.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let f = Colorpicker.create({
        el: "color-picker6",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj6.style.color = hex;
            banbie6.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let g = Colorpicker.create({
        el: "color-picker7",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj7.style.color = hex;
            banbie7.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let h = Colorpicker.create({
        el: "color-picker8",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj8.style.color = hex;
            banbie8.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let i = Colorpicker.create({
        el: "color-picker9",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj9.style.color = hex;
            banbie9.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let j = Colorpicker.create({
        el: "color-picker10",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj10.style.color = hex;
            banbie10.style.color = hex;
            let arr = [];
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    /**
     * 监听输入框
     */
    $("#live").bind("input propertychange", function () {
        let live = $("#live").val();
        if (live.length <= 4) {
            banbie.innerHTML = live;
            banbie.style.fontSize = '40px';
            banbie.style.paddingTop = '5px';
        }
        if (live.length >= 5) {
            banbie.innerHTML = live;
            banbie.style.fontSize = '32px';
            banbie.style.paddingTop = '8px';
        }
        if (live.length >= 6) {
            banbie.innerHTML = live;
            banbie.style.fontSize = '27px';
            banbie.style.paddingTop = '10px';
        }
    });

    $("#live2").bind("input propertychange", function () {
        let live2 = $("#live2").val();
        if (live2.length <= 4) {
            banbie2.innerHTML = live2;
            banbie2.style.fontSize = '40px';
            banbie2.style.paddingTop = '5px';
        }
        if (live2.length >= 5) {
            banbie2.innerHTML = live2;
            banbie2.style.fontSize = '32px';
            banbie2.style.paddingTop = '8px';
        }
        if (live2.length >= 6) {
            banbie2.innerHTML = live2;
            banbie2.style.fontSize = '27px';
            banbie2.style.paddingTop = '10px';
        }
    });

    $("#live3").bind("input propertychange", function () {
        let live3 = $("#live3").val();
        if (live3.length <= 4) {
            banbie3.innerHTML = live3;
            banbie3.style.fontSize = '40px';
            banbie3.style.paddingTop = '5px';
        }
        if (live3.length >= 5) {
            banbie3.innerHTML = live3;
            banbie3.style.fontSize = '32px';
            banbie3.style.paddingTop = '8px';
        }
        if (live3.length >= 6) {
            banbie3.innerHTML = live3;
            banbie3.style.fontSize = '27px';
            banbie3.style.paddingTop = '10px';
        }
    });


    $("#live4").bind("input propertychange", function () {
        let live4 = $("#live4").val();
        if (live4.length <= 4) {
            banbie4.innerHTML = live4;
            banbie4.style.fontSize = '40px';
            banbie4.style.paddingTop = '5px';
        }
        if (live4.length >= 5) {
            banbie4.innerHTML = live4;
            banbie4.style.fontSize = '32px';
            banbie4.style.paddingTop = '8px';
        }
        if (live4.length >= 6) {
            banbie4.innerHTML = live4;
            banbie4.style.fontSize = '27px';
            banbie4.style.paddingTop = '10px';
        }
    });


    $("#live5").bind("input propertychange", function () {
        let live5 = $("#live5").val();
        if (live5.length <= 4) {
            banbie5.innerHTML = live5;
            banbie5.style.fontSize = '40px';
            banbie5.style.paddingTop = '5px';
        }
        if (live5.length >= 5) {
            banbie5.innerHTML = live5;
            banbie5.style.fontSize = '32px';
            banbie5.style.paddingTop = '8px';
        }
        if (live5.length >= 6) {
            banbie5.innerHTML = live5;
            banbie5.style.fontSize = '27px';
            banbie5.style.paddingTop = '10px';
        }
    });


    $("#live6").bind("input propertychange", function () {
        let live6 = $("#live6").val();
        if (live6.length <= 4) {
            banbie6.innerHTML = live6;
            banbie6.style.fontSize = '40px';
            banbie6.style.paddingTop = '5px';
        }
        if (live6.length >= 5) {
            banbie6.innerHTML = live6;
            banbie6.style.fontSize = '32px';
            banbie6.style.paddingTop = '8px';
        }
        if (live6.length >= 6) {
            banbie6.innerHTML = live6;
            banbie6.style.fontSize = '27px';
            banbie6.style.paddingTop = '10px';
        }
    });


    $("#live7").bind("input propertychange", function () {
        let live7 = $("#live7").val();
        if (live7.length <= 4) {
            banbie7.innerHTML = live7;
            banbie7.style.fontSize = '40px';
            banbie7.style.paddingTop = '5px';
        }
        if (live7.length >= 5) {
            banbie7.innerHTML = live7;
            banbie7.style.fontSize = '32px';
            banbie7.style.paddingTop = '8px';
        }
        if (live7.length >= 6) {
            banbie7.innerHTML = live7;
            banbie7.style.fontSize = '27px';
            banbie7.style.paddingTop = '10px';
        }
    });


    $("#live8").bind("input propertychange", function () {
        let live8 = $("#live8").val();
        if (live8.length <= 4) {
            banbie8.innerHTML = live8;
            banbie8.style.fontSize = '40px';
            banbie8.style.paddingTop = '5px';
        }
        if (live8.length >= 5) {
            banbie8.innerHTML = live8;
            banbie8.style.fontSize = '32px';
            banbie8.style.paddingTop = '8px';
        }
        if (live8.length >= 6) {
            banbie8.innerHTML = live8;
            banbie8.style.fontSize = '27px';
            banbie8.style.paddingTop = '10px';
        }
    });

    $("#live9").bind("input propertychange", function () {
        let live9 = $("#live9").val();
        if (live9.length <= 4) {
            banbie9.innerHTML = live9;
            banbie9.style.fontSize = '40px';
            banbie9.style.paddingTop = '5px';
        }
        if (live9.length >= 5) {
            banbie9.innerHTML = live9;
            banbie9.style.fontSize = '32px';
            banbie9.style.paddingTop = '8px';
        }
        if (live9.length >= 6) {
            banbie9.innerHTML = live9;
            banbie9.style.fontSize = '27px';
            banbie9.style.paddingTop = '10px';
        }
    });


    $("#live10").bind("input propertychange", function () {
        let live10 = $("#live10").val();
        if (live10.length <= 4) {
            banbie10.innerHTML = live10;
            banbie10.style.fontSize = '40px';
            banbie10.style.paddingTop = '5px';
        }
        if (live10.length >= 5) {
            banbie10.innerHTML = live10;
            banbie10.style.fontSize = '32px';
            banbie10.style.paddingTop = '8px';
        }
        if (live10.length >= 6) {
            banbie10.innerHTML = live10;
            banbie10.style.fontSize = '27px';
            banbie10.style.paddingTop = '10px';
        }
    });

    function toPrint() {
        $("#print-btn").css("display", "none");
        $("#choose").css("display", "none")
        $(".first").css({
            border: "none"
        });
        $(".other").css({
            border: "none"
        });
        $(".imgBoxA").css({
            border: "1px solid transparent"
        });
        $(".imgBoxB").css({
            border: "1px solid transparent"
        });
        window.print();
    }


    $('body').mouseRight({
        menu: [
            {
                itemName: "中乾模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "32px");
                    $(".other1").attr("class", "other");
                    $("#switchGrade").attr("class", "first");
                    $("#switchImgBox1").attr("class", "imgBoxA");
                    $("#switchImgBox2").attr("class", "imgBoxB");
                    $("#switchImgBox3").attr("class", "imgBoxB");
                    $("#switchImgBox4").attr("class", "imgBoxB");
                    $("#switchImgBox5").attr("class", "imgBoxB");
                    $("#switchImgBox6").attr("class", "imgBoxB");
                    $("#switchImgBox7").attr("class", "imgBoxB");
                    $("#switchImgBox8").attr("class", "imgBoxB");
                    $("#switchImgBox9").attr("class", "imgBoxB");
                    $("#switchImgBox10").attr("class", "imgBoxB");
                    blockFont();
                    var len = $("#choose br").length;
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_top");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("fontSize", '32px');
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }
                }
            },
            {
                itemName: "中乾模板-新",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "32px");
                    $("#switchGrade").attr("class", "first1");
                    $(".other").attr("class", "other1");
                    $("#switchImgBox1").attr("class", "imgBoxA1");
                    $("#switchImgBox2").attr("class", "imgBoxB1");
                    $("#switchImgBox3").attr("class", "imgBoxB1");
                    $("#switchImgBox4").attr("class", "imgBoxB1");
                    $("#switchImgBox5").attr("class", "imgBoxB1");
                    $("#switchImgBox6").attr("class", "imgBoxB1");
                    $("#switchImgBox7").attr("class", "imgBoxB1");
                    $("#switchImgBox8").attr("class", "imgBoxB1");
                    $("#switchImgBox9").attr("class", "imgBoxB1");
                    $("#switchImgBox10").attr("class", "imgBoxB1");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_top");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("fontSize", '32px');
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }
                }
            },
            {
                itemName: "纸币中乾-2022（金标大）",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "32px");
                    $("#switchGrade").attr("class", "firstJb");
                    $(".other").attr("class", "other1");
                    $("#switchImgBox1").attr("class", "imgBoxAJB1");
                    $("#switchImgBox2").attr("class", "imgBoxAJB");
                    $("#switchImgBox3").attr("class", "imgBoxAJB");
                    $("#switchImgBox4").attr("class", "imgBoxAJB");
                    $("#switchImgBox5").attr("class", "imgBoxAJB");
                    $("#switchImgBox6").attr("class", "imgBoxAJB");
                    $("#switchImgBox7").attr("class", "imgBoxAJB");
                    $("#switchImgBox8").attr("class", "imgBoxAJB");
                    $("#switchImgBox9").attr("class", "imgBoxAJB");
                    $("#switchImgBox10").attr("class", "imgBoxAJB");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_top");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("fontSize", '32px');
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }
                    var other1 = document.getElementsByClassName("other1");
                    for (var i = 0; i < other1.length; i++) {
                        other1[i].style.height = "2.6cm"
                    }


                }
            },
            {
                itemName: "纸币中乾-2022（金标大－去头）",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBao111");
                    $("#switchImgBox2").attr("class", "imgBoxAJB");
                    $("#switchImgBox3").attr("class", "imgBoxAJB");
                    $("#switchImgBox4").attr("class", "imgBoxAJB");
                    $("#switchImgBox5").attr("class", "imgBoxAJB");
                    $("#switchImgBox6").attr("class", "imgBoxAJB");
                    $("#switchImgBox7").attr("class", "imgBoxAJB");
                    $("#switchImgBox8").attr("class", "imgBoxAJB");
                    $("#switchImgBox9").attr("class", "imgBoxAJB");
                    $("#switchImgBox10").attr("class", "imgBoxAJB");


                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_top");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("fontSize", '32px');
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }
                    blockFont();
                    var other1 = document.getElementsByClassName("other");
                    for (var i = 0; i < other1.length; i++) {
                        other1[i].style.height = "2.5cm"
                    }
                }


            },
            {
                itemName: "中乾模板-新mini模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("#switchGrade").attr("class", "first1");
                    $("h1").css("font-size", "19px");
                    $(".other").attr("class", "other1");
                    $("#switchImgBox1").attr("class", "imgBoxA1M");
                    $("#switchImgBox2").attr("class", "imgBoxA1M1");
                    $("#switchImgBox3").attr("class", "imgBoxA1M1");
                    $("#switchImgBox4").attr("class", "imgBoxA1M1");
                    $("#switchImgBox5").attr("class", "imgBoxA1M1");
                    $("#switchImgBox6").attr("class", "imgBoxA1M1");
                    $("#switchImgBox7").attr("class", "imgBoxA1M1");
                    $("#switchImgBox8").attr("class", "imgBoxA1M1");
                    $("#switchImgBox9").attr("class", "imgBoxA1M1");
                    $("#switchImgBox10").attr("class", "imgBoxA1M1");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '19px');
                                $(templateId).attr("class", "ZhongQianMIin6");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '19px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "ZhongQianMIin6");
                                $(templateId).css("fontSize", '19px');
                            } else {
                                $(templateId).css("fontSize", '19px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }
                }
            },

            {
                itemName: "中乾模板-新元模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("#switchGrade").attr("class", "first1");
                    $("h1").css("font-size", "32px");
                    $(".other").attr("class", "other1");
                    $("#switchImgBox1").attr("class", "imgBoxBaoYX");
                    $("#switchImgBox2").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox3").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox4").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox5").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox6").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox7").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox8").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox9").attr("class", "imgBoxBaoYX1");
                    $("#switchImgBox10").attr("class", "imgBoxBaoYX1");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "ZhongQianMIin6");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "ZhongQianMIin6");
                                $(templateId).css("fontSize", '32px');
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }

                }
            },

            {
                itemName: "宝鑫模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "32px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBao");
                    $("#switchImgBox2").attr("class", "imgBoxBao");
                    $("#switchImgBox3").attr("class", "imgBoxBao");
                    $("#switchImgBox4").attr("class", "imgBoxBao");
                    $("#switchImgBox5").attr("class", "imgBoxBao");
                    $("#switchImgBox6").attr("class", "imgBoxBao");
                    $("#switchImgBox7").attr("class", "imgBoxBao");
                    $("#switchImgBox8").attr("class", "imgBoxBao");
                    $("#switchImgBox9").attr("class", "imgBoxBao");
                    $("#switchImgBox10").attr("class", "imgBoxBao");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_top");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("fontSize", '32px');
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }

                }
            },
            {
                itemName: "宝鑫模板细体",
                callback: function () {
                    $("h1").css("font-weight", "normal");
                    $("h1").css("font-family", "宋体");
                    $("h1").css("font-size", "32px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBaoA");
                    $("#switchImgBox2").attr("class", "imgBoxBaoA");
                    $("#switchImgBox3").attr("class", "imgBoxBaoA");
                    $("#switchImgBox4").attr("class", "imgBoxBaoA");
                    $("#switchImgBox5").attr("class", "imgBoxBaoA");
                    $("#switchImgBox6").attr("class", "imgBoxBaoA");
                    $("#switchImgBox7").attr("class", "imgBoxBaoA");
                    $("#switchImgBox8").attr("class", "imgBoxBaoA");
                    $("#switchImgBox9").attr("class", "imgBoxBaoA");
                    $("#switchImgBox10").attr("class", "imgBoxBaoA");
                    blockFont();

                    var len = $("#choose br").length;
                    if (len == 28) {
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                    } else if (len == 30) {
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                    }

                }
            },

            {
                itemName: "mini模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "19px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBaoB");
                    $("#switchImgBox2").attr("class", "imgBoxBaoB");
                    $("#switchImgBox3").attr("class", "imgBoxBaoB");
                    $("#switchImgBox4").attr("class", "imgBoxBaoB");
                    $("#switchImgBox5").attr("class", "imgBoxBaoB");
                    $("#switchImgBox6").attr("class", "imgBoxBaoB");
                    $("#switchImgBox7").attr("class", "imgBoxBaoB");
                    $("#switchImgBox8").attr("class", "imgBoxBaoB");
                    $("#switchImgBox9").attr("class", "imgBoxBaoB");
                    $("#switchImgBox10").attr("class", "imgBoxBaoB");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '19px');
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("paddingTop", "15px");
                                $(templateId).css("marginLeft", "15px");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '19px');
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("paddingTop", "15px");
                                $(templateId).css("marginLeft", "15px");
                                $(templateId).css("fontSize", '19px');
                            } else {
                                $(templateId).css("fontSize", '19px');
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }
                }
            },
            {
                itemName: "元模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "32px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBaoY");
                    $("#switchImgBox2").attr("class", "imgBoxBaoY");
                    $("#switchImgBox3").attr("class", "imgBoxBaoY");
                    $("#switchImgBox4").attr("class", "imgBoxBaoY");
                    $("#switchImgBox5").attr("class", "imgBoxBaoY");
                    $("#switchImgBox6").attr("class", "imgBoxBaoY");
                    $("#switchImgBox7").attr("class", "imgBoxBaoY");
                    $("#switchImgBox8").attr("class", "imgBoxBaoY");
                    $("#switchImgBox9").attr("class", "imgBoxBaoY");
                    $("#switchImgBox10").attr("class", "imgBoxBaoY");
                    blockFont();
                    var len = $("#choose br").length;
                    if (len == 28) {
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                    } else if (len == 30) {
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                        $("#choose br:first").remove();
                    }

                }
            }, {
                itemName: "中乾模板-粮票模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "24px");
                    $("#switchGrade").attr("class", "first1");
                    $(".other").attr("class", "other1");
                    $("#switchImgBox1").attr("class", "imgBoxA1MAbc");
                    $("#switchImgBox2").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox3").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox4").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox5").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox6").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox7").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox8").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox9").attr("class", "imgBoxA1MAbc1");
                    $("#switchImgBox10").attr("class", "imgBoxA1MAbc1");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '20px');
                                $(templateId).attr("class", "test_top");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("fontSize", '20px');
                            } else {
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }


                }
            }, {
                itemName: "宝鑫模板-粮票模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "24px");
                    $("#switchGrade").attr("class", "other");
                    $(".other").attr("class", "other1");
                    $("#switchImgBox1").attr("class", "imgBoxA1Mdcv");
                    $("#switchImgBox2").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox3").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox4").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox5").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox6").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox7").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox8").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox9").attr("class", "imgBoxA1MAbc2");
                    $("#switchImgBox10").attr("class", "imgBoxA1MAbc2");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '20px');
                                $(templateId).attr("class", "test_top222");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).attr("class", "banbie3333");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top222");
                                $(templateId).css("fontSize", '20px');
                            } else {
                                $(templateId).attr("class", "banbie3333");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }


                }
            }, {
                itemName: "中乾模板-新(湖南)",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "32px");
                    $("#switchGrade").attr("class", "first1");
                    $(".other").attr("class", "otherHN");
                    $("#switchImgBox1").attr("class", "imgBoxA1HN");
                    $("#switchImgBox2").attr("class", "imgBoxBHn");
                    $("#switchImgBox3").attr("class", "imgBoxBHn");
                    $("#switchImgBox4").attr("class", "imgBoxBHn");
                    $("#switchImgBox5").attr("class", "imgBoxBHn");
                    $("#switchImgBox6").attr("class", "imgBoxBHn");
                    $("#switchImgBox7").attr("class", "imgBoxBHn");
                    $("#switchImgBox8").attr("class", "imgBoxBHn");
                    $("#switchImgBox9").attr("class", "imgBoxBHn");
                    $("#switchImgBox10").attr("class", "imgBoxBHn");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 10; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_topHN");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_topHN");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_topHN");
                                $(templateId).css("fontSize", '32px');
                            } else {
                                $(templateId).css("fontSize", '32px');
                                $(templateId).attr("class", "test_topHN");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                    }
                }
            },
            {
                itemName: "下移",
                callback: function () {

                    var thisSwitchGrade = $("#switchGrade");//
                    thisSwitchGrade.css("margin-top", "1cm");

                }
            },
            {
                itemName: "上移",
                callback: function () {
                    var thisSwitchGrade = $("#switchGrade");//
                    thisSwitchGrade.css("margin-top", "0cm");

                }
            },


            {
                itemName: "重来",
                callback: function () {
                    $("#print-btn").css("display", "block");
                    $("#choose").css("display", "block");
                    $("#tips").css("display", "block");
                    $(".first").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".first1").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".firstJb").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".other").css({
                        "border-left": '1px solid grey',
                        "border-bottom": '1px solid grey',
                        "border-right": '1px solid grey'
                    });
                    $(".other1").css({
                        "border-left": '1px solid grey',
                        "border-bottom": '1px solid grey',
                        "border-right": '1px solid grey'
                    });
                    $(".otherHN").css({
                        "border-left": '1px solid grey',
                        "border-bottom": '1px solid grey',
                        "border-right": '1px solid grey'
                    });

                    $(".imgBoxA1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1HN").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxB1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBHn").css({
                        border: "1px solid lightseagreen"
                    });

                    $(".imgBoxA").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBao").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoA").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoY").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1M").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1M1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoYX").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoYX1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxAJB1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxAJB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1Mdcv").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1MAbc1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1MAbc2").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1MAbc").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBao111").css({
                        border: "1px solid lightseagreen"
                    });

                }
            },
            {
                itemName: "打印",
                callback: function () {
                    $("#print-btn").css("display", "none");
                    $("#choose").css("display", "none");
                    $("#tips").css("display", "none");
                    $(".first").css({
                        border: "none"
                    });
                    $(".first1").css({
                        border: "none"
                    });
                    $(".firstJb").css({
                        border: "none"
                    });
                    $(".other").css({
                        border: "none"
                    });
                    $(".other1").css({
                        border: "none"
                    });
                    $(".otherHN").css({
                        border: "none"
                    });
                    $(".imgBoxBao").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoA").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBHn").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoY").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1HN").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxB1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1M").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1M1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoYX").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoYX1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxAJB1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxAJB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1Mdcv").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1MAbc1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1MAbc2").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1MAbc").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBao111").css({
                        border: "1px solid transparent"
                    });
                    setTimeout(function () {
                        window.print();
                    }, 500);

                }
            }
        ]
    });

    // 给按钮绑定点击事件 适应7字
    $("#adapt1").click(function (event) {
        let live = $("#live").val();
        banbie.innerHTML = live;
        banbie.style.fontSize = '23px';
        banbie.style.paddingTop = '10px';
    });

    $("#adapt2").click(function (event) {
        let live2 = $("#live2").val();
        banbie2.innerHTML = live2;
        banbie2.style.fontSize = '23px';
        banbie2.style.paddingTop = '10px';
    });

    $("#adapt3").click(function (event) {
        let live3 = $("#live3").val();
        banbie3.innerHTML = live3;
        banbie3.style.fontSize = '23px';
        banbie3.style.paddingTop = '10px';
    });

    $("#adapt4").click(function (event) {
        let live4 = $("#live4").val();
        banbie4.innerHTML = live4;
        banbie4.style.fontSize = '23px';
        banbie4.style.paddingTop = '10px';
    });

    $("#adapt5").click(function (event) {
        let live5 = $("#live5").val();
        banbie5.innerHTML = live5;
        banbie5.style.fontSize = '23px';
        banbie5.style.paddingTop = '10px';
    });
    $("#adapt6").click(function (event) {
        let live6 = $("#live6").val();
        banbie6.innerHTML = live6;
        banbie6.style.fontSize = '23px';
        banbie6.style.paddingTop = '10px';
    });
    $("#adapt7").click(function (event) {
        let live7 = $("#live7").val();
        banbie7.innerHTML = live7;
        banbie7.style.fontSize = '23px';
        banbie7.style.paddingTop = '10px';
    });
    $("#adapt8").click(function (event) {
        let live8 = $("#live8").val();
        banbie8.innerHTML = live8;
        banbie8.style.fontSize = '23px';
        banbie8.style.paddingTop = '10px';
    });
    $("#adapt9").click(function (event) {
        let live9 = $("#live9").val();
        banbie9.innerHTML = live9;
        banbie9.style.fontSize = '23px';
        banbie9.style.paddingTop = '10px';
    });
    $("#adapt10").click(function (event) {
        let live10 = $("#live10").val();
        banbie10.innerHTML = live10;
        banbie10.style.fontSize = '23px';
        banbie10.style.paddingTop = '10px';
    });

    // 给按钮绑定点击事件 适应8字
    $("#adapt1_8").click(function (event) {
        let live = $("#live").val();
        banbie.innerHTML = live;
        banbie.style.fontSize = '20px';
        banbie.style.paddingTop = '14px';
    });
    $("#adapt2_8").click(function (event) {
        let live2 = $("#live2").val();
        banbie2.innerHTML = live2;
        banbie2.style.fontSize = '20px';
        banbie2.style.paddingTop = '14px';
    });

    $("#adapt3_8").click(function (event) {
        let live3 = $("#live3").val();
        banbie3.innerHTML = live3;
        banbie3.style.fontSize = '20px';
        banbie3.style.paddingTop = '14px';
    });

    $("#adapt4_8").click(function (event) {
        let live4 = $("#live4").val();
        banbie4.innerHTML = live4;
        banbie4.style.fontSize = '20px';
        banbie4.style.paddingTop = '14px';
    });

    $("#adapt5_8").click(function (event) {
        let live5 = $("#live5").val();
        banbie5.innerHTML = live5;
        banbie5.style.fontSize = '20px';
        banbie5.style.paddingTop = '14px';
    });
    $("#adapt6_8").click(function (event) {
        let live6 = $("#live6").val();
        banbie6.innerHTML = live6;
        banbie6.style.fontSize = '20px';
        banbie6.style.paddingTop = '14px';
    });
    $("#adapt7_8").click(function (event) {
        let live7 = $("#live7").val();
        banbie7.innerHTML = live7;
        banbie7.style.fontSize = '20px';
        banbie7.style.paddingTop = '14px';
    });
    $("#adapt8_8").click(function (event) {
        let live8 = $("#live8").val();
        banbie8.innerHTML = live8;
        banbie8.style.fontSize = '20px';
        banbie8.style.paddingTop = '14px';
    });
    $("#adapt9_8").click(function (event) {
        let live9 = $("#live9").val();
        banbie9.innerHTML = live9;
        banbie9.style.fontSize = '20px';
        banbie9.style.paddingTop = '14px';
    });
    $("#adapt10_8").click(function (event) {
        let live10 = $("#live10").val();
        banbie10.innerHTML = live10;
        banbie10.style.fontSize = '20px';
        banbie10.style.paddingTop = '14px';
    });


    function minFont(number) {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        // let minFont=$("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok2 = $(".otherHN h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        let thisFont = $("#banbie" + (number == 1 ? "" : number));//
        let minFont = thisFont;

        if (ok != 0) {
            let minFontAll = $(".other h1");//比较是那个模板对应修改参数

            let fontSize = parseInt(minFont.css("font-size").substring(0, (minFont.css("font-size").length - 2))) - 1;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");
        }

        if (ok1 != 0) {
            let minFontAll = $(".other1 h1");//比较是那个模板对应修改参数

            let fontSize = parseInt(minFont.css("font-size").substring(0, (minFont.css("font-size").length - 2))) - 1;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");
        }
        if (ok2 != 0) {
            let minFontAll = $(".otherHN h1");//比较是那个模板对应修改参数

            let fontSize = parseInt(minFont.css("font-size").substring(0, (minFont.css("font-size").length - 2))) - 1;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");
        }


    }

    function maxFont(number) {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        // let maxFont=$("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok2 = $(".otherHN h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        let thisFont = $("#banbie" + (number == 1 ? "" : number));//

        let maxFont = thisFont;

        if (ok != 0) {

            let maxFontAll = $(".other h1");//
            let fontSize = parseInt(maxFont.css("font-size").substring(0, (maxFont.css("font-size").length - 2))) + 1;

            // maxFont.css("font-size",fontSize+"px");
            // maxFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");

        }

        if (ok1 != 0) {

            let maxFontAll = $(".other1 h1");//
            let fontSize = parseInt(maxFont.css("font-size").substring(0, (maxFont.css("font-size").length - 2))) + 1;

            // maxFont.css("font-size",fontSize+"px");
            // maxFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");

        }

        if (ok2 != 0) {

            let maxFontAll = $(".otherHN h1");//
            let fontSize = parseInt(maxFont.css("font-size").substring(0, (maxFont.css("font-size").length - 2))) + 1;

            // maxFont.css("font-size",fontSize+"px");
            // maxFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");

        }


    }

    function blockFont() {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        let blockFont = $("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        if (ok != 0) {

            let blockFontAll = $(".other h1");//
            let weight = blockFont.css("font-weight");
            if ("600" > weight) {
                blockFont.css("font-weight", "700");
                blockFontAll.css("font-weight", "700");
            } else {
                blockFont.css("font-weight", "100");
                blockFontAll.css("font-weight", "100");
            }
        }

        if (ok1 != 0) {

            let blockFontAll = $(".other1 h1");//
            let weight = blockFont.css("font-weight");
            if ("600" > weight) {
                blockFont.css("font-weight", "700");
                blockFontAll.css("font-weight", "700");
            } else {
                blockFont.css("font-weight", "100");
                blockFontAll.css("font-weight", "100");
            }


        }


    }

    function familyFont(number) {//改变位置
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        let familyFont = $("#switchGrade h1");//首个


        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        if (ok != 0) {

            let familyFontAll = $(".other h1");//
            let family = "青鸟华光简行楷";//字体

            family = $("#familyFont" + number).val();//字体

            familyFont.css("font-family", family);
            familyFontAll.css("font-family", family);


        }

        if (ok1 != 0) {
            let familyFontAll = $(".other1 h1");//
            let family = "青鸟华光简行楷";//字体

            family = $("#familyFont" + number).val();//字体

            familyFont.css("font-family", family);
            familyFontAll.css("font-family", family);

        }

    }


    function changeTop(number) {


        var thisFontSize = $("#banbie" + (number == 1 ? "" : number)).css("font-size");

        var nameLen = $("#live" + (number == 1 ? "" : number)).val();
        nameLen = nameLen.length;
        if (nameLen > 12) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "7px");
        } else if (nameLen == 6 && parseInt(thisFontSize) == 28) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");

        } else if (nameLen == 7 && parseInt(thisFontSize) == 24) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");

        } else if (nameLen == 8 && parseInt(thisFontSize) == 21) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");
        } else if (nameLen == 9 && parseInt(thisFontSize) == 18) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");
        } else {
            if (parseInt(thisFontSize) == 32) {
                $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "8px");
            }
        }

    }

    function adaptiveFont(number) {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        let adaptiveFont = $("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok2 = $(".otherHN h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整


        if (ok != 0) {

            let adaptiveFontAll = $(".other h1");//

            let nameLen = $("#live" + (number == 1 ? "" : number)).val();

            let thisFont = $("#banbie" + (number == 1 ? "" : number));//

            nameLen = nameLen.length;
            if (nameLen <= 3) {
                // adaptiveFont.css("font-size","40px");
                // adaptiveFontAll.css("font-size","40px");
                thisFont.css("font-size", "40px");
            } else if (nameLen == 6) {
                // adaptiveFont.css("font-size","32px");
                // adaptiveFontAll.css("font-size","32px");
                thisFont.css("font-size", "28px");
            } else if (nameLen == 7) {
                // adaptiveFont.css("font-size","23px");
                // adaptiveFontAll.css("font-size","23px");
                thisFont.css("font-size", "24px");
            } else if (nameLen == 8) {
                // adaptiveFont.css("font-size","20px");
                // adaptiveFontAll.css("font-size","20px");
                thisFont.css("font-size", "21px");
            } else if (nameLen <= 10) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "18px");
            } else if (nameLen <= 11) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "25px");
            }


        }

        if (ok1 != 0) {

            let adaptiveFontAll = $(".other1 h1");//
            let nameLen = $("#live" + (number == 1 ? "" : number)).val();

            let thisFont = $("#banbie" + (number == 1 ? "" : number));//

            nameLen = nameLen.length;

            if (nameLen <= 3) {
                // adaptiveFont.css("font-size","40px");
                // adaptiveFontAll.css("font-size","40px");
                thisFont.css("font-size", "40px");
            } else if (nameLen == 6) {
                // adaptiveFont.css("font-size","32px");
                // adaptiveFontAll.css("font-size","32px");
                thisFont.css("font-size", "28px");
            } else if (nameLen == 7) {
                // adaptiveFont.css("font-size","23px");
                // adaptiveFontAll.css("font-size","23px");
                thisFont.css("font-size", "24px");
            } else if (nameLen == 8) {
                // adaptiveFont.css("font-size","20px");
                // adaptiveFontAll.css("font-size","20px");
                thisFont.css("font-size", "21px");
            } else if (nameLen <= 10) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "18px");
            } else if (nameLen <= 11) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "25px");
            }

        }
        if (ok2 != 0) {

            let adaptiveFontAll = $(".other1 h1");//
            let nameLen = $("#live" + (number == 1 ? "" : number)).val();

            let thisFont = $("#banbie" + (number == 1 ? "" : number));//

            nameLen = nameLen.length;

            if (nameLen <= 3) {
                // adaptiveFont.css("font-size","40px");
                // adaptiveFontAll.css("font-size","40px");
                thisFont.css("font-size", "40px");
            } else if (nameLen == 6) {
                // adaptiveFont.css("font-size","32px");
                // adaptiveFontAll.css("font-size","32px");
                thisFont.css("font-size", "28px");
            } else if (nameLen == 7) {
                // adaptiveFont.css("font-size","23px");
                // adaptiveFontAll.css("font-size","23px");
                thisFont.css("font-size", "24px");
            } else if (nameLen == 8) {
                // adaptiveFont.css("font-size","20px");
                // adaptiveFontAll.css("font-size","20px");
                thisFont.css("font-size", "21px");
            } else if (nameLen <= 10) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "18px");
            } else if (nameLen <= 11) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "25px");
            }

        }


    }


    function allFont(number) {


        let arr = [];
        for (let k = 1; k < 11; k++) {
            let val = $("#lab" + k + " span").css("border");
            if (val == "1px solid rgb(0, 0, 0)") {
                arr.push(k);
            }
            // console.log(k);
        }
        // console.log(arr);//目前选择的具体位置


        if (number == 11) {//全选
            for (let k = 1; k < 11; k++) {
                selectedElements = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");

            }


        } else if (number == 12) {//返选

            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + (k) + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {//选中
                    let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
                } else {
                    let val = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");
                }

            }

        } else if (number == 13) {//取消
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
            }
        }

        else {//单击
            let val = $("#lab" + (number + 1) + " span").css("border");
            if (val == "1px solid rgb(0, 0, 0)") {//选中
                let val = $("#lab" + (number + 1) + " span").css("border", "0px none rgb(0, 0, 0)");
            } else {
                let val = $("#lab" + (number + 1) + " span").css("border", "1px solid rgb(0, 0, 0)");
            }

        }


    }


    function allFont1(number) {


        let val = $("#allFont" + (number + 1)).val();
        if ("全选" == val) {
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");
            }
        }
        if ("反选" == val) {
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + (k) + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {//选中
                    let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
                } else {
                    let val = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");
                }

            }
        }
        if ("取消" == val) {
            for (let k = 1; k < 11; k++) {
                let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
            }
        }

    }

    function increaseFontSize() {

       $(".imgBoxAJB").css("top","5px");
    }

    function decreaseFontSize() {
        for (let i = 1; i < 11; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");

            let fontSize = parseInt(thisFont.css("font-size").substring(0, (thisFont.css("font-size").length - 2))) - 1;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.cmoveRightss("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");

        }
    }
    let initialMarginRight = 0; // 初始 margin-right 值

    function moveRight() {
        for (let i = 1; i < 11; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");

            if (initialMarginRight === 0) {
                // 记录初始 margin-right 值
                initialMarginRight = parseInt(thisFont.css("margin-right").substring(0, (thisFont.css("margin-right").length - 2)));
            }

            // 累积增加 margin-right 值
            initialMarginRight -= 1;

            thisFont.css("margin-right", initialMarginRight + "px");
        }
    }

    function moveLeft() {
        for (let i = 1; i < 11; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");
            let marginLeft = parseInt(thisFont.css("margin-left").substring(0, (thisFont.css("margin-left").length -
                2))) - 5;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("margin-left", marginLeft + "px");

        }
    }

    function fontFamilySelect(str) {
        var thisValue = "#fontFamilySelect";
        var sssValue = "";
        var zzzValue = "";
        if (str=='live'){
            sssValue =thisValue+' option:selected';
        }
        if (str=='live2'){
            sssValue =thisValue+"2"+" option:selected";
        }
        if (str=='live3'){
            sssValue =thisValue+"3"+" option:selected";
        }
        if (str=='live4'){
            sssValue =thisValue+"4"+" option:selected";
        }
        if (str=='live5'){
            sssValue =thisValue+"5"+" option:selected";
        }
        if (str=='live6'){
            sssValue =thisValue+"6"+" option:selected";
        }
        if (str=='live7'){
            sssValue =thisValue+"7"+" option:selected";
        } if (str=='live8'){
            sssValue =thisValue+"8"+" option:selected";
        }
        if (str=='live9'){
            sssValue =thisValue+"9"+" option:selected";
        }
        if (str=='live10'){
            sssValue =thisValue+"10"+" option:selected";
        }

        zzzValue =  $(sssValue).text();
        if ("请选择模板" === zzzValue) {
            $(".banbie").removeClass("mirrorRotateVertical");
        }
        if ("人物1" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/0010.png' width='320%;' height='80%;' style='margin-left: -135%;margin-top: -15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("人物2" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/0011.png' width='320%;' height='80%;' style='margin-left: -135%;margin-top: -15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("人物3" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/0012.png' width='320%;' height='80%;' style='margin-left: -135%;margin-top: -15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("人物4" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/0013.png' width='320%;' height='80%;' style='margin-left: -135%;margin-top: -15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("人物5" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/0014.png' width='320%;' height='80%;' style='margin-left: -135%;margin-top: -15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("人物6" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/0015.png' width='320%;' height='80%;' style='margin-left: -135%;margin-top: -15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("人物7" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/0016.png' width='320%;' height='80%;' style='margin-left: -135%;margin-top: -15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("钛白·纤云" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/1111.png' width='100%;' height='80%;' style='margin-left: -5%;margin-top: -3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("开门红" === zzzValue) {

            $("#"+str).val("<img src='/platformFramework/img/1112.png' width='85%;' height='80%;' style='margin-left: -5%;margin-top: -25%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }

        if ("好运右绿9" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2201.png' width='70%;' height='80%;' style='margin-left: -5%;margin-top: -1%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("好运右红9" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2202.png' width='70%;' height='80%;' style='margin-left: -5%;margin-top: -1%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("好运叠色9" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2203.png' width='70%;' height='80%;' style='margin-left: -5%;margin-top: -1%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        } if ("好运红黄蓝9" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2204.png' width='70%;' height='80%;' style='margin-left: -5%;margin-top: -1%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("好运三红9" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2205.png' width='70%;' height='80%;' style='margin-left: -5%;margin-top: -1%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }if ("蓝妖王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2206.png' width='55%;' height='80%;' style='margin-left: 10%;margin-top: -5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }if ("长长久久" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2207.png' width='55%;' height='80%;' style='margin-left: -5%;margin-top: -5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("红双喜" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2217.png' width='75%;' height='80%;' style='margin-left: -8%;margin-top: -5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("吉祥开门钞绿" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2209.png' width='100%;' height='80%;' style='margin-left: -5%;margin-top: -10%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("绿钞王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2210.png' width='90%;' height='80%;' style='margin-top: -7%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("同号绿钞王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2211.png' width='95%;' height='80%;' style='margin-top: -8%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("吉祥开门钞三色" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2212.png' width='75%;' height='90%;'style='margin-top: -4.3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("靓号绿钞王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2213.png' width='90%;' height='80%;' style='margin-top: -7%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("幼线体" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2214.png' width='90%;' height='80%;' style='margin-left:20%; margin-top: ;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
         if ("盛世向上" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2215.png' width='85%;' height='90%;' style='margin-top: -7%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("四季有钱" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2216.png' width='85%;' height='90%;'style='margin-left: 15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("金龙钞王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2218.png' width='85%;' height='90%;'style='margin-left: -8%;margin-top: -5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("真·金龙钞王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2219.jpg' width='85%;' height='90%;'style='margin-left: ;margin-top: ;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("龙·金龙钞王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2219.png' width='95%;' height='90%;'style='margin-left: ;margin-top: ;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("天蓝之冠" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2220.png' width='95%;' height='90%;'style='margin-left: ;margin-top: ;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("粉红佳墨" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2221.png' width='100%;' height='90%;'style='margin-left: -22%; margin-top: ;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("红色光辉" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2222.png' width='95%;' height='90%;'style='margin-left: -22%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("长码红光" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2223.png' width='90%;' height='90%;'style='margin-left: ;margin-top: -7%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("黄金圣龙" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2225.png' width='70%;' height='90%;'style='margin-left: 16%;margin-top: -5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("盛世同胞" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2226.png' width='100%;' height='90%;'style='margin-left: 18%; margin-top:2%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("一帆风顺" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2227.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("九九连心" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2228.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("豹王" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2229.png' width='90%;' height='90%;'style='margin-left: 10%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("紫金铃" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2230.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-2%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("中国红" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2231.jpg' width='75%;' height='90%;' style='margin-top: -6%; margin-left: 15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }

        if ("披霞带珠" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2233.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("开门绿钞" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2232.png' width='75%;' height='90%;' style='margin-top: -6%; margin-left: 15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
          if ("中国梦" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2234.png' width='65%;' height='90%;'style='margin-top: -8%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }

        if ("匠心独运" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2235.png' width='130%;' height='90%;'style='margin-top: -60%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("金玉良缘" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2236.png' width='250%;' height='90%;'style='margin-top: -18%; margin-left: -65%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("龙鳞彩鹤" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2240.png' width='90%;' height='90%;'style='margin-left: -10%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("一心一意红" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2237.png' width='85%;' height='90%;'style='margin-left: 10%; margin-top:-8%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("蓝冠之星" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2238.png' width='85%;' height='90%;'style='margin-left: 10%; margin-top:-4%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("一心一意绿" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2239.png' width='85%;' height='90%;'style='margin-left: 10%; margin-top:-8%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("二龙腾飞" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2241.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
         if ("浅版桃花" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2242.png' width='95%;' height='90%;'style='margin-left: ;margin-top: ;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
         if ("九九同心" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2243.png' width='90%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
         if ("开门中国梦" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2244.png' width='75%;' height='90%;' style='margin-top: -5%; margin-left: 15%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("黑珍珠长长久久" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2245.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("雪龙王长长久久" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2246.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("开门桃花红" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2247.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("开门幼线体" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2248.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("幼线体开门小号" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2249.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("桃花红开门小号" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2250.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("爱版黑美人" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2251.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("开门绿晶灵" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2252.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("开门绿水晶" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2253.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
	if ("蓝冠五彩星" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2254.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }if ("龙鳞之星" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2255.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }if ("大团结" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2256.png' width='72%;' height='90%;'style='margin-left: 10%; margin-top:-6%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("粉红翡翠" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2257.png' width='95%;' height='85%;'style='margin-left: 6%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("青绿荧光美翠" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2258.png' width='95%;' height='85%;'style='margin-left: 6%; margin-top:-3%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
         if ("帆风顺" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2259.png' width='95%;' height='90%;'style='margin-left: 10%; margin-top:-5%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
        if ("蛇年纪念钞" === zzzValue) {
            $("#"+str).val("<img src='/platformFramework/img/2260.png' width='72%;' height='90%;'style='margin-left: -15%; margin-top:-6%;'>");
            //<img src="/platformFramework/img/0016.png" width="320%;" height="80%;" style="margin-left: -150%;margin-top: -10%;">
            $("#"+str)[0].dispatchEvent(new Event('input'));
        }
    }
</script><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 142px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 243px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 367px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 490px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 614px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 715px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 838px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 962px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 1085px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div><div style="position: absolute; z-index: 2; display: none; left: 750px; top: 1209px;"><div style="position: fixed; top: 0px; right: 0px; bottom: 0px; left: 0px;"></div>
				<div style="position: inherit;z-index: 100;display: flex;box-shadow: rgba(0, 0, 0, 0.3) 0px 0px 2px, rgba(0, 0, 0, 0.3) 0px 4px 8px;">
					<div style="width:180px;padding:10px;background: #f9f9f9;display: flex;flex-flow: row wrap;align-content: space-around;justify-content: space-around;" class="color-palette">
						<p style="width:20px;height:20px;background:rgb(0, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(67, 67, 67);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(102, 102, 102);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(204, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 217, 217);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(152, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 153, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 0);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 255, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(74, 134, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(0, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(153, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 0, 255);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(230, 184, 175);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(244, 204, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(252, 229, 205);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 242, 204);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 234, 211);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(208, 224, 227);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(201, 218, 248);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(207, 226, 243);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(217, 210, 233);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 209, 220);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(221, 126, 107);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(234, 153, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(249, 203, 156);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(255, 229, 153);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(182, 215, 168);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(162, 196, 201);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(164, 194, 244);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(159, 197, 232);margin:0 5px;border: solid 1px #d0d0d0;"></p><p style="width:20px;height:20px;background:rgb(180, 167, 214);margin:0 5px;border: solid 1px #d0d0d0;"></p>
					</div>
					<div class="colorpicker-pancel" style="background: rgb(255, 255, 255);box-sizing: initial; width: 225px; font-family: Menlo;">
						<div style="width: 100%; padding-bottom: 55%; position: relative; border-radius: 2px 2px 0px 0px; overflow: hidden;">
							<div class="color-pancel" style="position: absolute; inset: 0px; background: rgb(255, 0, 255);">
								<style>
									.saturation-white {background: -webkit-linear-gradient(to right, #fff, rgba(255,255,255,0));background: linear-gradient(to right, #fff, rgba(255,255,255,0));}
									.saturation-black {background: -webkit-linear-gradient(to top, #000, rgba(0,0,0,0));background: linear-gradient(to top, #000, rgba(0,0,0,0));}
								</style>
								<div class="saturation-white" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									<div class="saturation-black" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px;">
									</div>
									<div class="pickerBtn" style="position: absolute; top: 124px; left: 0px; cursor: default;">
										<div style="width: 12px; height: 12px; border-radius: 6px; box-shadow: rgb(255, 255, 255) 0px 0px 0px 1px inset; transform: translate(-6px, -6px);">
										</div>
									</div>
								</div>
							</div>
						</div>
						<div style="padding: 0 16px 20px;">
							<div class="flexbox-fix" style="display: flex;align-items: center;height: 40px;">
								<div style="width: 32px;">
									<div style="width: 16px; height: 16px; border-radius: 8px; position: relative; overflow: hidden;">
										<div class="colorpicker-showColor" style="position: absolute; inset: 0px; border-radius: 8px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 0px 1px inset; background: rgb(0, 0, 0); z-index: 2;"></div>
										<div class="" style="position: absolute; top: 0px; right: 0px; bottom: 0px; left: 0px; background: url(&quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==&quot;) left center;"></div>
									</div>
								</div>
								<div style="-webkit-box-flex: 1; flex: 1 1 0%;"><div style="height: 10px; position: relative;">
									<div style="position: absolute; top: 0px;right: 0px; bottom: 0px; left: 0px;">
										<div class="hue-horizontal" style="padding: 0px 2px; position: relative; height: 100%;">
											<style>
												.hue-horizontal {background: linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to right, #f00 0%, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
												.hue-vertical {background: linear-gradient(to top, #f00 0%, #ff0 17%, #0f0 33%,#0ff 50%, #00f 67%, #f0f 83%, #f00 100%);background: -webkit-linear-gradient(to top, #f00 0%, #ff0 17%,#0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, #f00 100%);}
											</style>
											<div class="colorBar-color-picker" style="position: absolute; left: 0px;">
												<div style="width: 12px; height: 12px; border-radius: 6px; transform: translate(-6px, -1px); background-color: rgb(248, 248, 248); box-shadow: rgba(0, 0, 0, 0.37) 0px 1px 4px 0px;">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="flexbox-fix" style="display: flex;">
							<div class="flexbox-fix colorpicker-inputWrap" style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; margin-left: -6px;">
									
							<div style="padding-left: 6px; width: 100%;">
								<div style="position: relative;">
									<input class="colorpicker-hexInput" value="#ff0000" spellcheck="false" style="font-size: 11px; color: rgb(51, 51, 51); width: 100%; border-radius: 2px; border: none; box-shadow: rgb(218, 218, 218) 0px 0px 0px 1px inset; height: 21px; text-align: center;">
									<span style="text-transform: uppercase; font-size: 11px; line-height: 11px; color: rgb(150, 150, 150); text-align: center; display: block; margin-top: 12px;">hex</span>
								</div>
							</div>
							</div>
							<div class="colorpicker-showModeBtn" style="width: 32px; text-align: right; position: relative;">
								<div style="margin-right: -4px;  cursor: pointer; position: relative;">
									<svg viewBox="0 0 24 24" style="width: 24px; height: 24px; border: 1px solid transparent; border-radius: 5px;"><path fill="#333" d="M12,5.83L15.17,9L16.58,7.59L12,3L7.41,7.59L8.83,9L12,5.83Z"></path><path fill="#333" d="M12,18.17L8.83,15L7.42,16.41L12,21L16.59,16.41L15.17,15Z"></path></svg>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div></div>


</body></html>