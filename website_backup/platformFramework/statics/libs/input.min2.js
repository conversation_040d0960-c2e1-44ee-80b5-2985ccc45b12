const provinceList = ['霸王花','霸王云','白钻','背红','背金沙','背绿沙','背绿星星黄金甲','背祥云','苍松翠鹤','穿越时空','大双边','蝶中彩','东方红','枫叶红','高密丝','红光蓝鹤','红金龙','红口5','红麒麟','红太阳','红霞鹤影','红钻之光','黄金背红','黄金甲','黄金甲纤云','黄牡丹','姐妹花','金杯桃花红','金粉桃花红','金观音','金光国徽','金光神鹰','金光星辉','金龙王','金满堂','金满堂·背红','金满堂·黄金甲','金满堂·金牡丹','金牡丹（只标注DQ）','金网鹤王','金星绿波','开门红','开门红纤云','蓝凤朝阳','蓝天瑞云','两边荧光','两边荧光','流浪地球（只标注强荧光）','绿翡翠','绿美人','绿牡丹','绿幽灵','绿钻','绿钻关门冠','绿钻首发冠','满版开门红','满版中国红','满堂彩','满堂红(只标注PA)','满天星桃花红','青绿美翠','青天白日背祥','青天白日青丝','青天白日正祥','清荷绿','日月双蝶','三色彩蝶','双边金牡丹（只标注DQ）','太白金星','钛白纤云','天地绿','万紫千红','五彩苍松','五彩金花','五星光辉','纤丝','纤云.红光蓝鹤','小纤云','小纤云','小纤云','小纤云.苍松翠鹤','幸运树','燕子桃花红','荧光版','荧光版','荧光版','右单边荧光','宇宙之眼','浴火凤凰','正红背绿','正祥云','中国红','中国龙','中国梦','间荧光','左单边荧光'];
let vueDemos = new Vue({
    el: '#demos',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live").val(data);

            updedata1();
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});



let vueDemos1 = new Vue({
    el: '#demos1',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live2").val(data);
            updedata(2);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});


let vueDemos2 = new Vue({
    el: '#demos2',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live3").val(data);
            updedata(3);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});


let vueDemos3 = new Vue({
    el: '#demos3',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live4").val(data);
            updedata(4);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});


let vueDemos4 = new Vue({
    el: '#demos4',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live5").val(data);
            updedata(5);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});


let vueDemos5 = new Vue({
    el: '#demos5',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live6").val(data);
            updedata(6);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});


let vueDemos6 = new Vue({
    el: '#demos6',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live7").val(data);
            updedata(7);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});

let vueDemos7 = new Vue({
    el: '#demos7',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live8").val(data);
            updedata(8);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});

let vueDemos8 = new Vue({
    el: '#demos8',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live9").val(data);
            updedata(9);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});

let vueDemos9 = new Vue({
    el: '#demos9',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live10").val(data);
            updedata(10);
            // console.log(this);
            // this.$("#live").val(data);
            // $this.firstname=data;
            // this.firstname=data;
        }

    }
});


function updedata1() {

    let live = $("#live").val();

    if (live.length <= 4) {
        $("#banbie").text(live);
        banbie.style.fontSize = '40px';
        banbie.style.paddingTop = '5px';
    }
    if (live.length >= 5) {
        $("#banbie").text(live);
        banbie.style.fontSize = '32px';
        banbie.style.paddingTop = '8px';
    }
    if (live.length >= 6) {
        $("#banbie").text(live);
        banbie.style.fontSize = '27px';
        banbie.style.paddingTop = '10px';
    }
}




 function updedata(i) {

     let live = $("#live"+i).val();

     if (live.length <= 4) {
         $("#banbie"+i).text(live);
         $("#banbie"+i).css("fontSize","40px");
         $("#banbie"+i).css("paddingTop","5px");
     }
     if (live.length >= 5) {
         $("#banbie"+i).text(live);
         $("#banbie"+i).css("fontSize","32px");
         $("#banbie"+i).css("paddingTop","8px");
     }
     if (live.length >= 6) {
         $("#banbie"+i).text(live);
         $("#banbie"+i).css("fontSize","27px");
         $("#banbie"+i).css("paddingTop","10px");
     }
}
