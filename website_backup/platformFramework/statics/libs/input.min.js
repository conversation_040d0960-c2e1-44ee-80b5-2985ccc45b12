const provinceList = ['霸王花','霸王云','白钻','背红','背金沙','背绿沙','背绿星星黄金甲','背祥云','苍松翠鹤','穿越时空','大双边','蝶中彩','东方红','枫叶红','高密丝','红光蓝鹤','红金龙','红口5','红麒麟','红太阳','红霞鹤影','红钻之光','黄金背红','黄金甲','黄金甲纤云','黄牡丹','姐妹花','金杯桃花红','金粉桃花红','金观音','金光国徽','金光神鹰','金光星辉','金龙王','金满堂','金满堂·背红','金满堂·黄金甲','金满堂·金牡丹','金牡丹（只标注DQ）','金网鹤王','金星绿波','开门红','开门红纤云','蓝凤朝阳','蓝天瑞云','两边荧光','两边荧光','流浪地球（只标注强荧光）','绿翡翠','绿美人','绿牡丹','绿幽灵','绿钻','绿钻关门冠','绿钻首发冠','满版开门红','满版中国红','满堂彩','满堂红(只标注PA)','满天星桃花红','青绿美翠','青天白日背祥','青天白日青丝','青天白日正祥','清荷绿','日月双蝶','三色彩蝶','双边金牡丹（只标注DQ）','太白金星','钛白纤云','天地绿','万紫千红','五彩苍松','五彩金花','五星光辉','纤丝','纤云.红光蓝鹤','小纤云','小纤云','小纤云','小纤云.苍松翠鹤','幸运树','燕子桃花红','荧光版','荧光版','荧光版','右单边荧光','宇宙之眼','浴火凤凰','正红背绿','正祥云','中国红','中国龙','中国梦','间荧光','左单边荧光'];
let vueDemos = new Vue({
    el: '#demos',
    data: {
        provinceList1: {
            list: provinceList,
        },
        province1: ''
    },
    methods: {
        // 跟智能输入框同步选中的业务
        collectProvince1(data) {
            this.province1 = data;
            $("#live").val(data);
            updedata();
            let banbie = document.getElementsByClassName("banbie")
            if (data.search("红") != -1){
                for (var i = 0; i < banbie.length; i++) {
                    banbie[i].style.color = '#ff0000';
                }

            }else if (data.search("青") != -1){
                for (var i = 0; i < banbie.length; i++) {
                    banbie[i].style.color = '#E1FFFF';
                }
            }else if (data.search("绿") != -1){
                for (var i = 0; i < banbie.length; i++) {
                    banbie[i].style.color = '#808000';
                }
            }else if (data.search("金") != -1){
                for (var i = 0; i < banbie.length; i++) {
                    banbie[i].style.color = '#A0522D';
                }
            }else if (data.search("荧光") != -1){
                for (var i = 0; i < banbie.length; i++) {
                    banbie[i].style.color = '#fff2cc';
                }
            }else if (data.search("白") != -1){
                for (var i = 0; i < banbie.length; i++) {
                    banbie[i].style.color = '#F8F8FF';
                }
            }




        }

    }
});

function updedata() {

    let banbie=document.getElementsByClassName("banbie");

    let live=$("#live").val();
    if(live.length<=4){
        for (var i = 0; i < banbie.length; i++) {
            banbie[i].innerHTML = $("#live").val();
            $('.banbie').css("font-size", "40px");
            banbie[i].style.paddingTop = '5px';
        }
    }
    if(live.length==5){
        for (var i = 0; i < banbie.length; i++) {
            banbie[i].innerHTML = $("#live").val();
            $('.banbie').css("font-size", "32px");
            banbie[i].style.paddingTop = '8px';
        }
    }
    if(live.length>=6){
        for (var i = 0; i < banbie.length; i++) {
            banbie[i].innerHTML = $("#live").val();
            $('.banbie').css("font-size", "27px");
            banbie[i].style.paddingTop = '10px';
        }
    }

}
