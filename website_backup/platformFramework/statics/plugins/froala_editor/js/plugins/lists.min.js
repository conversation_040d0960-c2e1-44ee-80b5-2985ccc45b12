/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
!function(a){a.Editable.commands=a.extend(a.Editable.commands,{insertOrderedList:{title:"Numbered List",icon:"fa fa-list-ol",refresh:a.Editable.prototype.refreshDefault,callback:function(a){this.formatList(a)},undo:!0},insertUnorderedList:{title:"Bulleted List",icon:"fa fa-list-ul",refresh:a.Editable.prototype.refreshDefault,callback:function(a){this.formatList(a)},undo:!0}}),a.Editable.prototype.processBackspace=function(b){var c=b.prev();if(c.length){for(this.removeMarkers(),("UL"==c.get(0).tagName||"OL"==c.get(0).tagName)&&(c=c.find("li:last"));c.find("> ul, > ol").length;)c=c.find("> ul li:last, > ol li:last");var d=c.find("> p, > h1, > h3, > h4, > h5, > h6, > div, > pre, > blockquote");0===c.text().length&&0===c.find("img, table, input, iframe, video").length?c.remove():(this.keep_enter=!0,b.find("> p, > h1, > h3, > h4, > h5, > h6, > div, > pre, > blockquote").each(function(b,c){a(c).replaceWith(a(c).html())}),this.keep_enter=!1,d.length?(a(d[d.length-1]).append('<span class="f-marker" data-type="false" data-id="0" data-fr-verified="true"></span><span class="f-marker" data-type="true" data-id="0" data-fr-verified="true"></span>'),a(d[d.length-1]).append(b.html())):(c.append('<span class="f-marker" data-type="false" data-id="0" data-fr-verified="true"></span><span class="f-marker" data-type="true" data-id="0" data-fr-verified="true"></span>'),c.append(b.html())),b.remove(),this.restoreSelectionByMarkers())}else this.formatList(b.parents("ul").length?"insertUnorderedList":"insertOrderedList");this.$element.find("breakli").remove(),this.sync()},a.Editable.prototype.liBackspace=function(){var b,c=this.getSelectionElement(),d=a(c).parents("table, li");if(d.length>0&&"TABLE"===d[0].tagName)return!0;if(b="LI"==c.tagName?a(c):a(c).parents("li:first"),this.removeMarkers(),this.insertHTML("<breakli></breakli>"),b.find("breakli").prev().length&&"TABLE"===b.find("breakli").prev().get(0).tagName&&b.find("breakli").next().length&&"BR"===b.find("breakli").next().get(0).tagName)return this.setSelection(b.find("breakli").prev().find("td:first").get(0)),b.find("breakli").next().remove(),this.$element.find("breakli").remove(),!1;for(var e,f=b.html(),g=[],h=0;h<f.length;h++){if(chr=f.charAt(h),"<"!=chr)return this.$element.find("breakli").remove(),!0;var i=f.indexOf(">",h+1);if(-1!==i){e=f.substring(h,i+1);var j=this.tagName(e);if(h=i,"breakli"==j){if(!this.isClosingTag(e)&&!this.isClosingTag(g[g.length-1]))return this.processBackspace(b),!1}else g.push(e)}}return this.$element.find("breakli").remove(),!0},a.Editable.prototype.emptyLiEnter=function(b){this.removeMarkers();var c="ol";b.parents("ul:first").length>0&&(c="ul");var d=a(b.parents("ul, ol")[0]);b.replaceWith(d.parents("ul, ol").length>0?'<span id="close-'+c+'" data-fr-verified="true"></span><li><p><span class="f-marker" data-type="false" data-id="0" data-fr-verified="true"></span><span class="f-marker" data-type="true" data-id="0" data-fr-verified="true"></span><br/></p></li><span id="open-'+c+'" data-fr-verified="true"></span>':'<span id="close-'+c+'" data-fr-verified="true"></span><p><span class="f-marker" data-type="false" data-id="0" data-fr-verified="true"></span><span class="f-marker" data-type="true" data-id="0" data-fr-verified="true"></span><br/></p><span id="open-'+c+'" data-fr-verified="true"></span>'),html=d.html(),html=html.replace('<span id="close-ul" data-fr-verified="true"></span>',"</ul>"),html=html.replace('<span id="close-ol" data-fr-verified="true"></span>',"</ol>"),html=html.replace('<span id="open-ul" data-fr-verified="true"></span>',"<ul>"),html=html.replace('<span id="open-ol" data-fr-verified="true"></span>',"<ol>"),d.replaceWith("<"+c+">"+html+"</"+c+">"),this.cleanupLists()},a.Editable.prototype.textLiEnter=function(b){this.removeMarkers(),this.insertHTML("<breakli></breakli>",!1);for(var c,d=b.html(),e=[],f={},g=[],h=0,i=0;i<d.length;i++)if(chr=d.charAt(i),"<"==chr){var j=d.indexOf(">",i+1);if(-1!==j){c=d.substring(i,j+1);var k=this.tagName(c);if(i=j,"breakli"==k){if(!this.isClosingTag(c)){for(var l=e.length-1;l>=0;l--){var m=this.tagName(e[l]);g.push("</"+m+">")}g.push("</li>"),g.push("<li>");for(var n=0;n<e.length;n++)g.push(e[n]);g.push('<span class="f-marker" data-type="false" data-collapsed="true" data-id="0" data-fr-verified="true"></span><span class="f-marker" data-type="true" data-collapsed="true" data-id="0" data-fr-verified="true"></span>')}}else if(g.push(c),!this.isSelfClosingTag(c))if(this.isClosingTag(c)){var o=f[k].pop();e.splice(o,1)}else e.push(c),void 0===f[k]&&(f[k]=[]),f[k].push(e.length-1)}}else h++,g.push(chr);var p=a(b.parents("ul, ol")[0]);b.replaceWith("<li>"+g.join("")+"</li>"),p.find("p:empty + table").prev().remove(),p.find("p + table").each(function(b,c){var d=a(c);d.prev().append(d.clone()),d.remove()}),p.find("table + p").each(function(b,c){var d=a(c);d.append(d.prev().clone()),d.prev().remove()}),p.find(a.Editable.VALID_NODES.join(",")).each(function(b,c){"LI"!=c.tagName&&0===a(c).find("br").length&&""===a(c).text().trim()&&a(c).append("<br/>")}),p.find("li").each(a.proxy(function(b,c){this.wrapTextInElement(a(c))},this))},a.Editable.prototype.liEnter=function(){var b,c=this.getSelectionElement(),d=a(c).parents("table, li");return d.length>0&&"TABLE"==d[0].tagName?!0:(b="LI"==c.tagName?a(c):a(c).parents("li:first"),0===b.text().length&&0===b.find("img, table, iframe, input, object").length?this.emptyLiEnter(b):this.textLiEnter(b),this.$element.find("breakli").remove(),this.restoreSelectionByMarkers(),this.sync(),!1)},a.Editable.prototype.listTab=function(){var b=a(this.getSelectionElement());return b.parents("ul, ol").length>0?(this.indent(),!1):void 0},a.Editable.prototype.listShiftTab=function(){var b=a(this.getSelectionElement());return b.parents("ul, ol").length>0?(this.outdent(),!1):void 0},a.Editable.prototype.initList=function(){this.addListener("tab",this.listTab),this.addListener("shift+tab",this.listShiftTab),this.domInsertList(),this.isImage||this.isLink||this.options.editInPopup||this.$element.on("keydown",a.proxy(function(b){var c=b.which,d=this.getSelectionElement();if("LI"==d.tagName||a(d).parents("li").length>0){if(13==c&&!b.shiftKey)return this.liEnter();if(8==c)return this.liBackspace()}},this))},a.Editable.initializers.push(a.Editable.prototype.initList),a.Editable.prototype.formatList=function(b){if(this.browser.msie&&a.Editable.getIEversion()<9)return document.execCommand(b,!1,!1),!1;var c,d,e=!1,f=!0,g=!1,h=this.getSelectionElements(),i=a(h[0]).parents("ul, ol");if(i.length&&("UL"===i[0].tagName?"insertUnorderedList"!=b&&(e=!0):"insertOrderedList"!=b&&(e=!0)),this.saveSelectionByMarkers(),e){c="ol","insertUnorderedList"===b&&(c="ul");var j=a(i[0]);j.replaceWith("<"+c+">"+j.html()+"</"+c+">")}else{for(var k=0;k<h.length;k++)if(d=a(h[k]),("TD"==d.get(0).tagName||"TH"==d.get(0).tagName)&&this.wrapTextInElement(d),d.parents("li").length>0||"LI"==d.get(0).tagName){var l;l="LI"==d.get(0).tagName?d:a(d.parents("li")[0]);var m=d.parents("ul, ol");m.length>0&&(c=m[0].tagName.toLowerCase(),l.before('<span class="close-'+c+'" data-fr-verified="true"></span>'),l.after('<span class="open-'+c+'" data-fr-verified="true"></span>')),(0===a(m[0]).parents("ol, ul").length||e)&&l.replaceWith(0===l.find(a.Editable.VALID_NODES.join(",")).length?l.html()+"<br>":l.html()),g=!0}else f=!1;g&&this.cleanupLists(),(f===!1||e===!0)&&(this.wrapText(),this.restoreSelectionByMarkers(),h=this.getSelectionElements(),this.saveSelectionByMarkers(),this.elementsToList(h,b),this.unwrapText(),this.cleanupLists())}this.restoreSelectionByMarkers(),this.repositionEditor(),b="insertUnorderedList"==b?"unorderedListInserted":"orderedListInserted",this.triggerEvent(b)},a.Editable.prototype.elementsToList=function(b,c){var d=a("<ol>");"insertUnorderedList"==c&&(d=a("<ul>")),b[0]==this.$element.get(0)&&(b=this.$element.find("> "+a.Editable.VALID_NODES.join(", >")));for(var e=0;e<b.length;e++)$element=a(b[e]),$element.get(0)!=this.$element.get(0)&&("TD"===$element.get(0).tagName||"TH"===$element.get(0).tagName?(this.wrapTextInElement($element),this.elementsToList($element.find("> "+a.Editable.VALID_NODES.join(", >")),c)):(d.append(a("<li>").html($element.clone())),d.find("li").length>1?$element.remove():$element.replaceWith(d)))},a.Editable.prototype.indentLi=function(b){var c=b.parents("ul, ol"),d=c.get(0).tagName.toLowerCase();b.find("> ul, > ol").length>0&&b.prev("li").length>0?(this.wrapTextInElement(b),b.find("> "+a.Editable.VALID_NODES.join(" , > ")).each(function(b,c){a(c).wrap("<"+d+"></"+d+">").wrap("<li></li>")}),b.prev("li").append(b.find("> ul, > ol")),b.remove(),this.cleanupLists()):0===b.find("> ul, > ol").length&&b.prev("li").length>0&&(b.prev().append(a("<"+d+">").append(b.clone())),b.remove(),a(c.find("li").get().reverse()).each(function(b,c){var d=a(c);d.find(" > ul, > ol").length>0&&d.prev()&&d.prev().find(" > ul, > ol").length>0&&(d.prev().append(d.html()),d.remove())}),this.cleanupLists())},a.Editable.prototype.outdentLi=function(b){var c=a(b.parents("ul, ol")[0]),d=c.parents("ul, ol"),e=c.get(0).tagName.toLowerCase();0===b.prev("li").length&&b.parents("li").length>0?(b.before('<span class="close-'+e+'" data-fr-verified="true"></span>'),b.before('<span class="close-li" data-fr-verified="true"></span>'),b.before('<span class="open-li" data-fr-verified="true"></span>'),b.after('<span class="open-'+e+'" data-fr-verified="true"></span>'),b.replaceWith(b.html())):(b.before('<span class="close-'+e+'" data-fr-verified="true"></span>'),b.after('<span class="open-'+e+'" data-fr-verified="true"></span>'),b.parents("li").length>0&&(b.before('<span class="close-li" data-fr-verified="true"></span>'),b.after('<span class="open-li" data-fr-verified="true"></span>'))),d.length||b.replaceWith(b.html()),this.cleanupLists()},a.Editable.prototype.domInsertList=function(){this.$element.on("DOMNodeInserted",a.proxy(function(b){"BR"!==b.target.tagName||"LI"!==a(b.target).parent().get(0).tagName||this.keep_enter||a(b.target).remove()},this))}}(jQuery);