/*!
 * froala_editor v1.2.2 (http://editor.froala.com)
 * Copyright 2014-2014 Froala
 */
!function(a){a.Editable.COLORS=["#000000","#444444","#666666","#999999","#CCCCCC","#EEEEEE","#F3F3F3","#FFFFFF","#FF0000","#FF9900","#FFFF00","#00FF00","#00FFFF","#0000FF","#9900FF","#FF00FF","#F4CCCC","#FCE5CD","#FFF2CC","#D9EAD3","#D0E0E3","#CFE2F3","#D9D2E9","#EAD1DC","#EA9999","#F9CB9C","#FFE599","#B6D7A8","#A2C4C9","#9FC5E8","#B4A7D6","#D5A6BD","#E06666","#F6B26B","#FFD966","#93C47D","#76A5AF","#6FA8DC","#8E7CC3","#C27BA0","#CC0000","#E69138","#F1C232","#6AA84F","#45818E","#3D85C6","#674EA7","#A64D79","#990000","#B45F06","#BF9000","#38771D","#134F5C","#0B5394","#351C75","#741B47","#660000","#783F04","#7F6000","#274E13","#0C343D","#073763","#201211","#4C1130"],a.Editable.prototype.refreshColors=function(){var a=this.getSelectionElement();this.$editor.find(".fr-color-picker button.fr-color-bttn").removeClass("active"),this.refreshColor(a,"background-color","backColor"),this.refreshColor(a,"color","foreColor")},a.Editable.prototype.refreshColor=function(b,c,d){for(;a(b).get(0)!=this.$element.get(0);){if("transparent"!==a(b).css(c)&&"rgba(0, 0, 0, 0)"!==a(b).css(c)){this.$editor.find('.fr-color-picker button.fr-color-bttn[data-cmd="'+d+'"][data-val="'+a.Editable.RGBToHex(a(b).css(c))+'"]').addClass("active");break}b=a(b).parent()}},a.Editable.commands=a.extend(a.Editable.commands,{color:{icon:"fa fa-tint",title:"Color",refreshOnShow:a.Editable.prototype.refreshColors,seed:[{cmd:"backColor",value:a.Editable.COLORS,title:"Background Color"},{cmd:"foreColor",value:a.Editable.COLORS,title:"Text Color"}],callback:function(a,b,c){this[c].apply(this,[b])},callbackWithoutSelection:function(b,c,d){"backColor"===d&&(d="background-color"),"foreColor"===d&&(d="color"),this._startInFontExec(d,b,c),"#123456"===c&&""===this.text()&&(this.cleanify(!0,!1),this.$element.find("span").each(function(b,c){var d=a(c),e=d.css("background-color");("#123456"===e||"#123456"===a.Editable.RGBToHex(e))&&d.css("background-color",""),e=d.css("color"),("#123456"===e||"#123456"===a.Editable.RGBToHex(e))&&d.css("color","")}))},undo:!0}}),a.Editable.prototype.command_dispatcher=a.extend(a.Editable.prototype.command_dispatcher,{color:function(a){var b=this.buildDropdownColor(a),c=this.buildDropdownButton(a,b,"fr-color-picker");return c}}),a.Editable.prototype.buildDropdownColor=function(a){for(var b='<div class="fr-dropdown-menu">',c=0;c<a.seed.length;c++){for(var d=a.seed[c],e='<div><p><span data-text="true">'+d.title+'</span> <a data-val="#123456" data-cmd="color" data-param="'+d.cmd+'" class="fr-bttn" title="Clear"><i class="fa fa-eraser"></i></a></p>',f=0;f<d.value.length;f++){var g=d.value[f];e+='<button type="button" class="fr-color-bttn" data-val="'+g+'" data-cmd="color" data-param="'+d.cmd+'" style="background-color: '+g+';">&nbsp;</button>',f%8==7&&f>0&&(e+="<hr/>",(7==f||15==f)&&(e+='<div class="separator"></div>'))}e+="</div>",b+=e}return b+="</div>"},a.Editable.prototype.backColor=function(b){this.inlineStyle("background-color","backColor",b),this.$element.find("span").each(function(b,c){var d=a(c),e=d.css("background-color");("#123456"===e||"#123456"===a.Editable.RGBToHex(e))&&d.css("background-color","")});var c=this.$editor.find('button.fr-color-bttn[data-cmd="backColor"][data-val="'+b+'"]');c.addClass("active"),c.siblings().removeClass("active")},a.Editable.prototype.foreColor=function(b){this.inlineStyle("color","foreColor",b),this.$element.find("span").each(function(b,c){var d=a(c),e=d.css("color");("#123456"===e||"#123456"===a.Editable.RGBToHex(e))&&d.css("color","")});var c=this.$editor.find('button.fr-color-bttn[data-cmd="foreColor"][data-val="'+b+'"]');c.addClass("active"),c.siblings().removeClass("active")}}(jQuery);