@media screen and (min-width: 900px) {
    #demos {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos {
        margin: 0 auto;
    }
}
#demos section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    margin: 0 0 16px;
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos section .section-demo {
    padding: 0px 0px;
}
#demos section .section-description, #demos section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos section .section-description-title {
    color: steelblue;
}
#demos section .input-value {
    padding: 12px 0;
}
#demos section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}
@font-face {
    font-family: '剑豪体';
    src: url('/statics/css/Aa剑豪体.ttf') format('truetype');
}
@font-face {
    font-family: '云峰飞云体';
    src: url('/statics/css/云峰飞云体.ttf') format('truetype');
}

@font-face {
    font-family: '仓耳周珂正大榜书';
    src: url('/statics/css/仓耳周珂正大榜书.ttf') format('truetype');
}