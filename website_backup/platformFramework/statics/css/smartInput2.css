/* smartInput输入框需要的样式表 */
.friendSearchContainer input,
.friendSearchContainer ul,
.friendSearchContainer li,
.friendSearchContainer div {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}
/* 基于bootstrap的样式引入 */
.friendSearchContainer .smartInput-input {
    display: block;
    width: 60%;
    padding: .3rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #495057;
    background-color: rgba(188, 199, 188, 0.39);
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: .25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    z-index: 1;
    position: relative;
}
.friendSearchContainer {
    position: relative;
}
.friendSearchList {
    width: 100%;
    padding: 6px 12px;
    overflow-y: scroll;
    max-height: 300px;
    background: #fff;
    z-index: 10;
    box-shadow: 0 10px 10px rgba(0, 0, 0, .2);
    border: 1px solid #ccc;
    position: absolute;
}
.friendSearchList li {
    padding: 3px 12px;
}
.friendSearchList li.smartInput-active {
    background: #337ab7;
    color: #fff;
}
.friendSearchList li.hover {
    background-color: #36bc7f;
    color: #fff;
}
.friendSearchList li.smartInput-active:hover {
    background-color: #36bc7f;
}
.friendSearchModal {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    z-index: 1;
}
.friendSearchContainer .invalid-msg {
    color: darkred;
    padding: 5px 0;
}
.friendSearchContainer .invalid-msg:before {
    content: '错误提示：';
}
.smartInput-badge-list {
    display: flex;
    flex-wrap: wrap;
}
.friendSearchContainer .smartInput-badge {
    color: rgba(0, 0, 0, 0.65);
    /* background-color: #fafafa; */
    border: 1px solid #e8e8e8;
    border-radius: 2px;
    cursor: default;
    max-width: 99%;
    position: relative;
    overflow: hidden;
    transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    margin-right: .5rem;
    margin-bottom: .5rem;
    padding: .4rem;
    font-size: .9em;
    line-height: .9em;
    display: flex;
    align-items: center;
}
.friendSearchContainer .smartInput-badge:last-child {
    border: 0;
}
.smartInput-badge-icon {
    cursor: pointer;
    color: #ccc;
}
.smartInput-badge-icon:hover {
    color: #999;
}
.smartInput-search {
    border: 0;
    outline: none;
}