@media screen and (min-width: 900px) {
    #demos {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos {
        margin: 0 auto;
    }
}
#demos section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos section .section-demo {
    padding: 0px 0px;
}
#demos section .section-description, #demos section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos section .section-description-title {
    color: steelblue;
}
#demos section .input-value {
    padding: 12px 0;
}
#demos section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}

@media screen and (min-width: 900px) {
    #demos1 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos1 {
        margin: 0 auto;
    }
}
#demos1 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos1 section .section-demo {
    padding: 0px 0px;
}
#demos1 section .section-description, #demos1 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos1 section .section-description-title {
    color: steelblue;
}
#demos1 section .input-value {
    padding: 12px 0;
}
#demos1 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}



@media screen and (min-width: 900px) {
    #demos2 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos2 {
        margin: 0 auto;
    }
}
#demos2 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos2 section .section-demo {
    padding: 0px 0px;
}
#demos2 section .section-description, #demos2 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos2 section .section-description-title {
    color: steelblue;
}
#demos2 section .input-value {
    padding: 12px 0;
}
#demos2 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}




@media screen and (min-width: 900px) {
    #demos3 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos3 {
        margin: 0 auto;
    }
}
#demos3 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos3 section .section-demo {
    padding: 0px 0px;
}
#demos3 section .section-description, #demos3 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos3 section .section-description-title {
    color: steelblue;
}
#demos3 section .input-value {
    padding: 12px 0;
}
#demos3 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}


@media screen and (min-width: 900px) {
    #demos4 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos4 {
        margin: 0 auto;
    }
}
#demos4 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos4 section .section-demo {
    padding: 0px 0px;
}
#demos4 section .section-description, #demos4 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos4 section .section-description-title {
    color: steelblue;
}
#demos4 section .input-value {
    padding: 12px 0;
}
#demos4 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}


@media screen and (min-width: 900px) {
    #demos5 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos5 {
        margin: 0 auto;
    }
}
#demos5 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos5 section .section-demo {
    padding: 0px 0px;
}
#demos5 section .section-description, #demos5 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos5 section .section-description-title {
    color: steelblue;
}
#demos5 section .input-value {
    padding: 12px 0;
}
#demos5 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}


@media screen and (min-width: 900px) {
    #demos6 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos6 {
        margin: 0 auto;
    }
}
#demos6 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos6 section .section-demo {
    padding: 0px 0px;
}
#demos6 section .section-description, #demos6 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos6 section .section-description-title {
    color: steelblue;
}
#demos6 section .input-value {
    padding: 12px 0;
}
#demos6 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}

@media screen and (min-width: 900px) {
    #demos7 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos7 {
        margin: 0 auto;
    }
}
#demos7 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos7 section .section-demo {
    padding: 0px 0px;
}
#demos7 section .section-description, #demos7 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos7 section .section-description-title {
    color: steelblue;
}
#demos7 section .input-value {
    padding: 12px 0;
}
#demos7 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}


@media screen and (min-width: 900px) {
    #demos8 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos8 {
        margin: 0 auto;
    }
}
#demos8 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos8 section .section-demo {
    padding: 0px 0px;
}
#demos8 section .section-description, #demos8 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos8 section .section-description-title {
    color: steelblue;
}
#demos8 section .input-value {
    padding: 12px 0;
}
#demos8 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}

@media screen and (min-width: 900px) {
    #demos9 {
        width: 100%;
        margin: 0 auto;
    }
}
@media screen and (max-width: 900px) {
    #demos9 {
        margin: 0 auto;
    }
}
#demos9 section{
    border-radius: 2px;
    display: inline-block;
    width: 108%;
    position: relative;
    
    transition: all 0.2s;
    color: rgba(0, 0, 0, 0.65);
}
#demos9 section .section-demo {
    padding: 0px 0px;
}
#demos9 section .section-description, #demos9 section .section-code{
    padding: 12px 24px;
    border-top: 1px solid #ebedf0;
    position: relative;
    overflow: scroll;
}
#demos9 section .section-description-title {
    color: steelblue;
}
#demos9 section .input-value {
    padding: 12px 0;
}
#demos9 section .code-expand-icon {
    position: absolute;
    right: 16px;
    bottom: 23px;
    cursor: pointer;
    width: 24px;
    height: 16px;
    line-height: 16px;
    text-align: center;
    color: cornflowerblue;
    font-size: 12px;
}



@font-face {
    font-family: '剑豪体';
    src: url('/statics/css/Aa剑豪体.ttf') format('truetype');
}
@font-face {
    font-family: '云峰飞云体';
    src: url('/statics/css/云峰飞云体.ttf') format('truetype');
}

@font-face {
    font-family: '仓耳周珂正大榜书';
    src: url('/statics/css/仓耳周珂正大榜书.ttf') format('truetype');
}




