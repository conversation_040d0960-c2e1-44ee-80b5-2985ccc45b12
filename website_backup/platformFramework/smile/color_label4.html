<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>彩色标签打印（单个）</title>
    <style media="print">
        @page {
            size: auto;
            margin: 0mm;
        }

    </style>
    <style>
        body {
            padding: 0;
            margin: 0;
        }

        /* 首张div */
        .first {
            width: 19cm;
            height: 3.7cm;
            border: 1px solid cornflowerblue;
        }

        /* 其他div */
        .other {
            width: 19cm;
            height: 2.1cm;
            border-left: 1px solid grey;
            border-bottom: 1px solid grey;
            border-right: 1px solid grey;
        }

        /* 首张div */
        .first1 {
            width: 19cm;
            height: 2.1cm;
            border: 1px solid cornflowerblue;
        }

        /* 首张div */
        .firstJb {
            width: 19cm;
            height: 4.3cm;
            border: 1px solid cornflowerblue;
        }

        .first1mini {
            top: 4.1cm;
            width: 19cm;
            border: 1px solid cornflowerblue;
        }


        /* 其他div */
        .other1 {
            width: 19cm;
            height: 2.6cm;
            border-left: 1px solid grey;
            border-bottom: 1px solid grey;
            border-right: 1px solid grey;
        }


        /* 图片位置框 */
        .imgBoxA {
            position: relative;
            top: 0px;
            left: 180px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxB {
            position: relative;
            top: 0px;
            left: 180px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxAJB {
            position: relative;
            top: 13px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxAJB1 {
            position: relative;
            top: 82px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1 {
            position: relative;
            top: 77px;
            left: 375px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxA1M {
            position: relative;
            top: 77px;
            left: 265px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxA1M1 {
            position: relative;
            top: 20px;
            left: 265px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxBaoYX {
            position: relative;
            top: 77px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoYX1 {
            position: relative;
            top: 20px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxB1 {
            position: relative;
            top: 15px;
            left: 375px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }


        .imgBoxBao {
            position: relative;
            top: 5px;
            left: 380px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoY {
            position: relative;
            top: 5px;
            left: 335px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoA {
            position: relative;
            top: 5px;
            left: 380px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            font-weight: normal;
        }

        .imgBoxBaoB {
            position: relative;
            top: 1px;
            left: 285px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            font-weight: normal;
        }


        .picker {
            width: 50px;
            height: 21px;
            border-radius: 4px;
            cursor: pointer;
        }

        .abc {
            /*padding-top: 2px;*/
            float: left;
        }

        #choose {
            position: absolute;
            top: 10px;
            left: 750px;
        }

        #tips {
            position: absolute;
            top: 10px;
            left: 1100px;
        }

        h1 {
            text-align: center;
            line-height: 22px;
            font-family: 青鸟华光简行楷;
        }

        #live, #live2, #live3, #live4, #live5,
        #live6, #live7, #live8, #live9, #live10, #live11, #live12, #live13 {
            display: inline-block;
            width: 60%;
            height: 20px;
            line-height: 1.5;
            padding: 4px 7px;
            font-size: 14px;
            border: 1px solid #dddee1;
            border-radius: 4px;
            background-color: #d1deef;
            background-image: none;
            position: relative;
            cursor: text;
            transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
        }

        /* 鼠标右键 */
        .shade {
            width: 100%;
            height: 100%;
            position: absolute;
            top: 0px;
            left: 0px;

        }

        .wrap-ms-right {
            list-style: none;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 9999;
            padding: 5px 0;
            min-width: 80px;
            margin: 0;
            display: none;
            font-family: "微软雅黑";
            font-size: 14px;
            background-color: #fff;
            border: 1px solid rgba(0, 0, 0, .15);
            box-sizing: border-box;
            border-radius: 4px;
            -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -ms-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            -o-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .ms-item {
            height: 30px;
            line-height: 30px;
            text-align: center;
            cursor: pointer;
        }

        .ms-item:hover {
            background-color: #343a40;
            color: #FFFFFF;
        }

        .up3 {
            margin-top: 1px;
        }

        .imgBoxBao111 {
            position: relative;
            top: 5px;
            left: 360px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .imgBoxBaoB2222 {
            position: relative;
            top: 7px;
            left: 350px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
            position: relative;
        }

        .imgBoxA2222 {
            position: relative;
            top: 82px;
            left: 350px;
            width: 4.5cm;
            height: 2cm;
            border: 1px solid lightseagreen;
        }

        .banbie2222 {
            margin-top: 15px;
            margin-left: 22px;
        }
        .banbie3333 {
            margin-top: 15px;
            margin-left: 15px;
        }
        test_top2{
            margin-top: 3px;
        }
        .test_top {
            margin-top: 3px;
            margin-left: 15px;

        }
    </style>
</head>
<body>


<!-- 操作栏 -->
<div id="choose">
    <span style="color: Crimson;">[谷歌浏览器下使用]</span>
    <br/><!--<br/><br/>-->
    <!-- 左右箭头按钮 -->
    <button onclick="moveLeft()" class="arrow-button">←</button>
    <button onclick="moveRight()" class="arrow-button">→</button>
    <button onclick="moveUp()" class="arrow-button">↑</button>
    <button onclick="moveDown()" class="arrow-button">↓</button>
    <br/><br/>
    <label id="lab1" onclick="allFont(0)"><span>1</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(0)" id="allFont1">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live" placeholder="第一条" style="margin-top: 10px;"/>
    <div id="demos">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker"></div>
    </div>
    <input type="button" value="小" onclick="minFont(1)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(1)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(0)" id="familyFont0">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(1)"/>
    <input type="button" value="调节" onclick="changeTop(1)"/>

    <!--    <br/><br/>-->
    <br/>
    <br/>
    <label id="lab2" onclick="allFont(1)"><span>2</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(1)" id="allFont2">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live2" placeholder="第二条"/>
    <div id="demos1">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker2"></div>
    </div>
    <input type="button" value="小" onclick="minFont(2)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(2)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(1)" id="familyFont1">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(2)"/>
    <input type="button" value="调节" onclick="changeTop(2)"/>

    <!--  <br/>
      <br/>
      <br/>-->
    <br/>
    <br/>
    <label id="lab3" onclick="allFont(2)"><span>3</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(2)" id="allFont3">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live3" placeholder="第三条"/>
    <div id="demos2">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker3"></div>
    </div>
    <input type="button" value="小" onclick="minFont(3)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(3)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(2)" id="familyFont2">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(3)"/>
    <input type="button" value="调节" onclick="changeTop(3)"/>

    <!--  <br/><br/>
      <br/>-->
    <br/>
    <br/>

    <label id="lab4" onclick="allFont(3)"><span>4</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(3)" id="allFont4">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live4" placeholder="第四条"/>
    <div id="demos3">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker4"></div>
    </div>
    <input type="button" value="小" onclick="minFont(4)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(4)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(3)" id="familyFont3">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(4)"/>
    <input type="button" value="调节" onclick="changeTop(4)"/>

    <!-- <br/> <br/> <br/>-->
    <br/>
    <br/>
    <label id="lab5" onclick="allFont(4)"><span>5</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(4)" id="allFont5">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live5" placeholder="第五条"/>
    <div id="demos4">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker5"></div>
    </div>
    <input type="button" value="小" onclick="minFont(5)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(5)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(4)" id="familyFont4">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(5)"/>
    <input type="button" value="调节" onclick="changeTop(5)"/>

    <!-- <br/>
     <br/>-->
    <br/>
    <br/>
    <br/>
    <label id="lab6" onclick="allFont(5)"><span>6</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(5)" id="allFont6">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live6" placeholder="第六条"/>
    <div id="demos5">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker6"></div>
    </div>
    <input type="button" value="小" onclick="minFont(6)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(6)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(5)" id="familyFont5">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(6)"/>
    <input type="button" value="调节" onclick="changeTop(6)"/>

    <!-- <br/><br/><br/>-->
    <br/>
    <br/>
    <label id="lab7" onclick="allFont(6)"><span>7</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(6)" id="allFont7">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live7" placeholder="第七条"/>
    <div id="demos6">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker7"></div>
    </div>
    <input type="button" value="小" onclick="minFont(7)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(7)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(6)" id="familyFont6">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(7)"/>
    <input type="button" value="调节" onclick="changeTop(7)"/>

    <!-- <br/>
     <br/><br/>-->
    <br/>
    <br/>
    <label id="lab8" onclick="allFont(7)"><span>8</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(7)" id="allFont8">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live8" placeholder="第八条"/>
    <div id="demos7">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker8"></div>
    </div>
    <input type="button" value="小" onclick="minFont(8)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(8)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(7)" id="familyFont7">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(8)"/>
    <input type="button" value="调节" onclick="changeTop(8)"/>

    <!--   <br/>
       <br/><br/>-->
    <br/>
    <br/>
    <label id="lab9" onclick="allFont(8)"><span>9</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(8)" id="allFont9">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live9" placeholder="第九条"/>
    <div id="demos8">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker9"></div>
    </div>
    <input type="button" value="小" onclick="minFont(9)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(9)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(8)" id="familyFont8">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(9)"/>
    <input type="button" value="调节" onclick="changeTop(9)"/>

    <!-- <br/>
     <br/><br/>-->
    <br/>
    <br/>
    <br/>
    <label id="lab10" onclick="allFont(9)"><span>10</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(9)" id="allFont10">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live10" placeholder="第十条"/>
    <div id="demos9">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker10"></div>
    </div>
    <input type="button" value="小" onclick="minFont(10)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(10)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(9)" id="familyFont9">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(10)"/>
    <input type="button" value="调节" onclick="changeTop(10)"/>



    <!--13行-->
    <!--  <br/>
      <br/><br/>-->
    <br/>
    <br/>
    <label id="lab11" onclick="allFont(10)"><span>11</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(10)" id="allFont11">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live11" placeholder="第十一条"/>
    <div id="demos10">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker11"></div>
    </div>
    <input type="button" value="小" onclick="minFont(11)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(11)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(10)" id="familyFont10">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(11)"/>
    <input type="button" value="调节" onclick="changeTop(11)"/>

    <br/>
    <br/>
    <!--<br/>
    <br/><br/>-->
    <label id="lab12" onclick="allFont(11)"><span>12</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(11)" id="allFont12">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live12" placeholder="第十二条"/>
    <div id="demos11">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker12"></div>
    </div>
    <input type="button" value="小" onclick="minFont(12)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(12)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(11)" id="familyFont11">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(12)"/>
    <input type="button" value="调节" onclick="changeTop(12)"/>

    <br/>
    <br/>
    <!--<br/>
    <br/><br/>-->
    <label id="lab13" onclick="allFont(12)"><span>13</span></label> <select style="
    height: 23px;
    width: 50px;
" onchange="allFont1(12)" id="allFont13">
    <option value=""></option>
    <option value="全选" onclick="allFont(1000)">全选</option>
    <option value="反选" onclick="allFont(2000)">反选</option>
    <option value="取消" onclick="allFont(3000)">取消</option>
</select>
    <input type="text" id="live13" placeholder="第十三条"/>
    <div id="demos12">
        <section style="display: none">
            <div class="section-demo">
                <smart-input v-bind="provinceList1" @collect="collectProvince1"></smart-input>
                <!--                <div class="input-value">1当前值：<span class="description">{{province1}}</span></div>-->
            </div>
        </section>
    </div>
    <div class="abc">
        <div class="picker" id="color-picker13"></div>
    </div>
    <input type="button" value="小" onclick="minFont(13)"/>
    <input type="button" value="B" onclick="blockFont()"/>
    <input type="button" value="大" onclick="maxFont(13)"/>
    <select style="
    height: 23px;
    width: 50px;
" onchange="familyFont(12)" id="familyFont12">
        <option value="青鸟华光简行楷">青鸟华光简行楷</option>
        <option value="SimSun">宋体</option>
        <option value="SimHei">黑体</option>
        <option value="Microsoft Yahei">微软雅黑</option>
        <option value="Microsoft JhengHei">微软正黑体</option>
        <option value="KaiTi">楷体</option>
        <option value="NSimSun">新宋体</option>
        <option value="FangSong">仿宋</option>
        <option value="STKaiti">华文楷体</option>
        <option value="STSong">华文宋体</option>
        <option value="STFangsong">华文仿宋</option>
        <option value="STZhongsong">华文中宋</option>
        <option value="STHupo">华文琥珀</option>
        <option value="STXinwei">华文新魏</option>
        <option value="STLiti">华文隶书</option>
        <option value="STXingkai">华文行楷</option>
        <option value="YouYuan">幼圆</option>
        <option value="LiSu">隶书</option>
        <option value="STXihei">华文细黑</option>
        <option value="STCaiyun">华文彩云</option>
        <option value="FZShuTi">方正舒体</option>
        <option value="FZYaoti">方正姚体</option>
    </select>
    <input type="button" value="智能" onclick="adaptiveFont(13)"/>
    <input type="button" value="调节" onclick="changeTop(13)"/>

</div>

<div id="tips">
    <P>
        <label style="color:crimson;font-size: 18px;">调</label>
        <label style="color: green;font-size: 18px;">色</label>
        <label style="color: mediumpurple;font-size: 18px;">表</label>
        Ctrl+F 可搜索
    </P>
    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(255,0,0);">1.大红色</span>
        （纵二横三）：{
        </br>
        <code>&lt;p style='font-size: 12px;'&gt;&lt;/p&gt;</code>
        </br>
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;补号，幼线体，数字冠，大王冠，首发冠，平水，凸版，渡水，中水，小圆水，
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;大圆水，爱情号，长号，宽水红，爱情号，豹子号，老虎号，中国梦，红二平，
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;生日快乐，爱版，窄水红，红光蓝鹤，
            </br>
            &nbsp;
            }
        </a>
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(152,0,0);">2.深红色</span>
        （纵二横二）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;浴火凤凰
            }
        </a>
    </p>
    <p style="border: 1px solid lightblue;">
        <span style="color: black;">3.黑色</span>
        （纵一)：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            满堂彩，炭黑，深版
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(0,255,0);">4.绿色</span>
        （纵三）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            绿幽灵，绿钻
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(0,255,0);">5.背绿</span>
        （#95db95）
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgba(185,215,168);">6.深青色</span>
        （纵七）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            青绿美翠，五彩苍松，苍松翠鹤
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #0096db;">7.浅蓝色</span>
        （#95db95）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            玉钩国
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(0,255,255);">8.天蓝色</span>
        （纵三横二）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            天蓝，蓝凤朝阳
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(74,134,232);">9.蓝色</span>
        （纵三横三）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            中密丝
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(0,0,255);">10.深蓝色</span>
        （纵三横四）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            高密丝
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(204,204,204);">11.水墨色</span>
        （纵一横四）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            古币水印，古币墨水印，海鸥水印
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #f05799;">12.深粉色</span>
        （#f05799）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            满天星桃花红
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #f27e7e;">13.香槟粉</span>
        （#f27e7e）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            金杯桃花红
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: rgb(255,153,0);">14.橙色</span>
        （纵二横四）：{
        <a>
            </br>
            &nbsp;&nbsp;&nbsp;&nbsp;
            金牡丹，红金龙，金龙王，金光蓝鹤
            </br>
            &nbsp;}
        </a>
    </p>

    <p style="border: 1px solid lightblue;">
        <span style="color: #e68e09;">15.金星绿波</span>
        （#e68e09）
    </p>
</div>

<!-- 首张 -->
<div class="first first1" id="switchGrade" onclick="allFont(0)">
    <div class="imgBoxA" id="switchImgBox1">
        <h1 id="banbie"></h1>
    </div>
</div>

<!-- 2 -->
<div class="other" onclick="allFont(1)">
    <div class="imgBoxB" id="switchImgBox2">
        <h1 id="banbie2"></h1>
    </div>
</div>
<div class="other" onclick="allFont(2)">
    <div class="imgBoxB" id="switchImgBox3">
        <h1 id="banbie3"></h1>
    </div>
</div>
<div class="other" onclick="allFont(3)">
    <div class="imgBoxB" id="switchImgBox4">
        <h1 id="banbie4"></h1>
    </div>
</div>
<div class="other" onclick="allFont(4)">
    <div class="imgBoxB" id="switchImgBox5">
        <h1 id="banbie5"></h1>
    </div>
</div>
<div class="other" onclick="allFont(5)">
    <div class="imgBoxB" id="switchImgBox6">
        <h1 id="banbie6"></h1>
    </div>
</div>
<div class="other" onclick="allFont(6)">
    <div class="imgBoxB" id="switchImgBox7">
        <h1 id="banbie7"></h1>
    </div>
</div>
<div class="other" onclick="allFont(7)">
    <div class="imgBoxB" id="switchImgBox8">
        <h1 id="banbie8"></h1>
    </div>
</div>
<div class="other" onclick="allFont(8)">
    <div class="imgBoxB" id="switchImgBox9">
        <h1 id="banbie9"></h1>
    </div>
</div>
<div class="other" onclick="allFont(9)">
    <div class="imgBoxB" id="switchImgBox10">
        <h1 id="banbie10"></h1>
    </div>
</div>
<div class="other" onclick="allFont(10)">
    <div class="imgBoxB" id="switchImgBox11">
        <h1 id="banbie11"></h1>
    </div>
</div>
<div class="other" onclick="allFont(11)">
    <div class="imgBoxB" id="switchImgBox12">
        <h1 id="banbie12"></h1>
    </div>
</div>
<div class="other" onclick="allFont(12)">
    <div class="imgBoxB" id="switchImgBox13">
        <h1 id="banbie13"></h1>
    </div>
</div>

<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<script src="/platformFramework/js/smile/color.js?"></script>
<script src="/platformFramework/js/smile/mouseRight.min.js" type="text/javascript" charset="utf-8"></script>


<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<script src="/platformFramework/statics/libs/smartInput1.js"></script>
<script src="/platformFramework/statics/libs/input.min2.js" type="text/javascript" charset="UTF-8"></script>
<link rel="stylesheet" href="/platformFramework/statics/css/inputq2.css">
<link rel="stylesheet" href="/platformFramework/statics/css/smartInput2.css">


<script>
    var banbie = document.getElementById("banbie");
    var banbie2 = document.getElementById("banbie2");
    var banbie3 = document.getElementById("banbie3");
    var banbie4 = document.getElementById("banbie4");
    var banbie5 = document.getElementById("banbie5");
    var banbie6 = document.getElementById("banbie6");
    var banbie7 = document.getElementById("banbie7");
    var banbie8 = document.getElementById("banbie8");
    var banbie9 = document.getElementById("banbie9");
    var banbie10 = document.getElementById("banbie10");
    var banbie11 = document.getElementById("banbie11");
    var banbie12 = document.getElementById("banbie12");
    var banbie13 = document.getElementById("banbie13");

    let obj = document.getElementById("live");
    let obj2 = document.getElementById("live2");
    let obj3 = document.getElementById("live3");
    let obj4 = document.getElementById("live4");
    let obj5 = document.getElementById("live5");
    let obj6 = document.getElementById("live6");
    let obj7 = document.getElementById("live7");
    let obj8 = document.getElementById("live8");
    let obj9 = document.getElementById("live9");
    let obj10 = document.getElementById("live10");
    let obj11 = document.getElementById("live11");
    let obj12 = document.getElementById("live12");
    let obj13 = document.getElementById("live13");


    let a = Colorpicker.create({
        el: "color-picker",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj.style.color = hex;
            banbie.style.color = hex;

            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }

        }
    });


    let b = Colorpicker.create({
        el: "color-picker2",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj2.style.color = hex;
            banbie2.style.color = hex;

            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let c = Colorpicker.create({
        el: "color-picker3",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj3.style.color = hex;
            banbie3.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let d = Colorpicker.create({
        el: "color-picker4",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj4.style.color = hex;
            banbie4.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let e = Colorpicker.create({
        el: "color-picker5",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj5.style.color = hex;
            banbie5.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let f = Colorpicker.create({
        el: "color-picker6",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj6.style.color = hex;
            banbie6.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let g = Colorpicker.create({
        el: "color-picker7",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj7.style.color = hex;
            banbie7.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let h = Colorpicker.create({
        el: "color-picker8",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj8.style.color = hex;
            banbie8.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    let i = Colorpicker.create({
        el: "color-picker9",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj9.style.color = hex;
            banbie9.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let j = Colorpicker.create({
        el: "color-picker10",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj10.style.color = hex;
            banbie10.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let k = Colorpicker.create({
        el: "color-picker11",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj11.style.color = hex;
            banbie11.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let l = Colorpicker.create({
        el: "color-picker12",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj12.style.color = hex;
            banbie12.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });

    let m = Colorpicker.create({
        el: "color-picker13",
        color: "blue",
        change: function (elem, hex) {
            elem.style.backgroundColor = hex;
            obj13.style.color = hex;
            banbie13.style.color = hex;
            let arr = [];
            for (let k = 1; k < 15; k++) {
                let val = $("#lab" + k + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {
                    arr.push(k);
                }
                // console.log(k);
            }

            for (let k = 0; k < arr.length; k++) {
                let num = arr[k] == 1 ? "" : arr[k];//选中的
                let banbie = document.getElementById("banbie" + num);
                let obj = document.getElementById("live" + num);
                obj.style.color = hex;
                banbie.style.color = hex;
            }
        }
    });


    /**
     * 监听输入框
     */
    $("#live").bind("input propertychange", function () {
        let live = $("#live").val();
        if (live.length <= 4) {
            banbie.innerHTML = live;
            banbie.style.fontSize = '40px';
            banbie.style.paddingTop = '5px';
        }
        if (live.length >= 5) {
            banbie.innerHTML = live;
            banbie.style.fontSize = '32px';
            banbie.style.paddingTop = '8px';
        }
        if (live.length >= 6) {
            banbie.innerHTML = live;
            banbie.style.fontSize = '27px';
            banbie.style.paddingTop = '10px';
        }
    });

    $("#live2").bind("input propertychange", function () {
        let live2 = $("#live2").val();
        if (live2.length <= 4) {
            banbie2.innerHTML = live2;
            banbie2.style.fontSize = '40px';
            banbie2.style.paddingTop = '5px';
        }
        if (live2.length >= 5) {
            banbie2.innerHTML = live2;
            banbie2.style.fontSize = '32px';
            banbie2.style.paddingTop = '8px';
        }
        if (live2.length >= 6) {
            banbie2.innerHTML = live2;
            banbie2.style.fontSize = '27px';
            banbie2.style.paddingTop = '10px';
        }
    });

    $("#live3").bind("input propertychange", function () {
        let live3 = $("#live3").val();
        if (live3.length <= 4) {
            banbie3.innerHTML = live3;
            banbie3.style.fontSize = '40px';
            banbie3.style.paddingTop = '5px';
        }
        if (live3.length >= 5) {
            banbie3.innerHTML = live3;
            banbie3.style.fontSize = '32px';
            banbie3.style.paddingTop = '8px';
        }
        if (live3.length >= 6) {
            banbie3.innerHTML = live3;
            banbie3.style.fontSize = '27px';
            banbie3.style.paddingTop = '10px';
        }
    });


    $("#live4").bind("input propertychange", function () {
        let live4 = $("#live4").val();
        if (live4.length <= 4) {
            banbie4.innerHTML = live4;
            banbie4.style.fontSize = '40px';
            banbie4.style.paddingTop = '5px';
        }
        if (live4.length >= 5) {
            banbie4.innerHTML = live4;
            banbie4.style.fontSize = '32px';
            banbie4.style.paddingTop = '8px';
        }
        if (live4.length >= 6) {
            banbie4.innerHTML = live4;
            banbie4.style.fontSize = '27px';
            banbie4.style.paddingTop = '10px';
        }
    });


    $("#live5").bind("input propertychange", function () {
        let live5 = $("#live5").val();
        if (live5.length <= 4) {
            banbie5.innerHTML = live5;
            banbie5.style.fontSize = '40px';
            banbie5.style.paddingTop = '5px';
        }
        if (live5.length >= 5) {
            banbie5.innerHTML = live5;
            banbie5.style.fontSize = '32px';
            banbie5.style.paddingTop = '8px';
        }
        if (live5.length >= 6) {
            banbie5.innerHTML = live5;
            banbie5.style.fontSize = '27px';
            banbie5.style.paddingTop = '10px';
        }
    });


    $("#live6").bind("input propertychange", function () {
        let live6 = $("#live6").val();
        if (live6.length <= 4) {
            banbie6.innerHTML = live6;
            banbie6.style.fontSize = '40px';
            banbie6.style.paddingTop = '5px';
        }
        if (live6.length >= 5) {
            banbie6.innerHTML = live6;
            banbie6.style.fontSize = '32px';
            banbie6.style.paddingTop = '8px';
        }
        if (live6.length >= 6) {
            banbie6.innerHTML = live6;
            banbie6.style.fontSize = '27px';
            banbie6.style.paddingTop = '10px';
        }
    });


    $("#live7").bind("input propertychange", function () {
        let live7 = $("#live7").val();
        if (live7.length <= 4) {
            banbie7.innerHTML = live7;
            banbie7.style.fontSize = '40px';
            banbie7.style.paddingTop = '5px';
        }
        if (live7.length >= 5) {
            banbie7.innerHTML = live7;
            banbie7.style.fontSize = '32px';
            banbie7.style.paddingTop = '8px';
        }
        if (live7.length >= 6) {
            banbie7.innerHTML = live7;
            banbie7.style.fontSize = '27px';
            banbie7.style.paddingTop = '10px';
        }
    });


    $("#live8").bind("input propertychange", function () {
        let live8 = $("#live8").val();
        if (live8.length <= 4) {
            banbie8.innerHTML = live8;
            banbie8.style.fontSize = '40px';
            banbie8.style.paddingTop = '5px';
        }
        if (live8.length >= 5) {
            banbie8.innerHTML = live8;
            banbie8.style.fontSize = '32px';
            banbie8.style.paddingTop = '8px';
        }
        if (live8.length >= 6) {
            banbie8.innerHTML = live8;
            banbie8.style.fontSize = '27px';
            banbie8.style.paddingTop = '10px';
        }
    });

    $("#live9").bind("input propertychange", function () {
        let live9 = $("#live9").val();
        if (live9.length <= 4) {
            banbie9.innerHTML = live9;
            banbie9.style.fontSize = '40px';
            banbie9.style.paddingTop = '5px';
        }
        if (live9.length >= 5) {
            banbie9.innerHTML = live9;
            banbie9.style.fontSize = '32px';
            banbie9.style.paddingTop = '8px';
        }
        if (live9.length >= 6) {
            banbie9.innerHTML = live9;
            banbie9.style.fontSize = '27px';
            banbie9.style.paddingTop = '10px';
        }
    });


    $("#live10").bind("input propertychange", function () {
        let live10 = $("#live10").val();
        if (live10.length <= 4) {
            banbie10.innerHTML = live10;
            banbie10.style.fontSize = '40px';
            banbie10.style.paddingTop = '5px';
        }
        if (live10.length >= 5) {
            banbie10.innerHTML = live10;
            banbie10.style.fontSize = '32px';
            banbie10.style.paddingTop = '8px';
        }
        if (live10.length >= 6) {
            banbie10.innerHTML = live10;
            banbie10.style.fontSize = '27px';
            banbie10.style.paddingTop = '10px';
        }
    });


    $("#live11").bind("input propertychange", function () {
        let live11 = $("#live11").val();
        if (live11.length <= 4) {
            banbie11.innerHTML = live11;
            banbie11.style.fontSize = '40px';
            banbie11.style.paddingTop = '5px';
        }
        if (live11.length >= 5) {
            banbie11.innerHTML = live11;
            banbie11.style.fontSize = '32px';
            banbie11.style.paddingTop = '8px';
        }
        if (live11.length >= 6) {
            banbie11.innerHTML = live11;
            banbie11.style.fontSize = '27px';
            banbie11.style.paddingTop = '10px';
        }
    });


    $("#live12").bind("input propertychange", function () {
        let live12 = $("#live12").val();
        if (live12.length <= 4) {
            banbie12.innerHTML = live12;
            banbie12.style.fontSize = '40px';
            banbie12.style.paddingTop = '5px';
        }
        if (live12.length >= 5) {
            banbie12.innerHTML = live12;
            banbie12.style.fontSize = '32px';
            banbie12.style.paddingTop = '8px';
        }
        if (live12.length >= 6) {
            banbie12.innerHTML = live12;
            banbie12.style.fontSize = '27px';
            banbie12.style.paddingTop = '10px';
        }
    });


    $("#live13").bind("input propertychange", function () {
        let live13 = $("#live13").val();
        if (live13.length <= 4) {
            banbie13.innerHTML = live13;
            banbie13.style.fontSize = '40px';
            banbie13.style.paddingTop = '5px';
        }
        if (live13.length >= 5) {
            banbie13.innerHTML = live13;
            banbie13.style.fontSize = '32px';
            banbie13.style.paddingTop = '8px';
        }
        if (live13.length >= 6) {
            banbie13.innerHTML = live13;
            banbie13.style.fontSize = '27px';
            banbie13.style.paddingTop = '10px';
        }
    });


    function toPrint() {
        $("#print-btn").css("display", "none");
        $("#choose").css("display", "none")
        $(".first").css({
            border: "none"
        });
        $(".other").css({
            border: "none"
        });
        $(".imgBoxA").css({
            border: "1px solid transparent"
        });
        $(".imgBoxB").css({
            border: "1px solid transparent"
        });
        window.print();
    }


    $('body').mouseRight({
        menu: [
            {
                itemName: "中乾850机-13行小标",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "18px");
                    $(".other1").attr("class", "other");
                    $("#switchGrade").attr("class", "first1");
                    $("#switchImgBox1").attr("class", "imgBoxA");
                    $("#switchImgBox2").attr("class", "imgBoxB");
                    $("#switchImgBox3").attr("class", "imgBoxB");
                    $("#switchImgBox4").attr("class", "imgBoxB");
                    $("#switchImgBox5").attr("class", "imgBoxB");
                    $("#switchImgBox6").attr("class", "imgBoxB");
                    $("#switchImgBox7").attr("class", "imgBoxB");
                    $("#switchImgBox8").attr("class", "imgBoxB");
                    $("#switchImgBox9").attr("class", "imgBoxB");
                    $("#switchImgBox10").attr("class", "imgBoxB");
                    $("#switchImgBox11").attr("class", "imgBoxB");
                    $("#switchImgBox12").attr("class", "imgBoxB");
                    $("#switchImgBox13").attr("class", "imgBoxB");
                    blockFont();
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 13; i++) {
                        if (i === 1) {
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                $(templateId).attr("class", "test_top2");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).attr("class", "banbie2222");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test = $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g, "$&\x01").replace(/.{9}\x01?/g, "$&<br>").replace(/\x01/g, "");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top2");
                            } else {
                                $(templateId).attr("class", "banbie2222");
                            }
                            livesId = livesId.slice(0, 5);
                            templateId = templateId.slice(0, 7);
                        }

                }
            }}, {
                itemName: "中乾8160机-13行小标",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "19px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox2").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox3").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox4").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox5").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox6").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox7").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox8").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox9").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox10").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox11").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox12").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox13").attr("class", "imgBoxBaoB2222");
                    var template = $("#banbie").attr("id");
                    var lives = $("#live").attr("id");
                    var livesId = "#" + lives;
                    var templateId = "#" + template;
                    for (var i = 1; i <= 13; i++) {
                        if ( i === 1) {
                          var test =  $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g,"$&\x01").replace(/.{9}\x01?/g,"$&<br>").replace(/\x01/g,"");
                                $(templateId).css("fontSize" , '20px');
                                $(templateId).attr("class", "test_top");
                                // $(templateId).html(newStr);
                            } else {
                                $(templateId).attr("class", "banbie3333");
                            }
                        } else {
                            livesId += i;
                            templateId += i;
                            var test =  $(livesId).val();
                            if (test.length > 5) {
                                var h1Text = document.querySelector(templateId).textContent;
                                var newStr = h1Text.replace(/[^\x00-\xff]/g,"$&\x01").replace(/.{9}\x01?/g,"$&<br>").replace(/\x01/g,"");
                                // $(templateId).html(newStr);
                                $(templateId).attr("class", "test_top");
                                $(templateId).css("fontSize" , '20px');
                            } else {
                                $(templateId).attr("class", "banbie3333");
                            }
                            livesId=livesId.slice(0,5);
                            templateId=templateId.slice(0,7);
                        }

                    }
                    blockFont();
                }
            },
            {
                itemName: "宝鑫模板-粮票模板",
                callback: function () {
                    $("h1").css("font-weight", "bold");
                    $("h1").css("font-family", "青鸟华光简行楷");
                    $("h1").css("font-size", "19px");
                    $("#switchGrade").attr("class", "other");
                    $(".other1").attr("class", "other");
                    $("#switchImgBox1").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox2").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox3").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox4").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox5").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox6").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox7").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox8").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox9").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox10").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox11").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox12").attr("class", "imgBoxBaoB2222");
                    $("#switchImgBox13").attr("class", "imgBoxBaoB2222");
                    $("#banbie").attr("class", "banbie2222");
                    $("#banbie2").attr("class", "banbie2222");
                    $("#banbie3").attr("class", "banbie2222");
                    $("#banbie4").attr("class", "banbie2222");
                    $("#banbie5").attr("class", "banbie2222");
                    $("#banbie6").attr("class", "banbie2222");
                    $("#banbie7").attr("class", "banbie2222");
                    $("#banbie8").attr("class", "banbie2222");
                    $("#banbie9").attr("class", "banbie2222");
                    $("#banbie10").attr("class", "banbie2222");
                    $("#banbie11").attr("class", "banbie2222");
                    $("#banbie12").attr("class", "banbie2222");
                    $("#banbie13").attr("class", "banbie2222");
                    blockFont();

                }
            },

            {
                itemName: "下移",
                callback: function () {

                    let thisSwitchGrade = $("#switchGrade");//
                    thisSwitchGrade.css("margin-top", "1cm");

                }
            },

            {
                itemName: "上移",
                callback: function () {
                    let thisSwitchGrade = $("#switchGrade");//
                    thisSwitchGrade.css("margin-top", "0cm");

                }
            },


            {
                itemName: "重来",
                callback: function () {
                    $("#print-btn").css("display", "block");
                    $("#choose").css("display", "block");
                    $("#tips").css("display", "block");
                    $(".first").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".first1").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".firstJb").css({
                        border: "1px solid cornflowerblue"
                    });
                    $(".other").css({
                        "border-left": '1px solid grey',
                        "border-bottom": '1px solid grey',
                        "border-right": '1px solid grey'
                    });
                    $(".other1").css({
                        "border-left": '1px solid grey',
                        "border-bottom": '1px solid grey',
                        "border-right": '1px solid grey'
                    });

                    $(".imgBoxA1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxB1").css({
                        border: "1px solid lightseagreen"
                    });

                    $(".imgBoxA").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBao").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoA").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoY").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1M").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA1M1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoYX").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoYX1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxAJB1").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxAJB").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBaoB2222").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxBao111").css({
                        border: "1px solid lightseagreen"
                    });
                    $(".imgBoxA2222").css({
                        border: "1px solid lightseagreen"
                    });
                }
            },
            {
                itemName: "打印",
                callback: function () {
                    $("#print-btn").css("display", "none");
                    $("#choose").css("display", "none");
                    $("#tips").css("display", "none");
                    $(".first").css({
                        border: "none"
                    });
                    $(".first1").css({
                        border: "none"
                    });
                    $(".firstJb").css({
                        border: "none"
                    });
                    $(".other").css({
                        border: "none"
                    });
                    $(".other1").css({
                        border: "none"
                    });
                    $(".imgBoxBao").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoA").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoY").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxB1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1M").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA1M1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoYX").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoYX1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxAJB1").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxAJB").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBaoB2222").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxBao111").css({
                        border: "1px solid transparent"
                    });
                    $(".imgBoxA2222").css({
                        border: "1px solid transparent"
                    });
                    setTimeout(function () {
                        window.print();
                    }, 100);

                }
            },
        ]
    });

    // 给按钮绑定点击事件 适应7字
    $("#adapt1").click(function (event) {
        let live = $("#live").val();
        banbie.innerHTML = live;
        banbie.style.fontSize = '23px';
        banbie.style.paddingTop = '10px';
    });

    $("#adapt2").click(function (event) {
        let live2 = $("#live2").val();
        banbie2.innerHTML = live2;
        banbie2.style.fontSize = '23px';
        banbie2.style.paddingTop = '10px';
    });

    $("#adapt3").click(function (event) {
        let live3 = $("#live3").val();
        banbie3.innerHTML = live3;
        banbie3.style.fontSize = '23px';
        banbie3.style.paddingTop = '10px';
    });

    $("#adapt4").click(function (event) {
        let live4 = $("#live4").val();
        banbie4.innerHTML = live4;
        banbie4.style.fontSize = '23px';
        banbie4.style.paddingTop = '10px';
    });

    $("#adapt5").click(function (event) {
        let live5 = $("#live5").val();
        banbie5.innerHTML = live5;
        banbie5.style.fontSize = '23px';
        banbie5.style.paddingTop = '10px';
    });
    $("#adapt6").click(function (event) {
        let live6 = $("#live6").val();
        banbie6.innerHTML = live6;
        banbie6.style.fontSize = '23px';
        banbie6.style.paddingTop = '10px';
    });
    $("#adapt7").click(function (event) {
        let live7 = $("#live7").val();
        banbie7.innerHTML = live7;
        banbie7.style.fontSize = '23px';
        banbie7.style.paddingTop = '10px';
    });
    $("#adapt8").click(function (event) {
        let live8 = $("#live8").val();
        banbie8.innerHTML = live8;
        banbie8.style.fontSize = '23px';
        banbie8.style.paddingTop = '10px';
    });
    $("#adapt9").click(function (event) {
        let live9 = $("#live9").val();
        banbie9.innerHTML = live9;
        banbie9.style.fontSize = '23px';
        banbie9.style.paddingTop = '10px';
    });
    $("#adapt10").click(function (event) {
        let live10 = $("#live10").val();
        banbie10.innerHTML = live10;
        banbie10.style.fontSize = '23px';
        banbie10.style.paddingTop = '10px';
    });

    // 给按钮绑定点击事件 适应8字
    $("#adapt1_8").click(function (event) {
        let live = $("#live").val();
        banbie.innerHTML = live;
        banbie.style.fontSize = '20px';
        banbie.style.paddingTop = '14px';
    });
    $("#adapt2_8").click(function (event) {
        let live2 = $("#live2").val();
        banbie2.innerHTML = live2;
        banbie2.style.fontSize = '20px';
        banbie2.style.paddingTop = '14px';
    });

    $("#adapt3_8").click(function (event) {
        let live3 = $("#live3").val();
        banbie3.innerHTML = live3;
        banbie3.style.fontSize = '20px';
        banbie3.style.paddingTop = '14px';
    });

    $("#adapt4_8").click(function (event) {
        let live4 = $("#live4").val();
        banbie4.innerHTML = live4;
        banbie4.style.fontSize = '20px';
        banbie4.style.paddingTop = '14px';
    });

    $("#adapt5_8").click(function (event) {
        let live5 = $("#live5").val();
        banbie5.innerHTML = live5;
        banbie5.style.fontSize = '20px';
        banbie5.style.paddingTop = '14px';
    });
    $("#adapt6_8").click(function (event) {
        let live6 = $("#live6").val();
        banbie6.innerHTML = live6;
        banbie6.style.fontSize = '20px';
        banbie6.style.paddingTop = '14px';
    });
    $("#adapt7_8").click(function (event) {
        let live7 = $("#live7").val();
        banbie7.innerHTML = live7;
        banbie7.style.fontSize = '20px';
        banbie7.style.paddingTop = '14px';
    });
    $("#adapt8_8").click(function (event) {
        let live8 = $("#live8").val();
        banbie8.innerHTML = live8;
        banbie8.style.fontSize = '20px';
        banbie8.style.paddingTop = '14px';
    });
    $("#adapt9_8").click(function (event) {
        let live9 = $("#live9").val();
        banbie9.innerHTML = live9;
        banbie9.style.fontSize = '20px';
        banbie9.style.paddingTop = '14px';
    });
    $("#adapt10_8").click(function (event) {
        let live10 = $("#live10").val();
        banbie10.innerHTML = live10;
        banbie10.style.fontSize = '20px';
        banbie10.style.paddingTop = '14px';
    });


    function minFont(number) {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        // let minFont=$("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        let thisFont = $("#banbie" + (number == 1 ? "" : number));//
        let minFont = thisFont;

        if (ok != 0) {
            let minFontAll = $(".other h1");//比较是那个模板对应修改参数

            let fontSize = parseInt(minFont.css("font-size").substring(0, (minFont.css("font-size").length - 2))) - 1;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");
        }

        if (ok1 != 0) {
            let minFontAll = $(".other1 h1");//比较是那个模板对应修改参数

            let fontSize = parseInt(minFont.css("font-size").substring(0, (minFont.css("font-size").length - 2))) - 1;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");
        }


    }

    function maxFont(number) {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        // let maxFont=$("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        let thisFont = $("#banbie" + (number == 1 ? "" : number));//

        let maxFont = thisFont;

        if (ok != 0) {

            let maxFontAll = $(".other h1");//
            let fontSize = parseInt(maxFont.css("font-size").substring(0, (maxFont.css("font-size").length - 2))) + 1;

            // maxFont.css("font-size",fontSize+"px");
            // maxFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");

        }

        if (ok1 != 0) {

            let maxFontAll = $(".other1 h1");//
            let fontSize = parseInt(maxFont.css("font-size").substring(0, (maxFont.css("font-size").length - 2))) + 1;

            // maxFont.css("font-size",fontSize+"px");
            // maxFontAll.css("font-size",fontSize+"px");
            thisFont.css("font-size", fontSize + "px");

        }


    }
    function changeTop(number) {


        var thisFontSize = $("#banbie" + (number == 1 ? "" : number)).css("font-size");

        var nameLen = $("#live" + (number == 1 ? "" : number)).val();
        nameLen = nameLen.length;

        if (nameLen == 6 && parseInt(thisFontSize) == 28) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");

        } else if (nameLen == 7 && parseInt(thisFontSize) == 24) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");

        } else if (nameLen == 8 && parseInt(thisFontSize) == 21) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");
        }else if (nameLen == 9 && parseInt(thisFontSize) == 18) {
            $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "18px");
        }else{
            if( parseInt(thisFontSize) == 32){
                $("#banbie" + (number == 1 ? "" : number)).css("margin-top", "8px");
            }
        }

    }
    function blockFont() {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        let blockFont = $("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        if (ok != 0) {

            let blockFontAll = $(".other h1");//
            let weight = blockFont.css("font-weight");
            if ("600" > weight) {
                blockFont.css("font-weight", "700");
                blockFontAll.css("font-weight", "700");
            } else {
                blockFont.css("font-weight", "100");
                blockFontAll.css("font-weight", "100");
            }
        }

        if (ok1 != 0) {

            let blockFontAll = $(".other1 h1");//
            let weight = blockFont.css("font-weight");
            if ("600" > weight) {
                blockFont.css("font-weight", "700");
                blockFontAll.css("font-weight", "700");
            } else {
                blockFont.css("font-weight", "100");
                blockFontAll.css("font-weight", "100");
            }


        }


    }

    function familyFont(number) {//改变位置
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        let familyFont = $("#switchGrade h1");//首个


        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        if (ok != 0) {

            let familyFontAll = $(".other h1");//
            let family = "青鸟华光简行楷";//字体

            family = $("#familyFont" + number).val();//字体

            familyFont.css("font-family", family);
            familyFontAll.css("font-family", family);


        }

        if (ok1 != 0) {
            let familyFontAll = $(".other1 h1");//
            let family = "青鸟华光简行楷";//字体

            family = $("#familyFont" + number).val();//字体

            familyFont.css("font-family", family);
            familyFontAll.css("font-family", family);

        }

    }


    function adaptiveFont(number) {
        //  $("h1").css("font-weight","bold");
        // $("h1").css("font-family","青鸟华光简行楷");
        // $("h1").css("font-size","32px");
        // $("#switchGrade").attr("class", "other");
        let adaptiveFont = $("#switchGrade h1");//首个

        let ok = $(".other h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整
        let ok1 = $(".other1 h1").length;//比较是那个模板对应修改参数 目前有两个后续需要调整

        if (ok != 0) {

            let adaptiveFontAll = $(".other h1");//

            let nameLen = $("#live" + (number == 1 ? "" : number)).val();

            let thisFont = $("#banbie" + (number == 1 ? "" : number));//

            nameLen = nameLen.length;
            if (nameLen <= 3) {
                // adaptiveFont.css("font-size","40px");
                // adaptiveFontAll.css("font-size","40px");
                thisFont.css("font-size", "40px");
            } else if (nameLen == 6) {
                // adaptiveFont.css("font-size","32px");
                // adaptiveFontAll.css("font-size","32px");
                thisFont.css("font-size", "28px");
            } else if (nameLen == 7) {
                // adaptiveFont.css("font-size","23px");
                // adaptiveFontAll.css("font-size","23px");
                thisFont.css("font-size", "24px");
            } else if (nameLen == 8) {
                // adaptiveFont.css("font-size","20px");
                // adaptiveFontAll.css("font-size","20px");
                thisFont.css("font-size", "21px");
            } else if (nameLen <= 10) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "18px");
            } else if (nameLen <= 11) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "25px");
            }


        }

        if (ok1 != 0) {

            let adaptiveFontAll = $(".other1 h1");//
            let nameLen = $("#live" + (number == 1 ? "" : number)).val();

            let thisFont = $("#banbie" + (number == 1 ? "" : number));//

            nameLen = nameLen.length;

            if (nameLen <= 3) {
                // adaptiveFont.css("font-size","40px");
                // adaptiveFontAll.css("font-size","40px");
                thisFont.css("font-size", "40px");
            } else if (nameLen == 6) {
                // adaptiveFont.css("font-size","32px");
                // adaptiveFontAll.css("font-size","32px");
                thisFont.css("font-size", "28px");
            } else if (nameLen == 7) {
                // adaptiveFont.css("font-size","23px");
                // adaptiveFontAll.css("font-size","23px");
                thisFont.css("font-size", "24px");
            } else if (nameLen == 8) {
                // adaptiveFont.css("font-size","20px");
                // adaptiveFontAll.css("font-size","20px");
                thisFont.css("font-size", "21px");
            } else if (nameLen <= 10) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "18px");
            } else if (nameLen <= 11) {
                // adaptiveFont.css("font-size","18px");
                // adaptiveFontAll.css("font-size","18px");
                thisFont.css("font-size", "25px");
            }

        }


    }


    function allFont(number) {


        let arr = [];
        for (let k = 1; k < 14; k++) {
            let val = $("#lab" + k + " span").css("border");
            if (val == "1px solid rgb(0, 0, 0)") {
                arr.push(k);
            }
            // console.log(k);
        }
        // console.log(arr);//目前选择的具体位置


        if (number == 1000) {//全选
            for (let k = 1; k < 14; k++) {
                let val = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");
            }

        } else if (number == 2000) {//返选

            for (let k = 1; k < 14; k++) {
                let val = $("#lab" + (k) + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {//选中
                    let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
                } else {
                    let val = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");
                }

            }

        } else if (number == 3000) {//取消
            for (let k = 1; k < 14; k++) {
                let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
            }
        } else {//单击
            let val = $("#lab" + (number + 1) + " span").css("border");
            if (val == "1px solid rgb(0, 0, 0)") {//选中
                let val = $("#lab" + (number + 1) + " span").css("border", "0px none rgb(0, 0, 0)");
            } else {
                let val = $("#lab" + (number + 1) + " span").css("border", "1px solid rgb(0, 0, 0)");
            }

        }


    }


    function allFont1(number) {


        let val = $("#allFont" + (number + 1)).val();
        if ("全选" == val) {
            for (let k = 1; k < 14; k++) {
                let val = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");
            }
        }
        if ("反选" == val) {
            for (let k = 1; k < 14; k++) {
                let val = $("#lab" + (k) + " span").css("border");
                if (val == "1px solid rgb(0, 0, 0)") {//选中
                    let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
                } else {
                    let val = $("#lab" + (k) + " span").css("border", "1px solid rgb(0, 0, 0)");
                }

            }
        }
        if ("取消" == val) {
            for (let k = 1; k < 14; k++) {
                let val = $("#lab" + (k) + " span").css("border", "0px none rgb(0, 0, 0)");
            }
        }

    }
    let initialMarginRight = 0; // 初始 margin-right 值
    function moveRight() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");

            if (initialMarginRight === 0) {
                // 记录初始 margin-right 值
                initialMarginRight = parseInt(thisFont.css("margin-right").substring(0, (thisFont.css("margin-right").length - 2)));
            }

            // 累积增加 margin-right 值
            initialMarginRight -= 1;

            thisFont.css("margin-right", initialMarginRight + "px");
        }
    }

    function moveLeft() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");
            let marginLeft = parseInt(thisFont.css("margin-left").substring(0, (thisFont.css("margin-left").length -
                2))) - 5;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("margin-left", marginLeft + "px");

        }
    }

    let initialPaddingTop = 0; // 初始 margin-right 值
    function moveDown() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");
            let marginLeft = parseInt(thisFont.css("padding-top").substring(0, (thisFont.css("padding-top").length -
                2))) + 5;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("padding-top", marginLeft + "px");

        }
    }

    function moveUp() {
        for (let i = 1; i < 14; i++) {
            let thisFont = $("#switchImgBox" + i + " h1");
            let marginLeft = parseInt(thisFont.css("padding-top").substring(0, (thisFont.css("padding-top").length -
                2))) - 5;

            // minFont.css("font-size",fontSize+"px");
            // minFontAll.css("font-size",fontSize+"px");
            thisFont.css("padding-top", marginLeft + "px");

        }
    }
</script>
</body>
</html>
