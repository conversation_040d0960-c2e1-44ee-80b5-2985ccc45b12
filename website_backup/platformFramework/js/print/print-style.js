var PrintBuild = function() {

    // var lodop = getLodop(document.getElementById('LODOP1'), document.getElementById('LODOP_EM1'));
    this.whiteCoin = function (rowData) {
        // if (lodop == null) {
        //     return;
        // }
        // lodop.PRINT_INITA(0,0, 42, 21,"钱币打印_" + rowData.nummber);
        // lodop.ADD_PRINT_BARCODE(0, 0, 206, 78, "128B", rowData.nummber);
        // lodop.SET_PRINT_STYLEA(0,"FontSize", 12);
        // lodop.PRINT();
    }
    
    this.buildCoin = function (ids, option,conversion) {
        // alert(1111)
        loadPrintPlugin(ids, option.label, option.service,conversion.value);
    }
    this.buildBaiCoin = function (ids,conversion) {
        loadPrintPlugin(ids, 'JiaHeBai/baibiao.grf', 'Bai',conversion.value);
    }
    this.bhhsljCoin = function (ids) {
        loadPrintPlugin(ids, 'label-big-n.grf', 'getBhhsljCoin');
        print();
    }

    this.flaseLabel = function (ids) {

        loadPrintPlugin(ids, 'label-false.grf', 'getFalseCoin');

    }
    this.verticalLabel = function (ids) {
        loadPrintPlugin(ids, 'label-vertical.grf', 'getVerticalCoin');

    }
    this.whiteLabel = function (ids) {
        // loadPrintPlugin(ids, 'label-jizhi-s-t.grf', 'getVerticalCoin');
        // loadPrintPlugin(ids, 'label-jizhi-s.grf', 'getVerticalCoin');
        // loadPrintPlugin(ids, 'label-jizhi-b-t.grf', 'getVerticalCoin');
        // loadPrintPlugin(ids, 'label-yinding-b.grf', 'getVerticalCoin');
        // loadPrintPlugin(ids, 'label-jizhi-b.grf', 'getVerticalCoin');
        loadPrintPlugin(ids, 'JiaHeBai/baibiao.grf', 'Bai',0);
        // loadPrintPlugin(ids, 'label-big.grf', 'getVerticalCoin');
    }

    this.print = function() {
        print();
    }

    function print() {
        var ReportViewer = document.getElementById("ReportViewer");
        ReportViewer.ShowPrintDlg = false; //禁止控件内部显示打印对话框
        Report = ReportViewer.Report;
        // Report.Language = '2052';
        /*Report.Printer.PrinterName = "<%=printer%>";*/
        if (Report == null || Report.Print == null) {
            confirm('找不到打印插件，请确认是否已经安装；插件仅支持IE内核浏览器，是否下载插件？', function () {
                location.href= '../js/print/grbsctl6.exe';
            });
            return;
        }
        Report.Print(true);
    }


    function loadPrintPlugin(ids, label, service,conversion) {
        var idsParam = ids.join(',');
        // alert(label)
        var html = CreatePrintViewerEx("100%", "800px", "../js/print/" + label + "?" + "_=" + new Date().getTime(), "../printData/" + service + "?ids=" + idsParam + "&_=" + new Date().getTime()+"&conversion="+conversion, true, "");
        // var html = CreatePrintViewerEx("100%", "800px", "../js/print/" + label + "?" + "_=" + new Date().getTime(), "http://www.gridreport.cn/demos/data/DataCenter.ashx?data=SaleDetail", true, "");
        $('#ReportViewerBox').html(html);
    }
}







