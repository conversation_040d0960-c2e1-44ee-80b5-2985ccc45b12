{"Version": "*******", "Title": "2a.基本分组", "Author": "锐浪报表软件", "Description": "演示一个最一般的分组报表:首先定义一个分组，在报表布局窗口中会自动加入分组头与分组尾。通过设定分组对象的‘依据字段(ByFields)’属性，指定分组生成的依据字段。在分组头与分组尾中加入相应的部件框，实现分组信息的显示。使用统计框或Memo框实现数据的统计分析。", "Font": {"Name": "宋体", "Size": 90000, "Weight": 400, "Charset": 134}, "Printer": {"Size": 256, "LeftMargin": 1.5, "TopMargin": 0, "RightMargin": 1, "BottomMargin": 0}, "DetailGrid": {"CenterView": true, "Border": {"Styles": "[]", "ShadowWidth": 0, "ShadowColor": "FFFFFF", "Pen": {"Width": 0, "Color": "FFFFFF"}, "InnerPen": {"Width": 0, "Color": "FFFFFF"}}, "ColLine": {"Width": 0, "Color": "646F71"}, "RowLine": {"Width": 0, "Color": "646F71"}, "Recordset": {"ConnectionString": "Provider=Microsoft.Jet.OLEDB.4.0;\r\nUser ID=Admin;\r\nData Source=C:\\Grid++Report 6\\Samples\\Data\\Northwind.mdb", "QuerySQL": "select m.OrderID,m.<PERSON>,c<PERSON>,m.<PERSON>,<PERSON><PERSON>,\r\nd.<PERSON>ID,p.ProductName,d.UnitPrice,d.Quantity,d.Discount,\r\nd.UnitPrice*d.Quantity as Amount, Amount*d.Discount as DiscountAmt, Amount-DiscountAmt as NetAmount\r\nfrom (Orders m inner join \r\n(OrderDetails as d inner join Products p on P.ProductID=D.ProductID) on m.OrderId=d.OrderId)\r\nleft join Customers c on c.CustomerID=m.CustomerID\r\nwhere m.OrderID<=10300\r\norder by m.OrderDate, m.OrderID", "Field": [{"Name": "OrderID", "Type": "Integer"}, {"Name": "CustomerID"}, {"Name": "CompanyName"}, {"Name": "OrderDate", "Type": "DateTime", "Format": "yyyy年MM月dd日"}, {"Name": "Freight", "Type": "Float", "Format": "#,##0.00"}, {"Name": "ProductID", "Type": "Integer"}, {"Name": "ProductName"}, {"Name": "UnitPrice", "Type": "Float", "Format": "#,##0.##"}, {"Name": "Quantity", "Type": "Integer"}, {"Name": "Discount", "Type": "Float", "Format": "0.00%"}, {"Name": "Amount", "Type": "Float", "Format": "#,##0.00"}, {"Name": "DiscountAmt", "Type": "Float", "Format": "#,##0.00"}, {"Name": "NetAmount", "Type": "Float", "Format": "#,##0.00"}, {"Name": "number"}, {"Name": "dafen"}, {"Name": "star"}, {"Name": "Link"}, {"Name": "score"}, {"Name": "bank"}, {"Name": "pingxiang"}, {"Name": "name1"}, {"Name": "guanhao"}, {"Name": "score_1"}, {"Name": "pingxiang_1"}, {"Name": "dafen_1"}, {"Name": "pingxiang_2"}, {"Name": "score_2"}, {"Name": "genuine_2"}, {"Name": "genuine_1"}, {"Name": "genuine"}, {"Name": "genuine_zh"}, {"Name": "live"}]}, "Column": [{"Name": "UnitPriceaaa", "Width": 2.77813}, {"Name": "UnitPrice", "Width": 1.98438}, {"Name": "Quantity", "Width": 1.5875}, {"Name": "Discount", "Width": 1.79917}, {"Name": "Amount", "Width": 2.59292}, {"Name": "DisCountAmt", "Width": 13.2292}], "ColumnContent": {"Height": 0, "ColumnContentCell": [{"Column": "UnitPriceaaa", "DataField": "ProductName"}, {"Column": "UnitPrice", "TextAlign": "MiddleRight", "DataField": "UnitPrice"}, {"Column": "Quantity", "TextAlign": "MiddleRight", "DataField": "Quantity"}, {"Column": "Discount", "TextAlign": "MiddleRight", "DataField": "Discount"}, {"Column": "Amount", "TextAlign": "MiddleRight", "DataField": "Amount"}, {"Column": "DisCountAmt", "TextAlign": "MiddleRight", "DataField": "DiscountAmt"}]}, "ColumnTitle": {"Height": 2, "RepeatStyle": "OnPage", "ColumnTitleCell": [{"GroupTitle": false, "Column": "UnitPriceaaa"}, {"GroupTitle": false, "Column": "UnitPrice"}, {"GroupTitle": false, "Column": "Quantity"}, {"GroupTitle": false, "Column": "Discount"}, {"GroupTitle": false, "Column": "Amount"}, {"GroupTitle": false, "Column": "DisCountAmt"}]}, "Group": [{"Name": "Group1", "ByFields": "OrderID", "GroupHeader": {"Height": 2.5, "Font": {"Name": "宋体", "Size": 112500, "Weight": 400, "Charset": 134}, "Control": [{"Type": "Barcode", "Name": "Barcode1", "Left": 16.4042, "Top": 0.238125, "Width": 1.5, "Height": 1.55, "Border": {"Pen": {"Width": 0}}, "BarcodeType": "QRCode", "CaptionPosition": "None", "Text": "[#Link#]"}, {"Type": "FieldBox", "Name": "number", "Left": 16.0867, "Top": 1.71979, "Width": 2.11667, "Height": 0.4, "Font": {"Name": "Times New Roman", "Size": 82500, "Bold": true}, "TextAlign": "BottomCenter", "DataField": "number"}, {"Type": "FieldBox", "Name": "dafen", "Left": 15.2929, "Top": 0.105833, "Width": 0.3175, "Height": 1.40229, "Font": {"Name": "<PERSON><PERSON>", "Size": 82500, "Bold": true}, "WordWrap": true, "TextAlign": "BottomCenter", "DataField": "dafen"}, {"Type": "FieldBox", "Name": "score", "Left": 13.8906, "Width": 1.61, "Height": 1.69333, "Font": {"Name": "Gazette LT Std", "Size": 427500, "Bold": true}, "TextAlign": "BottomCenter", "DataField": "score"}, {"Type": "FieldBox", "Name": "pingxiang", "Left": 13.0969, "Top": 1.34938, "Width": 4.39208, "Height": 0.4, "Font": {"Name": "<PERSON><PERSON>", "Size": 90000, "Bold": true}, "TextAlign": "BottomCenter", "DataField": "pingxiang"}, {"Type": "FieldBox", "Name": "bank", "Left": 3.99521, "Top": 0.211667, "Width": 8.38729, "Height": 0.79375, "Font": {"Name": "<PERSON><PERSON>", "Size": 142500, "Bold": true}, "DataField": "bank"}, {"Type": "FieldBox", "Name": "name1", "Left": 4.1275, "Top": 1.01, "Width": 8.30792, "Height": 0.58, "Font": {"Name": "Times New Roman", "Size": 105000, "Bold": true}, "DataField": "name1"}, {"Type": "FieldBox", "Name": "star", "Left": 15.531, "Top": 0.132292, "Width": 1.00542, "Height": 1.61396, "Font": {"Name": "Times New Roman", "Size": 262500, "Bold": true}, "DataField": "star"}, {"Type": "FieldBox", "Name": "dafen_1", "Left": 15.9808, "Top": 0.105833, "Width": 0.3175, "Height": 1.40229, "Font": {"Name": "<PERSON><PERSON>", "Size": 82500, "Bold": true}, "WordWrap": true, "TextAlign": "BottomCenter", "DataField": "dafen_1"}, {"Type": "FieldBox", "Name": "score_1", "Left": 13.8906, "Width": 1.61, "Height": 1.69333, "Font": {"Name": "Gazette LT Std", "Size": 390000, "Bold": true}, "TextAlign": "BottomCenter", "DataField": "score_1"}, {"Type": "FieldBox", "Name": "pingxiang_1", "Left": 13.0969, "Top": 1.34938, "Width": 3.20146, "Height": 0.4, "Font": {"Name": "Times New Roman", "Size": 90000, "Bold": true}, "TextAlign": "BottomCenter", "DataField": "pingxiang_1"}, {"Type": "FieldBox", "Name": "pingxiang_2", "Left": 12.6735, "Top": 1.34938, "Width": 4.57729, "Height": 0.4, "Font": {"Name": "Times New Roman", "Size": 90000, "Bold": true}, "TextAlign": "BottomCenter", "DataField": "pingxiang_2"}, {"Type": "FieldBox", "Name": "score_2", "Left": 14.1817, "Top": -0.0264583, "Width": 1.5875, "Height": 1.56104, "Font": {"Name": "Gazette LT Std", "Size": 390000, "Bold": true}, "TextAlign": "BottomCenter", "DataField": "score_2"}, {"Type": "FieldBox", "Name": "genuine_2", "Left": 13.2027, "Top": 0.687917, "Width": 3.20146, "Height": 1.19063, "Font": {"Name": "Gazette LT Std", "Size": 180000, "Bold": true}, "TextAlign": "MiddleCenter", "DataField": "genuine_2"}, {"Type": "FieldBox", "Name": "genuine", "Left": 12.0121, "Top": 0.3175, "Width": 3.20146, "Height": 1.19063, "Font": {"Name": "Gazette LT Std", "Size": 180000, "Bold": true}, "TextAlign": "MiddleCenter", "DataField": "genuine"}, {"Type": "FieldBox", "Name": "genuine_zh", "ShiftMode": "Never", "Left": 13.1498, "Top": -0.0264583, "Width": 3.20146, "Height": 1.08479, "Font": {"Name": "Gazette LT Std", "Size": 217500, "Bold": true}, "TextAlign": "MiddleCenter", "DataField": "genuine_zh"}, {"Type": "FieldBox", "Name": "genuine_1", "Left": 12.3825, "Top": 0.264583, "Width": 3.20146, "Height": 1.19063, "Font": {"Name": "Gazette LT Std", "Size": 180000, "Bold": true}, "TextAlign": "MiddleCenter", "DataField": "genuine_1"}, {"Type": "FieldBox", "Name": "guanhao", "Left": 4.1275, "Top": 1.6, "Width": 8.30792, "Height": 0.608542, "Font": {"Name": "Times New Roman", "Size": 105000, "Bold": true}, "DataField": "guanhao"}, {"Type": "FieldBox", "Name": "flag", "ForeColor": "0099CC", "Left": 13.7583, "Top": 1.64042, "Width": 3.2, "Height": 0.5, "DataField": "CompanyName"}]}, "GroupFooter": {"Height": 0.1, "Font": {"Name": "宋体", "Size": 90000, "Bold": true, "Charset": 134}}}]}, "PageHeader": {"Height": 0.3}, "PageFooter": {"Height": 7.01146}, "ReportHeader": [{"Height": 0, "Control": [{"Type": "StaticBox", "Name": "TitleBox", "Center": "Horizontal", "Left": 9.15458, "Top": 0.396875, "Width": 5.63563, "Height": 0.582083, "Font": {"Name": "宋体", "Size": 150000, "Bold": true, "Charset": 134}, "TextAlign": "MiddleCenter", "Text": "按单统计销售明细报表"}]}]}