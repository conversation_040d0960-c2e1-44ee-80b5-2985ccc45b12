$(function () {
	$("#jqGrid").Grid({
		url: '../pjosendform/queryCoinList',
		postData: {
			'cointype': "01"
		},
		colModel: [
			{label: 'id', name: 'id', hidden: true},
			{
				label: '鉴定板块', width: 50, align: 'center',formatter: function (value) {
						return "<a style=\"color: crimson;\">纸币</a>";
				}
			},
			{label: '送评单', name: 'sendnum',key: true, align: 'center',width: 50},
			// {label: '钱币数量', name: 'coinCount', align: 'center', width: 30},
			{label: '真实姓名', name: 'rname', align: 'center', width: 50},
			{label: '网名', name: 'nickname', align: 'center', width: 50},
			{
				label: '创建时间', name: 'inupttime',  width: 60,align: 'center', formatter: function (value) {
					return transDate(value);
				}
			},
			{
				label: '评单审核状态', name: 'checkStatus',  width: 50, align: 'center',formatter: function (value) {
					if(value==1){
						return "<a style=\"color: green;\">✔</a>";
					}else{
						return "<a style=\"color: red;\">X</a>";
					}
				}
			},
		],
	});
});
function  checkNUll(value) {
	if(value!=null&&value!=""&&value!="无"){
		return true;
	}
	return false;
}
function closeEdit(){
	vm.showList = true;
	vm.ChangeState = false;
}
let vm = new Vue({
	el: '#vue-table',
	data: {
		showList: true,
		ChangeState: false,
		frameLink:null,
		info: false,
		addSendform:false,
		title: null,
		pjOSendform: {},
		statePjOSendform: {},
		itemList: {},
		activeTab:0,
		projects: [],
		project: {
			isPack: 1,
			packType: 1
		},
		checkList: [
			{value: "1", name: 'yes'},
			{value: "0", name: 'no'},
		],
		statusList: [
			{value: "0", name: '待寄入'},
			{value: "1", name: '快递邮寄'},
			{value: "2", name: '评级中'},
			{value: "3", name: '待支付'},
			{value: "4", name: '评级完成'},
			{value: "5", name: '已寄出'},
			{value: "6", name: '待退费'}
		],
		packTypeList: [
			{id: 1, name: '密封盒'},
			{id: 2, name: '开放盒'},
			{id: 3, name: '证书盒'}
		],
		isPackList: [
			{id: 1, name: '是'},
			{id: 2, name: '否'}
		],
		ruleValidate: {
			name: [
				{required: true, message: '名称不能为空', trigger: 'blur'},
			],
			nummber:[
				{required: true, message: '编号不能为空', trigger: 'blur'}
			]
		},
		q: {
			rname: '',
			nummber: '',
			sendnum: '',
			zbnum: ''
		}
		, editDetail: {},

		/**
		 * 新增加钱币所需
		 */
		outline:true,
		active_user:'',
		read:0,
		bq:0,
		showDiyList:false,
		title: null,
		pjClCoin: {},
		pjClCoinName: "",
		uploadList: [],
		uploadList2: [],
		visible: false,
		parentName:"",
		jzbScoretype:1,
		gmList: [
			{id: '03', name: '机制币'},
			{id: '02', name: '古钱币'},
			{id: '04', name: '银锭'},
			{id: '01', name: '纸币'}
		],
		statusList:resultArray,
		isPackList: isPackArray,
		jzbScoreList: jzbScoreArray,
		jzbAllScoreList: jzbAllScoreArray,
		ybAllScoreList: ybAllScoreArray,
		gqbScoreList: gqbScoreArray,
		ydScoreList: ydScoreListArray,
		zbScoreList: zbScoreArray,
		extbqList: extbqListArray,
		yd20List:[
			{id:"0",name:"空"},
			{id:"1/5",name:"1/5"},
			{id:"2/5",name:"2/5"},
			{id:"3/5",name:"3/5"},
			{id:"4/5",name:"4/5"},
			{id:"5/5",name:"5/5"}
		],
		yd20List2:[
			{id:"0",name:"空"},
			{id:"1/5",name:"1/5"},
			{id:"2/5",name:"2/5"},
			{id:"3/5",name:"3/5"},
			{id:"4/5",name:"4/5"},
			{id:"5/5",name:"5/5"}
		],
		ydQXList: ydQXArray,
		gqbQXList: gqbQXArray,
		jzbQXList: jzbQXArray,

		coinName:"",
		coinDynasty:"",
		coinAuthenticity:"",
		coinScores:"",
		coinRepair:"",
		coinScoreNote:"",
		coinUpdateTime:"",
		sendnum:"",
		tips:""

	},
	methods: {
		query: function () {
			vm.reload();
		},
		update: function (event) {
			let id = getSelectedRow("#jqGrid");
			if (id == null) {
				return;
			}
			var grid = $("#jqGrid");
			var rowData = grid.jqGrid("getRowData", id);
			vm.frameLink ="../jiandin/zhibi2.html?id="+rowData.id;
			vm.showList = false;
			vm.showDiyList = false;
			vm.addSendform= false;
			vm.ChangeState = true;
			vm.title = "修改";
		},
		clearPrice: function(){
			let value=vm.pjClCoin.result;
			if(value==3||value==5||value==7){
				vm.pjClCoin.bzfee=0;
				vm.pjClCoin.gjfee=0;
				vm.pjClCoin.zk=10;
				vm.pjClCoin.finalfee=0;
				vm.pjClCoin.boxfee=0;
			}
		},
		reloadSearch: function() {
			vm.q = {
				name: '',
				nummber: '',
				gm: ''
			}
			vm.reload();
		},
		handleSubmit: function (name) {
			handleSubmitValidate(this, name, function () {
				vm.saveOrUpdate()
			});
		},
		handleReset: function (name) {
			handleResetForm(this, name);
		},
		handleBeforeUpload:function() {
			return true;

		},
		handleFormatError: function (file) {
			this.$Notice.warning({
				title: '文件格式不正确',
				desc: '文件 ' + file.name + ' 格式不正确，请上传 jpg 或 png 格式的图片。'
			});
		},
		handleMaxSize: function (file) {
			this.$Notice.warning({
				title: '超出文件大小限制',
				desc: '文件 ' + file.name + ' 太大，不能超过 10M。'
			});
		},
		getInfo: function(id){
			Ajax.request({
				url: "../pjosendform/info/"+id,
				async: true,
				successCallback: function (r) {
					vm.itemList = r.itemList;
					//vm.pjOSendform = r.pjOSendform;
				}
			});
		},
		reload: function (event) {
			vm.showList = true;
			vm.showDiyList = false;
			vm.ChangeState = false;
			vm.addSendform = false;
			let page = $("#jqGrid").jqGrid('getGridParam', 'page');
			$("#jqGrid").jqGrid('setGridParam', {
				postData: {
					'rname': vm.q.rname.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'nummber': vm.q.nummber.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'sendnum': vm.q.sendnum.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'zbnum': vm.q.zbnum.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,'')
				},
				page: page
			}).trigger("reloadGrid");
		},
		reloadSearch: function() {
			vm.q = {
				rname: '',
				nummber: '',
				sendnum: '',
				zbnum: ''
			}
			vm.reload();
		},
		handleSubmit: function (name) {
			handleSubmitValidate(this, name, function () {
				vm.saveOrUpdate()
			});
		},
		checkYes: function () {
			let ids = getSelectedRows("#jqGrid");
			if (ids == null){
				return;
			}

			var count=0;
			// 检查钱币是够已经打分
			Ajax.request({
				url: "../pjclcoin/checkScore?sendnum="+ids,
				type: "POST",
				contentType: "application/json",
				successCallback: function (r) {
					count=r.pjClCoinList.length;
					if(count>0){
						alert('此单含有未打分钱币');
						return;
					}
				}
			});
			if(count==0){
				vm.pjOSendform.checkName="”审核通过√”";
				layer.confirm('确定所选项修改为'+vm.pjOSendform.checkName+"？", {
					btn: ['确定','取消'] //按钮
				}, function(){
					Ajax.request({
						url: "../pjosendform/checkYesBatch",
						params: JSON.stringify(ids),
						type: "POST",
						contentType: "application/json",
						successCallback: function (r) {
							layer.msg('审核成功', {icon: 1});
							vm.reload();
						}
					});
				}, function(){
					layer.msg('取消');
				});
			}
		},
		checkNo: function () {
			let ids = getSelectedRows("#jqGrid");
			if (ids == null){
				return;
		}
		vm.pjOSendform.checkName="”审核不通过x”";
		layer.confirm('确定所选项修改为'+vm.pjOSendform.checkName+"？", {
			btn: ['确定','取消'] //按钮
			}, function(){
				Ajax.request({
					url: "../pjosendform/checkNoBatch",
					params: JSON.stringify(ids),
					type: "POST",
					contentType: "application/json",
					successCallback: function (r) {
						layer.msg('驳回成功', {icon: 1});
						vm.reload();
					}
				});
			},function(){
				layer.msg('取消');
			});
		},
	}
});