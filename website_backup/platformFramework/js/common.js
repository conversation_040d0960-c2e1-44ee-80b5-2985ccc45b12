var web_root = "http://127.0.0.1:8088/platform-framework/";
//iframe自适应
$(window).on('resize', function () {
    var $content = $('#mainApp');
    $content.height($(this).height());
    $content.find('iframe').each(function () {
        $(this).height($content.height() - 150);
    });
    var $rrapp = $('#rrapp').parent();
    $rrapp.height($(this).height());
    $(this).height($content.height());
}).resize();

//重写alert
window.alert = function (msg, callback) {
    // parent.layer.alert 弹出在iframe外的页面。
    layer.alert(msg, function (index) {
        layer.close(index);
        if (typeof (callback) === "function") {
            callback("ok");
        }
    });
};

//重写confirm式样框
window.confirm = function (msg, callback) {
    //如果没有定义回调函数，直接返回true
    if (!callback) {
        return true;
    }
    var la = layer.confirm(msg, {
            skin: 'layui-layer-molv', btn: ['确定', '取消']
        },
        function () {//确定事件
            if (typeof (callback) === "function") {
                try {
                    callback("ok");
                } catch (err) {
                }
                layer.close(la);
            }
        });
};

/**
 *
 * @param options
 */
window.openWindow = function (options) {
    var globalParams = {
        skin: 'layui-layer-molv',//皮肤
        title: '标题',//标题
        type: 1,//打开窗口的类型 1：html里的div内容 2：iframe方式，页面的路径
        closeBtn: 1, //关闭按钮的形状 0、1
        anim: -1,
        isOutAnim: false,
        shadeClose: false,
        area: ['90%', '95%'],
        content: '',
        btn: false, //按钮
        top: false //窗口弹出是否在iframe上层
    };
    globalParams = $.extend(globalParams, options);
    if (globalParams.top) {
        parent.layer.open(globalParams);
    } else {
        layer.open(globalParams);
    }
};

//获取选中的数据
function getSelectedRowData(gridId) {
    var id = getSelectedRow(gridId);
    return $(gridId).jqGrid('getRowData', id);
}

//选择一条记录
function getSelectedRow(gridId) {
    var grid = $(gridId);
    var rowKey = grid.getGridParam("selrow");
    if (!rowKey) {
        iview.Message.error("请选择一条记录");
        return;
    }

    var selectedIDs = grid.getGridParam("selarrrow");
    if (selectedIDs.length > 1) {
        iview.Message.error("只能选择一条记录");
        return;
    }

    return selectedIDs[0];
};

//选择多条记录
function getSelectedRows(gridId) {
    var grid = $(gridId);
    var rowKey = grid.getGridParam("selrow");
    if (!rowKey) {
        iview.Message.error("请选择一条记录");
        return;
    }
    return grid.getGridParam("selarrrow");
};

/**
 * 预览图片
 * @param url
 */
function eyeImage(url) {
    if (!url) {
        iview.Message.error('请先上传图片');
        return;
    }
    if (url.indexOf("http") == -1) {//test 生产环境删掉此代码，使用nginx方向代理指过去。
        url = web_root + url;
    }
    layer.photos({
        photos: {
            "title": "预览", //相册标题
            "start": 0, //初始显示的图片序号，默认0
            "data": [   //相册包含的图片，数组格式
                {
                    "src": url //原图地址
                }
            ]
        }, anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机
    });
};

/**
 * 预览图片
 * @param data
 */
function eyeImages(data) {
    layer.photos({
        photos: {
            "title": "预览", //相册标题
            "start": 0, //初始显示的图片序号，默认0
            "data": data
        }, anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机
    });
};

/**
 * 重置验证
 * @param vue vue对象
 * @param name
 */
function handleResetForm(vue, name) {
    vue.$refs[name].resetFields();
};

/**
 * 表单验证
 * @param vue vue对象
 * @param name 验证规则
 * @param callback 验证通过回调函数
 */
function handleSubmitValidate(vue, name, callback) {
    vue.$refs[name].validate(function (valid) {
        if (valid) {
            callback();
        } else {
            iview.Message.error('请填写完整信息!');
            return false;
        }
    })
};

/**
 * 翻译日期
 * @param date
 * @param fmt
 * @returns {*}
 */
function transDate(date, fmt) {
    if (date) {
        if (typeof date == 'number') {
            return new Date(date).dateFormat(fmt);
        } else {
            try {
                return new Date(date.replace('-', '/').replace('-', '/')).dateFormat(fmt);
            } catch (e) {
                return '-';
            }
        }
    } else {
        return '-';
    }
};

/**
 * 翻译图片
 * @param url
 * @returns {*}
 */
function transImg(url) {
    if (url) {
        if (url.indexOf("http") == -1) {//test 生产环境删掉此代码，使用nginx方向代理指过去。
            url = web_root + url;
        }
        return '<img width="50px" height="50px" src="' + url + '">';
    } else {
        return '-';
    }
};

/**
 * 翻译图片
 * @param url
 * @returns {*}
 */
function transImgUrl(url) {
    if (url) {
        if (url.indexOf("http") == -1) {//test 生产环境删掉此代码，使用nginx方向代理指过去。
            url = web_root + url;
            return url;
        }
    } else {
        return '-';
    }
};

/**
 * 翻译性别
 * @param gender
 * @returns {*}
 */
function transGender(gender) {
    if (gender == 1) {
        return '男';
    }
    if (gender == 2) {
        return '女';
    }
    return '未知';
};

function transIsNot(value) {
    if (value == 1) {
        return '<span class="label label-success">是</span>';
    }
    return '<span class="label label-danger">否</span>';
};

function transStatus(value) {
    if (value == 1) {
        return '<span class="label label-success">有效</span>';
    }
    return '<span class="label label-danger">无效</span>';
};

function toUrl(href) {
    window.location.href = href;
}

function dialogLoading(flag) {
    if (flag) {
        top.layer.load(0, {
            shade: [0.5, '#fff'],
            time: 5000,
            content: '处理中...'
        });
    } else {
        top.layer.closeAll('loading');
    }
}

/**
 * 用JS获取地址栏参数的方法
 * 使用示例 location.href = http://localhost:8080/index.html?id=123
 *          getQueryString('id') --> 123;
 * @param name
 * @returns {null}
 * @constructor
 */
function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}

/**
 * 主要功能:导出功能公共方法
 *
 * @param formId 表单ID,带'#'号,如'#formId'
 * @param url 请求后台地址
 * @param extraObj 往后台请求额外参数,对象格式,如:{'aaa': 111}
 */
function exportFile(formId, url, extraObj) {
    var form = $('<form>'); //定义一个form表单
    form.attr('style', 'display: none');
    form.attr('target', '');
    form.attr('method', 'post');
    form.attr('action', url);

    var json = getJson(formId);
    if (typeof extraObj != 'undefined') {
        json = $.extend(json, extraObj);
    }

    $('body').append(form);//将表单放置在web中
    for (var i in json) {
        var input = $('<input>');
        input.attr('type', 'hidden');
        input.attr('name', i);
        input.attr('value', json[i]);
        form.append(input);
    }

    form.submit();//表单提交
}

/**
 * 将form转化为json
 * @param form 传入 form表单的dom $("#baseFm")
 * @returns {___anonymous49_50}  序列化的键值对 {key:value,key2:value2,....}
 */
function getJson(form) {
    var o = {};
    var $form = $(form).find('input,textarea,select');
    $.each($form, function (i, item) {
        var $this = $(item);

        if ($this.attr("type") == 'radio') {
            o[$this.attr("name")] = $("input[name='" + $this.attr("name") + "']:checked").val();
            return true;
        }
        o[$this.attr("name")] = $this.val();
    })
    return o;
}

/**
 *
 Ajax.request({
        url: '', //访问路径
        dataType: 'json', //访问类型 'json','html'等
        params: getJson(form),
        resultMsg: true, false, //是否需要提示信息
        type: 'GET',//,'get','post'
        beforeSubmit: function (data) {},//提交前处理
        successCallback: function (data) {} //提交后处理
    });
 */
Ajax = function () {

    //var opt = { type:'GET',dataType:'json',resultMsg:true };
    function request(opt) {

        //添加遮罩层
        dialogLoading(true);

        if (typeof opt.cache == 'undefined') {
            opt.cache = false;
        }

        if (typeof opt == 'undefined') {
            return;
        }
        //opt = $.extend(opt, p);
        if (typeof (opt.type) == 'undefined') {
            opt.type = 'GET'
        }
        if (typeof (opt.async) == 'undefined') {
            opt.async = false;
        }
        if (typeof (opt.dataType) == 'undefined') {
            opt.dataType = 'json'
        }
        if (typeof (opt.contentType) == 'undefined') {
            opt.contentType = 'application/x-www-form-urlencoded;chartset=UTF-8'
        }
        if (typeof (opt.params) == 'undefined' || opt.params == null) {
            opt.params = {};
        }
        opt.params.date = new Date();
        if (typeof (opt.beforeSubmit) != 'undefined') {
            var flag = opt.beforeSubmit(opt);
            if (!flag) {
                return;
            }
        }

        if (typeof (opt.resultMsg) == 'undefined') {
            opt.resultMsg = true;
        }

        $.ajax({
            async: opt.async,
            url: opt.url,
            dataType: opt.dataType,
            contentType: opt.contentType,
            data: opt.params,
            crossDomain: opt.crossDomain || false,
            type: opt.type,
            cache: opt.cache,
            success: function (data) {
                //关闭遮罩
                dialogLoading(false);

                if (typeof data == 'string' && data.indexOf("exception") > 0 || typeof data.code != 'undefined' && data.code != '0') {
                    var result = {code: null};
                    if (typeof data == 'string') {
                        result = eval('(' + data + ')')
                    } else if (typeof data == 'object') {
                        result = data;
                    }

                    if (opt.resultMsg && result.msg) {
                        layer.alert(result.msg, {icon: 5});
                    }
                    return;
                }
                if (opt.resultMsg && data.msg) {
                    layer.alert(data.msg, {icon: 6}, function () {
                        if (typeof (opt.successCallback) != 'undefined') {
                            opt.successCallback(data);
                        }
                    });
                    return;
                }

                if (typeof (opt.successCallback) != 'undefined') {
                    opt.successCallback(data);
                }
            },
            error: function () {
                //关闭遮罩
                dialogLoading(false);
                // layer.alert("此页面发生未知异常,请联系管理员", {icon: 5});
                layer.open({
                    title: '此页面发生未知异常,请联系管理员(登录超时请重新登录)'
                    ,content: '如若不想刷新此页面,请打开新标签登录后台系统,在返回此页面进行操作即可'
                });


            }
        });
    }

    return {
        /**
         * Ajax调用request
         */
        request: request
    };
}();

/**
 * 缓存字典数据
 * 使用方法：字典 调用方式为在table的列或者columns 的列中 formatter:function(value){ return Dict.getDictValue(groupCode,value);}
 * 其中value为类型code  返回值为类型名称
 */
Dict = function () {
    return {
        getDictValue: function (groupCode, dictKey) {
            var dictValue = '-';
            Ajax.request({
                url: '/sys/dict/getDictValue',
                dataType: 'json',
                params: {
                    groupCode: groupCode, dictKey: dictKey
                },
                cache: true,
                async: false,
                type: 'GET',
                successCallback: function (data) {
                    dictValue = data.dictValue;
                }
            });
            return dictValue;
        }
    };
}();


var niandaiArray = new Array(//朝代对应表，用户input提示
    '先秦',
    '秦朝',
    '西楚',
    '汉代',
    '三国',
    '两晋十六国',
    '南北朝',
    '隋代',
    '唐代',
    '五代十国',
    '北宋',
    '南宋',
    '辽代',
    '西夏',
    '金代',
    '元代',
    '明代',
    '南明',
    '清代',
    '民国',
    '新莽'
);

var gmArray = [
    {id: '03', name: '机制币'},
    {id: '02', name: '古钱币'},
    {id: '04', name: '银锭'},
    {id: '01', name: '纸币'}
];
var isPackArray = [
    {id: 1, name: '是'},
    {id: 2, name: '否'}
];

var jzbAllofScoreArray = [
    {id: '空@@空@@ ', name: ' '},
    {id: '弱品@@PO@@1', name: '弱品PO1'},
    {id: '弱品@@PO@@', name: '弱品PO'},
    {id: '普通品@@FR@@2', name: '普通品FR2'},
    {id: '普通品@@FR@@', name: '普通品FR'},
    {id: '近好品@@AG@@3', name: '近好品AG3'},
    {id: '近好品@@AG@@', name: '近好品AG'},
    {id: '好品@@G@@4', name: '好品G4'},
    {id: '好品@@G@@6', name: '好品G6'},
    {id: '好品@@G@@', name: '好品G好品'},
    {id: '上好品@@VG@@8', name: '上好品VG8'},
    {id: '上好品@@VG@@10', name: '上好品VG10'},
    {id: '上好品@@VG@@', name: '上好品VG'},
    {id: '美品@@F@@12', name: '美品F12'},
    {id: '美品@@F@@15', name: '美品F15'},
    {id: '美品@@F@@', name: '美品F美品'},
    {id: '上美品@@VF@@20', name: '上美品VF20'},
    {id: '上美品@@VF@@25', name: '上美品VF25'},
    {id: '上美品@@VF@@30', name: '上美品VF30'},
    {id: '上美品@@VF@@35', name: '上美品VF35'},
    {id: '上美品@@VF@@', name: '上美品VF'},
    {id: '极美品@@XF@@40', name: '极美品XF40'},
    {id: '极美品@@XF@@45', name: '极美品XF45'},
    {id: '极美品@@XF@@45+', name: '极美品XF45+'},
    {id: '极美品@@XF@@', name: '极美品XF极美品'},
    {id: '近未流通品@@AU@@50', name: '近未流通品AU50'},
    {id: '近未流通品@@AU@@50+', name: '近未流通品AU50+'},
    {id: '近未流通品@@AU@@53', name: '近未流通品AU53'},
    {id: '近未流通品@@AU@@53+', name: '近未流通品AU53+'},
    {id: '近未流通品@@AU@@55', name: '近未流通品AU55'},
    {id: '近未流通品@@AU@@55+', name: '近未流通品AU55+'},
    {id: '近未流通品@@AU@@58', name: '近未流通品AU58'},
    {id: '近未流通品@@AU@@58+', name: '近未流通品AU58+'},
    {id: '近未流通品@@AU@@', name: '近未流通品AU'},
    {id: '未流通品@@MS@@60', name: '未流通品MS60'},
    {id: '未流通品@@MS@@61', name: '未流通品MS61'},
    {id: '未流通品@@MS@@62', name: '未流通品MS62'},
    {id: '未流通品@@MS@@62+', name: '未流通品MS62+'},
    {id: '未流通品@@MS@@63', name: '未流通品MS63'},
    {id: '未流通品@@MS@@63+', name: '未流通品MS63+'},
    {id: '未流通品@@MS@@64', name: '未流通品MS64'},
    {id: '未流通品@@MS@@64+', name: '未流通品MS64+'},
    {id: '未流通品@@MS@@65', name: '未流通品MS65'},
    {id: '未流通品@@MS@@65+', name: '未流通品MS65+'},
    {id: '未流通品@@MS@@66', name: '未流通品MS66'},
    {id: '未流通品@@MS@@66+', name: '未流通品MS66+'},
    {id: '未流通品@@MS@@67', name: '未流通品MS67'},
    {id: '未流通品@@MS@@67+', name: '未流通品MS67+'},
    {id: '未流通品@@MS@@68', name: '未流通品MS68'},
    {id: '未流通品@@MS@@68+', name: '未流通品MS68+'},
    {id: '未流通品@@MS@@69', name: '未流通品MS69'},
    {id: '未流通品@@MS@@70', name: '未流通品MS70'},
    {id: '未流通品@@MS@@', name: '未流通品MS'},
    {id: '真品@@Genuine@@', name: '真品Genuine真品'},
    {id: '不出分机制币@@UNC@@', name: '不出分机制币UNC'},
    {id: '不在打分评级范围@@NS@@', name: '不在打分评级范围NS'},
    {id: '精致币@@PF@@', name: '精致币PF'},
    {id: '精致币@@PF@@1', name: '精致币PF1'},
    {id: '精致币@@PF@@2', name: '精致币PF2'},
    {id: '精致币@@PF@@3', name: '精致币PF3'},
    {id: '精致币@@PF@@4', name: '精致币PF4'},
    {id: '精致币@@PF@@6', name: '精致币PF6'},
    {id: '精致币@@PF@@8', name: '精致币PF8'},
    {id: '精致币@@PF@@10', name: '精致币PF10'},
    {id: '精致币@@PF@@12', name: '精致币PF12'},
    {id: '精致币@@PF@@15', name: '精致币PF15'},
    {id: '精致币@@PF@@20', name: '精致币PF20'},
    {id: '精致币@@PF@@25', name: '精致币PF25'},
    {id: '精致币@@PF@@30', name: '精致币PF30'},
    {id: '精致币@@PF@@35', name: '精致币PF35'},
    {id: '精致币@@PF@@40', name: '精致币PF40'},
    {id: '精致币@@PF@@45', name: '精致币PF45'},
    {id: '精致币@@PF@@45+', name: '精致币PF45+'},
    {id: '精致币@@PF@@50', name: '精致币PF50'},
    {id: '精致币@@PF@@50+', name: '精致币PF50+'},
    {id: '精致币@@PF@@53', name: '精致币PF53'},
    {id: '精致币@@PF@@53+', name: '精致币PF53+'},
    {id: '精致币@@PF@@55', name: '精致币PF55'},
    {id: '精致币@@PF@@55+', name: '精致币PF55+'},
    {id: '精致币@@PF@@58', name: '精致币PF58'},
    {id: '精致币@@PF@@58+', name: '精致币PF58+'},
    {id: '精致币@@PF@@60', name: '精致币PF60'},
    {id: '精致币@@PF@@61', name: '精致币PF61'},
    {id: '精致币@@PF@@62', name: '精致币PF62'},
    {id: '精致币@@PF@@62+', name: '精致币PF62+'},
    {id: '精致币@@PF@@63', name: '精致币PF63'},
    {id: '精致币@@PF@@63+', name: '精致币PF63+'},
    {id: '精致币@@PF@@64', name: '精致币PF64'},
    {id: '精致币@@PF@@64+', name: '精致币PF64+'},
    {id: '精致币@@PF@@65', name: '精致币PF65'},
    {id: '精致币@@PF@@65+', name: '精致币PF65+'},
    {id: '精致币@@PF@@66', name: '精致币PF66'},
    {id: '精致币@@PF@@66+', name: '精致币PF66+'},
    {id: '精致币@@PF@@67', name: '精致币PF67'},
    {id: '精致币@@PF@@67+', name: '精致币PF67+'},
    {id: '精致币@@PF@@68', name: '精致币PF68'},
    {id: '精致币@@PF@@68+', name: '精致币PF68+'},
    {id: '精致币@@PF@@69', name: '精致币PF69'},
    {id: '精致币@@PF@@70', name: '精致币PF70'},
    {id: '样币@@SP@@', name: '样币SP'},
    {id: '样币@@SP@@1', name: '样币SP1'},
    {id: '样币@@SP@@2', name: '样币SP2'},
    {id: '样币@@SP@@3', name: '样币SP3'},
    {id: '样币@@SP@@4', name: '样币SP4'},
    {id: '样币@@SP@@6', name: '样币SP6'},
    {id: '样币@@SP@@8', name: '样币SP8'},
    {id: '样币@@SP@@10', name: '样币SP10'},
    {id: '样币@@SP@@12', name: '样币SP12'},
    {id: '样币@@SP@@15', name: '样币SP15'},
    {id: '样币@@SP@@20', name: '样币SP20'},
    {id: '样币@@SP@@25', name: '样币SP25'},
    {id: '样币@@SP@@30', name: '样币SP30'},
    {id: '样币@@SP@@35', name: '样币SP35'},
    {id: '样币@@SP@@40', name: '样币SP40'},
    {id: '样币@@SP@@45', name: '样币SP45'},
    {id: '样币@@SP@@45+', name: '样币SP45+'},
    {id: '样币@@SP@@50', name: '样币SP50'},
    {id: '样币@@SP@@50+', name: '样币SP50+'},
    {id: '样币@@SP@@53', name: '样币SP53'},
    {id: '样币@@SP@@53+', name: '样币SP53+'},
    {id: '样币@@SP@@55', name: '样币SP55'},
    {id: '样币@@SP@@55+', name: '样币SP55+'},
    {id: '样币@@SP@@58', name: '样币SP58'},
    {id: '样币@@SP@@58+', name: '样币SP58+'},
    {id: '样币@@SP@@60', name: '样币SP60'},
    {id: '样币@@SP@@61', name: '样币SP61'},
    {id: '样币@@SP@@62', name: '样币SP62'},
    {id: '样币@@SP@@62+', name: '样币SP62+'},
    {id: '样币@@SP@@63', name: '样币SP63'},
    {id: '样币@@SP@@63+', name: '样币SP63+'},
    {id: '样币@@SP@@64', name: '样币SP64'},
    {id: '样币@@SP@@64+', name: '样币SP64+'},
    {id: '样币@@SP@@65', name: '样币SP65'},
    {id: '样币@@SP@@65+', name: '样币SP65+'},
    {id: '样币@@SP@@66', name: '样币SP66'},
    {id: '样币@@SP@@66+', name: '样币SP66+'},
    {id: '样币@@SP@@67', name: '样币SP67'},
    {id: '样币@@SP@@67+', name: '样币SP67+'},
    {id: '样币@@SP@@68', name: '样币SP68'},
    {id: '样币@@SP@@68+', name: '样币SP68+'},
    {id: '样币@@SP@@69', name: '样币SP69'},
    {id: '样币@@SP@@70', name: '样币SP70'}
]

var resultArray = [
    {id: 0, name: '未鉴定'},
    {id: 1, name: '真'},
    {id: 2, name: '赝品'},
    {id: 3, name: '存疑'},
    // {id: 4, name: '性质伪'},
    {id: 5, name: '不提供服务'},

    {id: 8, name: '不适合评级(赝品)'},
    {id: 9, name: '不适合评级(锭体、铭文修复超出规定)'},
    {id: 10, name: '不适合评级(老假、老仿、臆造)'},
    //旧的
    {id: 6, name: '不适合评级'},
    {id: 7, name: '撤评'},
    {id: 11, name: '暂不提供服务(香港)'},
]

var jzbScoreArray = [
    {id: '空@@空@@ ', name: ' '},
    {id: '弱品@@PO@@1', name: '弱品PO1'},
    {id: '@@MS@@68', name: 'MS68'},
    {id: '弱品@@PO@@', name: '弱品PO'},
    {id: '普通品@@FR@@2', name: '普通品FR2'},
    {id: '普通品@@FR@@', name: '普通品FR'},
    {id: '近好品@@AG@@3', name: '近好品AG3'},
    {id: '近好品@@AG@@', name: '近好品AG'},
    {id: '好品@@G@@4', name: '好品G4'},
    {id: '好品@@G@@6', name: '好品G6'},
    {id: '好品@@G@@', name: '好品G'},
    {id: '上好品@@VG@@8', name: '上好品VG8'},
    {id: '上好品@@VG@@10', name: '上好品VG10'},
    {id: '上好品@@VG@@', name: '上好品VG'},
    {id: '美品@@F@@12', name: '美品F12'},
    {id: '美品@@F@@15', name: '美品F15'},
    {id: '美品@@F@@', name: '美品F'},
    {id: '上美品@@VF@@20', name: '上美品VF20'},
    {id: '上美品@@VF@@25', name: '上美品VF25'},
    {id: '上美品@@VF@@30', name: '上美品VF30'},
    {id: '上美品@@VF@@35', name: '上美品VF35'},
    {id: '上美品@@VF@@', name: '上美品VF'},
    {id: '极美品@@XF@@40', name: '极美品XF40'},
    {id: '极美品@@XF@@45', name: '极美品XF45'},
    {id: '极美品@@XF@@45+', name: '极美品XF45+'},
    {id: '极美品@@XF@@', name: '极美品XF'},
    {id: '近未流通品@@AU@@50', name: '近未流通品AU50'},
    {id: '近未流通品@@AU@@50+', name: '近未流通品AU50+'},
    {id: '近未流通品@@AU@@53', name: '近未流通品AU53'},
    {id: '近未流通品@@AU@@53+', name: '近未流通品AU53+'},
    {id: '近未流通品@@AU@@55', name: '近未流通品AU55'},
    {id: '近未流通品@@AU@@55+', name: '近未流通品AU55+'},
    {id: '近未流通品@@AU@@58', name: '近未流通品AU58'},
    {id: '近未流通品@@AU@@58+', name: '近未流通品AU58+'},
    {id: '近未流通品@@AU@@', name: '近未流通品AU'},
    {id: '未流通品@@MS@@60', name: '未流通品MS60'},
    {id: '未流通品@@MS@@61', name: '未流通品MS61'},
    {id: '未流通品@@MS@@62', name: '未流通品MS62'},
    {id: '未流通品@@MS@@62+', name: '未流通品MS62+'},
    {id: '未流通品@@MS@@63', name: '未流通品MS63'},
    {id: '未流通品@@MS@@63+', name: '未流通品MS63+'},
    {id: '未流通品@@MS@@64', name: '未流通品MS64'},
    {id: '未流通品@@MS@@64+', name: '未流通品MS64+'},
    {id: '未流通品@@MS@@65', name: '未流通品MS65'},
    {id: '未流通品@@MS@@65+', name: '未流通品MS65+'},
    {id: '未流通品@@MS@@66', name: '未流通品MS66'},
    {id: '未流通品@@MS@@66+', name: '未流通品MS66+'},
    {id: '未流通品@@MS@@67', name: '未流通品MS67'},
    {id: '未流通品@@MS@@67+', name: '未流通品MS67+'},
    {id: '未流通品@@MS@@68', name: '未流通品MS68'},
    {id: '未流通品@@MS@@68+', name: '未流通品MS68+'},
    {id: '未流通品@@MS@@69', name: '未流通品MS69'},
    {id: '未流通品@@MS@@70', name: '未流通品MS70'},
    {id: '未流通品@@UNC@@', name: '未流通品UNC'},
    {id: '真品@@Genuine@@', name: '真品Genuine'},
    //{id: '不出分机制币@@UNC@@', name: '不出分机制币UNC'},
    {id: '不在打分评级范围@@NS@@', name: '不在打分评级范围NS'}
]

var jzbAllScoreArray = [
    {id: 'PF@@', name: 'PF'},
    {id: 'PF@@1', name: 'PF1'},
    {id: 'PF@@2', name: 'PF2'},
    {id: 'PF@@3', name: 'PF3'},
    {id: 'PF@@4', name: 'PF4'},
    {id: 'PF@@6', name: 'PF6'},
    {id: 'PF@@8', name: 'PF8'},
    {id: 'PF@@10', name: 'PF10'},
    {id: 'PF@@12', name: 'PF12'},
    {id: 'PF@@15', name: 'PF15'},
    {id: 'PF@@20', name: 'PF20'},
    {id: 'PF@@25', name: 'PF25'},
    {id: 'PF@@30', name: 'PF30'},
    {id: 'PF@@35', name: 'PF35'},
    {id: 'PF@@40', name: 'PF40'},
    {id: 'PF@@45', name: 'PF45'},
    {id: 'PF@@45+', name: 'PF45+'},
    {id: 'PF@@50', name: 'PF50'},
    {id: 'PF@@50+', name: 'PF50+'},
    {id: 'PF@@53', name: 'PF53'},
    {id: 'PF@@53+', name: 'PF53+'},
    {id: 'PF@@55', name: 'PF55'},
    {id: 'PF@@55+', name: 'PF55+'},
    {id: 'PF@@58', name: 'PF58'},
    {id: 'PF@@58+', name: 'PF58+'},
    {id: 'PF@@60', name: 'PF60'},
    {id: 'PF@@61', name: 'PF61'},
    {id: 'PF@@62', name: 'PF62'},
    {id: 'PF@@62+', name: 'PF62+'},
    {id: 'PF@@63', name: 'PF63'},
    {id: 'PF@@63+', name: 'PF63+'},
    {id: 'PF@@64', name: 'PF64'},
    {id: 'PF@@64+', name: 'PF64+'},
    {id: 'PF@@65', name: 'PF65'},
    {id: 'PF@@65+', name: 'PF65+'},
    {id: 'PF@@66', name: 'PF66'},
    {id: 'PF@@66+', name: 'PF66+'},
    {id: 'PF@@67', name: 'PF67'},
    {id: 'PF@@67+', name: 'PF67+'},
    {id: 'PF@@68', name: 'PF68'},
    {id: 'PF@@68+', name: 'PF68+'},
    {id: 'PF@@69', name: 'PF69'},
    {id: 'PF@@70', name: 'PF70'}
];

var ybAllScoreArray = [
    {id: 'SP@@', name: 'SP'},
    {id: 'SP@@1', name: 'SP1'},
    {id: 'SP@@2', name: 'SP2'},
    {id: 'SP@@3', name: 'SP3'},
    {id: 'SP@@4', name: 'SP4'},
    {id: 'SP@@6', name: 'SP6'},
    {id: 'SP@@8', name: 'SP8'},
    {id: 'SP@@10', name: 'SP10'},
    {id: 'SP@@12', name: 'SP12'},
    {id: 'SP@@15', name: 'SP15'},
    {id: 'SP@@20', name: 'SP20'},
    {id: 'SP@@25', name: 'SP25'},
    {id: 'SP@@30', name: 'SP30'},
    {id: 'SP@@35', name: 'SP35'},
    {id: 'SP@@40', name: 'SP40'},
    {id: 'SP@@45', name: 'SP45'},
    {id: 'SP@@45+', name: 'SP45+'},
    {id: 'SP@@50', name: 'SP50'},
    {id: 'SP@@50+', name: 'SP50+'},
    {id: 'SP@@53', name: 'SP53'},
    {id: 'SP@@53+', name: 'SP53+'},
    {id: 'SP@@55', name: 'SP55'},
    {id: 'SP@@55+', name: 'SP55+'},
    {id: 'SP@@58', name: 'SP58'},
    {id: 'SP@@58+', name: 'SP58+'},
    {id: 'SP@@60', name: 'SP60'},
    {id: 'SP@@61', name: 'SP61'},
    {id: 'SP@@62', name: 'SP62'},
    {id: 'SP@@62+', name: 'SP62+'},
    {id: 'SP@@63', name: 'SP63'},
    {id: 'SP@@63+', name: 'SP63+'},
    {id: 'SP@@64', name: 'SP64'},
    {id: 'SP@@64+', name: 'SP64+'},
    {id: 'SP@@65', name: 'SP65'},
    {id: 'SP@@65+', name: 'SP65+'},
    {id: 'SP@@66', name: 'SP66'},
    {id: 'SP@@66+', name: 'SP66+'},
    {id: 'SP@@67', name: 'SP67'},
    {id: 'SP@@67+', name: 'SP67+'},
    {id: 'SP@@68', name: 'SP68'},
    {id: 'SP@@68+', name: 'SP68+'},
    {id: 'SP@@69', name: 'SP69'},
    {id: 'SP@@70', name: 'SP70'}
];

var gqbScoreArray = [
    {id: '空@@空@@ ', name: ' '},
    {id: '弱品@@PO@@18', name: '弱品PO18'},
    {id: '普品@@FR@@28', name: '普品FR28'},
    {id: '普品@@FR@@38', name: '普品FR38'},
    {id: '好品@@G@@45', name: '好品G45'},
    {id: '好品@@G@@好品', name: '好品G好品'},
    {id: '好品@@G@@50', name: '好品G50'},
    {id: '好品@@G@@55', name: '好品G55'},
    {id: '美品@@F@@60', name: '美品F60'},
    {id: '美品@@F@@美品', name: '美品F美品'},
    {id: '美品@@F@@62', name: '美品F62'},
    {id: '美品@@F@@65', name: '美品F65'},
    {id: '美品@@F@@68', name: '美品F68'},
    {id: '上美品@@VF@@上美品', name: '上美品VF上美品'},
    {id: '上美品@@VF@@70', name: '上美品VF70'},
    {id: '上美品@@VF@@72', name: '上美品VF72'},
    {id: '上美品@@VF@@75', name: '上美品VF75'},
    {id: '上美品@@VF@@78', name: '上美品VF78'},
    {id: '极美品@@XF@@极美品', name: '极美品XF极美品'},
    {id: '极美品@@XF@@80', name: '极美品XF80'},
    {id: '极美品@@XF@@82', name: '极美品XF82'},
    {id: '极美品@@XF@@85', name: '极美品XF85'},
    {id: '极美品@@XF@@88', name: '极美品XF88'},
    {id: '完美品@@PF@@完美品', name: '完美品PF完美品'},
    {id: '完美品@@PF@@90', name: '完美品PF90'},
    {id: '完美品@@PF@@91', name: '完美品PF91'},
    {id: '完美品@@PF@@92', name: '完美品PF92'},
    {id: '完美品@@PF@@93', name: '完美品PF93'},
    {id: '完美品@@PF@@94', name: '完美品PF94'},
    {id: '完美品@@PF@@95', name: '完美品PF95'},
    {id: '完美品@@PF@@96', name: '完美品PF96'},
    {id: '完美品@@PF@@97', name: '完美品PF97'},
    {id: '完美品@@PF@@98', name: '完美品PF98'},
    {id: '完美品@@PF@@99', name: '完美品PF99'},
    {id: '真品@@Genuine@@真品', name: '真品Genuine真品'},
    {id: '不在打分评级范围@@NS@@', name: '不在打分评级范围NS'}
];

var ydScoreListArray = [
    {id: '完未+@@UNC+', name: '完未+UNC+'},
    {id: '完未@@UNC', name: '完未UNC'},
    {id: '近未+@@AU+', name: '近未+AU+'},
    {id: '近未@@AU', name: '近未AU'},
    {id: '极美+@@XF+', name: '极美+XF+'},
    {id: '极美@@XF', name: '极美XF'},
    {id: '美品@@VF', name: '美品VF'},
    {id: '普品@@F', name: '普品F'},
    {id: '真品@@Genuine', name: '真品Genuine'},
];

var ydScoreListArray1 = [
    {id: 'MS@@70', name: 'MS 70'},
    {id: 'MS@@69', name: 'MS 69'},
    {id: 'MS@@68', name: 'MS 68'},
    {id: 'MS@@67', name: 'MS 67'},
    {id: 'MS@@66', name: 'MS 66'},
    {id: 'MS@@65', name: 'MS 65'},
    {id: 'MS@@64', name: 'MS 64'},
    {id: 'MS@@63', name: 'MS 63'},
    {id: 'MS@@62', name: 'MS 62'},
    {id: 'MS@@61', name: 'MS 61'},
    {id: 'MS@@60', name: 'MS 60'},

    {id: 'AU@@58', name: 'AU 58'},
    {id: 'AU@@55', name: 'AU 55'},
    {id: 'AU@@53', name: 'AU 53'},
    {id: 'AU@@50', name: 'AU 50'},

    {id: 'XF@@45', name: 'XF 45'},
    {id: 'XF@@40', name: 'XF 40'},

    {id: 'VF@@35', name: 'VF 35'},
    {id: 'VF@@30', name: 'VF 30'},
    {id: 'VF@@25', name: 'VF 25'},
    {id: 'VF@@20', name: 'VF 20'},

    {id: 'F@@15', name: 'F 15'},
    {id: 'F@@10', name: 'F 10'},
    {id: 'F@@5', name: 'F 5'},

];




var ydScoreListArray2 = [
    {id: '1', name: '+'},
];

var ydScoreListArray3 = [
    {id: '2', name: '使用分数评级'},
];



var zbScoreArray = [
    {id: 'Superb Gem Unc@@70', name: 'Superb Gem Unc70'},
    {id: 'Superb Gem Unc@@69', name: 'Superb Gem Unc69'},
    {id: 'Superb Gem Unc@@68', name: 'Superb Gem Unc68'},
    {id: 'Superb Gem Unc@@67', name: 'Superb Gem Unc67'},
    {id: 'Gem Uncirculated@@66', name: 'Gem Uncirculated66'},
    {id: 'Gem Uncirculated@@65', name: 'Gem Uncirculated65'},
    {id: 'Choice Uncirculated@@64', name: 'Choice Uncirculated64'},
    {id: 'Choice Uncirculated@@63', name: 'Choice Uncirculated63'},
    {id: 'Uncirculated@@62', name: 'Uncirculated62'},
    {id: 'Uncirculated@@61', name: 'Uncirculated61'},
    {id: 'Uncirculated@@60', name: 'Uncirculated60'},
    {id: 'Choice About Unc@@58', name: 'Choice About Unc58'},
    {id: 'About Uncirculated@@55', name: 'About Uncirculated55'},
    {id: 'About Uncirculated@@53', name: 'About Uncirculated53'},
    {id: 'About Uncirculated@@50', name: 'About Uncirculated50'},
    {id: 'Choice Extremely Fine@@45', name: 'Choice Extremely Fine45'},
    {id: 'Extremely Fine@@40', name: 'Extremely Fine40'},
    {id: 'Choice Very Fine@@35', name: 'Choice Very Fine35'},
    {id: 'Very Fine@@30', name: 'Very Fine30'},
    {id: 'Very Fine@@25', name: 'Very Fine25'},
    {id: 'Genuine@@', name: 'Genuine'},
    {id: 'Genuine@@真品', name: 'Genuine真品'},
    {id: '极美品@@', name: '极美品'},
    {id: '精美品@@', name: '精美品'},
    {id: '美品@@', name: '美品'},
    {id: '上美品@@', name: '上美品'},

];


var extbqListArray = [
    {id: 11, name: '★'},
    {id: 12, name: 'EPQ'},
    {id: 13, name: 'NET'}
];


var ydQXArray = [
    {id: '@@', name: ' '},
    {id: '01A@@不提供服务', name: '01A不提供服务'},
    {id: '01B@@存疑', name: '01B存疑'},
    {id: '02A@@环境污染', name: '02A环境污染'},
    {id: '02B@@磨损', name: '02B磨损'},
    {id: '02C@@严重划伤', name: '02C严重划伤'},
    {id: '02D@@严重损伤', name: '02D严重损伤'},
    {id: '03A@@可疑颜色', name: '03A可疑颜色'},
    {id: '03B@@过度清洗', name: '03B过度清洗'},
    {id: '03C@@剪边', name: '03C剪边'},
    {id: '03D@@验槽', name: '03D验槽'},
    {id: '03E@@人工修复', name: '03E人工修复'},
    {id: '03F@@高温过火', name: '03F高温过火'},
    {id: '03G@@后加戳', name: '03G后加戳'},
    {id: '04A1@@锭面修补', name: '04A1锭面修补'},
    {id: '04A2@@锭边修补', name: '04A2锭边修补'},
    {id: '04A3@@锭侧修补', name: '04A3锭侧修补'},
    {id: '04A4@@锭底修补', name: '04A4锭底修补'},
    {id: '04B@@铭文修补', name: '04B铭文修补'},
    {id: '05A@@不适合评级', name: '05A不适合评级'},
    {id: '05B@@赝品', name: '05B赝品'}


];


var gqbQXArray = [
    {id: '@@', name: ' '},
    {id: '03@@修补品标识', name: '03修补品标识'},
    {id: '04@@改变表面状态标识', name: '04改变表面状态标识'},
    {id: '05@@可视瑕疵标识', name: '05可视瑕疵标识'}
];
var tgjzbScoreList = [
    {id: 'UNC@@', name: 'UNC'},
    {id: 'UNC Detail@@', name: 'UNC Detail'},
    {id: 'XF Detail@@', name: 'XF Detail'},
    {id: 'XF10@@', name: 'XF10'},
    {id: 'XF9@@', name: 'XF9'},
    {id: 'XF8@@', name: 'XF8'},
    {id: 'XF7@@', name: 'XF7'},
    {id: 'XF6@@', name: 'XF6'},
    {id: 'XF5@@', name: 'XF5'},
    {id: 'XF4@@', name: 'XF4'},
    {id: 'XF3@@', name: 'XF3'},
    {id: 'XF2@@', name: 'XF2'},
    {id: 'XF1@@', name: 'XF1'},
    {id: 'VF Detail@@', name: 'VF Detail'},
    {id: 'VF10@@', name: 'VF10'},
    {id: 'VF9@@', name: 'VF9'},
    {id: 'VF8@@', name: 'VF8'},
    {id: 'VF7@@', name: 'VF7'},
    {id: 'VF6@@', name: 'VF6'},
    {id: 'VF5@@', name: 'VF5'},
    {id: 'VF4@@', name: 'VF4'},
    {id: 'VF3@@', name: 'VF3'},
    {id: 'VF2@@', name: 'VF2'},
    {id: 'VF1@@', name: 'VF1'},
    {id: 'Fine@@', name: 'Fine'},
    {id: 'Fine Detail@@', name: 'Fine Detail'},
    {id: 'Good Detail@@', name: 'Good Detail'}

]

var tgjzbc1ScoreList = [
    {id: 'C1-UNC@@', name: 'C1-UNC'},
    {id: 'C1-UNC Detail@@', name: 'C1-UNC Detail'},
    {id: 'C1-XF10@@', name: 'C1-XF10'},
    {id: 'C1-XF10 Detail@@', name: 'C1-XF10 Detail'},
    {id: 'C1-XF9@@', name: 'C1-XF9'},
    {id: 'C1-XF8@@', name: 'C1-XF8'},
    {id: 'C1-XF7@@', name: 'C1-XF7'},
    {id: 'C1-XF6@@', name: 'C1-XF6'},
    {id: 'C1-XF5@@', name: 'C1-XF5'},
    {id: 'C1-XF4@@', name: 'C1-XF4'},
    {id: 'C1-XF3@@', name: 'C1-XF3'},
    {id: 'C1-XF2@@', name: 'C1-XF2'},
    {id: 'C1-XF1@@', name: 'C1-XF1'},
    {id: 'C1-VF10@@', name: 'C1-VF10'},
    {id: 'C1-VF10 Detail@@', name: 'C1-VF10 Detail'},
    {id: 'C1-VF9@@', name: 'C1-VF9'},
    {id: 'C1-VF8@@', name: 'C1-VF8'},
    {id: 'C1-VF7@@', name: 'C1-VF7'},
    {id: 'C1-VF6@@', name: 'C1-VF6'},
    {id: 'C1-VF5@@', name: 'C1-VF5'},
    {id: 'C1-VF4@@', name: 'C1-VF4'},
    {id: 'C1-VF3@@', name: 'C1-VF3'},
    {id: 'C1-VF2@@', name: 'C1-VF2'},
    {id: 'C1-VF1@@', name: 'C1-VF1'},
    {id: 'C1-Fine@@', name: '-C1-Fine'},
    {id: 'C1-Fine Detail@@', name: 'C1-Fine Detail'},
    {id: 'C1-Good Detail@@', name: 'C1-Good Detail'}
]

var jzbQXArray = [
    {id: '@@', name: ' '},
    {id: '82@@币的边齿有锉、修', name: '82币的边齿有锉、修'},
    {id: '83@@钱体不平', name: '83钱体不平'},
    {id: '84@@币面有窟窿的地方被堵上', name: '84币面有窟窿的地方被堵上'},
    {id: '92@@因为严重清洗，导致币的表面受损', name: '92因为严重清洗，导致币的表面受损'},
    {id: '93@@多处缺陷', name: '93多处缺陷'},
    {id: '95@@大或深的划痕', name: '95大或深的划痕'},
    {id: '97@@币由于环境原因造成的腐蚀、破坏或附着太厚的包浆', name: '97币由于环境原因造成的腐蚀、破坏或附着太厚的包浆'},
    {id: '98@@故意对币表面的损害、乱划、改刻', name: '98故意对币表面的损害、乱划、改刻'},
    {id: '99@@PVC的塑料分解、污染了币', name: '99PVC的塑料分解、污染了币'}
];

var yd20Array = [
    {id: '1/5', name: '1/5'},
    {id: '2/5', name: '2/5'},
    {id: '3/5', name: '3/5'},
    {id: '4/5', name: '4/5'},
    {id: '5/5', name: '5/5'}
];


