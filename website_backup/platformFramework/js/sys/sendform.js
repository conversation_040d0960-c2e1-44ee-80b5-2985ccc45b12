$(function () {
	$("#jqGrid").Grid({
		url: '../pjosendform/list',
		colModel: [
			{label: 'id', name: 'id', hidden: true},
			{label: '送评单号', name: 'sendnum',key: true, align: 'center',width: 50},
			{label: '网名', name: 'nickname', align: 'center', width: 50},
			{label: '真实姓名', name: 'rname', align: 'center', width: 50},
			{label: '项目数量', name: 'coinCount', align: 'center', width: 30},
			{label: '总费用', name: 'sumfeetw',align: 'center',width: 40},
			{
				label: '创建时间', name: 'inupttime',  width: 60,align: 'center', formatter: function (value) {
					return transDate(value);
				}
			},
			{
				label: '财务核对', name: 'ifyou',  width: 50, align: 'center',formatter: function (value) {
					if(value==1){
						return "<a style=\"color: green;\">√</a>";
					}else{
						return "";
					}
				}
			},
			{
				label: '评单审核', name: 'checkStatus',  width: 50, align: 'center',formatter: function (value) {
					if(value==1){
						return "<a style=\"color: green;\">yes</a>";
					}else{
						return "<a style=\"color: red;\">no</a>";
					}
				}
			},
            {
                label: '扫码审核', name: 'fullyOpen',  width: 50, align: 'center',formatter: function (value) {
                    if(value==1){
                        return "<a style=\"color: green;\">已开启</a>";
                    }else{
                        return "";
                    }
                }
            },

		],
	});

	/**
	 * 名称1失去焦点:
	 * @type {string}
	 */
	$('#name').autocomplete({
		width:350,
		autoFill: false,
		serviceUrl: '../coins/queryTree',
		params: vm.pjClCoin.name,
		paramName:"m",
		triggerSelectOnValidInput:false,
		transformResult: function(response) {
			//解析服务器传过来的json字符串
			var obj = $.parseJSON(response);
			return {
				// <数据下拉列表展示>
				suggestions: $.map(obj.list, function(dataItem) {
					// 展示数据拼接
					var show=dataItem.name;
					if(checkNUll(dataItem.name2)==true){
						let name2=" "+dataItem.name2;
						show+=name2;
					}
					if(checkNUll(dataItem.mianzhi)==true){
						let mianzhi="@"+dataItem.mianzhi;
						show+=mianzhi;
					}
					if(checkNUll(dataItem.catalog)==true){
						let catalog=""+dataItem.catalog;
						show+=catalog;
					}
					return {
						//名称1 名称2 年代 等级 版别 面值 目录
						value:show,
						name1: dataItem.name,
						name2: dataItem.name2,
						age: dataItem.niandai,
						level:dataItem.grade,
						bottle:dataItem.edition,
						faceValue: dataItem.mianzhi,
						catalog:dataItem.catalog,
						size:dataItem.size,
						weight:dataItem.weight,
						type:dataItem.cointype
					};
				})
			};
		},
		/**
		 * 选中值后数据处理
		 * 面值：机制币、银锭
		 * 等级：古钱币
		 */
		onSelect: function (suggestion) {
			vm.pjClCoin.name = suggestion.name1;//名称一
			if(checkNUll(suggestion.name2)==true){
				vm.pjClCoin.name2 = suggestion.name2;//名称二
			}
			if(checkNUll(suggestion.age)==true){
				vm.pjClCoin.niandai = suggestion.age;//年代
			}
			if(checkNUll(suggestion.level)==true){//等级
				vm.pjClCoin.rank = suggestion.level;
			}
			if(checkNUll(suggestion.bottle)==true){//版别
				vm.pjClCoin.edition = suggestion.bottle;
			}
			if(checkNUll(suggestion.faceValue)==true){
				vm.pjClCoin.mianzhi = suggestion.faceValue;//面值
			}
			if(checkNUll(suggestion.catalog)==true){
				vm.pjClCoin.catalog = suggestion.catalog;//目录
			}
			if(checkNUll(suggestion.type)==true&&suggestion.type==="03"||suggestion.type==3){
				if(checkNUll(suggestion.size)==true){
					vm.pjClCoin.size = suggestion.size;//尺寸
				}
				if(checkNUll(suggestion.weight)==true){
					vm.pjClCoin.weight = suggestion.weight;//重量
				}
			}
		}
	});
});
function  checkNUll(value) {
	if(value!=null&&value!=""&&value!="无"){
		return true;
	}
	return false;
}
function closeEdit(){
	vm.showList = true;
	vm.ChangeState = false;
}
let vm = new Vue({
	el: '#vue-table',
	data: {
		showList: true,
		ChangeState: false,
		frameLink:null,
		info: false,
		addSendform:false,
		title: null,
		pjOSendform: {},
		statePjOSendform: {},
		itemList: {},
		activeTab:0,
		projects: [],
		project: {
			isPack: 1,
			packType: 1
		},
		checkList: [
			{value: "1", name: 'yes'},
			{value: "0", name: 'no'},
		],
		statusList: [
			{value: "0", name: '待寄入'},
			{value: "1", name: '快递邮寄'},
			{value: "2", name: '评级中'},
			{value: "3", name: '待支付'},
			{value: "4", name: '评级完成'},
			{value: "5", name: '已寄出'},
			{value: "6", name: '待退费'}
		],
		packTypeList: [
			{id: 1, name: '密封盒'},
			{id: 2, name: '开放盒'},
			{id: 3, name: '证书盒'}
		],
		isPackList: [
			{id: 1, name: '是'},
			{id: 2, name: '否'}
		],
		ruleValidate: {
			name: [
				{required: true, message: '名称不能为空', trigger: 'blur'},
			],
			nummber:[
				{required: true, message: '编号不能为空', trigger: 'blur'}
			]
		},
		q: {
			rname: '',
			nummber: '',
			sendnum: '',
			nickname: ''
		}
		, editDetail: {},

		/**
		 * 新增加钱币所需
		 */
		outline:true,
		active_user:'',
		read:0,
		bq:0,
		showDiyList:false,
		title: null,
		pjClCoin: {},
		pjClCoinName: "",
		uploadList: [],
		uploadList2: [],
		visible: false,
		parentName:"",
		jzbScoretype:1,
		gmList: [
			{id: '03', name: '机制币'},
			{id: '02', name: '古钱币'},
			{id: '04', name: '银锭'},
			{id: '01', name: '纸币'}
		],
		statusList:resultArray,
		isPackList: isPackArray,
		jzbScoreList: jzbScoreArray,
		jzbAllScoreList: jzbAllScoreArray,
		ybAllScoreList: ybAllScoreArray,
		gqbScoreList: gqbScoreArray,
		ydScoreList: ydScoreListArray,
		zbScoreList: zbScoreArray,
		extbqList: extbqListArray,
		yd20List:[
			{id:"0",name:"空"},
			{id:"1/5",name:"1/5"},
			{id:"2/5",name:"2/5"},
			{id:"3/5",name:"3/5"},
			{id:"4/5",name:"4/5"},
			{id:"5/5",name:"5/5"}
		],
		yd20List2:[
			{id:"0",name:"空"},
			{id:"1/5",name:"1/5"},
			{id:"2/5",name:"2/5"},
			{id:"3/5",name:"3/5"},
			{id:"4/5",name:"4/5"},
			{id:"5/5",name:"5/5"}
		],
		ydQXList: ydQXArray,
		gqbQXList: gqbQXArray,
		jzbQXList: jzbQXArray,

		coinName:"",
		coinDynasty:"",
		coinAuthenticity:"",
		coinScores:"",
		coinRepair:"",
		coinScoreNote:"",
		coinUpdateTime:"",
		sendnum:"",
		tips:""

	},
	methods: {
		query: function () {
			vm.reload();
		},
		update: function (event) {
			let id = getSelectedRow("#jqGrid");
			if (id == null) {
				return;
			}
			var grid = $("#jqGrid");
			var rowData = grid.jqGrid("getRowData", id);
			vm.frameLink ="../sys/addsendform.html?id="+rowData.id;
			vm.showList = false;
			vm.showDiyList = false;
			vm.addSendform= false;
			vm.ChangeState = true;
			vm.title = "修改";
		},
		del: function (event) {
			let ids = getSelectedRows("#jqGrid");
			if (ids == null){
				return;
			}

			confirm('确定要删除选中的记录？', function () {
				Ajax.request({
					url: "../pjosendform/delete",
					params: JSON.stringify(ids),
					type: "POST",
					contentType: "application/json",
					successCallback: function () {
						alert('操作成功', function (index) {
							vm.reload();
						});
					}
				});
			});
		},
		add: function(event){
			let id = getSelectedRow("#jqGrid");
			if (id == null) {
				return;
			}
			var grid = $("#jqGrid");
			var rowData = grid.jqGrid("getRowData", id);
			vm.sendnum=rowData.sendnum;
			vm.showList = false;
			vm.showDiyList = true;
			vm.title = "新增";
			vm.read = 0;
			vm.pjClCoin = {"cointype":"04","zk":10};
			vm.uploadList = [];
		},
		queryzbbq: function () {
			var zkRes2 = (vm.pjClCoin.scoreshow2 != null  && vm.pjClCoin.scoreshow2 != undefined  && vm.pjClCoin.scoreshow2 != '');
			var  num=$(" input[ name='num' ] ").val();
			if(vm.pjClCoin.cointype=='01'){
				if(num!=""&&num!=null){
					//update
					if(vm.bq==0){
						vm.bq =1;
					}else if(vm.bq ==1){
						$.each(vm.zbScoreList, function(i, item){
							if(item.name==vm.pjClCoin.scoreshow2){
								scores = item.id.split("@@")
								if(scores.length>1){
									if(scores[1]>=70){
										vm.pjClCoin.extbqs = ['11','12'];
									}else if(scores[1]>=60){
										vm.pjClCoin.extbqs = ['12'];
									}else{
										vm.pjClCoin.extbqs = [];
									}
								}
							}
						});
					}
				}else{
					//add
					$.each(vm.zbScoreList, function(i, item){
						if(item.name==vm.pjClCoin.scoreshow2){
							scores = item.id.split("@@")
							if(scores.length>1){
								if(scores[1]>=70){
									vm.pjClCoin.extbqs = ['11','12'];
								}else if(scores[1]>=60){
									vm.pjClCoin.extbqs = ['12'];
								}else{
									vm.pjClCoin.extbqs = [];
								}

							}
						}
					});
				}
			}
		},
		clearPrice: function(){
			let value=vm.pjClCoin.result;
			if(value==3||value==5||value==7){
				vm.pjClCoin.bzfee=0;
				vm.pjClCoin.gjfee=0;
				vm.pjClCoin.zk=10;
				vm.pjClCoin.finalfee=0;
				vm.pjClCoin.boxfee=0;
			}
		},
		getFee: function (item) {
			if(item.zk == 0){
				vm.pjClCoin.finalfee = 0;
				item.finalfee =0;
				return;
			}
			var bzRes = (item.bzfee != null && item.bzfee != undefined && item.bzfee != '');
			var gjRes = (item.gjfee != null  && item.gjfee != undefined  && item.gjfee != '');
			var zkRes = (item.zk != null  && item.zk != undefined  && item.zk != '');
			var zk;
			if(zkRes){
				zk = item.zk;
			}else{
				zk = 10;
			}
			if (bzRes&&gjRes) {
				item.finalfee = Number((item.gjfee * zk) / 10).toFixed(2);
				return
			}else{
				if (bzRes) {
					item.finalfee = Number((item.bzfee * zk) / 10).toFixed(2);
					return
				}
				if (gjRes) {
					item.finalfee = Number((item.gjfee * zk) / 10).toFixed(2);
					return
				}
			}
		},
		reloadSearch: function() {
			vm.q = {
				name: '',
				nummber: '',
				gm: ''
			}
			vm.reload();
		},
		handleSubmit: function (name) {
			handleSubmitValidate(this, name, function () {
				vm.saveOrUpdate()
			});
		},
		handleReset: function (name) {
			handleResetForm(this, name);
		},
		handleView:function(name) {
			this.imgName = name;
			this.visible = true;
		},
		handleRemove:function(file) {
			// 从 upload 实例删除数据
			const fileList = this.uploadList;
			this.uploadList.splice(fileList.indexOf(file), 1);
		},
		handleSuccess:function(res, file) {
			// 因为上传过程为实例，这里模拟添加 url
			file.imgUrl = res.url;
			file.name = res.url;
			vm.uploadList.add(file);
		},
		handleBeforeUpload:function() {
			return true;

		},
		handleFormatError: function (file) {
			this.$Notice.warning({
				title: '文件格式不正确',
				desc: '文件 ' + file.name + ' 格式不正确，请上传 jpg 或 png 格式的图片。'
			});
		},
		handleMaxSize: function (file) {
			this.$Notice.warning({
				title: '超出文件大小限制',
				desc: '文件 ' + file.name + ' 太大，不能超过 10M。'
			});
		},
		getInfo: function(id){
			Ajax.request({
				url: "../pjosendform/info/"+id,
				async: true,
				successCallback: function (r) {
					vm.itemList = r.itemList;
					//vm.pjOSendform = r.pjOSendform;
				}
			});
		},
		saveOrUpdate: function (event) {

			let url = "../pjclcoin/save";
			vm.pjClCoin.imgList = vm.uploadList;
			var zkRes = (vm.pjClCoin.scoreshow != null  && vm.pjClCoin.scoreshow != undefined  && vm.pjClCoin.scoreshow != '');
			var zkRes2 = (vm.pjClCoin.scoreshow2 != null  && vm.pjClCoin.scoreshow2 != undefined  && vm.pjClCoin.scoreshow2 != '');
			var scoreRes = (vm.pjClCoin.score != null  && vm.pjClCoin.score != undefined  && vm.pjClCoin.score != '');
			var qxRes = (vm.pjClCoin.qxshow != null  && vm.pjClCoin.qxshow != undefined  && vm.pjClCoin.qxshow != '');



			if(vm.pjClCoin.cointype=='03'){
				if(vm.jzbScoretype == 1 || vm.jzbScoretype == 2){
					if(zkRes2){
						if(vm.jzbScoretype == 1){
							$.each(vm.jzbAllScoreList, function(i, item){
								if(item.name==vm.pjClCoin.scoreshow2){
									scores = item.id.split("@@")
									if(scores.length>1){
										vm.pjClCoin.scoreName = "精致币";
										vm.pjClCoin.scoreNum = scores[0];
										vm.pjClCoin.score = scores[1];
									}
								}
							});
						}else if(vm.jzbScoretype == 2){
							$.each(vm.ybAllScoreList, function(i, item){
								if(item.name==vm.pjClCoin.scoreshow2){
									scores = item.id.split("@@")
									if(scores.length>1){
										vm.pjClCoin.scoreName = "样币";
										vm.pjClCoin.scoreNum = scores[0];
										vm.pjClCoin.score = scores[1];
									}
								}
							});

						}
					}
				}else if(vm.jzbScoretype == 3){
					if(zkRes){
						$.each(vm.jzbScoreList, function(i, item){
							if(item.name==vm.pjClCoin.scoreshow){
								scores = item.id.split("@@")
								if(scores.length>1){
									vm.pjClCoin.scoreName = scores[0];
									vm.pjClCoin.scoreNum = scores[1];
									if(scores.length>2){
										vm.pjClCoin.score = scores[2];
									}
								}
							}
						});
					}
				}

				if(qxRes){
					$.each(vm.jzbQXList, function(i, item){
						if(item.name==vm.pjClCoin.qxshow){
							scores = item.id.split("@@")
							if(scores.length=2){
								vm.pjClCoin.cldef = scores[0];
								vm.pjClCoin.cldefNum = scores[0];
								vm.pjClCoin.cldefname = scores[1];
							}
						}
					});
				}
			}
			if(vm.pjClCoin.cointype=='02'){
				if(zkRes){
					$.each(vm.gqbScoreList, function(i, item){
						if(item.name==vm.pjClCoin.scoreshow){
							scores = item.id.split("@@")
							if(scores.length>1){
								vm.pjClCoin.scoreName = scores[0];
								vm.pjClCoin.scoreNum = scores[1];
								if(scores.length>2){
									vm.pjClCoin.score = scores[2];
								}
							}
						}
					});
				}
				if(qxRes){
					$.each(vm.gqbQXList, function(i, item){
						if(item.name==vm.pjClCoin.qxshow){
							scores = item.id.split("@@")
							if(scores.length>1){
								vm.pjClCoin.cldef = scores[0];
								vm.pjClCoin.cldefNum = scores[0];
								vm.pjClCoin.cldefname = scores[1];
							}
						}
					});
				}

			}
			if(vm.pjClCoin.cointype=='04'){
				vm.pjClCoin.score = "";
				console.log(vm.pjClCoin.scoreshow);
				if(zkRes){
					$.each(vm.ydScoreList, function(i, item){
						if(item.name==vm.pjClCoin.scoreshow){
							scores = item.id.split("@@")
							if(scores.length>1){
								vm.pjClCoin.scoreName = scores[0];
								vm.pjClCoin.scoreNum = scores[1];
								if(scores.length>2){
									vm.pjClCoin.score = scores[2];
								}
							}
						}
					});
				}
				if(qxRes){
					$.each(vm.ydQXList, function(i, item){
						if(item.name==vm.pjClCoin.qxshow){
							scores = item.id.split("@@")
							if(scores.length=2){
								vm.pjClCoin.cldef = scores[0];
								if(scores[0].indexOf("04A") != -1){
									vm.pjClCoin.cldef = "04A";
								}
								vm.pjClCoin.cldefNum = scores[0];
								vm.pjClCoin.cldefname = scores[1];
							}
						}
					});
				}
			}
			if(vm.pjClCoin.cointype=='01'){
				vm.pjClCoin.score = "";
				vm.pjClCoin.scoreName = "";
				vm.pjClCoin.scoreNum ="";
				vm.pjClCoin.cldef = "";
				vm.pjClCoin.cldefNum = "";
				vm.pjClCoin.cldefname = "";
				if(zkRes2){
					$.each(vm.zbScoreList, function(i, item){
						if(item.name==vm.pjClCoin.scoreshow2){
							scores = item.id.split("@@")
							scores = item.id.split("@@")
							if(scores.length>1){
								vm.pjClCoin.scoreName = scores[0];
								vm.pjClCoin.scoreNum = scores[0];
								vm.pjClCoin.score = scores[1];
							}
						}
					});
				}
			}


			var  str=vm.pjClCoin.zbnum;

			if(str!=""&&str!=null&&str.indexOf("<") != -1){
				vm.pjClCoin.zbnum =str.substring(1,str.length-1)+"v";
			}
			vm.pjClCoin.sendnum=vm.sendnum;
			Ajax.request({
				url: url,
				params: JSON.stringify(vm.pjClCoin),
				type: "POST",
				contentType: "application/json",
				successCallback: function (r) {
					alert('操作成功', function (index) {
						vm.reload();
					});
				}
			});
		},
		reload: function (event) {
			vm.showList = true;
			vm.showDiyList = false;
			vm.ChangeState = false;
			vm.addSendform = false;
			let page = $("#jqGrid").jqGrid('getGridParam', 'page');
			$("#jqGrid").jqGrid('setGridParam', {
				postData: {
					'rname': vm.q.rname.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'nummber': vm.q.nummber.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'sendnum': vm.q.sendnum.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'nickname': vm.q.nickname.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,'')
				},
				page: page
			}).trigger("reloadGrid");
		},
		reloadSearch: function() {
			vm.q = {
				rname: '',
				nummber: '',
				sendnum: '',
				nickname: ''
			}
			vm.reload();
		},
		handleSubmit: function (name) {
			handleSubmitValidate(this, name, function () {
				vm.saveOrUpdate()
			});
		},
		come_home: function(){
			if(vm.pjClCoin.serialNumber.length<8||vm.pjClCoin.serialNumber.length>10){
				vm.tips='编码位数不正确！';
				$("#submit").attr('disabled',true);
				return;
			}
			Ajax.request({
				url: '../pjclcoin/queryNumber',
				params: JSON.stringify(vm.pjClCoin),
				type: "POST",
				contentType: "application/json",
				successCallback: function (r) {
					if(r.total==1){
						$("#submit").attr('disabled',true);
						vm.tips='当前编码不可用！';
						$("#tips").css("color","red");
						return;
					}else{
						vm.tips='编码可用 √';
						$("#tips").css("color","green");
						$("#submit").attr('disabled',false);
					}

				}
			});

		},
		checkYes: function () {
			let ids = getSelectedRows("#jqGrid");
			if (ids == null){
				return;
			}
			vm.pjOSendform.checkName="”审核通过√”";
			layer.confirm('确定所选项修改为'+vm.pjOSendform.checkName+"？", {
				btn: ['确定','取消'] //按钮
			}, function(){
				Ajax.request({
					url: "../pjosendform/checkYesBatch",
					params: JSON.stringify(ids),
					type: "POST",
					contentType: "application/json",
					successCallback: function (r) {
						layer.msg('审核成功', {icon: 1});
						vm.reload();
					}
				});
			}, function(){
				layer.msg('取消');
			});

		},
		checkNo: function () {
			let ids = getSelectedRows("#jqGrid");
			if (ids == null){
				return;
			}
			vm.pjOSendform.checkName="”审核不通过x”";
			layer.confirm('确定所选项修改为'+vm.pjOSendform.checkName+"？", {
				btn: ['确定','取消'] //按钮
			}, function(){
				Ajax.request({
					url: "../pjosendform/checkNoBatch",
					params: JSON.stringify(ids),
					type: "POST",
					contentType: "application/json",
					successCallback: function (r) {
						layer.msg('驳回成功', {icon: 1});
						vm.reload();
					}
				});
			}, function(){
				layer.msg('取消');
			});

		},
		onLineScannerOpen:function () {
            let ids = getSelectedRows("#jqGrid");
            if (ids == null){
                return;
            }
            layer.confirm('为此订单开启官网查询？', {
                btn: ['确定','取消'] //按钮
            }, function(){
                Ajax.request({
                    url: "../pjosendform/onLineOpen",
                    params: JSON.stringify(ids),
                    type: "POST",
                    contentType: "application/json",
                    successCallback: function (r) {
                        layer.msg('已开启', {icon: 1});
                        vm.reload();
                    }
                });
            }, function(){
                layer.msg('取消');
            });

		},
		onLineScannerClose:function () {
			let ids = getSelectedRows("#jqGrid");
			if (ids == null){
				return;
			}
			layer.confirm('为此订单关闭官网查询？', {
				btn: ['确定','取消'] //按钮
			}, function(){
				Ajax.request({
					url: "../pjosendform/onLineClose",
					params: JSON.stringify(ids),
					type: "POST",
					contentType: "application/json",
					successCallback: function (r) {
						layer.msg('已关闭', {icon: 1});
						vm.reload();
					}
				});
			}, function(){
				layer.msg('取消');
			});

		}
	}
});