/**
 * To start!
 */
$(function () {
    $("#jqGrid").Grid({
        url: '../pjclcoin/dylist',
        colModel: [
            {label: 'id', name: 'id', key: true, hidden: true},
            {label: '钱币编号', name: 'nummber', width: 80, sortable: false},
            {label: '送评单号', name: 'sendnum', width: 60, sortable: false},
            {
                label: '结果', name: 'result', width: 60, sortable: false, formatter: function (value) {
                    switch (value) {
                        case 0:
                            return "未鉴定";
                            break;
                        case 1:
                            return "真";
                            break;
                        case 2:
                            return "赝品";
                            break;
                        case 3:
                            return "存疑";
                            break;
                        case 4:
                            return "性质伪";
                            break;
                        case 5:
                            return "不提供服务";
                            break;
                        case 6:
                            return "不适合评级";
                            break;
                        case 7:
                            return "撤评";
                            break;
                        case 8:
                            return "不适合评级(赝品)";
                            break;
                        case 9:
                            return "不适合评级(锭体、铭文修复超出规定)";
                            break;
                        case 10:
                            return "不适合评级(老假、老仿、臆造)";
                            break;
                        default:
                            return "未鉴定";
                            break;
                    }
                }

            },

            {label: '名称', name: 'nameex', width: 80, sortable: false},
            {label: '版别', name: 'edition', width: 60, sortable: false},
            {label: '附加', name: 'live', width: 80, sortable: false},
            {label: '年代', name: 'yearex', width: 60, sortable: false},
            {label: '等级', name: 'rank', width: 60, sortable: false},
            {label: '重量', name: 'weight', width: 60, sortable: false},
            {label: '尺寸', name: 'size', width: 60, sortable: false},
            {label: '品相分数', name: 'scoreex', width: 60, sortable: false},
            {label: '姓名 / 网名', name: 'rnameex', width: 80, sortable: false},
            {label: '费用', name: 'feeex', width: 60, sortable: false},
        ]
    });

    $("#jqGrid").on("click", 'tr[role="row"]', function () {
        $('#PrintBtn').attr("disabled", "disabled");
    });

    $("#cb_jqGrid").click(function (event) {
        $('#PrintBtn').attr("disabled", "disabled");
    });
});

var vm = new Vue({
    el: '#vue-table',
    data: {
        read: 0,
        showList: true,
        title: null,
        pjClCoin: {},
        uploadList: [],
        visible: false,
        parentName: "",
        statusList: [
            {id: 0, name: '未鉴定'},
            {id: 1, name: '真'},
            {id: 2, name: '赝品'},
            {id: 3, name: '存疑'},
            {id: 4, name: '性质伪'},
            {id: 5, name: '不提供服务'},
            {id: 6, name: '不适合评级'},
            {id: 7, name: '撤评'},
            {id: 110, name: '非真'}
        ],
        gmList: [
            {id: '03', name: '机制币'},
            {id: '02', name: '古钱币'},
            {id: '04', name: '银锭'},
            {id: '01', name: '纸币'}
        ],
        ruleValidate: {
            name: [
                {required: true, message: '名称不能为空', trigger: 'blur'}
            ]
        },
        q: {
            gm: '',
            nummbers: '',
            sendnum: ''
        },
        printType: 0,
        printTypeList: [
            {id: 0, name: '请选择标签类型'},
            //HongKong 特殊
            {id: 14, name: '银锭特殊', label: 'DingZi/xiaots.grf', service: 'SilverSmall'},
      // /*      {id: 1, name: '打印人名（临时-大小标签）', label: 'DingZi/xiao-name.grf', service: 'printName'},
            {id: 22, name: '广东集藏（金）', label: 'ZhiBi/GDJC1.grf', service: 'GDJC1'},
            {id: 22, name: '广东集藏（蓝）', label: 'ZhiBi/GDJC2.grf', service: 'GDJC1'},
            {id: 22, name: '广东集藏（红）', label: 'ZhiBi/GDJC3.grf', service: 'GDJC1'},
            {id: 22, name: '纸币中乾-(背特殊)', label: 'ZhiBi/zhibi-2-zhongqian-bflag.grf', service: 'ZhiBiflag'},
            {id: 1, name: '临时横标签', label: 'DingZi/name-heng.grf', service: 'printName'},
            //1.古钱币
            {id: 1, name: '古钱币（小）', label: 'GuQian/xiao.grf', service: 'GuQianBi'},
            {id: 1, name: '古钱币（中）', label: 'GuQian/zhong.grf', service: 'GuQianBi'},
            {id: 2, name: '古钱币（大）', label: 'GuQian/da.grf', service: 'GuQianBi'},
            {id: 2, name: '古钱币（大-新加坡）', label: 'GuQian/da(3).grf', service: 'GuQianBi'},
            {id: 2, name: '背标（新加坡）', label: 'GuQian/beibiao-3.grf', service: 'GuQianBi'},
            {id: 25, name: '古钱币（大-体积）', label: 'GuQian/da_tj.grf', service: 'GuQianBi'},
            //2.机制币
            {id: 3, name: '机制币（小）', label: 'JiZhi/xiao.grf', service: 'JiZhiBi'},
            {id: 3, name: '机制币（小特殊）', label: 'JiZhi/xiao3h.grf', service: 'JiZhiBi22'},
            {id: 3, name: '机制币（小）-1行', label: 'JiZhi/xiao-1.grf', service: 'JiZhiBi'},
            {id: 3, name: '机制币-香港（小）', label: 'JiZhi/xiao1.grf', service: 'JiZhiBi'},
            {id: 3, name: '机制币（小）真品', label: 'JiZhi/xiaozhen.grf', service: 'JiZhiBi'},
            {id: 3, name: '机制币（货布）', label: 'JiZhi/huobu.grf', service: 'JiZhiBi'},
            {id: 3, name: '机制币（货布泰国）', label: 'JiZhi/huobu2.grf', service: 'JiZhiBi'},
            {id: 4, name: '机制币（大）', label: 'JiZhi/da.grf', service: 'JiZhiBi'},
            {id: 4, name: '机制币-泰國', label: 'JiZhi/xiao2.grf',  service: 'JiZhiBi'},
            {id: 5, name: '机制币（小）en', label: 'JiZhi/xiao-en.grf', service: 'JiZhiBi'},
            {id: 5, name: '机制币-年代增长（小）en', label: 'JiZhi/xiao-en-niandai.grf', service: 'JiZhiBi'},
            {id: 6, name: '机制币（大）en', label: 'JiZhi/da-en.grf', service: 'JiZhiBi'},
            //3.货布
            {id: 7, name: '货布（竖）', label: 'HuoBu/shu.grf', service: 'HuoBu'},
            {id: 8, name: '货布（横）', label: 'HuoBu/heng.grf', service: 'HuoBu'},
            {id: 9, name: '货布（横）真品', label: 'HuoBu/heng-zhen.grf', service: 'HuoBu'},
            //4.假
            {id: 10, name: '假标签（不适合评级）', label: 'JiaHeBai/false.grf', service: 'Fake'},
            {id: 11, name: '假标签（赝品）', label: 'JiaHeBai/false.grf', service: 'Fake2'},
            //5.银锭
            {id: 12, name: '银锭（大）', label: 'DingZi/da.grf', service: 'SilverBig'},
            {id: 12, name: '银锭（大）3行', label: 'DingZi/da3.grf', service: 'SilverBig3'},
            {id: 13, name: '银锭（大）背', label: 'DingZi/da-bei.grf', service: 'SilverBigBei'},
            {id: 14, name: '银锭（小）2行-4字', label: 'DingZi/xiao.grf', service: 'SilverSmall'},
            {id: 14, name: '银锭（小）2行-4字（香港）', label: 'DingZi/xiao_HongKong.grf', service: 'SilverSmall'},
            {id: 14, name: '银锭（小）2行-7字', label: 'DingZi/xiao7.grf', service: 'SilverSmall'},
            {id: 14, name: '银锭（小）3行-4字', label: 'DingZi/xiao-3hang.grf', service: 'SilverSmall3'},
            {id: 14, name: '银锭（小）3行-7字', label: 'DingZi/xiao-3hang7.grf', service: 'SilverSmall3'},
            {id: 15, name: '银锭（小）竖', label: 'DingZi/xiao-shu.grf', service: 'SilverSmallStand'},
            {id: 16, name: '银锭长（横）', label: 'DingZi/chang-heng.grf', service: 'SilverLongAcross'},
            {id: 16, name: '银锭长（横）3行', label: 'DingZi/chang-heng3.grf', service: 'SilverLongAcross3'},
            {id: 17, name: '银锭长（竖）', label: 'DingZi/chang-shu.grf', service: 'SilverLongStand'},
            {id: 17, name: '银锭长（竖）3行', label: 'DingZi/chang-shu3.grf', service: 'SilverLongStand3'},
            {id: 18, name: '银锭长（竖）县', label: 'DingZi/xiao-shu-xiaobao.grf', service: 'SilverLongStand2'},
            {id: 14, name: '银锭（小）真品', label: 'DingZi/xiaozhen.grf', service: 'SilverSmall'},
            //6.纸币
            {id: 19, name: '纸币', label: 'ZhiBi/zhibi.grf', service: 'ZhiBi'},
            {id: 20, name: '纸币（金叶子）', label: 'ZhiBi/jinyezi.grf', service: 'ZhiBiJinYeZi'},
            {id: 21, name: '纸币（-2）', label: 'ZhiBi/zhibi-2.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（-2）彩标', label: 'ZhiBi/zhibi-2-color.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（移除码）', label: 'ZhiBi/zhibi-2-remove.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（2）', label: 'ZhiBi/zhibi-new.grf', service: 'ZhiBi'},
            {id: 22, name: '纸币（宝鑫）', label: 'ZhiBi/zhibi-2-baoxin.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（中乾）', label: 'ZhiBi/zhibi-2-zhongqian.grf', service: 'ZhiBi2'},
            {id: 22, name: '（华文仿宋-五角星）', label: 'ZhiBi/zhibi-2-zhongqian-flagfs.grf', service: 'ZhiBiflag'},
            {id: 22, name: '纸币（中乾-五角星）', label: 'ZhiBi/zhibi-2-zhongqian-flag.grf', service: 'ZhiBiflag'},
            {id: 22, name: '纸币新（宝鑫）', label: 'ZhiBi/zhibi-2-baoxin1.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币新（中乾）', label: 'ZhiBi/zhibi-2-zhongqian1.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币新（江门）', label: 'ZhiBi/zhibi-2-jiangmen01.grf', service: 'ZhiBics'},
            {id: 22, name: '纸币新（去冠号长沙）', label: 'ZhiBi/zhibi-2-changsha2.grf', service: 'ZhiBicsqqza'},
            {id: 22, name: '纸币新（长沙套装）', label: 'ZhiBi/zhibi-2-changsha2.grf', service: 'ZhiBics'},
            {id: 22, name: '纸币新（长沙刀币）', label: 'ZhiBi/zhibi-2-changsha3.grf', service: 'ZhiBics'},
            {id: 22, name: '纸币（宝鑫）-小', label: 'ZhiBi/zhibi-2-baoxin-xiao.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（宝鑫）-小-长字', label: 'ZhiBi/zhibi-2-baoxin-xiao-chang.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（宝鑫）-2角', label: 'ZhiBi/zhibi-2-baoxin-xiao2.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（宝鑫）-2元', label: 'ZhiBi/zhibi-2-baoxin-2.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币新（中乾）-小', label: 'ZhiBi/zhibi-2-baoxin-xiao1.grf', service: 'ZhiBi2'},
            // {id: 22, name: '纸币新（山西）-小', label: 'ZhiBi/zhibi-2-baoxin-xiao6.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币新（中乾）-小-长字', label: 'ZhiBi/zhibi-2-baoxin-xiao-chang1.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币新（中乾）-2角', label: 'ZhiBi/zhibi-2-baoxin-xiao21.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币新（中乾）-2元', label: 'ZhiBi/zhibi-2-baoxin-21.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（宝鑫）-2', label: 'ZhiBi/zhibi-2-baoxin2.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（移除码）中乾新', label: 'ZhiBi/zhibi-3-remove.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币中乾-(13行小标-去五星)', label: 'ZhiBi/zhibi-2022-zqpj-Noflag.grf', service: 'ZhiBiflag'},
            {id: 22, name: '纸币中乾-(13行小标-五角星)', label: 'ZhiBi/zhibi-2022-zqpj-flag.grf', service: 'ZhiBiflag'},
            {id: 22, name: '纸币中乾-(金标大)', label: 'ZhiBi/zhibi-2-zhongqian1-jinbiaoda.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币中乾-(金标大-去黑线)', label: 'ZhiBi/zhibi-2-zhongqian1-jinbiaoda-del.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（中乾-10行）粮票模板', label: 'ZhiBi/zhibi-foodCoupon-zhongqian-10.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（宝鑫-10行）粮票模板', label: 'ZhiBi/zhibi-foodCoupon-baoxing-10.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（中乾-13行）粮票模板', label: 'ZhiBi/zhibi-foodCoupon-zhongqian.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（中乾-13行）梅州', label: 'ZhiBi/zhibi-foodCoupon-zhongqian2.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币（中乾-13行）去银行' , label: 'ZhiBi/zhibi-doubleLine.grf', service: 'ZhiBiDoubleLine'},
            {id: 22, name: '纸币（宝鑫-13行）粮票模板', label: 'ZhiBi/zhibi-foodCoupon-baoxing.grf', service: 'ZhiBi2'},
            {id: 22, name: '纸币周口-(13行刀币)', label: 'ZhiBi/zhibi-zhoukou.grf', service: 'ZhiBiflag'},
            {id: 22, name: '纸币（周口普标五角星）', label: 'ZhiBi/zhibi-2-zhongqian-flag2.grf', service: 'ZhiBiflagzk'},
            {id: 22, name: '纸币（周口刀币五角星）', label: 'ZhiBi/zhibi-2022-zqpj-flag2.grf', service: 'ZhiBiflagzk'},
            //7.白
            {id: 23, name: '白标签', label: 'JiaHeBai/baibiao.grf', service: 'Bai'},
            {id: 24, name: '======================='},

            // 兼容小机子的标签模板
            {id: 1, name: '★大标签', label: 'new/da.grf', service: 'silver'},
            {id: 2, name: '★大标签(三排状态)', label: 'new/da3z.grf', service: 'silver3'},
            {id: 2, name: '★大标签(三排分数测试)', label: 'new/da3f.grf', service: 'SilverBig3'},
            {id: 3, name: '★大标签(东三省)', label: 'new/dongdansheng.grf', service: 'silver'},

            // 小标签
            {id: 4, name: '★小标签', label: 'new/xiao.grf', service: 'silver'},
            {id: 5, name: '★小标签(二排)', label: 'new/xiao2.grf', service: 'SilverBig3'},
            {id: 5, name: '★小标签(三排)', label: 'new/xiao3.grf', service: 'SilverBig3'},
            {id: 6, name: '★小标签(竖)', label: 'new/xiaoshu.grf', service: 'SilverSmallShu'},
            {id: 7, name: '★小标签(竖)三排', label: 'new/xiaoshu3.grf', service: 'SilverSmallShu3'},

            // 长横标横
            {id: 8, name: '★长标签横', label: 'new/h2.grf', service: 'across'},
            {id: 9, name: '★长标签横(三排)', label: 'new/h3.grf', service: 'across3'},

            //长竖标签
            {id: 10, name: '★长标竖', label: 'new/shu.grf', service: 'changS'},
            {id: 11, name: '★长标竖(县)', label: 'new/shu2.grf', service: 'changS_Xian'},
            {id: 12, name: '★长标竖(三排)', label: 'new/shu3.grf', service: 'changS3'},

            //集藏标签 大小：silver    长：across
            {id: 13, name: '★(集藏)大标', label: 'new/da-ji.grf', service: 'silverJi'},
            {id: 13, name: '★(集藏)大标-分数', label: 'new/da-ji1.grf', service: 'silverJi'},
            {id: 13, name: '★(集藏)大标-分数-4', label: 'new/da-ji14.grf', service: 'silverJi'},
            {id: 14, name: '★(集藏)小标', label: 'new/xiao-ji.grf', service: 'silverJi'},
            {id: 14, name: '★(集藏)小标-分数', label: 'new/xiao-ji1.grf', service: 'silverJi'},
            {id: 15, name: '★(集藏)长标', label: 'new/h2-ji.grf', service: 'acrossJi'},
            {id: 15, name: '★(集藏)长标-分数', label: 'new/h2-ji-f.grf', service: 'acrossJi1'},
            {id: 15, name: '★(集藏)长标-分数-4', label: 'new/h2-ji-f4.grf', service: 'acrossJi1'},
            {id: 16, name: '★(集藏)长标竖', label: 'new/shu-ji.grf', service: 'acrossJi'},
            {id: 16, name: '★(集藏)长标竖-分数', label: 'new/shu-ji1.grf', service: 'acrossJi1'},
            /*{id: 25, name: 'test', label: 'new/shu-ji111.grf', service: 'acrossJi1'},*/
        ],
        conversionType: 0,
         conversionList: [
            {id: 1, name: '默认',                value: '0'},
            {id: 2, name: '繁体（纸币除外）',     value: '1'},

        ],
        isPrintModel: false,
        printBuild: null,
    },
    methods: {

        query: function () {
            vm.reload();
        },
        reload: function (event) {
            $('#PrintBtn').attr("disabled", "disabled");
            vm.showList = true;
            var page = $("#jqGrid").jqGrid('getGridParam', 'page');
            var sendnums;
            if (vm.q.sendnums != undefined && vm.q.sendnums != '') {
                sendnums = vm.q.sendnums.replace(/\n/g, ",").replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, '');
            }
            if (vm.q.nummbers == "" && vm.q.sendnums == undefined) {
                layer.msg("编号不能为空！");
                return;
            }
            $("#jqGrid").jqGrid('setGridParam', {
                postData: {
                    'gm': vm.q.gm.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''),
                    'nummbers': vm.q.nummbers.replace(/\n/g, ",").replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''),
                    'sendnums': sendnums,
                },
                page: page
            }).trigger("reloadGrid");
        },
        reloadSearch: function () {
            vm.q = {
                gm: '',
                nummbers: '',
                sendnums: ''
            },
                vm.reload();
        },
        handleSubmit: function (name) {
            handleSubmitValidate(this, name, function () {
                vm.saveOrUpdate()
            });
        },
        print: function () {
            this.printBuild.print();
        },
        prePrint: function () {
            var that = this;
            var ids = $('#jqGrid').getGridParam("selarrrow");
            if (this.printType == null || this.printType == 0) {
                this.$Message.error('请选择标签类型');
                return;
            }

            function printItems(ids) {
                that.printBuild = new PrintBuild();
                that.printBuild.buildCoin(ids, that.printTypeList[that.printType], that.conversionList[that.conversionType]);
            }

            if (ids == null || ids.length == 0) {
                confirm('不选中默认打印当前页面，是否继续？',
                    function () {//确定事件
                        ids = $("#jqGrid").getDataIDs();
                        printItems(ids);
                    });
            } else {
                printItems(ids);
            }
            this.isPrintModel = true;
        },
        printWhite: function () {
            var that = this;
            var id = getSelectedRows("#jqGrid");
            if (id == null) {
                return;
            }
            var ids = $('#jqGrid').getGridParam("selarrrow");

            function printItems(ids) {
                that.printBuild = new PrintBuild();
                that.printBuild.buildBaiCoin(ids, that.conversionList[that.conversionType]);

            }

            if (ids == null || ids.length == 0) {
                confirm('不选中默认打印当前页面，是否继续？',
                    function () {//确定事件
                        ids = $("#jqGrid").getDataIDs();
                        printItems(ids);
                    });
            } else {
                printItems(ids);
            }
            this.isPrintModel = true;

        },
        coinCheck: function () {
            // 审核查询
            var that = this;
            var ids = $('#jqGrid').getGridParam("selarrrow");
            if (ids == undefined || ids == null || ids == "") {
                layer.msg("勾选结果为空");
                return;
            }
            var really = 0;
            var sendnummber = "";
            Ajax.request({
                url: "../pjclcoin/checkSendForm?ids=" + ids,
                type: "POST",
                contentType: "application/json",
                successCallback: function (r) {
                    var arr = r.pjClCoinList;
                    arr.map(function (item, index) {
                        if (item.checkstatus == 0) {
                            sendnummber += item.sendnummber + "、";
                            really++;
                        }
                    });
                }
            });

            if (really > 0) {
                layer.alert('送评单号：' + sendnummber + '未审核！', {
                    skin: 'layui-layer-molv'
                    , closeBtn: 0
                });
                return;
            }
            layer.msg('审核通过', {icon: 1});
            $('#PrintBtn').attr("disabled", false);
        }
    }
});