function filterParams(obj){


	let _newPar = {};
	for (let keyval in obj) {
		//如果对象属性的值不为空，就保存该属性（这里我做了限制，如果属性的值为0，保存该属性。如果属性的值全部是空格，属于为空。）
		if ((obj[keyval] === 0 || obj[keyval]) && obj[keyval].toString().replace(/(^\s*)|(\s*$)/g, '') !== '') {
			//记录属性
			_newPar[keyval] = obj[keyval];
		}
	}
	//返回对象
	return _newPar;
}

function extbqPack(extbq){

	if(extbq==""||extbq==null){
		return extbq;
	}
	extbq=extbq.replace("11@",";★");
	extbq=extbq.replace("12@",";EPQ");
	extbq=extbq.replace("13@",";NET");

	return extbq;

}

function extbqPackDe(extbq){

	if(extbq==""||extbq==null){
		return extbq;
	}
	extbq=extbq.replace(";★", "11@");
	extbq=extbq.replace(";EPQ","12@");
	extbq=extbq.replace(";NET","13@");

	return extbq;

}


function boxtypePack(boxtype){
	if(boxtype==""||boxtype==null){
		return boxtype;
	}

	if(boxtype==1){
		return "密封盒";
	}
	if(boxtype==2){
		return "开放式";
	}
	if(boxtype==3){
		return "证书";
	}
	if(boxtype==4){
		return "五星";
	}
	if(boxtype==5){
		return "四星";
	}
	if (boxtype == 6) {
		return "三星";
	}
}

function boxtypePackDe(boxtype){
	if(boxtype==""||boxtype==null){
		return boxtype;
	}

	if(boxtype=="密封盒"){
		return 1;
	}
	if(boxtype=="开放式"){
		return 2;
	}
	if(boxtype=="证书"){
		return 3;
	}
	if(boxtype=="五星"){
		return 4;
	}
	if(boxtype=="四星"){
		return 5;
	}
	if (boxtype == "三星") {
		return 6;
	}
}

// 						case 4:
// 							return "性质伪";
// 							break;
//
// 						case 5:
// 							return "不提供服务";
// 							break;
//
// 						case 6:
// 							return "不适合评级";
// 							break;
//
// 						case 7:
// 							return "撤评";
// 							break;
// 						case 8:
// 							return "不适合评级(赝品)";
// 							break;
// 						case 9:
// 							return "不适合评级(锭体、铭文修复超出规定)";
// 							break;
// 						case 10:
// 							return "不适合评级(老假、老仿、臆造)";
// 							break;
// 						default:
// 							return "未鉴定";
// 							break;
// 					}
// 				}
//				source:[ '0|未鉴定','1|真','2|赝品','3|存疑','4|不提供服务','5|不适合评级(赝品)','6|不适合评级(锭体、铭文修复超出规定)','9|不适合评级(老假、老仿、臆造)','8|不适合评级','7|撤评'],

function resultPack(result) {
	if(result==""||result==null){
		return result;
	}

	if(result==0){
		return "0|未鉴定";
	}
	if(result==1){
		return "1|真";
	}
	if(result==2){
		return "2|赝品";
	}
	if(result==3){
		return "3|存疑";
	}
	if(result==4){
		return "4|不提供服务";
	}
	if(result==5){
		return "5|不适合评级(赝品)";
	}
	if(result==6){
		return "6|不适合评级(锭体、铭文修复超出规定)";
	}if(result==7){
		return "7|撤评";
	}
	if(result==8){
		return "8|不适合评级";
	}
	if(result==9){
		return "9|不适合评级(老假、老仿、臆造)";
	}



}


function resultPackDe(result) {
	if(result==""||result==null){
		return result;
	}

	if(result=="0|未鉴定"){
		return 0;
	}
	if(result=="1|真"){
		return 1;
	}
	if(result=="2|赝品"){
		return 2;
	}
	if(result=="3|存疑"){
		return 3;
	}
	if(result=="4|不提供服务"){
		return 4;
	}
	if(result=="5|不适合评级(赝品)"){
		return 5;
	}
	if(result=="6|不适合评级(锭体、铭文修复超出规定)"){
		return 6;
	}if(result=="7|撤评"){
		return 7;
	}
	if(result=="8|不适合评级"){
		return 8;
	}
	if(result=="9|不适合评级(老假、老仿、臆造)"){
		return 9;
	}


}

function scoreshow2Pack1(scoreshow2){
	if(scoreshow2=="Superb Gem Unc70"){
		return "Superb Gem Unc";
	}
else if(scoreshow2=="Superb Gem Unc69"){return "Superb Gem Unc";}
else if(scoreshow2=="Superb Gem Unc68"){return "Superb Gem Unc";}
else if(scoreshow2=="Superb Gem Unc67"){return "Superb Gem Unc";}
else if(scoreshow2=="Gem Uncirculated66"){return "Gem Uncirculated";}
else if(scoreshow2=="Gem Uncirculated65"){return "Gem Uncirculated";}
else if(scoreshow2=="Choice Uncirculated64"){return "Choice Uncirculated";}
else if(scoreshow2=="Choice Uncirculated63"){return "Choice Uncirculated";}
else if(scoreshow2=="Uncirculated62"){return "Uncirculated";}
else if(scoreshow2=="Uncirculated61"){return "Uncirculated";}
else if(scoreshow2=="Uncirculated60"){return "Uncirculated";}
else if(scoreshow2=="Choice About Unc58"){return "Choice About Unc";}
else if(scoreshow2=="About Uncirculated55"){return "About Uncirculated";}
else if(scoreshow2=="About Uncirculated53"){return "About Uncirculated";}
else if(scoreshow2=="About Uncirculated50"){return "About Uncirculated";}
else if(scoreshow2=="Choice Extremely Fine45"){return "Choice Extremely Fine";}
else if(scoreshow2=="Extremely Fine40"){return "Extremely Fine";}
else if(scoreshow2=="Choice Very Fine35"){return "Choice Very Fine";}
else if(scoreshow2=="Very Fine30"){return "Very Fine";}
else if(scoreshow2=="Very Fine25"){return "Very Fine";}
else if(scoreshow2=="Genuine"){return "Genuine";}
else if(scoreshow2=="Genuine真品"){return "Genuine";}
else if(scoreshow2=="极美品"){return "极美品";}
else if(scoreshow2=="精美品"){return "精美品";}
else if(scoreshow2=="美品"){return "美品";}
else if(scoreshow2=="上美品"){return "上美品";}




}



function scoreshow2Pack2(scoreshow2){

	if(scoreshow2=="Superb Gem Unc70"){
		return "70";
	}
	else if(scoreshow2=="Superb Gem Unc69"){return "69";}
	else if(scoreshow2=="Superb Gem Unc68"){return "68";}
	else if(scoreshow2=="Superb Gem Unc67"){return "67";}
	else if(scoreshow2=="Gem Uncirculated66"){return "66";}
	else if(scoreshow2=="Gem Uncirculated65"){return "65";}
	else if(scoreshow2=="Choice Uncirculated64"){return "64";}
	else if(scoreshow2=="Choice Uncirculated63"){return "63";}
	else if(scoreshow2=="Uncirculated62"){return "62";}
	else if(scoreshow2=="Uncirculated61"){return "61";}
	else if(scoreshow2=="Uncirculated60"){return "60";}
	else if(scoreshow2=="Choice About Unc58"){return "58";}
	else if(scoreshow2=="About Uncirculated55"){return "55";}
	else if(scoreshow2=="About Uncirculated53"){return "53";}
	else if(scoreshow2=="About Uncirculated50"){return "50";}
	else if(scoreshow2=="Choice Extremely Fine45"){return "45";}
	else if(scoreshow2=="Extremely Fine40"){return "40";}
	else if(scoreshow2=="Choice Very Fine35"){return "35";}
	else if(scoreshow2=="Very Fine30"){return "30";}
	else if(scoreshow2=="Very Fine25"){return "25";}
	else if(scoreshow2=="Genuine"){return "";}
	else if(scoreshow2=="Genuine真品"){return "真品";}
	else if(scoreshow2=="极美品"){return "极美品";}
	else if(scoreshow2=="精美品"){return "精美品";}
	else if(scoreshow2=="美品"){return "美品";}
	else if(scoreshow2=="上美品"){return "上美品";}




}


function editionPack(edition) {
	return "12|车床工人";
	if(edition==""||edition==null){
		return edition;
	}

	if(edition==0){
		return "12|车床工人";
	}




}


function zbnumPack(zbnum) {

	zbnum=zbnum.replace("<","〈");
	zbnum=zbnum.replace(">","〉");

	return zbnum;

}



function catalogPack(catalog) {


	 if(catalog==""||catalog==null){
		
		 return catalog;
	 }else {
         catalog=catalog.replace("Pick# ","");
         catalog=catalog.replace("Pick#","");
	 	return catalog;
	 }

}

function catalogPackSave(catalog) {
	if(catalog.replace(/(^s*)|(s*$)/g, "").length ==0){
		return catalog;
	}else {
		return "Pick# "+catalog;
	}

}




var setting = {
	data: {
		simpleData: {
			enable: true,
			idKey: "deptId",
			pIdKey: "parentId",
			rootPId: -1
		},
		key: {
			url: "nourl"
		},
		jzbScoretype:1
	}
};
var ztree;

let vm = new Vue({
	el: '#rrapp',
	data: {
		dataNum:1,
		btnNum:1,
		read:0,
		showList: true,
		title: null,
		pjClCoin: {},
		activeGrid:"#jqGrid1",
		projects1: [{}],
		projects2: [{}],
		projects3: [{}],
		projects4: [{}],
		projects4Ve: [{}],
		projects4Ve1: [{}],
		projects5Ve: [],
		uploadList: [{}],
		visible: false,
		parentName:"",
		statusList: resultArray,
		jzbScoreList: jzbAllofScoreArray,
		gqbScoreList: gqbScoreArray,
		ydScoreList: ydScoreListArray,
		zbScoreList: zbScoreArray,
		extbqList: extbqListArray,
		ydQXList: ydQXArray,
		gqbQXList: gqbQXArray,
		jzbQXList: jzbQXArray,
		extbqList: extbqListArray,
		packTypeList: [
			{id: "1", name: '密封盒'},
			{id: "2", name: '开放式'},
			{id: "3", name: '证书'}
		],
		ruleValidate: {
			name: [
				{required: true, message: '名称不能为空', trigger: 'blur'}
			]
		},
		q: {
			nummber: '',
			addType:'0'
		},
		nummbers: '',
	},
	methods: {
		getDepts: function (item2,index2,n1,v1){
			var zkRes = (item2.scoreshow != null  && item2.scoreshow != undefined  && item2.scoreshow != '');
			var zkRes2 = (item2.scoreshow2 != null  && item2.scoreshow2 != undefined  && item2.scoreshow2 != '');
			var scoreRes = (item2.score != null  && item2.score != undefined  && item2.score != '');
			var qxRes = (item2.qxshow != null  && item2.qxshow != undefined  && item2.qxshow != '');
			var prjoects;
			if("#jqGrid1"==vm.activeGrid){
				prjoects = vm.projects1;
				item2.cointype = "02";
			}
			if("#jqGrid2"==vm.activeGrid){
				prjoects = vm.projects2;
				item2.cointype = "04"
			}
			if("#jqGrid3"==vm.activeGrid){
				prjoects = vm.projects3;
				item2.cointype = "03";
			}
			if("#jqGrid4"==vm.activeGrid){
				prjoects = vm.projects4;
				item2.cointype = "01";

			}
			if("#jqGrid5"==vm.activeGrid){
				prjoects = vm.projects4;
				item2.cointype = "01";

			}
			//debugger;
			if(index2==0){
				$.each(prjoects, function(i, item3){
					Vue.set(item3,n1,v1);
				});
			}


		},
		getDept: function (item2,index2) {
			var zkRes = (item2.scoreshow != null  && item2.scoreshow != undefined  && item2.scoreshow != '');
			var zkRes2 = (item2.scoreshow2 != null  && item2.scoreshow2 != undefined  && item2.scoreshow2 != '');
			var scoreRes = (item2.score != null  && item2.score != undefined  && item2.score != '');
			var qxRes = (item2.qxshow != null  && item2.qxshow != undefined  && item2.qxshow != '');
			var prjoects;
			if("#jqGrid1"==vm.activeGrid){
				prjoects = vm.projects1;
				item2.cointype = "02";
			}
			if("#jqGrid2"==vm.activeGrid){
				prjoects = vm.projects2;
				item2.cointype = "04"
			}
			if("#jqGrid3"==vm.activeGrid){
				prjoects = vm.projects3;
				item2.cointype = "03";
			}
			if("#jqGrid4"==vm.activeGrid){
				prjoects = vm.projects4;
				item2.cointype = "01";
			}
			if(item2.cointype=='03'){

				if(zkRes){
					$.each(vm.jzbScoreList, function(i, item){
						if(item.name==item2.scoreshow){
							scores = item.id.split("@@")
							if(scores.length>1){
								item2.scoreName = scores[0];
								item2.scoreNum = scores[1];
								if(scores.length>2){
									item2.score = scores[2];
								}
							}
						}
					});
				}

				if(qxRes){
					$.each(vm.jzbQXList, function(i, item){
						if(item.name==item2.qxshow){
							scores = item.id.split("@@")
							if(scores.length=2){
								item2.cldef = scores[0];
								item2.cldefNum = scores[0];
								item2.cldefname = scores[1];
							}
						}
					});
				}
			}
			if(item2.cointype=='02'){
				if(zkRes){
					$.each(vm.gqbScoreList, function(i, item){
						if(item.name==item2.scoreshow){
							scores = item.id.split("@@")
							if(scores.length>1){
								item2.scoreName = scores[0];
								item2.scoreNum = scores[1];
								if(scores.length>2){
									item2.score = scores[2];
								}
							}
						}
					});
				}
				if(qxRes){
					$.each(vm.gqbQXList, function(i, item){
						if(item.name==item2.qxshow){
							scores = item.id.split("@@")
							if(scores.length>1){
								item2.cldef = scores[0];
								item2.cldefNum = scores[0];
								item2.cldefname = scores[1];
							}
						}
					});
				}

			}
			if(item2.cointype=='04'){
				item2.score = "";
				if(zkRes){
					$.each(vm.ydScoreList, function(i, item){
						if(item.name==item2.scoreshow){
							scores = item.id.split("@@")
							if(scores.length>1){
								item2.scoreName = scores[0];
								item2.scoreNum = scores[1];
								if(scores.length>2){
									item2.score = scores[2];
								}
							}
						}
					});
				}
				if(qxRes){
					$.each(vm.ydQXList, function(i, item){
						if(item.name==item2.qxshow){
							scores = item.id.split("@@")
							if(scores.length=2){
								item2.cldef = scores[0];
								if(scores[0].indexOf("04A") != -1){
									item2.cldef = "04A";
								}
								item2.cldefNum = scores[0];
								item2.cldefname = scores[1];
							}
						}
					});
				}
			}
			if(item2.cointype=='01'){
				item2.score = "";
				item2.scoreName = "";
				item2.scoreNum ="";
				item2.cldef = "";
				item2.cldefNum = "";
				item2.cldefname = "";
				if(zkRes2){
					$.each(vm.zbScoreList, function(i, item){
						if(item.name==item2.scoreshow2){
							scores = item.id.split("@@")
							if(scores.length>1){
								item2.scoreName = scores[0];
								item2.scoreNum = scores[0];
								item2.score = scores[1];
							}
						}
					});
				}
			}
			//debugger;
			if(index2==0){
				$.each(prjoects, function(i, item3){
					item3.scoreName = item2.scoreName ;
					item3.scoreNum = item2.scoreNum ;
					item3.score = item2.score ;
					item3.cldef = item2.cldef;
					item3.cldefNum = item2.cldefNum;
					item3.cldefname = item2.cldefname;
					item3.qxshow = item2.qxshow;
					item3.scoreshow2 = item2.scoreshow2;
					item3.scoreshow = item2.scoreshow;
				});
			}
		},
		deptTree: function () {
			openWindow({
				title: "选择纲目",
				area: ['300px', '450px'],
				content: jQuery("#deptLayer"),
				btn: ['确定', '取消'],
				btn1: function (index) {
					var node = ztree.getSelectedNodes();
					//选择上级纲目
					item2.cointype = node[0].deptId;
					vm.parentName = node[0].name;

					layer.close(index);
				}
			});
		},
		query: function () {
			vm.reload();
		},
		setActiveGrid: function (name) {
			vm.activeGrid = name;
			$(".ivu-input")[0].focus();
		},
		sm: function () {
			var prjoects;
			var nummber = vm.q.nummber.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,'');
			var numbers = [];
			var gm;
			if("#jqGrid1"==vm.activeGrid){
				prjoects = vm.projects1;
				gm = "02";
			}
			if("#jqGrid2"==vm.activeGrid){
				prjoects = vm.projects2;
				gm = "04"
			}
			if("#jqGrid3"==vm.activeGrid){
				prjoects = vm.projects3;
				gm = "03";
			}
			if("#jqGrid4"==vm.activeGrid){
				prjoects = vm.projects4;
				gm = "01";
			}
			if("#jqGrid5"==vm.activeGrid){
				prjoects = vm.projects4Ve1;
				gm = "01";
			}
			//debugger;
			var result = false;
			if(prjoects.length == 1){
				result = true;
			}else{
				$.each(prjoects, function(i, item){
					if(i==0){
						return true;
					}else{
						if(item.nummber==nummber){
							iview.Message.error("该检查条形码已存在，无需重复录入!");
							result = false;
							return false;
						}else{
							result = true;
							numbers.push(item.nummber);
						}
					}

				});
			}

			if(!result){
				return;
			}
			var param = {"nummber":nummber,"gm":gm,"addType":vm.q.addType,"numbers":numbers};
			Ajax.request({
				url: "../pjclcoin/queryAllSm",
				params: JSON.stringify(param),
				contentType: "application/json",
				type: 'POST',
				successCallback: function (r) {
					var gm;

				/*	$.each(r.list, function(i, item){
						if("#jqGrid1"==vm.activeGrid){
							vm.projects1.push(item);
						}
						if("#jqGrid2"==vm.activeGrid){
							vm.projects2.push(item);
						}
						if("#jqGrid3"==vm.activeGrid){
							vm.projects3.push(item);
						}
						if("#jqGrid4"==vm.activeGrid){
							vm.projects4.push(item);

						}
					});*/

					// console.log(r.list);
					$.each(r.list, function(i, item){
						if("#jqGrid1"==vm.activeGrid){
							vm.projects1.push(item);
						}
						if("#jqGrid2"==vm.activeGrid){
							vm.projects2.push(item);
						}
						if("#jqGrid3"==vm.activeGrid){
							vm.projects3.push(item);
						}
						if("#jqGrid4"==vm.activeGrid){

							vm.projects4Ve.push(item);
							// vm.projects4.push(item);
						}
						if("#jqGrid5"==vm.activeGrid){

							// vm.projects4Ve.push(item);//重复单号判断
							vm.projects4Ve1.push(item);

							let data1T =[item.nummber,
								         item.name,
								         item.name2,
								  		 item.live,
										 item.edition,
								//editionPack(item.edition),
										 item.niandai,
								item.zbnum,
								  		 item.bank,
								  		 item.scoreshow2,
								extbqPack(item.extbq),
								resultPack(item.result),
								  		 item.addr,
								catalogPack(item.catalog),
								  		 item.coindesc,
								boxtypePack(item.boxtype),
								  		 item.backweight,
								  		 item.description];//
							data1T.push(item.addType);
							data1T.push(item.amount);
							data1T.push(item.boxfee);
							data1T.push(item.bzfee);
							data1T.push(item.classid);
							data1T.push(item.cldef);
							data1T.push(item.cldefNum);
							data1T.push(item.cldefname);
							data1T.push(item.cointype);
							data1T.push(item.corder);
							data1T.push(item.createTime);
							data1T.push(item.createUser);
							data1T.push(item.ctCaizhi);
							data1T.push(item.ctJiyuan);
							data1T.push(item.ctPrint);
							data1T.push(item.ctValue);
							data1T.push(item.ctZhuzb);
							data1T.push(item.extbqs);
							data1T.push(item.extbqs1);
							data1T.push(item.extbqs2);
							data1T.push(item.feeex);
							data1T.push(item.finalfee);
							data1T.push(item.gjfee);
							data1T.push(item.gm);
							data1T.push(item.gmname);
							data1T.push(item.grade);
							data1T.push(item.id);
							data1T.push(item.ids);
							data1T.push(item.imgList);
							data1T.push(item.indate);
							data1T.push(item.intro);
							data1T.push(item.ispack);
							data1T.push(item.issm);
							data1T.push(item.jiaji);
							data1T.push(item.jiajifee);

							data1T.push(item.marker);
							data1T.push(item.mianzhi);
							data1T.push(item.name3);
							data1T.push(item.nameex);
							data1T.push(item.nickname);
							data1T.push(item.numbers);
							data1T.push(item.pic);
							data1T.push(item.qtfee);
							data1T.push(item.qxshow);
							data1T.push(item.rank);
							data1T.push(item.rname);
							data1T.push(item.rnameex);
							data1T.push(item.score);
							data1T.push(item.scoreDesc);
							data1T.push(item.scoreName);
							data1T.push(item.scoreNum);
							data1T.push(item.scoreex);
							data1T.push(item.scoreshow);
							data1T.push(item.sendnum);
							data1T.push(item.shape);
							data1T.push(item.shuizhong);
							data1T.push(item.size);
							data1T.push(item.smtime);
							data1T.push(item.status);
							data1T.push(item.subclassid);
							data1T.push(item.thirdclassid);
							data1T.push(item.thumb);
							data1T.push(item.updateTime);
							data1T.push(item.updateUser);
							data1T.push(item.weight);
							data1T.push(item.year);
							data1T.push(item.yearex);
							data1T.push(item.zk);
							vm.projects5Ve.push(data1T);
							// vm.projects4.push(item);

							//盒子类型 boxtype 密封盒1 开放式2 证书3
							//★ /EPQ/NET extbq ★11@  EPQ12@ NET13@ 三个11@12@13@
							//真伪 result 0 1 2 3...


						}




						//dataNum  btnNum
					});
					//console.log("------查询-----");
					//console.log(vm.projects5Ve);
					// console.log(vm.projects5Ve);
					//
					// let data1 =[];
					//
					//
					// 	let objce=vm.projects5Ve;
					// 	for (let i = 1; i < (objce.length-1); i++) {
					// 		let data1T =[objce[i].nummber,objce[i].name,objce[i].name2,objce[i].edition,objce[i].niandai,objce[i].zbnum,objce[i].bank,objce[i].scoreshow2,objce[i].extbqs,objce[i].result
					// 			,objce[i].addr,objce[i].catalog,objce[i].coindesc,objce[i].boxtype,objce[i].backweight,objce[i].description];//

							// data1T.push(objce[i].addType);
							// data1T.push(objce[i].amount);
							// data1T.push(objce[i].boxfee);
							// data1T.push(objce[i].bzfee);
							// data1T.push(objce[i].classid);
							// data1T.push(objce[i].cldef);
							// data1T.push(objce[i].cldefNum);
							// data1T.push(objce[i].cldefname);
							// data1T.push(objce[i].cointype);
							// data1T.push(objce[i].corder);
							// data1T.push(objce[i].createTime);
							// data1T.push(objce[i].createUser);
							// data1T.push(objce[i].ctCaizhi);
							// data1T.push(objce[i].ctJiyuan);
							// data1T.push(objce[i].ctPrint);
							// data1T.push(objce[i].ctValue);
							// data1T.push(objce[i].ctZhuzb);
							// data1T.push(objce[i].extbq);
							// data1T.push(objce[i].extbqs1);
							// data1T.push(objce[i].extbqs2);
							// data1T.push(objce[i].feeex);
							// data1T.push(objce[i].finalfee);
							// data1T.push(objce[i].gjfee);
							// data1T.push(objce[i].gm);
							// data1T.push(objce[i].gmname);
							// data1T.push(objce[i].grade);
							// data1T.push(objce[i].id);
							// data1T.push(objce[i].ids);
							// data1T.push(objce[i].imgList);
							// data1T.push(objce[i].indate);
							// data1T.push(objce[i].intro);
							// data1T.push(objce[i].ispack);
							// data1T.push(objce[i].issm);
							// data1T.push(objce[i].jiaji);
							// data1T.push(objce[i].jiajifee);
							// data1T.push(objce[i].live);
							// data1T.push(objce[i].marker);
							// data1T.push(objce[i].mianzhi);
							// data1T.push(objce[i].name3);
							// data1T.push(objce[i].nameex);
							// data1T.push(objce[i].nickname);
							// data1T.push(objce[i].numbers);
							// data1T.push(objce[i].pic);
							// data1T.push(objce[i].qtfee);
							// data1T.push(objce[i].qxshow);
							// data1T.push(objce[i].rank);
							// data1T.push(objce[i].rname);
							// data1T.push(objce[i].rnameex);
							// data1T.push(objce[i].score);
							// data1T.push(objce[i].scoreDesc);
							// data1T.push(objce[i].scoreName);
							// data1T.push(objce[i].scoreNum);
							// data1T.push(objce[i].scoreex);
							// data1T.push(objce[i].scoreshow);
							// data1T.push(objce[i].sendnum);
							// data1T.push(objce[i].shape);
							// data1T.push(objce[i].shuizhong);
							// data1T.push(objce[i].size);
							// data1T.push(objce[i].smtime);
							// data1T.push(objce[i].status);
							// data1T.push(objce[i].subclassid);
							// data1T.push(objce[i].thirdclassid);
							// data1T.push(objce[i].thumb);
							// data1T.push(objce[i].updateTime);
							// data1T.push(objce[i].updateUser);
							// data1T.push(objce[i].weight);
							// data1T.push(objce[i].year);
							// data1T.push(objce[i].yearex);
							// data1T.push(objce[i].zk);

							// data1T.nummber=objce[i].nummber;
							// data1T.name=objce[i].name;
							// data1T.name2=objce[i].name2;
							// data1T.edition=objce[i].edition;
							// data1T.niandai=objce[i].niandai;
							// data1T.zbnum=objce[i].zbnum;
							// data1T.bank=objce[i].bank;
							// data1T.scoreshow2=objce[i].scoreshow2;
							// data1T.extbqs=objce[i].extbqs;
							// data1T.result=objce[i].result;
							// data1T.addr=objce[i].addr;
							// data1T.catalog=objce[i].catalog;
							// data1T.coindesc=objce[i].coindesc;
							// data1T.boxtype=objce[i].boxtype;
							// data1T.backweight=objce[i].backweight;
							// data1T.description=objce[i].description;
						// 	data1.push(data1T);
						//
						// }


					if (vm.projects5Ve.length>1){
						vm.projects5Ve.pop();//删掉最后一行空数据
					}

					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);


					//table1.deleteRow(0,);
					table1.setData(vm.projects5Ve);
						// console.log(vm.projects5Ve);




					/*let listData=r.list;
					let listDataLen=r.list.length;//总数*/

					let listDataLen1=vm.projects4Ve.length;//总数

					vm.btnNum=Math.ceil(listDataLen1/50);//总页数
					// vm.dataNum=1;
					$("#spanNum").text(vm.dataNum);
					$("#spanNum2").text(vm.btnNum);

					vm.projects4= [];
					for (let i = 0; i < (vm.btnNum==1?listDataLen1:50); i++) {
						// console.log(listData[i]);
						vm.projects4.push(vm.projects4Ve[i]);
					}






					/*for (let i = 0; i < 10; i++) {
						if("#jqGrid4"==vm.activeGrid){

							vm.projects4.push(r.list[i].item);

						}
					}*/
					/*console.log(vm.btnNum);
					console.log(vm.projects4);
					console.log(vm.projects4Ve);*/



					vm.q.nummber = "";
					$(".ivu-input")[0].focus();
					alert('录入成功', function (index) {
						vm.reload();

						// $("#spreadsheet1").change(function(){
						// 	console.log($(this));
						// 	console.log('样式改变1');
						// });
						//
						// $("#spreadsheet1").bind("DOMNodeInserted",function(e){
						// 	console.log($(this));
						// 	console.log(e);
						// 	console.log('样式改变2');
						// });
						// $("#spreadsheet1").bind("MutationObserver",function(e){
						// 	console.log($(this));
						// 	console.log(e);
						// 	console.log('样式改变3');
						// });

						// IE老版本使用onpropertychange
						// 新的IE和其他浏览器使用MutationObserver跟踪
						// console.log("点击了修改");
						// $("[data-x=6]","[data-y=1]").click();
						// $("[data-x=6]","[data-y=1]").click();
						// console.log($("[data-x=6]","[data-y=1]"));
					});
				}
			});
		},
		spanNumOS: function(item){


			//dataNum  btnNum总页数
			if(item==0){//上
				if (vm.dataNum>1) {//&&vm.dataNum<=vm.btnNum
					vm.dataNum=vm.dataNum-1;
					let listDataLen1=vm.projects4Ve.length;//总数
					vm.btnNum=Math.ceil(listDataLen1/50);//总页数
					$("#spanNum").text(vm.dataNum);
					vm.projects4= [{}];
					for (let i = (vm.dataNum-1)*50; i < (((vm.dataNum-1)*50+50)>listDataLen1?listDataLen1:((vm.dataNum-1)*50+50)); i++) {
						// console.log(listData[i]);
						vm.projects4.push(vm.projects4Ve[i]);
					}
				};
			}
			if(item==1){//下
				if (vm.dataNum<vm.btnNum) {//&&vm.dataNum<=vm.btnNum
					vm.dataNum=vm.dataNum+1;
					let listDataLen1=vm.projects4Ve.length;//总数
					vm.btnNum=Math.ceil(listDataLen1/50);//总页数
					$("#spanNum").text(vm.dataNum);
					vm.projects4= [{}];
					for (let i = (vm.dataNum-1)*50; i < (((vm.dataNum-1)*50+50)>listDataLen1?listDataLen1:((vm.dataNum-1)*50+50)); i++) {
						// console.log(listData[i]);
						vm.projects4.push(vm.projects4Ve[i]);
					}
				};
			}

			vm.reload();
/*			console.log(vm.projects4);
			console.log(vm.projects4Ve);*/

		},

		queryzbbq: function (item) {
			if(item.cointype=='01'){
				if(vm.bq!=0){
					var zkRes2 = (item.scoreshow2 != null  && item.scoreshow2 != undefined  && item.scoreshow2 != '');
					$.each(vm.zbScoreList, function(i, item){
						if(item.name==item.scoreshow2){
							scores = item.id.split("@@")
							if(scores.length>1){
								if(scores[1]>=70){
									item.extbqs = ['11','12'];
								}else if(scores[1]>=65){
									item.extbqs = ['12'];
								}else{
									item.extbqs = [];
								}

							}
						}
					});
				}else{
					vm.bq =1;
				}

			}
		},
		add: function () {
			vm.showList = false;
			vm.title = "新增";
			vm.read = 0;
			item2 = {"cointype":"01"};
			vm.uploadList = [];
			vm.getDept();
		},
		update: function (event) {
			var ids = getSelectedRows(vm.activeGrid);
			var project;
			var tips = '确定要将选中的记录修改成模板内容？';
			if (ids == null) {
				ids = $(vm.activeGrid).jqGrid('getDataIDs');
				tips = '确定要将该页面的所有记录修改成模板内容？';
			}
			if("#jqGrid1"==vm.activeGrid){
				project = vm.project1;
			}
			if("#jqGrid2"==vm.activeGrid){
				project = vm.project2;
			}
			if("#jqGrid3"==vm.activeGrid){
				project = vm.project3;
			}
			if("#jqGrid4"==vm.activeGrid){
				project = vm.project4;
			}
			var zkRes = (project.fenshu != null  && project.fenshu != undefined  && project.fenshu != '');
			if(zkRes){
				scores = project.fenshu.split("@@")
				if(scores.length=2){
					project.scoreName = scores[0];
					project.scoreNum = scores[1];
				}
			}
			project.ids = ids;
			project = filterParams(project);
			confirm(tips, function () {
				Ajax.request({
					url: "../pjclcoin/updateBatch",
					params: JSON.stringify(project),
					contentType: "application/json",
					type: 'POST',
					successCallback: function () {
						alert('操作成功', function (index) {
							vm.reload();
						});
					}
				});
			});
		},
		toSee: function (event) {
			let id = getSelectedRow("#jqGrid");
			if (id == null) {
				return;
			}
			vm.read = 1;
			vm.showList = false;
			vm.title = "修改";
			vm.uploadList = [];
			vm.getDept();
			vm.getInfo(id)
		},
		saveOrUpdate: function (event) {
			let url = item2.id == null ? "../pjclcoin/save" : "../pjclcoin/update";
			item2.imgList = vm.uploadList;
			Ajax.request({
				url: url,
				params: JSON.stringify(item2),
				type: "POST",
				contentType: "application/json",
				successCallback: function (r) {
					alert('操作成功', function (index) {
						vm.reload();
					});
				}
			});
		},
		rightClick:function(){

			numberRow="";
			numberRowOk=0;
            //console.log(table1.getSelectedColumns());//列
            //console.log(table1.getSelectedRows());//行
            //console.log(table1.getValueFromCoords(table1.getSelectedColumns()[0], table1.getSelectedRows()[0].rowIndex-1));//坐标取值
            let xTab=table1.getSelectedColumns()[0]+1;
            let yTab=table1.getSelectedRows()[0].rowIndex;//选择第一
            let y1Tab=table1.getSelectedRows()[table1.getSelectedRows().length-1].rowIndex;//选中最后
			let  text1=table1.getValueFromCoords(xTab-1,yTab-1);



			layer.tab({
				area: ['600px', '300px'],
				shadeClose: true, //开启遮罩关闭
				tab: [{
					title: '连号'
					,content: '<div style="padding:50px;">' +
						'<input type="number" value="'+xTab+'" id="value01"  style="display:none" disabled>' +
						'<input type="number" value="'+yTab+'" id="value02"  style="display:none" disabled>' +

						// '<input type="number" value="'+y1Tab+'" id="value03" style="display:none" disabled><span>区域：</span><span>第'+xTab+'列第'+yTab+'行至第'+xTab+'列第'+'</span><input type="number" placeholder="'+y1Tab+'" style="width: 35px;" id="value1"><span>行(选中范围仅单列有效)</span><br/>' +
						'<input type="number" value="'+y1Tab+'" id="value03" style="display:none" disabled><span>区域：</span><input type="number" id="value1"><span>行(选中范围仅单列有效)</span><br/>' +

						'<span>内容：</span><input type="text" id="value2" value="'+text1+'"><span>(固定文本)</span><br/><span></span><input type="number" id="value3" style="display:none" disabled><br/><br/><br/><button onclick="updateTab('+y1Tab+')">修改</brbutton></div>'
				}, {
					title: '向下复制',
					content: '<div style="padding-top:50px;"><span>复制：</span><input type="number" id="tab2Val1"><span>行,共:'+table1.rows.length+'</span><br/><br/><br/><button onclick="updateTab1('+y1Tab+')">修改</brbutton></div>'
				}]
			});


			// layer.open({
			// 	type: 1 //Page层类型
			// 	,area: ['500px', '300px']
			// 	,title: '公式'
			// 	,shade: 0.6 //遮罩透明度
			// 	,maxmin: false //允许全屏最小化
			// 	,shadeClose: true //开启遮罩关闭
			// 	,anim: 1 //0-6的动画形式，-1不开启
			// 	,content: '<div style="padding:50px;">' +
            //         '<input type="number" value="'+xTab+'" id="value01"  style="display:none" disabled>' +
            //         '<input type="number" value="'+yTab+'" id="value02"  style="display:none" disabled>' +
            //         '<input type="number" value="'+y1Tab+'" id="value03" style="display:none" disabled><span>区域：</span><span>第'+xTab+'列第'+yTab+'行至第'+xTab+'列第'+'</span><input type="number" placeholder="'+y1Tab+'" style="width: 35px;" id="value1"><span>行(选中范围仅单列有效)</span><br/>' +
            //         '<span>内容：</span><input type="text" id="value2"><span>(固定文本)</span><br/><span>位置：</span><input type="number" id="value3"><span>(从第N位整数排序)</span><br/><br/><br/><button onclick="updateTab()">修改</brbutton></div>'
			// });



			// console.log("单击表单");


		},
		saveAll: function (event) {
			// var ids = getSelectedRows(vm.activeGrid);
			var project;
			if("#jqGrid1"==vm.activeGrid){
				project = vm.projects1;
			}
			if("#jqGrid2"==vm.activeGrid){
				project = vm.projects2;
			}
			if("#jqGrid3"==vm.activeGrid){
				project = vm.projects3;
			}
			if("#jqGrid4"==vm.activeGrid){
				project = vm.projects4;

				// console.log("44444444444444444");
				// console.log(JSON.stringify(project));
				// console.log("44444444444444444");

			}
			if("#jqGrid5"==vm.activeGrid){

				project = [];

				let data=table1.getData();
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据
				data.pop();//删掉最后一行空数据

				// console.log("-----------");
				// console.log(data);
				// console.log("-----------");
				for (let i = 0; i < data.length; i++) {
					let json={};

						let data1=data[i];

					json.addr=(data1[11]);//addr
					json.bank=(data1[7]); //bank
					json.bzfee=(data1[20]);//bzfee
					json.catalog=(catalogPackSave(data1[12]));//catalog
					json.classid=(data1[21]);//classid
					json.cldef=(data1[22]);//cldef
					json.cldefNum=(data1[23]);//cldefNum
					json.cldefname=(data1[24]);//cldefname
					json.cointype=(data1[25]);//cointype
					json.createTime=(data1[27]);//createTime
					json.createUser=(data1[28]);//createUser
					json.edition=(data1[4]); //edition
					json.extbq=(extbqPackDe(data1[9])); //extbq
					json.finalfee=(data1[38]);//finalfee
					json.gjfee=(data1[39]);//gjfee
					json.gm=(data1[40]);//gm
					json.gmname=(data1[41]);//gmname
					json.id=(data1[43]);//id
					json.jiaji=(data1[50]);//jiaji
					json.live=(data1[3]);//live
					json.name=(data1[1]); //name
					json.niandai=(data1[5]); //niandai
					json.nummber=(data1[0]); //nummber
					json.pic=(data1[58]);//pic
					json.qxshow=(data1[60]);//qxshow
					json.result=(resultPackDe(data1[10])); //result
					json.score=(scoreshow2Pack2(data1[8]));//score 分数data1[64]
					json.scoreName=(data1[66]);//scoreName
					json.scoreNum=(scoreshow2Pack1(data1[8]));//scoreNum 分数表现data1[67]
					json.scoreshow=(data1[69]);//scoreshow
					json.scoreshow2=(data1[8]); //scoreshow2
					json.sendnum=(data1[70]);//sendnum
					json.subclassid=(data1[76]);//subclassid
					json.thumb=(data1[78]);//thumb
					json.updateTime=(data1[79]);//updateTime
					json.updateUser=(data1[80]);//updateUser
					json.zbnum=zbnumPack(data1[6]); //zbnum
					json.zk=(data1[84]);//zk
					json.boxtype=(boxtypePackDe(data1[14]));//boxtype
					json.backweight=(data1[15]);//backweight
					json.description=(data1[16]);//description
					json.coindesc=(data1[13]);//coindesc
					json.name2=(data1[2]);//name2

					project.push(json);//boxtypePackDe

				}

			}


			table1.updateSelectionFromCoords();




			// console.log(project);
			let url = "../pjclcoin/updateAll";
			Ajax.request({
				url: url,
				params: JSON.stringify(project),
				type: "POST",
				contentType: "application/json",
				successCallback: function (r) {
					alert('操作成功', function (index) {
						vm.reload();
					});
				}
			});
		},
		del: function (event) {
			let ids = getSelectedRows("#jqGrid");
			if (ids == null){
				return;
			}

			confirm('确定要删除选中的记录？', function () {
				Ajax.request({
					url: "../pjclcoin/delete",
					params: JSON.stringify(ids),
					type: "POST",
					contentType: "application/json",
					successCallback: function () {
						alert('操作成功', function (index) {
							vm.reload();
						});
					}
				});
			});
		},
		getInfo: function(id){
			Ajax.request({
				url: "../pjclcoin/info/"+id,
				async: true,
				successCallback: function (r) {
					item2 = r.pjClCoin;
					vm.uploadList = r.imgList;
				}
			});
		},
		reload: function (event) {
			vm.showList = true;
			let page = $(vm.activeGrid).jqGrid('getGridParam', 'page');
			$(vm.activeGrid).jqGrid('setGridParam', {
				page: page
			}).trigger("reloadGrid");
		},
		reloadSearch: function() {
			var projects;
			if("#jqGrid1"==vm.activeGrid){
				projects = vm.projects1
			}
			if("#jqGrid2"==vm.activeGrid){
				projects = vm.projects2
			}
			if("#jqGrid3"==vm.activeGrid){
				projects = vm.projects3
			}
			if("#jqGrid4"==vm.activeGrid){
				projects = vm.projects4
			}
			var content = "";
			$.each(projects, function(i, item3){
				if(i!=0){
					content = content + item3.nummber +"\n"
				}

			})

			vm.nummbers = content;

			var oInput = document.createElement('textarea');
			oInput.value = content;
			document.body.appendChild(oInput);
			oInput.select(); // 选择对象
			document.execCommand("Copy"); // 执行浏览器复制命令
			oInput.className = 'oInput';
			oInput.style.display='none';
			alert('复制成功');



		},
		handleSubmit: function (name) {
			handleSubmitValidate(this, name, function () {
				vm.saveOrUpdate()
			});
		},
		handleReset: function (name) {
			handleResetForm(this, name);
		},
		handleView: function (name) {
			this.imgName = name;
			this.visible = true;
		},
		handleRemove:function(file) {
			// 从 upload 实例删除数据
			const fileList = this.uploadList;
			this.uploadList.splice(fileList.indexOf(file), 1);
		},
		handleSuccess:function(res, file) {
			// 因为上传过程为实例，这里模拟添加 url
			file.imgUrl = res.url;
			file.name = res.url;
			vm.uploadList.add(file);
		},
		handleBeforeUpload:function() {
			/**/        const check = this.uploadList.length < 5;
			if (!check) {
				this.$Notice.warning({
					title: '最多只能上传 5 张图片。'
				});
			}
			return check;
			return true;
		},
		handleFormatError: function (file) {
			this.$Notice.warning({
				title: '文件格式不正确',
				desc: '文件 ' + file.name + ' 格式不正确，请上传 jpg 或 png 格式的图片。'
			});
		},
		handleMaxSize: function (file) {
			this.$Notice.warning({
				title: '超出文件大小限制',
				desc: '文件 ' + file.name + ' 太大，不能超过 10M。'
			});
		},
		mounted:function() {
			this.uploadList = this.$refs.upload.fileList;
		},
		getFee: function (item) {
			var bzRes = (item.bzfee != null && item.bzfee != undefined && item.bzfee != '');
			var gjRes = (item.gjfee != null  && item.gjfee != undefined  && item.gjfee != '');
			var zkRes = (item.zk != null  && item.zk != undefined  && item.zk != '');
			var zk;
			if(zkRes){
				zk = item.zk;
			}else{
				zk = 10;
			}
			if (bzRes&&gjRes) {
				item.finalfee = Number((item.gjfee * zk) / 10).toFixed(2);
				return
			}else{
				if (bzRes) {
					item.finalfee = Number((item.bzfee * zk) / 10).toFixed(2);
					return
				}
				if (gjRes) {
					item.finalfee = Number((item.gjfee * zk) / 10).toFixed(2);
					return
				}
			}
		},
		deleteProject: function (item, index,type) {
			if(type=="1"){
				this.projects1.splice(index, 1);
			}
			if(type=="2"){
				this.projects2.splice(index, 1);
			}
			if(type=="3"){
				this.projects3.splice(index, 1);
			}
			if(type=="4"){
				this.projects4.splice(index, 1);
			}

		},
	}
});