$(function () {
    $("#jqGrid").Grid({
        url: '../pjclcoin/dylist',
        multiselect: false,
        colModel: [
            {label: '订单号', name: 'nummber', width: 80, sortable: false},
            {
                label: '结果', name: 'result', width: 60, sortable: false, formatter: function (value) {
                    switch (value) {
                        case 0:
                            return "未鉴定";
                            break;
                        case 1:
                            return "真";
                            break;
                        case 2:
                            return "赝品";
                            break;
                        case 3:
                            return "存疑";
                            break;

                        // case 4:
                        // 	return "性质伪";
                        // 	break;

                        case 5:
                            return "不提供服务";
                            break;
                        case 6:
                            return "不适合评级";
                            break;
                        case 7:
                            return "撤评";
                            break;
                        case 8:
                            return "不适合评级(赝品)";
                            break;
                        case 9:
                            return "不适合评级(锭体、铭文修复超出规定)";
                            break;
                        case 10:
                            return "不适合评级(老假、老仿、臆造)";
                            break;
                        default:
                            return "未鉴定";
                            break;
                    }
                }

            },
            {label: '名称', name: 'nameex', width: 80, sortable: false},
            {label: '版别', name: 'edition', width: 60, sortable: false},
            {label: '年代', name: 'yearex', width: 60, sortable: false},
            {label: '等级', name: 'rank', width: 60, sortable: false},
            {label: '重量', name: 'weight', width: 60, sortable: false},
            {label: '尺寸', name: 'size', width: 60, sortable: false},
            {label: '品相分数', name: 'scoreex', width: 60, sortable: false},
            {label: '姓名/网名', name: 'rnameex', width: 80, sortable: false},
            {label: '送评单', name: 'sendnum', width: 60, sortable: false},
            {label: '费用', name: 'feeex', width: 60, sortable: false},
            {
                label: '币种',
                name: 'bizhong',
                width: 60,
                sortable: false,
                formatter: function (cellValue, options, rowObject) {
                    // rowObject 是整行数据对象，包含 rnameex
                    let rnameex = rowObject.rnameex;

                    if (rnameex.includes('香港')) {
                        return '港币';
                    } else if (rnameex.includes('新加坡')) {
                        return '新加坡元';
                    } else {
                        return '人民币';
                    }
                }
            }
        ]
    });
});

let vm = new Vue({
    el: '#rrapp',
    data: {
        read: 0,
        showList: true,
        title: null,
        pjClCoin: {},
        uploadList: [],
        visible: false,
        parentName: "",
        statusList: [
            {id: 0, name: '未鉴定'},
            {id: 1, name: '真'},
            {id: 2, name: '赝品'},
            {id: 3, name: '存疑'},
            //{id: 4, name: '性质伪'},
            {id: 5, name: '不提供服务'},
            {id: 6, name: '不适合评级'},
            // {id: 7, name: '撤评'},
            // {id: 110, name: '非真'}
            {id: 7, name: '撤评'},
            {id: 8, name: '不适合评级(赝品)'},
            {id: 9, name: '不适合评级(锭体、铭文修复超出规定)'},
            {id: 10, name: '不适合评级(老假、老仿、臆造)'},
        ],
        gmList: [
            {id: '03', name: '机制币'},
            {id: '02', name: '古钱币'},
            {id: '04', name: '银锭'},
            {id: '01', name: '纸币'}
        ],
        ruleValidate: {
            name: [
                {required: true, message: '名称不能为空', trigger: 'blur'}
            ]
        },
        q: {
            result: '',
            gm: '',
            rname: '',
            niandai: '',
            nummbers: '',
            sendnum: ''
        },
        printType: 0,
        isPrintModel: false,
        printBuild: null,
    },
    methods: {
        query: function () {
            vm.reload();
        },
        reload: function (event) {
            vm.showList = true;
            let page = $("#jqGrid").jqGrid('getGridParam', 'page');
            console.log(vm.q);
            var sendnums;
            if (vm.q.sendnums != undefined && vm.q.sendnums != '') {
                sendnums = vm.q.sendnums.replace(/\n/g, ",").replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, '');
            }
            vm.q.result += "";
            var sendnums
            $("#jqGrid").jqGrid('setGridParam', {
                postData: {
                    'result': vm.q.result.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''),
                    'gm': vm.q.gm.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''),
                    'rname': vm.q.rname.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''),
                    'niandai': vm.q.niandai.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''),
                    'nummbers': vm.q.nummbers.replace(/\n/g, ",").replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''),
                    'sendnums': sendnums,
                },
                page: page,
            }).trigger("reloadGrid");
            vm.handleReset('formValidate');
        },
        reloadSearch: function () {
            vm.q = {
                result: '',
                gm: '',
                rname: '',
                niandai: '',
                nummbers: '',
                sendnums: ''
            },
                vm.reload();
        },
        handleSubmit: function (name) {
            handleSubmitValidate(this, name, function () {
                vm.saveOrUpdate()
            });
        },
        handleReset: function (name) {
        },
        exportExcel: function () {
            if (vm.q.sendnums == undefined) {
                vm.q.sendnums = '';
            }
            if (vm.q.nummbers == undefined) {
                vm.q.nummbers = '';
            }
            var param = "?result=" + encodeURI(vm.q.result.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, '')) + "&gm=" + encodeURI(vm.q.gm.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, '')) + "&rname=" + encodeURI(vm.q.rname.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, '')) + "&niandai=" + encodeURI(vm.q.niandai.replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, '')) + "&nummbers=" + encodeURI(vm.q.nummbers.replace(/\n/g, ",").replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, '')) + "&sendnums=" + encodeURI(vm.q.sendnums.replace(/\n/g, ",").replace(/^(\s|\u00A0)+/, '').replace(/(\s|\u00A0)+$/, ''));
            location.href = '../export/cola' + param;
        }
    }
});


