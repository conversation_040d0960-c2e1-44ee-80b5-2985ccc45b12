/**
 * 判断ID是否为空，不为空则是【送评单列表】页面
 * 点击编辑按钮跳转过来的，打开别的页面，配合编辑
 */
$(function () {
    var id = getQueryString("id");
    if (id != null) {
        vm.id = id;
        vm.otherPage = true;
        vm.reload();
        //$("#pjCoinsTb tr").removeClass("activeTr");
        $("#coin0").addClass("activeTr");
    }

});

$(function () {


})

/**
 * 1.(古钱币)项填写失去焦点:
 * [自动识别 & 选中后填充]
 * @type {string}
 */
var text1 = "";
$(document).on('input autocomplete', "input[name='GQautoName']", function (even) {
    text1 = even.target.value;
});
// <失去焦点>
$(document).on('click', "input[name='GQautoName']", function (even) {
    $("input[name='GQautoName']").autocomplete({
        width: 350,
        serviceUrl: '../coins/queryTree',
        params: {
            "m": function () {
                return text1
            }
        },
        triggerSelectOnValidInput: false,
        transformResult: function (response) {
            // <接收服务器返回的JSON数据>
            var obj = $.parseJSON(response);
            return {
                // <数据下拉列表展示>
                suggestions: $.map(obj.list, function (dataItem) {
                    // 展示数据拼接
                    var show = dataItem.name;

                    if (checkNUll(dataItem.name2) == true) {
                        let name2 = " " + dataItem.name2;
                        show += name2;
                    }
                    if (checkNUll(dataItem.mianzhi) == true) {
                        let mianzhi = "@" + dataItem.mianzhi;
                        show += mianzhi;
                    }
                    return {
                        //名称1 名称2 年代 等级 版别
                        value: show,
                        name1: dataItem.name,
                        name2: dataItem.name2,
                        age: dataItem.niandai,
                        level: dataItem.grade,
                        bottle: dataItem.edition
                    };
                })
            };
        },
        //选中值后数据处理
        onSelect: function (suggestion) {
            let index = localStorage.getItem("$SendfromGQBIndexs");

            //名称1
            let name = document.getElementsByName('GQautoName')[index];
            name.value = suggestion.name1;

            //名称2
            let name2 = document.getElementsByName('GQautoName2')[index];
            name2.value = suggestion.name2;

            //年代
            let age = document.getElementsByName('GQniandai')[index];
            age.value = suggestion.age;

            //等级
            let level = document.getElementsByName('GQgrade')[index];

            if (checkNUll(suggestion.level) == true) {
                level.value = suggestion.level;
            }

            //版别
            let bottle = document.getElementsByName('GQautoBanbie')[index];
            bottle.value = suggestion.bottle;

            //确认事件，避免失去选择的值
            name.dispatchEvent(new Event('input'));
            name2.dispatchEvent(new Event('input'));
            age.dispatchEvent(new Event('input'));
            level.dispatchEvent(new Event('input'));
            bottle.dispatchEvent(new Event('input'));
        }
    });
});


/**
 * 2.(机制币)项填写失去焦点:
 * [自动识别 & 选中后填充]
 * @type {string}
 */
var text2 = "";
$(document).on('input autocomplete', "input[name='JZautoName']", function (even) {
    text2 = even.target.value;
});
// <失去焦点>
$(document).on('click', "input[name='JZautoName']", function (even) {
    $("input[name='JZautoName']").autocomplete({
        width: 350,
        serviceUrl: '../coins/queryTree',
        params: {
            "m": function () {
                return text2
            }
        },
        triggerSelectOnValidInput: false,
        transformResult: function (response) {
            // <接收服务器返回的JSON数据>
            var obj = $.parseJSON(response);
            return {
                suggestions: $.map(obj.list, function (dataItem) {
                    // 展示数据拼接
                    var show = dataItem.name;

                    if (checkNUll(dataItem.name2) == true) {
                        let name2 = " " + dataItem.name2;
                        show += name2;
                    }
                    if (checkNUll(dataItem.mianzhi) == true) {
                        let mianzhi = "@" + dataItem.mianzhi;
                        show += mianzhi;
                    }
                    return {
                        //名称1 名称2 面值 年代 版别
                        value: show,
                        name1: dataItem.name,
                        name2: dataItem.name2,
                        faceValue: dataItem.mianzhi,
                        age: dataItem.niandai,
                        bottle: dataItem.edition,
                        size: dataItem.size,
                        weight: dataItem.weight
                    };
                })
            };
        },
        //选中值后数据处理
        onSelect: function (suggestion) {
            let index = localStorage.getItem("$SendfromJZBIndexs");

            //名称1
            let name = document.getElementsByName('JZautoName')[index];
            name.value = suggestion.name1;

            //名称2
            let name2 = document.getElementsByName('JZautoName2')[index];
            name2.value = suggestion.name2;

            //年代
            let age = document.getElementsByName('JZniandai')[index];
            age.value = suggestion.age;

            //面值
            let faceValue = document.getElementsByName('JZmianzhi')[index];
            faceValue.value = suggestion.faceValue;

            if (suggestion.mianzhi != null && suggestion.mianzhi != "") {
                faceValue.value = suggestion.level;
            }

            //版别
            let bottle = document.getElementsByName('JZbanbie')[index];
            bottle.value = suggestion.bottle;

            let size = document.getElementsByName('JZsize')[index];
            size.value = suggestion.size;

            let weight = document.getElementsByName('JZweight')[index];
            weight.value = suggestion.weight;

            //确认事件，避免失去选择的值
            name.dispatchEvent(new Event('input'));
            name2.dispatchEvent(new Event('input'));
            age.dispatchEvent(new Event('input'));
            faceValue.dispatchEvent(new Event('input'));
            bottle.dispatchEvent(new Event('input'));
            size.dispatchEvent(new Event('input'));
            weight.dispatchEvent(new Event('input'));
        }
    });
});


/**
 * 3.(银锭)项填写失去焦点:
 * [自动识别 & 选中后填充]
 * @type {string}
 */
var text3 = "";
$(document).on('input autocomplete', "input[name='YDautoName']", function (even) {
    text3 = even.target.value;
});
// <失去焦点>
$(document).on('click', "input[name='YDautoName']", function (even) {
    $("input[name='YDautoName']").autocomplete({
        width: 350,
        //服务器地址
        serviceUrl: '../coins/queryTree',
        params: {
            "m": function () {
                return text3
            }
        },
        triggerSelectOnValidInput: false,
        transformResult: function (response) {
            // <接收服务器返回的JSON数据>
            var obj = $.parseJSON(response);
            return {
                // <数据下拉列表展示>
                suggestions: $.map(obj.list, function (dataItem) {
                    var show = dataItem.name;

                    if (checkNUll(dataItem.name2) == true) {
                        let name2 = " " + dataItem.name2;
                        show += name2;
                    }
                    if (checkNUll(dataItem.mianzhi) == true) {
                        let mianzhi = "@" + dataItem.mianzhi;
                        show += mianzhi;
                    }
                    return {
                        //名称1 名称2 面值 年代 版别
                        value: show,
                        name1: dataItem.name,
                        name2: dataItem.name2,
                        faceValue: dataItem.mianzhi,
                        age: dataItem.niandai,
                        bottle: dataItem.edition
                    };
                })
            };
        },
        //选中值后数据处理
        onSelect: function (suggestion) {
            let index = localStorage.getItem("$SendfromYDIndexs");

            //名称1
            let name = document.getElementsByName('YDautoName')[index];
            name.value = suggestion.name1;

            //名称2
            let name2 = document.getElementsByName('YDautoName2')[index];
            name2.value = suggestion.name2;

            //年代
            let age = document.getElementsByName('YDniandai')[index];
            age.value = suggestion.age;

            //面值
            let faceValue = document.getElementsByName('YDmianzhi')[index];
            faceValue.value = suggestion.faceValue;

            if (suggestion.mianzhi != null && suggestion.mianzhi != "") {
                faceValue.value = suggestion.level;
            }

            //版别
            let bottle = document.getElementsByName('YDbanbie')[index];
            bottle.value = suggestion.bottle;

            //确认事件，避免失去选择的值
            name.dispatchEvent(new Event('input'));
            name2.dispatchEvent(new Event('input'));
            age.dispatchEvent(new Event('input'));
            faceValue.dispatchEvent(new Event('input'));
            bottle.dispatchEvent(new Event('input'));
        }
    });
});

/**
 * 4.(纸币)项填写失去焦点:
 * [自动识别 & 选中后填充]
 * @type {string}
 */
var text4 = "";
$(document).on('input autocomplete', "input[name='ZBautoName']", function (even) {
    text4 = even.target.value;
});
// <失去焦点>
$(document).on('click', "input[name='ZBautoName']", function (even) {
    $("input[name='ZBautoName']").autocomplete({
        width: 350,
        serviceUrl: '../coins/queryTree',
        params: {
            "m": function () {
                return text4
            }
        },
        triggerSelectOnValidInput: false,
        transformResult: function (response) {
            // <接收服务器返回的JSON数据>
            var obj = $.parseJSON(response);
            return {
                // <数据下拉列表展示>
                suggestions: $.map(obj.list, function (dataItem) {
                    // 展示数据拼接
                    let show = dataItem.name;

                    if (checkNUll(dataItem.name2) == true) {
                        let name2 = " " + dataItem.name2;
                        show += name2;
                    }
                    if (checkNUll(dataItem.catalog) == true) {
                        let catalog = "@" + dataItem.catalog;
                        show += catalog;
                    }
                    return {
                        //名称1 年代 目录 版别
                        value: show,
                        name1: dataItem.name,
                        age: dataItem.niandai,
                        catalog: dataItem.catalog,
                        bottle: dataItem.edition
                    };
                })
            };
        },
        //选中值后数据处理
        onSelect: function (suggestion) {
            let index = localStorage.getItem("$SendfromZBIndexs");

            //名称1
            let name = document.getElementsByName('ZBautoName')[index];
            name.value = suggestion.name1;

            //年代
            let age = document.getElementsByName('ZBniandai')[index];
            age.value = suggestion.age;

            //目录
            let catalog = document.getElementsByName('ZBmulu')[index];
            catalog.value = suggestion.catalog;

            //版别
            let bottle = document.getElementsByName('ZBbanbie')[index];
            bottle.value = suggestion.bottle;

            //确认事件，避免失去选择的值
            name.dispatchEvent(new Event('input'));
            age.dispatchEvent(new Event('input'));
            catalog.dispatchEvent(new Event('input'));
            bottle.dispatchEvent(new Event('input'));


        }
    });
});

function checkNUll(value) {
    if (value != null && value != "" && value != "无") {
        return true;
    }
    return false;
}


function changeUpdatedPjcoin(pjClCoin, sumFinalFee) {
    vm.pjCoins[vm.changePjCoinIndex].serialNumber = pjClCoin.serialNumber;
    vm.pjCoins[vm.changePjCoinIndex].name = pjClCoin.name;
    vm.pjCoins[vm.changePjCoinIndex].pic = pjClCoin.pic;
    vm.pjCoins[vm.changePjCoinIndex].weight = pjClCoin.weight;
    vm.pjCoins[vm.changePjCoinIndex].size = pjClCoin.size;
    vm.pjCoins[vm.changePjCoinIndex].score = pjClCoin.score;
    vm.pjCoins[vm.changePjCoinIndex].result = pjClCoin.result;
    vm.pjCoins[vm.changePjCoinIndex].scoreNum = pjClCoin.scoreNum;
    vm.pjOSendform.sumFinalFee = sumFinalFee;
}

function lastOne() {
    if (vm.changePjCoinIndex != 0) {
        vm.changePjCoinIndex = vm.changePjCoinIndex - 1;
        vm.frameLink = "../sys/editpjclcoin.html?id=" + vm.pjCoins[vm.changePjCoinIndex].id;
        $("#pjCoinsTb tr").removeClass("activeTr");
        $("#coin" + vm.changePjCoinIndex).addClass("activeTr");
    } else {
        alert("当前已经是第一个");
    }
}

function nextOne() {
    if (vm.changePjCoinIndex != vm.pjCoins.length) {
        vm.changePjCoinIndex = vm.changePjCoinIndex + 1;
        vm.frameLink = "../sys/editpjclcoin.html?id=" + vm.pjCoins[vm.changePjCoinIndex].id;
        $("#pjCoinsTb tr").removeClass("activeTr");
        $("#coin" + vm.changePjCoinIndex).addClass("activeTr");
    } else {
        alert("当前已经是最后一个");
    }

}

let vm = new Vue({
    el: '#rrapp',
    data: {
        outline: true,
        isReadOnly: false,
        addSendform: true,
        changePjCoin: false,
        isShowSendForm: true,
        id: null,
        /*        addSendform:false,
                changePjCoin:true,
                id:27,*/
        frameLink: null,
        title: null,
        otherPage: false,
        pjOSendform: {issms: 0, rtype: 2, urgenttype: 0, stype: 0},
        itemList: {},
        activeTab: 0,
        projects1: [],
        projectsAll:{},
        projects2: [],
        projects3: [],
        projects4: [],
        pjCoins: [],
        changePjCoinIndex: null,
        project: {},
        statusList: [
            {value: "0", name: '待寄入'},
            {value: "1", name: '快递邮寄'},
            {value: "2", name: '评级中'},
            {value: "3", name: '待支付'},
            {value: "4", name: '评级完成'},
            {value: "5", name: '已寄出'},
            {value: "6", name: '待退费'}
        ],
        sresList: [
            {id: 0, name: '未鉴定'},
            {id: 1, name: '真'},
            {id: 2, name: '赝品'},
            {id: 3, name: '存疑'},
            {id: 4, name: '性质伪'},
            {id: 5, name: '不提供服务'},
            {id: 6, name: '不适合评级'},
            {id: 7, name: '撤评'}
        ],
        packTypeList: [
            {id: 1, name: '密封盒'},
            {id: 2, name: '开放式'},
            {id: 3, name: '证书'}
        ],
        companyList: [
            {id: 1, name: '中乾评级'},
            {id: 2, name: '宝鑫评级'}
        ],
        isPackList: [
            {id: 1, name: '是'},
            {id: 2, name: '否'}
        ],
        ruleValidate: {
            rphone: [
                {required: true, message: '手机不能为空', trigger: 'blur'}
            ],
            rname: [
                {required: true, message: '姓名不能为空', trigger: 'blur'}
            ]
        },
        q: {
            name: ''
        },
        isPrintModel: false,

        printBuild: null,
    },
    methods: {
        query: function () {
            vm.reload();
        },
        add: function () {
            vm.title = "新增";
            vm.pjOSendform = {"rtype": "1"};
            vm.itemList = {};
            vm.addSendform = true;
        },
        addDataMore: function () {
            vm.changePjCoin = false;
            vm.addSendform = true;
            vm.isShowSendForm = false;
        },
        exitDataMore: function () {
            vm.changePjCoin = true;
            vm.addSendform = false;
            vm.isShowSendForm = true;
        },
        getItemInfo: function () {
            let id = getSelectedRow("#jqGrid");
            if (id == null) {
                return;
            }
            vm.pjOSendform = {};
            vm.itemList = {};
            vm.addSendform = false;
            vm.title = "钱币项目详情";
            vm.getInfo(id)
        },

        saveOrUpdate: function (event) {
            alert();
            let url = "../pjosendform/saveBatch";
            Vue.set(vm.pjOSendform, "projects1", vm.projects1);
            Vue.set(vm.pjOSendform, "projects2", vm.projects2);
            Vue.set(vm.pjOSendform, "projects3", vm.projects3);
            Vue.set(vm.pjOSendform, "projects4", vm.projects4);
            Ajax.request({
                url: url,
                params: JSON.stringify(vm.pjOSendform),
                type: "POST",
                contentType: "application/json",
                successCallback: function (r) {
                    vm.id = r.id;
                    vm.reload();
                    alert('操作成功', function (index) {
                        vm.addSendform = false;
                        vm.changePjCoin = true;
                    });
                }
            });
        },
        /**
         * 创建送评单
         * @param event
         */
        saveOrUpdateSms: function (event) {
            let url = "../pjosendform/saveBatch";
            Vue.set(vm.pjOSendform, "projects1", vm.projects1);
            Vue.set(vm.pjOSendform, "projects2", vm.projects2);
            Vue.set(vm.pjOSendform, "projects3", vm.projects3);
            Vue.set(vm.pjOSendform, "projects4", vm.projects4);
            Ajax.request({
                url: url,
                params: JSON.stringify(vm.pjOSendform),
                type: "POST",
                contentType: "application/json",
                successCallback: function (r) {
                    layer.confirm('创建送评单成功！是否发送短信?', {
                        icon: 3, title: '信息',
                        btn: ['是', '否'],
                        btn1: function (index) {
                            if (!/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/.test(vm.pjOSendform.rphone)) {
                                layer.msg('手机号码格式不正确，检查后再次发送！', {icon: 5});
                                layer.close(index);
                                vm.id = r.id;
                                vm.reload();
                                vm.addSendform = false;
                                vm.changePjCoin = true;
                            } else {
                                layer.close(index);
                                Ajax.request({
                                    url: "../sms/sendfromInfo",
                                    async: true,
                                    successCallback: function (r) {
                                        layer.open({
                                            type: 1
                                            ,
                                            title: "短信内容"
                                            ,
                                            closeBtn: false
                                            ,
                                            area: '300px;'
                                            ,
                                            shade: 0
                                            ,
                                            id: 'LAY'
                                            ,
                                            resize: false
                                            ,
                                            content: '<div style="padding: 22px; line-height: 22px; background-color: #393D49; color: #e2e2e2; font-weight: 300;">您的送评单号：' +
                                                r.info[0].number +
                                                ' <br> 已确认，需缴纳评级费' +
                                                r.info[0].sumfeetw
                                                +
                                                '（元），请支付后与客服确认，详情请登陆网站。</div>'
                                            ,
                                            btn: ['发送信息', '取消']
                                            ,
                                            btnAlign: 'c'
                                            ,
                                            moveType: 1 //拖拽模式，0或者1
                                            ,
                                            success: function (layero) {
                                                $(".layui-layer-btn0").on("click", function () {
                                                    $.ajax({
                                                        url: "../sms/send",
                                                        data: {
                                                            "phone": vm.pjOSendform.rphone,
                                                            "number": r.info[0].number,
                                                            "money": r.info[0].sumfeetw
                                                        },
                                                        dataType: "json",
                                                        type: "post",
                                                        success: function (data) {
                                                            layer.msg('发送成功！', {icon: 1});
                                                            layer.close(index);
                                                            vm.id = r.info[0].id;
                                                            vm.reload();
                                                            vm.addSendform = false;
                                                            vm.changePjCoin = true;
                                                        },
                                                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                                                            alert("请求失败！");
                                                        }
                                                    });
                                                });
                                                $(".layui-layer-btn1").on("click", function () {
                                                    layer.msg('取消发送');
                                                    vm.id = r.info[0].id;
                                                    vm.reload();
                                                    vm.addSendform = false;
                                                    vm.changePjCoin = true;
                                                });
                                            }
                                        });

                                    }
                                });
                            }
                        },
                        btn2: function (index) {
                            vm.id = r.id;
                            vm.reload();
                            vm.addSendform = false;
                            vm.changePjCoin = true;
                        }
                    })
                    // alert('操作成功', function (index) {
                    // 	vm.addSendform = false;
                    // 	vm.changePjCoin = true;
                    // });
                }
            });
        },
        update: function (event) {
            let url = "../pjosendform/update";
            Ajax.request({
                url: url,
                params: JSON.stringify(vm.pjOSendform),
                type: "POST",
                contentType: "application/json",
                successCallback: function (r) {
                    alert('保存送评单成功', function (index) {
                    });
                }
            });
        },
        getInfo: function (id) {
            Ajax.request({
                url: "../pjosendform/info/" + id,
                async: true,
                successCallback: function (r) {
                    vm.itemList = r.itemList;
                }
            });
        },
        reload: function (event) {
            vm.addSendform = false;
            vm.changePjCoin = true;
            Ajax.request({
                url: "../pjosendform/infoCoin/" + vm.id,
                async: true,
                successCallback: function (r) {
                    vm.pjOSendform = r.sendform;
                    vm.pjCoins = r.pjCoins;
                    vm.frameLink = "../sys/editpjclcoin.html?id=" + vm.pjCoins[0].id;
                    vm.changePjCoinIndex = 0;
                }
            });
        },
        replacementSMS: function () {
            if (!/^1([38][0-9]|4[579]|5[0-3,5-9]|6[6]|7[0135678]|9[89])\d{8}$/.test(vm.pjOSendform.rphone)) {
                layer.msg('手机号码格式不正确，检查后再次发送！', {icon: 5});
                return;
            }
            $.ajax({
                url: "../pjosendform/scan",
                data: {"nummber": vm.pjCoins[vm.changePjCoinIndex].nummber},
                dataType: "json",
                type: "post",
                success: function (data) {
                    layer.open({
                        type: 1
                        ,
                        title: "短信内容"
                        ,
                        closeBtn: false
                        ,
                        area: '300px;'
                        ,
                        shade: 0
                        ,
                        id: 'LAY'
                        ,
                        resize: false
                        ,
                        content: '<div style="padding: 22px; line-height: 22px; background-color: #393D49; color: #e2e2e2; font-weight: 300;">您的送评单号：' +
                            data.send[0].sendnum +
                            ' <br> 已确认，需缴纳评级费' +
                            data.send[0].sumfeetw
                            +
                            '（元），请支付后与客服确认，详情请登陆网站。</div>'
                        ,
                        btn: ['发送信息', '取消']
                        ,
                        btnAlign: 'c'
                        ,
                        moveType: 1 //拖拽模式，0或者1
                        ,
                        success: function (layero) {
                            $(".layui-layer-btn0").on("click", function () {
                                $.ajax({
                                    url: "../sms/send",
                                    data: {
                                        "phone": vm.pjOSendform.rphone,
                                        "number": data.send[0].sendnum,
                                        "money": data.send[0].sumfeetw
                                    },
                                    dataType: "json",
                                    type: "post",
                                    success: function (data) {
                                        layer.msg('发送成功！', {icon: 1});
                                        layer.close(index);
                                    },
                                    error: function (XMLHttpRequest, textStatus, errorThrown) {
                                        alert("请求失败！");
                                    }
                                });
                            });
                            $(".layui-layer-btn1").on("click", function () {
                                layer.msg('取消发送');
                            });
                        }
                    });
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert("请求失败！");
                }
            });

        },
        reloadSearch: function () {
            vm.q = {
                name: ''
            }
            vm.reload();
        },
        handleSubmit: function (name) {
            handleSubmitValidate(this, name, function () {
                vm.saveOrUpdate()
            });
        },
        handleSubmitSms: function (name) {
            handleSubmitValidate(this, name, function () {
                vm.saveOrUpdateSms()
            });
        },
        handleReset: function (name) {
            handleResetForm(this, name);
        },
        addProjectAll:function (){
          /*  console.log(this.projectsAll);
            console.log(this.projects1);
            console.log(this.packTypeList);*/

            for (let i = 0; i < this.projects1.length; i++) {
                if(this.projectsAll.name!=""){
                  this.projects1[i].name= this.projectsAll.name;
                }
                if(this.projectsAll.name2!=""&&this.projectsAll.name2!=null){
                    this.projects1[i].name2=
                        this.projectsAll.name2;
                }
                if(this.projectsAll.niandai!=""&&this.projectsAll.niandai!=null){
                    this.projects1[i].niandai=
                        this.projectsAll.niandai;
                }
                if(this.projectsAll.catalog!=""&&this.projectsAll.catalog!=null){
                    this.projects1[i].catalog=
                        this.projectsAll.catalog;
                }
                if(this.projectsAll.bank!=""&&this.projectsAll.bank!=null){
                    this.projects1[i].bank=
                        this.projectsAll.bank;
                }
                if(this.projectsAll.boxtype!=""&&this.projectsAll.boxtype!=null&&this.projectsAll.boxtype!="0"){
                    this.projects1[i].boxtype= this.projectsAll.boxtype;

                }
                if(this.projectsAll.amount!=""&&this.projectsAll.amount!=null){
                    this.projects1[i].amount=
                        this.projectsAll.amount;
                }
                if(this.projectsAll.bzfee!=""&&this.projectsAll.bzfee!=null){
                    this.projects1[i].bzfee=
                        this.projectsAll.bzfee;
                }
                if(this.projectsAll.gjfee!=""&&this.projectsAll.gjfee!=null){
                    this.projects1[i].gjfee=
                        this.projectsAll.gjfee;
                }
                if(this.projectsAll.finalfee!=""&&this.projectsAll.finalfee!=null){
                    this.projects1[i].finalfee=
                        this.projectsAll.finalfee;
                }
                if(this.projectsAll.zk!=""&&this.projectsAll.zk!=null){
                    this.projects1[i].zk=
                        this.projectsAll.zk;
                }
                if(this.projectsAll.boxfee!=""&&this.projectsAll.boxfee!=null){
                    this.projects1[i].boxfee=
                        this.projectsAll.boxfee;
                }
                if(this.projectsAll.jiajifee!=""&&this.projectsAll.jiajifee!=null){
                    this.projects1[i].jiajifee=
                        this.projectsAll.jiajifee;
                }
                if(this.projectsAll.edition!=""&&this.projectsAll.edition!=null){
                    this.projects1[i].edition=
                        this.projectsAll.edition;
                }
                if(this.projectsAll.live!=""&&this.projectsAll.live!=null){
                    this.projects1[i].live=
                        this.projectsAll.live;
                }

                if(this.projectsAll.classid!=""&&this.projectsAll.classid!=null&&this.projectsAll.classid!="0"){
                    this.projects1[i].classid=
                        this.projectsAll.classid;
                }



            }


            console.log(this.projects1);

            this.projects1.push();
        },

        addProjectNumber: function (number){

            for (let i = 0; i < number; i++) {
                this.addProject(1);
            }
        },
        addProject: function (type) {
            var addBtn = $("button[name='addBtn']");
            if (type == 1) {
                $(addBtn[0]).attr("disabled", "disabled");
                $(addBtn[1]).attr("disabled", "disabled");
                $(addBtn[2]).attr("disabled", "disabled");
                $(addBtn[4]).attr("disabled", "disabled");
                $(addBtn[5]).attr("disabled", "disabled");
                $(addBtn[6]).attr("disabled", "disabled");
                $("#thead").attr("class","visible");
            }
            if (type == 2) {
                $(addBtn[1]).attr("disabled", "disabled");
                $(addBtn[2]).attr("disabled", "disabled");
                $(addBtn[3]).attr("disabled", "disabled");
                $(addBtn[4]).attr("disabled", "disabled");
                $(addBtn[5]).attr("disabled", "disabled");
                $(addBtn[6]).attr("disabled", "disabled");
            }
            if (type == 3) {
                $(addBtn[0]).attr("disabled", "disabled");
                $(addBtn[2]).attr("disabled", "disabled");
                $(addBtn[3]).attr("disabled", "disabled");
                $(addBtn[4]).attr("disabled", "disabled");
                $(addBtn[5]).attr("disabled", "disabled");
                $(addBtn[6]).attr("disabled", "disabled");
            }
            if (type == 4) {
                $(addBtn[0]).attr("disabled", "disabled");
                $(addBtn[1]).attr("disabled", "disabled");
                $(addBtn[3]).attr("disabled", "disabled");
                $(addBtn[4]).attr("disabled", "disabled");
                $(addBtn[5]).attr("disabled", "disabled");
                $(addBtn[6]).attr("disabled", "disabled");
            }

            this.project = {};
            Vue.set(this.project, "zk", "10");
            Vue.set(this.project, "jiaji", "0");
            Vue.set(this.project, "gjfee", "0");



            if (type == "1") {

                    let index = localStorage.getItem("$SendfromZBIndexs");
                    if (index != null) {
                        let ZBBZJ = document.getElementsByName('zbBZJ');
                        let ZBGJJ = document.getElementsByName('zbGZJ');

                        // if(ZBBZJ.length>0||ZBGJJ.length>0){
                        // 	if(ZBBZJ[index].value==""&&ZBGJJ[index].value==""){
                        // 		alert('价格不能为空！')
                        // 		return;
                        // 	}
                        // }
                    }


                    Vue.set(this.project, "COINTYPE", "01");
                    Vue.set(this.project, "gm", "01");
                    Vue.set(this.project, "gmname", "纸币");
                    this.projects1.push(this.project);

            }

            if (type == "2") {
                let index = localStorage.getItem("$SendfromGQBIndexs");
                if (index != null) {
                    let GQBZJ = document.getElementsByName('gqbBZJ');
                    let GQGJJ = document.getElementsByName('gqbGJJ');
                    // if(GQBZJ.length>0||GQGJJ.length>0){
                    // 	if(GQBZJ[index].value==""&&GQGJJ[index].value==""){
                    // 		alert('价格不能为空！')
                    // 		return;
                    // 	}
                    // }
                }
                Vue.set(this.project, "COINTYPE", "02");
                Vue.set(this.project, "gm", "02");
                Vue.set(this.project, "gmname", "古钱币");
                this.projects2.push(this.project);
            }
            if (type == "3") {
                let index = localStorage.getItem("$SendfromJZBIndexs");
                if (index != null) {
                    let JZBBZJ = document.getElementsByName('jzbBZJ');
                    let JZBGJJ = document.getElementsByName('jzbGJJ');
                    // if(JZBBZJ.length>0||JZBGJJ.length>0){
                    // 	if(JZBBZJ[index].value==""&&JZBGJJ[index].value==""){
                    // 		alert('价格不能为空！')
                    // 		return;
                    // 	}
                    // }
                }
                Vue.set(this.project, "COINTYPE", "03");
                Vue.set(this.project, "gm", "03");
                Vue.set(this.project, "gmname", "机制币");
                this.projects3.push(this.project);
            }
            if (type == "4") {

                let index = localStorage.getItem("$SendfromYDIndexs");
                if (index != null) {
                    let YDBZJ = document.getElementsByName('ydBZJ');
                    let YDGJJ = document.getElementsByName('ydGJJ');
                    // if(YDBZJ.length>0||YDGJJ.length>0){
                    // 	if(YDBZJ[index].value==""&&YDGJJ[index].value==""){
                    // 		alert('价格不能为空！')
                    // 		return;
                    // 	}
                    // }
                }
                Vue.set(this.project, "COINTYPE", "04");
                Vue.set(this.project, "gm", "04");
                Vue.set(this.project, "gmname", "银锭");
                this.projects4.push(this.project);
            }

        },
        deleteProject: function (item, index, type) {
            if (type == "1") {
                this.projects1.splice(index, 1);
            }
            if (type == "2") {
                this.projects2.splice(index, 1);
            }
            if (type == "3") {
                this.projects3.splice(index, 1);
            }
            if (type == "4") {
                this.projects4.splice(index, 1);
            }

        },
        getFee: function (item, index) {
            var bzRes = (item.bzfee != null && item.bzfee != undefined && item.bzfee != '');
            var gjRes = (item.gjfee != null && item.gjfee != undefined && item.gjfee != '');
            if (bzRes && gjRes && item.gjfee != 0) {
                item.finalfee = (item.gjfee * item.zk) / 10;
                return;
            } else {
                if (bzRes) {
                    item.finalfee = (item.bzfee * item.zk) / 10;
                    if (item.gjfee == "") {
                        item.gjfee = 0;
                    }
                    return;
                }
                if (gjRes) {
                    item.finalfee = (item.gjfee * item.zk) / 10;
                    return;
                }
            }
            item.finalfee = 0;

        },
        changePjCoinFun: function (item, index) {
            vm.changePjCoinIndex = index;
            vm.frameLink = "../sys/editpjclcoin.html?id=" + item.id;
            $("#pjCoinsTb tr").removeClass("activeTr");
            $("#coin" + index).addClass("activeTr");
        },
        getTrClass: function (index) {
            if (index == 0) {
                return "activeTr";
            } else {
                return ""
            }
        },
        reloadPage: function () {
            if (!vm.otherPage) {
                window.location.href = "../sys/addsendform.html";
            } else {
                window.parent.closeEdit();
            }

        },
        prePrint: function () {
            this.printBuild = new PrintBuild();
            var ids = [];
            for (var i = 0; i < this.pjCoins.length; i++) {
                ids.push(this.pjCoins[i].id);
            }
            this.printBuild.whiteLabel(ids);
            this.isPrintModel = true;
        },

        print: function () {
            this.printBuild.print();
        },
        getGQIndex: function (index) {
            localStorage.setItem("$SendfromGQBIndexs", index);
        }, getJZIndex: function (index) {
            localStorage.setItem("$SendfromJZBIndexs", index);
        }, getYDIndex: function (index) {
            localStorage.setItem("$SendfromYDIndexs", index);
        }, getZBIndex: function (index) {
            localStorage.setItem("$SendfromZBIndexs", index);
        },
        getUser: function () {
            Ajax.request({
                url: '../sys/user/info',
                async: true,
                successCallback: function (data) {
                    if (data.user.userName == "hongkong") {
                        //hongkong01
                        vm.pjOSendform.nickname = "香港送評中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "hongkong01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "香港送評中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "zhengzhou01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "郑州纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "zhengzhou02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "郑州纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "hunan01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "湖南纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "hunan02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "湖南纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }

                    if (data.user.userName == "jiangmen01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "江门纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "jiangmen02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "江门纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "shanxi01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "山西纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "shanxi02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "山西纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "guangzhou01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "广州纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "guangzhou02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "广州纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "meizhou01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "梅州纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "meizhou02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "梅州纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "hefei01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "合肥纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "hefei02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "合肥纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "zhoukou01") {
                        //hongkong01
                        vm.pjOSendform.nickname = "周口纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "zhoukou02") {
                        //hongkong01
                        vm.pjOSendform.nickname = "周口纸币送评中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "SG001") {
                        //hongkong01
                        vm.pjOSendform.nickname = "新加坡送評中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                    if (data.user.userName == "SG002") {
                        //hongkong01
                        vm.pjOSendform.nickname = "新加坡送評中心";
                        vm.outline = false;
                        vm.isReadOnly = true;
                    }
                }
            });
        }
    }, created: function () {
        this.getUser();
    }
});