!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t(e["happy-scroll"]={},e.Vue$)}(this,function(e,t){"use strict";function n(e,t,n){document.addEventListener?e.addEventListener(t,n):e.attachEvent("on"+t,n)}function i(e,t,n){document.addEventListener?e.removeEventListener(t,n):e.detachEvent("on"+t,n)}function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function r(e,t){return t={exports:{}},e(t,t.exports),t.exports}function s(){var e={},t=0,n=0,i=0;return{add:function(o,r){r||(r=o,o=0),o>n?n=o:o<i&&(i=o),e[o]||(e[o]=[]),e[o].push(r),t++},process:function(){for(var t=i;t<=n;t++)for(var o=e[t],r=0;r<o.length;r++)(0,o[r])()},size:function(){return t}}}function a(e){return e[E]}function l(e){return Array.isArray(e)||void 0!==e.length}function c(e){if(Array.isArray(e))return e;var t=[];return $(e,function(e){t.push(e)}),t}function d(e){return e&&1===e.nodeType}function h(e,t,n){var i=e[t];return void 0!==i&&null!==i||void 0===n?i:n}t=t&&t.hasOwnProperty("default")?t.default:t;var u=function(e){var t=Date.now();return function(n){if(n-t>(e||14))return t=n,!0}},f=function(e,t,n){var i,o,r,s,a,l=function l(){var c=(new Date).getTime()-s;c<t&&c>=0?i=setTimeout(l,t-c):(i=null,n||(a=e.apply(r,o),i||(r=o=null)))};return function(){r=this,o=arguments,s=(new Date).getTime();var c=n&&!i;return i||(i=setTimeout(l,t)),c&&(a=e.apply(r,o),r=o=null),a}},p={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"stripContainer",staticClass:"happy-scroll-strip",class:[e.horizontal?"happy-scroll-strip--horizontal":"happy-scroll-strip--vertical"],style:[e.initLocation],on:{"!wheel":function(t){return t.stopPropagation(),e.handlerWheel(t)}}},[n("div",{ref:"strip",staticClass:"happy-scroll-bar",style:[e.translate,o({},e.config.sizeAttr,e.length+"px"),e.initSize,{background:e.color},{opacity:e.isOpacity}],on:{mousedown:function(t){return t.stopPropagation(),e.handlerMouseDown(t)}}})])},staticRenderFns:[],name:"happy-scroll-strip",props:{horizontal:Boolean,left:Boolean,top:Boolean,move:{type:Number,default:0},size:{type:[Number,String],default:4},minLengthV:{type:Number,default:40},minLengthH:{type:Number,default:40},color:{type:String,default:"rgba(51,51,51,0.2)"},throttle:{type:Number,default:14}},data:function(){return{config:{},startMove:!1,binded:!1,length:0,percentage:0,maxOffset:0,currentOffset:0,moveThrottle:u(this.throttle)}},watch:{currentOffset:function(e){0===e?this.emitLocationEvent("start",0):e===this.maxOffset&&this.emitLocationEvent("end",e/this.percentage)}},computed:{initSize:function(){return o({},this.horizontal?"height":"width",this.size+"px")},isOpacity:function(){return this.percentage<1?1:0},translate:function(){var e=this.move*this.percentage;if(this.$refs.stripContainer)return e<0&&(e=0),e>this.maxOffset&&(e=this.maxOffset),this.currentOffset=e,{transform:this.config.translate+"("+e+"px)"}},initLocation:function(){return this.horizontal?this.top?{top:0,bottom:"auto"}:"":this.left?{left:0,right:"auto"}:""}},methods:{emitLocationEvent:function(e,t){var n=this.horizontal?"horizontal":"vertical";this.$emit(n+"-"+e,t)},computeStrip:function(e,t){var n=this.$refs.stripContainer[this.config.client];this.length=n*(t/e);var i=this.horizontal?this.minLengthH:this.minLengthV;i<1&&(i*=n),this.length=this.length<i?i:this.length;var o=this.maxOffset=n-this.length;this.percentage=o/(e-t)},bindEvents:function(){this.binded||(n(document,"mouseup",this.handlerMouseUp),n(document,"mousemove",this.handlerMove),this.binded=!0)},handlerMouseDown:function(e){if(0===e.button)return e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation(),this.startMove=!0,this.axis=e[this.config.clientAxis],this.bindEvents(),!1},handlerMouseUp:function(){this.startMove=!1},handlerMove:function(e){if(this.startMove&&this.moveThrottle(Date.now())){e.preventDefault(),e.stopPropagation(),e.stopImmediatePropagation();var t=this.$refs.stripContainer.getBoundingClientRect(),n=this.$refs.strip.getBoundingClientRect()[this.config.direction]-t[this.config.direction],i=e[this.config.clientAxis]-this.axis+n;this.axis=e[this.config.clientAxis],this.changeOffset(i)}},handlerWheel:function(e){var t=this.$refs.stripContainer.getBoundingClientRect(),n=this.$refs.strip.getBoundingClientRect()[this.config.direction]-t[this.config.direction]+e[this.config.wheelDelta];this.changeOffset(n,e)},changeOffset:function(e,t){e<0&&(e=0),e>this.maxOffset&&(e=this.maxOffset),t&&e>0&&e<this.maxOffset&&(t.preventDefault(),t.stopImmediatePropagation()),this.currentOffset=e,this.$refs.strip.style.transform=this.config.translate+"("+e+"px)",this.$emit("change",e/this.percentage)}},created:function(){var e={h:{sizeAttr:"width",client:"clientWidth",clientAxis:"clientX",translate:"translateX",direction:"left",wheelDelta:"deltaX"},v:{sizeAttr:"height",client:"clientHeight",clientAxis:"clientY",translate:"translateY",direction:"top",wheelDelta:"deltaY"}};this.config=this.horizontal?e.h:e.v},destroyed:function(){i(document,"mouseup",this.handlerClickUp),i(document,"mousemove",this.handlerMove)}},g=r(function(e){(e.exports={}).forEach=function(e,t){for(var n=0;n<e.length;n++){var i=t(e[n]);if(i)return i}}}),m=function(e){var t=e.stateHandler.getState;return{isDetectable:function(e){var n=t(e);return n&&!!n.isDetectable},markAsDetectable:function(e){t(e).isDetectable=!0},isBusy:function(e){return!!t(e).busy},markBusy:function(e,n){t(e).busy=!!n}}},v=function(e){function t(t){var i=e.get(t);return void 0===i?[]:n[i]||[]}var n={};return{get:t,add:function(t,i){var o=e.get(t);n[o]||(n[o]=[]),n[o].push(i)},removeListener:function(e,n){for(var i=t(e),o=0,r=i.length;o<r;++o)if(i[o]===n){i.splice(o,1);break}},removeAllListeners:function(e){var n=t(e);n&&(n.length=0)}}},b=function(){var e=1;return{generate:function(){return e++}}},y=function(e){var t=e.idGenerator,n=e.stateHandler.getState;return{get:function(e){var t=n(e);return t&&void 0!==t.id?t.id:null},set:function(e){var i=n(e);if(!i)throw new Error("setId required the element to have a resize detection state.");var o=t.generate();return i.id=o,o}}},S=function(e){function t(){}var n={log:t,warn:t,error:t};if(!e&&window.console){var i=function(e,t){e[t]=function(){var e=console[t];if(e.apply)e.apply(console,arguments);else for(var n=0;n<arguments.length;n++)e(arguments[n])}};i(n,"log"),i(n,"warn"),i(n,"error")}return n},w=r(function(e){var t=e.exports={};t.isIE=function(e){return!!function(){var e=navigator.userAgent.toLowerCase();return-1!==e.indexOf("msie")||-1!==e.indexOf("trident")||-1!==e.indexOf(" edge/")}()&&(!e||e===function(){var e=3,t=document.createElement("div"),n=t.getElementsByTagName("i");do{t.innerHTML="\x3c!--[if gt IE "+ ++e+"]><i></i><![endif]--\x3e"}while(n[0]);return e>4?e:void 0}())},t.isLegacyOpera=function(){return!!window.opera}}),x=r(function(e){(e.exports={}).getOption=function(e,t,n){var i=e[t];return void 0!==i&&null!==i||void 0===n?i:n}}),z=function(e){function t(){for(h=!0;d.size();){var e=d;d=s(),e.process()}h=!1}function n(){c=o(t)}function i(e){return clearTimeout(e)}function o(e){return function(e){return setTimeout(e,0)}(e)}var r=(e=e||{}).reporter,a=x.getOption(e,"async",!0),l=x.getOption(e,"auto",!0);l&&!a&&(r&&r.warn("Invalid options combination. auto=true and async=false is invalid. Setting async=true."),a=!0);var c,d=s(),h=!1;return{add:function(e,t){!h&&l&&a&&0===d.size()&&n(),d.add(e,t)},force:function(e){h||(void 0===e&&(e=a),c&&(i(c),c=null),e?n():t())}}},E="_erd",L={initState:function(e){return e[E]={},a(e)},getState:a,cleanState:function(e){delete e[E]}},H=function(e){function t(e){return o(e).object}var n=(e=e||{}).reporter,i=e.batchProcessor,o=e.stateHandler.getState;if(!n)throw new Error("Missing required dependency: reporter.");return{makeDetectable:function(e,t,r){r||(r=t,t=e,e=null),e=e||{},w.isIE(8)?r(t):function(e,t){function r(){function i(){if("static"===l.position){e.style.position="relative";var t=function(e,t,n,i){var o=n[i];"auto"!==o&&"0"!==function(e){return e.replace(/[^-\d\.]/g,"")}(o)&&(e.warn("An element that is positioned static has style."+i+"="+o+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",t),t.style[i]=0)};t(n,e,l,"top"),t(n,e,l,"right"),t(n,e,l,"bottom"),t(n,e,l,"left")}}""!==l.position&&(i(l),a=!0);var r=document.createElement("object");r.style.cssText=s,r.tabIndex=-1,r.type="text/html",r.onload=function(){function n(e,t){e.contentDocument?t(e.contentDocument):setTimeout(function(){n(e,t)},100)}a||i(),n(this,function(n){t(e)})},w.isIE()||(r.data="about:blank"),e.appendChild(r),o(e).object=r,w.isIE()&&(r.data="about:blank")}var s="display: block; position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none; padding: 0; margin: 0; opacity: 0; z-index: -1000; pointer-events: none;",a=!1,l=window.getComputedStyle(e),c=e.offsetWidth,d=e.offsetHeight;o(e).startSize={width:c,height:d},i?i.add(r):r()}(t,r)},addListener:function(e,n){function i(){n(e)}if(!t(e))throw new Error("Element is not detectable by this strategy.");w.isIE(8)?(o(e).object={proxy:i},e.attachEvent("onresize",i)):t(e).contentDocument.defaultView.addEventListener("resize",i)},uninstall:function(e){w.isIE(8)?e.detachEvent("onresize",o(e).object.proxy):e.removeChild(t(e)),delete o(e).object}}},C=g.forEach,T=function(e){function t(e){e.className+=" "+h+"_animation_active"}function n(e,t,n){if(e.addEventListener)e.addEventListener(t,n);else{if(!e.attachEvent)return s.error("[scroll] Don't know how to add event listeners.");e.attachEvent("on"+t,n)}}function i(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n);else{if(!e.detachEvent)return s.error("[scroll] Don't know how to remove event listeners.");e.detachEvent("on"+t,n)}}function o(e){return l(e).container.childNodes[0].childNodes[0].childNodes[0]}function r(e){return l(e).container.childNodes[0].childNodes[0].childNodes[1]}var s=(e=e||{}).reporter,a=e.batchProcessor,l=e.stateHandler.getState,c=e.idHandler;if(!a)throw new Error("Missing required dependency: batchProcessor");if(!s)throw new Error("Missing required dependency: reporter.");var d=function(){var e=document.createElement("div");e.style.cssText="position: absolute; width: 1000px; height: 1000px; visibility: hidden; margin: 0; padding: 0;";var t=document.createElement("div");t.style.cssText="position: absolute; width: 500px; height: 500px; overflow: scroll; visibility: none; top: -1500px; left: -1500px; visibility: hidden; margin: 0; padding: 0;",t.appendChild(e),document.body.insertBefore(t,document.body.firstChild);var n=500-t.clientWidth,i=500-t.clientHeight;return document.body.removeChild(t),{width:n,height:i}}(),h="erd_scroll_detection_container";return function(e,t){if(!document.getElementById(e)){var n=t+"_animation",i="/* Created by the element-resize-detector library. */\n";i+="."+t+" > div::-webkit-scrollbar { display: none; }\n\n",i+="."+t+"_animation_active { -webkit-animation-duration: 0.1s; animation-duration: 0.1s; -webkit-animation-name: "+n+"; animation-name: "+n+"; }\n",i+="@-webkit-keyframes "+n+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }\n",function(t,n){n=n||function(e){document.head.appendChild(e)};var i=document.createElement("style");i.innerHTML=t,i.id=e,n(i)}(i+="@keyframes "+n+" { 0% { opacity: 1; } 50% { opacity: 0; } 100% { opacity: 1; } }")}}("erd_scroll_detection_scrollbar_style",h),{makeDetectable:function(e,i,u){function f(){if(e.debug){var t=Array.prototype.slice.call(arguments);if(t.unshift(c.get(i),"Scroll: "),s.log.apply)s.log.apply(null,t);else for(var n=0;n<t.length;n++)s.log(t[n])}}function p(e){var t=l(e).container.childNodes[0],n=getComputedStyle(t);return!n.width||-1===n.width.indexOf("px")}function g(){var e=getComputedStyle(i),t={};return t.position=e.position,t.width=i.offsetWidth,t.height=i.offsetHeight,t.top=e.top,t.right=e.right,t.bottom=e.bottom,t.left=e.left,t.widthCSS=e.width,t.heightCSS=e.height,t}function m(){var e=g();l(i).startSize={width:e.width,height:e.height},f("Element start size",l(i).startSize)}function v(){l(i).listeners=[]}function b(){if(f("storeStyle invoked."),l(i)){var e=g();l(i).style=e}else f("Aborting because element has been uninstalled")}function y(e,t,n){l(e).lastWidth=t,l(e).lastHeight=n}function S(e){return o(e).childNodes[0]}function w(){return 2*d.width+1}function x(){return 2*d.height+1}function z(e){return e+10+w()}function E(e){return e+10+x()}function L(e){return 2*e+w()}function H(e){return 2*e+x()}function T(e,t,n){var i=o(e),s=r(e),a=z(t),l=E(n),c=L(t),d=H(n);i.scrollLeft=a,i.scrollTop=l,s.scrollLeft=c,s.scrollTop=d}function $(){var e=l(i).container;if(!e){(e=document.createElement("div")).className=h,e.style.cssText="visibility: hidden; display: inline; width: 0px; height: 0px; z-index: -1; overflow: hidden; margin: 0; padding: 0;",l(i).container=e,t(e),i.appendChild(e);var o=function(){l(i).onRendered&&l(i).onRendered()};n(e,"animationstart",o),l(i).onAnimationStart=o}return e}function A(){function e(){l(i).onExpand&&l(i).onExpand()}function t(){l(i).onShrink&&l(i).onShrink()}if(f("Injecting elements"),l(i)){!function(){var e=l(i).style;if("static"===e.position){i.style.position="relative";var t=function(e,t,n,i){var o=n[i];"auto"!==o&&"0"!==function(e){return e.replace(/[^-\d\.]/g,"")}(o)&&(e.warn("An element that is positioned static has style."+i+"="+o+" which is ignored due to the static positioning. The element will need to be positioned relative, so the style."+i+" will be set to 0. Element: ",t),t.style[i]=0)};t(s,i,e,"top"),t(s,i,e,"right"),t(s,i,e,"bottom"),t(s,i,e,"left")}}();var o=l(i).container;o||(o=$());var r=d.width,a=d.height,c="position: absolute; flex: none; overflow: hidden; z-index: -1; visibility: hidden; "+function(e,t,n,i){return e=e?e+"px":"0",t=t?t+"px":"0",n=n?n+"px":"0",i=i?i+"px":"0","left: "+e+"; top: "+t+"; right: "+i+"; bottom: "+n+";"}(-(1+r),-(1+a),-a,-r),u=document.createElement("div"),p=document.createElement("div"),g=document.createElement("div"),m=document.createElement("div"),v=document.createElement("div"),b=document.createElement("div");u.dir="ltr",u.style.cssText="position: absolute; flex: none; overflow: hidden; z-index: -1; visibility: hidden; width: 100%; height: 100%; left: 0px; top: 0px;",u.className=h,p.className=h,p.style.cssText=c,g.style.cssText="position: absolute; flex: none; overflow: scroll; z-index: -1; visibility: hidden; width: 100%; height: 100%;",m.style.cssText="position: absolute; left: 0; top: 0;",v.style.cssText="position: absolute; flex: none; overflow: scroll; z-index: -1; visibility: hidden; width: 100%; height: 100%;",b.style.cssText="position: absolute; width: 200%; height: 200%;",g.appendChild(m),v.appendChild(b),p.appendChild(g),p.appendChild(v),u.appendChild(p),o.appendChild(u),n(g,"scroll",e),n(v,"scroll",t),l(i).onExpandScroll=e,l(i).onShrinkScroll=t}else f("Aborting because element has been uninstalled")}function k(){function t(e,t,n){var i=S(e),o=z(t),r=E(n);i.style.width=o+"px",i.style.height=r+"px"}function n(n){var o=i.offsetWidth,r=i.offsetHeight;f("Storing current size",o,r),y(i,o,r),a.add(0,function(){if(l(i))if(d()){if(e.debug){var n=i.offsetWidth,a=i.offsetHeight;n===o&&a===r||s.warn(c.get(i),"Scroll: Size changed before updating detector elements.")}t(i,o,r)}else f("Aborting because element container has not been initialized");else f("Aborting because element has been uninstalled")}),a.add(1,function(){l(i)?d()?T(i,o,r):f("Aborting because element container has not been initialized"):f("Aborting because element has been uninstalled")}),n&&a.add(2,function(){l(i)?d()?n():f("Aborting because element container has not been initialized"):f("Aborting because element has been uninstalled")})}function d(){return!!l(i).container}function h(){f("notifyListenersIfNeeded invoked");var e=l(i);return void 0===l(i).lastNotifiedWidth&&e.lastWidth===e.startSize.width&&e.lastHeight===e.startSize.height?f("Not notifying: Size is the same as the start size, and there has been no notification yet."):e.lastWidth===e.lastNotifiedWidth&&e.lastHeight===e.lastNotifiedHeight?f("Not notifying: Size already notified"):(f("Current size not notified, notifying..."),e.lastNotifiedWidth=e.lastWidth,e.lastNotifiedHeight=e.lastHeight,void C(l(i).listeners,function(e){e(i)}))}function u(){if(f("Scroll detected."),p(i))f("Scroll event fired while unrendered. Ignoring...");else{var e=i.offsetWidth,t=i.offsetHeight;e!==i.lastWidth||t!==i.lastHeight?(f("Element size changed."),n(h)):f("Element size has not changed ("+e+"x"+t+").")}}if(f("registerListenersAndPositionElements invoked."),l(i)){l(i).onRendered=function(){if(f("startanimation triggered."),p(i))f("Ignoring since element is still unrendered...");else{f("Element rendered.");var e=o(i),t=r(i);0!==e.scrollLeft&&0!==e.scrollTop&&0!==t.scrollLeft&&0!==t.scrollTop||(f("Scrollbars out of sync. Updating detector elements..."),n(h))}},l(i).onExpand=u,l(i).onShrink=u;var g=l(i).style;t(i,g.width,g.height)}else f("Aborting because element has been uninstalled")}function N(){if(f("finalizeDomMutation invoked."),l(i)){var e=l(i).style;y(i,e.width,e.height),T(i,e.width,e.height)}else f("Aborting because element has been uninstalled")}function O(){u(i)}function _(){f("Installing..."),v(),m(),a.add(0,b),a.add(1,A),a.add(2,k),a.add(3,N),a.add(4,O)}u||(u=i,i=e,e=null),e=e||{},f("Making detectable..."),function(e){return!function(e){return e===e.ownerDocument.body||e.ownerDocument.body.contains(e)}(e)||null===getComputedStyle(e)}(i)?(f("Element is detached"),$(),f("Waiting until element is attached..."),l(i).onRendered=function(){f("Element is now attached"),_()}):_()},addListener:function(e,t){if(!l(e).listeners.push)throw new Error("Cannot add listener to an element that is not detectable.");l(e).listeners.push(t)},uninstall:function(e){var t=l(e);t&&(t.onExpandScroll&&i(o(e),"scroll",t.onExpandScroll),t.onShrinkScroll&&i(r(e),"scroll",t.onShrinkScroll),t.onAnimationStart&&i(t.container,"animationstart",t.onAnimationStart),t.container&&e.removeChild(t.container))}}},$=g.forEach,A=function(e){var t;if((e=e||{}).idHandler)t={get:function(t){return e.idHandler.get(t,!0)},set:e.idHandler.set};else{var n=b(),i=y({idGenerator:n,stateHandler:L});t=i}var o=e.reporter;o||(o=S(!1===o));var r=h(e,"batchProcessor",z({reporter:o})),s={};s.callOnAdd=!!h(e,"callOnAdd",!0),s.debug=!!h(e,"debug",!1);var a,u=v(t),f=m({stateHandler:L}),p=h(e,"strategy","object"),g={reporter:o,batchProcessor:r,stateHandler:L,idHandler:t};if("scroll"===p&&(w.isLegacyOpera()?(o.warn("Scroll strategy is not supported on legacy Opera. Changing to object strategy."),p="object"):w.isIE(9)&&(o.warn("Scroll strategy is not supported on IE9. Changing to object strategy."),p="object")),"scroll"===p)a=T(g);else{if("object"!==p)throw new Error("Invalid strategy name: "+p);a=H(g)}var x={};return{listenTo:function(e,n,i){function r(e){var t=u.get(e);$(t,function(t){t(e)})}function p(e,t,n){u.add(t,n),e&&n(t)}if(i||(i=n,n=e,e={}),!n)throw new Error("At least one element required.");if(!i)throw new Error("Listener required.");if(d(n))n=[n];else{if(!l(n))return o.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");n=c(n)}var g=0,m=h(e,"callOnAdd",s.callOnAdd),v=h(e,"onReady",function(){}),b=h(e,"debug",s.debug);$(n,function(e){L.getState(e)||(L.initState(e),t.set(e));var s=t.get(e);if(b&&o.log("Attaching listener to element",s,e),!f.isDetectable(e))return b&&o.log(s,"Not detectable."),f.isBusy(e)?(b&&o.log(s,"System busy making it detectable"),p(m,e,i),x[s]=x[s]||[],void x[s].push(function(){++g===n.length&&v()})):(b&&o.log(s,"Making detectable..."),f.markBusy(e,!0),a.makeDetectable({debug:b},e,function(e){if(b&&o.log(s,"onElementDetectable"),L.getState(e)){f.markAsDetectable(e),f.markBusy(e,!1),a.addListener(e,r),p(m,e,i);var t=L.getState(e);if(t&&t.startSize){var l=e.offsetWidth,c=e.offsetHeight;t.startSize.width===l&&t.startSize.height===c||r(e)}x[s]&&$(x[s],function(e){e()})}else b&&o.log(s,"Element uninstalled before being detectable.");delete x[s],++g===n.length&&v()}));b&&o.log(s,"Already detecable, adding listener."),p(m,e,i),g++}),g===n.length&&v()},removeListener:u.removeListener,removeAllListeners:u.removeAllListeners,uninstall:function(e){if(!e)return o.error("At least one element is required.");if(d(e))e=[e];else{if(!l(e))return o.error("Invalid arguments. Must be a DOM element or a collection of DOM elements.");e=c(e)}$(e,function(e){u.removeAllListeners(e),a.uninstall(e),L.cleanState(e)})}}},k=t;"undefined"!=typeof window&&window.Vue&&(k=window.Vue);var N={render:function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"happy-scroll",staticClass:"happy-scroll"},[n("div",{ref:"container",staticClass:"happy-scroll-container",style:[e.initSize],on:{scroll:function(t){return t.stopPropagation(),e.onScroll(t)}}},[n("div",{ref:"content",staticClass:"happy-scroll-content",style:[e.contentBorderStyle]},[e._t("default")],2)]),e.hideVertical?e._e():n("happy-scroll-strip",e._g(e._b({ref:"stripY",attrs:{throttle:e.throttle,move:e.moveY},on:{change:e.slideYChange}},"happy-scroll-strip",e.$attrs,!1),e.$listeners)),e.hideHorizontal?e._e():n("happy-scroll-strip",e._g(e._b({ref:"stripX",attrs:{horizontal:"",throttle:e.throttle,move:e.moveX},on:{change:e.slideXChange}},"happy-scroll-strip",e.$attrs,!1),e.$listeners))],1)},staticRenderFns:[],name:"happy-scroll",inheritAttrs:!1,components:{HappyScrollStrip:p},props:{scrollTop:{type:[Number,String],default:0},scrollLeft:{type:[Number,String],default:0},hideVertical:Boolean,hideHorizontal:Boolean,throttle:{type:Number,default:14},resize:Boolean,smallerMoveH:{type:String,default:""},smallerMoveV:{type:String,default:""},biggerMoveH:{type:String,default:""},biggerMoveV:{type:String,default:""}},data:function(){return{initSize:{},moveX:+this.scrollLeft,moveY:+this.scrollTop,scrollThrottle:u(this.throttle),browserHSize:0,browserVSize:0,isScrollNotUseSpace:void 0}},watch:{scrollTop:function(e){this.$refs.container.scrollTop=this.moveY=+e},scrollLeft:function(e){this.$refs.container.scrollLeft=this.moveX=+e},hideVertical:function(e){e||this.$nextTick(this.computeStripY)},hideHorizontal:function(e){e||this.$nextTick(this.computeStripX)}},computed:{contentBorderStyle:function(){return void 0===this.isScrollNotUseSpace?{}:{"border-right":20-this.browserHSize+"px solid transparent","border-bottom":20-this.browserVSize+"px solid transparent"}}},methods:{slideYChange:function(e){this.$refs.container.scrollTop=e,this.$emit("update:scrollTop",this.$refs.container.scrollTop)},slideXChange:function(e){this.$refs.container.scrollLeft=e,this.$emit("update:scrollLeft",this.$refs.container.scrollLeft)},onScroll:function(e){if(!this.scrollThrottle(Date.now()))return!1;this.moveY=e.target.scrollTop,this.moveX=e.target.scrollLeft,this.updateSyncScroll()},initBrowserSize:function(){void 0!==this.isScrollNotUseSpace&&(!0===this.isScrollNotUseSpace?(this.browserHSize=0,this.browserVSize=0):(this.browserHSize=this.$refs.container.offsetWidth-this.$refs.container.clientWidth,this.browserVSize=this.$refs.container.offsetHeight-this.$refs.container.clientHeight))},computeStripX:function(){if(!this.hideHorizontal){var e=this.$refs["happy-scroll"],t=this.$slots.default[0].elm;this.$refs.stripX.computeStrip(t.scrollWidth,e.clientWidth)}},computeStripY:function(){if(!this.hideVertical){var e=this.$refs["happy-scroll"],t=this.$slots.default[0].elm;this.$refs.stripY.computeStrip(t.scrollHeight,e.clientHeight)}},resizeListener:function(){var e=this;if(this.resize){var t=A({strategy:"scroll",callOnAdd:!1}),n=this.$slots.default[0].elm,i=n.clientHeight,o=n.clientWidth;t.listenTo(n,function(t){e.computeStripX(),e.computeStripY(),e.initBrowserSize();var n=void 0;t.clientHeight<i&&(n=e.smallerMoveH.toLocaleLowerCase()),t.clientHeight>i&&(n=e.biggerMoveH.toLocaleLowerCase()),"start"===n&&(e.moveY=0,e.slideYChange(e.moveY)),"end"===n&&(e.moveY=t.clientHeight,e.slideYChange(e.moveY)),i=t.clientHeight,n="",t.clientWidth<o&&(n=e.smallerMoveV.toLocaleLowerCase()),t.clientWidth>o&&(n=e.biggerMoveV.toLocaleLowerCase()),"start"===n&&(e.moveX=0,e.slideXChange(e.moveX)),"end"===n&&(e.moveX=t.clientWidth,e.slideXChange(e.moveX)),o=t.clientWidth})}},setContainerSize:function(){this.initSize={width:this.$refs["happy-scroll"].clientWidth+20+"px",height:this.$refs["happy-scroll"].clientHeight+20+"px"}},checkScrollMode:function(){if(void 0===k._happyJS._isScrollNotUseSpace){var e=this.$slots.default[0].elm,t=this.$refs.container;(e.offsetHeight>t.clientHeight||e.offsetWidth>t.clientWidth)&&(t.offsetWidth>t.clientWidth||t.offsetHeight>t.clientHeight?k._happyJS._isScrollNotUseSpace=!1:k._happyJS._isScrollNotUseSpace=!0,this.isScrollNotUseSpace=k._happyJS._isScrollNotUseSpace)}}},beforeCreate:function(){var e=k._happyJS=k._happyJS||{};this.isScrollNotUseSpace=e._isScrollNotUseSpace},created:function(){this.updateSyncScroll=f(function(){this.$emit("update:scrollTop",this.moveY),this.$emit("update:scrollLeft",this.moveX)},this.throttle)},mounted:function(){var e=this;this.setContainerSize(),this.$nextTick(function(){e.computeStripX(),e.computeStripY(),e.checkScrollMode(),e.initBrowserSize(),e.$nextTick(function(){e.scrollTop&&(e.$refs.container.scrollTop=+e.scrollTop),e.scrollLeft&&(e.$refs.container.scrollLeft=+e.scrollLeft)})}),this.resizeListener(),this.$watch("browserHSize",this.setContainerSize),this.$watch("browserVSize",this.setContainerSize)}};"undefined"!=typeof window&&window.Vue&&Vue.component("happy-scroll",N);var O={install:function(e){e.component("happy-scroll",N)},version:"2.1.1"};e.default=O,e.HappyScroll=N,e.version="2.1.1",Object.defineProperty(e,"__esModule",{value:!0})});
//# sourceMappingURL=happy-scroll.min.js.map