.happy-scroll {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.happy-scroll-strip--vertical {
    position: absolute;
    right: 0px;
    top:0;
    height: 100%;
}
.happy-scroll-strip--vertical .happy-scroll-bar {
    background-color: rgba(51,51,51,0.2);
    border-radius: 7px;
    width: 6px;
    height: 0;
    -webkit-transition: width 0.4s ease, height 0.6s ease;
    transition: width 0.4s ease, height 0.6s ease;
}
.happy-scroll-strip--horizontal {
    position: absolute;
    bottom: 0px;
    left:0;
    width: 100%;
}
.happy-scroll-strip--horizontal .happy-scroll-bar {
    background-color: rgba(51,51,51,0.2);
    border-radius: 7px;
    height: 6px;
    width: 0;
    -webkit-transition: height 0.4s ease, width 0.6s ease;
    transition: height 0.4s ease, width 0.6s ease;
}
/* 在webkit内核下将滚动条隐藏，Firefox下采用容器覆盖的机制 */
.happy-scroll-container::-webkit-scrollbar  {
    display: none;
}
.happy-scroll-container {
    overflow: auto;
}

.happy-scroll-container .happy-scroll-content{
    /* 解决横向滚动差 20px 的问题 */
    display: inline-block;
}