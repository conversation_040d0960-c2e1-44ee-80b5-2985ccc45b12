$(function () {
    $("#jqGrid").Grid({
        multiselect: true,
        multiboxonly:true,
        beforeSelectRow: beforeSelectRow,
        url: '../pjclcoin/dylist',
        colModel: [
            {label: 'id', name: 'id', key: true, hidden: true},
            {label: '编号', name: 'nummber',  width: 60,sortable:false},
            {label: '名称', name: 'nameex',  width: 100,sortable:false},
            {label: '等级', name: 'rank',  width: 60,sortable:false},
            {label: '年代', name: 'yearex',  width: 60,sortable:false},
            {label: '品相分数', name: 'scoreex',  width: 60,sortable:false},
            {label: '尺寸', name: 'size',  width: 60,sortable:false},
            {label: '重量', name: 'weight',  width: 60,sortable:false},
            { label: '操作', name: 'state', index: 'state', width: 50, edittype:"button", formatter:
                    function cmgStateFormat(cellValue,grid, rows) {
                           return "<button class='btn btn-primary' onclick='window.open(\"../sys/PrintPhoto.html?id=" + rows.id+"&nummber="+rows.nummber + "\")'>证书</button>";
                    }
                },
        ]
    });
    function beforeSelectRow(){
        $("#jqgridId").jqGrid('resetSelection');
        return(true);
    }

});

var vm = new Vue({
    el: '#rrapp',
    data: {
        read:0,
        showList: true,
        title: null,
        pjClCoin: {},
        uploadList: [],
        visible: false,
        parentName:"",
        statusList: [
            {id: 0, name: '未鉴定'},
            {id: 1, name: '真'},
            {id: 2, name: '赝品'},
            {id: 3, name: '存疑'},
            //{id: 4, name: '性质伪'},
            {id: 5, name: '不提供服务'},
            {id: 6, name: '不适合评级'},
            {id: 7, name: '撤评'},
            {id: 8, name: '不适合评级(赝品)'},
            {id: 9, name: '不适合评级(锭体、铭文修复超出规定)'},
            {id: 10,name: '不适合评级(老假、老仿、臆造)'},
            //{id: 110, name: '非真'}
        ],
        gmList: [
            {id: '03', name: '机制币'},
            {id: '02', name: '古钱币'},
            {id: '04', name: '银锭'},
            {id: '01', name: '纸币'}
        ],
        ruleValidate: {
            name: [
                {required: true, message: '名称不能为空', trigger: 'blur'}
            ]
        },
        q: {
            result: '',
            gm: '',
            rname: '',
            niandai: '',
            nummbers: '',
            sendnum: ''
        },
        isPrintModel: false,
        printBuild: null,
    },
    methods: {
        query: function () {
            vm.reload();
        },
        reload: function (event) {
            vm.showList = true;
            var page = $("#jqGrid").jqGrid('getGridParam', 'page');
            var sendnums;
            if(vm.q.sendnums != undefined && vm.q.sendnums!=''){
                sendnums = vm.q.sendnums.replace(/\n/g,",").replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,'');
            }
            vm.q.result +=   "";
            var sendnums
            $("#jqGrid").jqGrid('setGridParam', {
                postData: {
                    'result': vm.q.result.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
                    'gm': vm.q.gm.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
                    'rname': vm.q.rname.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
                    'niandai': vm.q.niandai.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
                    'nummbers': vm.q.nummbers.replace(/\n/g,",").replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
                    'sendnums': sendnums,
                },
                page: page
            }).trigger("reloadGrid");
        },
        reloadSearch: function() {
            vm.q= {
                result: '',
                gm: '',
                rname: '',
                niandai: '',
                nummbers: '',
                sendnums: ''
            },
                vm.reload();
        },
        handleSubmit: function (name) {
            handleSubmitValidate(this, name, function () {
                vm.saveOrUpdate()
            });
        },

    }
});


