$(function () {
	$("#jqGrid").Grid({
		caption: "鉴定单钱币明细 ↓",
		url: '../pjclcoin/dylist',
		colModel: [
			{label: 'id', name: 'id', key: true, hidden: true},
			{label: '钱币编号', name: 'nummber',  width: 20,sortable:false},
			{label: '类型', name: 'gmname', width: 20,sortable:false},
			{label: '钱币名称', name: 'name',  width: 80,sortable:false},
			{label: '评级费用', name: 'finalfee',  width: 80,sortable:false},
		],
		rowNum: 1000,
	});
});

let vm = new Vue({
	el: '#rrapp',
	data: {
		read:0,
		showList: true,
		pjClCoin: {},
		uploadList: [],
		visible: false,
		parentName:"",
		gmList: [
			{id: '03', name: '机制币'},
			{id: '02', name: '古钱币'},
			{id: '04', name: '银锭'},
			{id: '01', name: '纸币'}
		],
		ruleValidate: {
			name: [
				{required: true, message: '名称不能为空', trigger: 'blur'}
			]
		},
		q: {
			gm: '',
			nummbers: '',
			sendnum: ''
		},
		printType: 0,
		printTypeList: [
			{id: 0, name: '选择Excel模板'},
			{id: 1, name: '机制币、纸币 [通用]', label: 'puff/general-excel.grf',service: 'general'},
			{id: 2, name: '古钱币', label: 'puff/gq-excel.grf',service: 'gqexcel'},
			{id: 2, name: '银锭', label: 'puff/yd-excel.grf',service: 'yd'},
		],
		isPrintModel: false,
		printBuild: null,
	},
	methods: {
		query: function () {
			vm.reload();
		},
		reload: function (event) {

			vm.showList = true;
			let page = $("#jqGrid").jqGrid('getGridParam', 'page');
			var sendnums;
			if(vm.q.sendnums != undefined && vm.q.sendnums!=''){
				sendnums = vm.q.sendnums.replace(/\n/g,",").replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,'');
			}
			vm.q.result +=   "";
			var sendnums
			$("#jqGrid").jqGrid('setGridParam', {
				postData: {
					'gm': vm.q.gm.replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'nummbers': vm.q.nummbers.replace(/\n/g,",").replace(/^(\s|\u00A0)+/,'').replace(/(\s|\u00A0)+$/,''),
					'sendnums': sendnums,
				},
				page: page
			}).trigger("reloadGrid");
		},
		reloadSearch: function() {//重置
			vm.q= {
				gm: '',
				nummbers: '',
				sendnums: ''
			},
				vm.reload();
		},
		print: function() {
			this.printBuild.print();
		},
		prePrint: function () {
			var that = this;
			// var ids = getSelectedRows("#jqGrid");
			var ids = $('#jqGrid').getGridParam("selarrrow");
			if (this.printType == null || this.printType == 0) {
				this.$Message.error('请选择excel模板');
				return;
			}
			function printItems(ids) {
				that.printBuild = new PrintBuild();
				that.printBuild.buildCoin(ids, that.printTypeList[that.printType],0);
			}
			if (ids == null || ids.length == 0){
				confirm('不选中默认打印当前页面，是否继续？',
					function () {//确定事件
						ids = $("#jqGrid").getDataIDs();
						printItems(ids);
					});
			} else {
				printItems(ids);
			}
			this.isPrintModel = true;
		},

	}
});


