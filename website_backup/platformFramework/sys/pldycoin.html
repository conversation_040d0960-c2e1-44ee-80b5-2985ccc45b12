<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="expires" content="0">
    <title>批量打印</title>
    <meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<!--bootstrap-->
<link rel="stylesheet" href="/platformFramework/statics/css/bootstrap.min.css">
<link rel="stylesheet" href="/platformFramework/statics/css/font-awesome.min.css">
<!--jqgrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="/platformFramework/statics/plugins/ztree/css/metroStyle/metroStyle.css">
<!--main-->
<link rel="stylesheet" href="/platformFramework/statics/css/main.css">
<link rel="stylesheet" href="/platformFramework/statics/css/iview.css">
<link rel="stylesheet" href="/platformFramework/statics/css/style.css">
<!--treegrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/treegrid/jquery.treegrid.css">
<!--富文本-->
<link rel="stylesheet" href='/platformFramework/statics/plugins/froala_editor/css/froala_editor.min.css'/>

<!--jquery-->
<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<!--layer-->
<script src="/platformFramework/statics/plugins/layer/layer.js"></script>
<!--bootstrap-->
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<!--vue-->
<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/iview.min.js"></script>
<!--jqgrid-->
<script src="/platformFramework/statics/plugins/jqgrid/grid.locale-cn.js"></script>
<script src="/platformFramework/statics/plugins/jqgrid/jquery.jqGrid.min.js"></script>
<!--ztree-->
<script src="/platformFramework/statics/plugins/ztree/jquery.ztree.all.min.js"></script>
<!--treegrid-->
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.extension.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.min.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.bootstrap3.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/tree.table.js"></script>

<!--simplemde富文本-->
<script src='/platformFramework/statics/plugins/froala_editor/js/froala_editor.min.js'></script>
<!--[if lt IE 9]>
<script src="/platformFramework/statics/plugins/froala_editor/js/froala_editor_ie8.min.js"></script>
<![endif]-->
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/tables.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/lists.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/colors.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/media_manager.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_family.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_size.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/block_styles.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/video.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/langs/zh_cn.js"></script>

<script src="/platformFramework/statics/libs/jquery-extend.js"></script>
<script src="/platformFramework/js/common.js"></script>


<!--exce-->
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jexcel.css">
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jsuites.css">
<link rel="stylesheet" href="/platformFramework/js/v4/jquery-clockpicker.min.css">
<link rel="stylesheet" href="/platformFramework/js/v4/happy-scroll.css">
<!--<link rel="stylesheet" href="/platformFramework/statics/css/v8/jspreadsheet.css">-->
<script src="/platformFramework/js/v4/jexcel.js"></script>
<script src="/platformFramework/js/v4/jsuites.js"></script>
<script src="/platformFramework/js/v4/jquery-clockpicker.min.js"></script>
<script src="/platformFramework/js/v4/happy-scroll.min.js"></script>
<script src="/platformFramework/js/v4/jquery.cookie.min.js"></script>
<!--<script src="/platformFramework/js/v8/jspreadsheet.js"></script>--></head>
<body>
<div id="vue-table" v-cloak>
	<div v-show="!isPrintModel && showList">
        <Row :gutter="16">
             <div class="search-group">
                <i-col span="3">
                	<textarea id="nummbers" v-model="q.nummbers" placeholder="钱币编号（换行隔开）" style="width:180px" rows="3" class="ivu-input"></textarea>
                </i-col>

                <i-col span="3">
                	<textarea id="sendnums" v-model="q.sendnums" placeholder="送评单号（换行隔开）" style="width:180px" rows="3" class="ivu-input"></textarea>
                </i-col>

                <i-col span="3">
	                <i-select v-model="q.gm" filterable label-in-value>
	                     <i-option v-for="statusItem in gmList" :value="statusItem.id"
	                               :key="statusItem.id">{{statusItem.name}}
	                     </i-option>
	                </i-select>
                </i-col>
                 <i-button type="success" @click="query">查询</i-button>
                 &nbsp;&nbsp;
                 <i-button @click="reloadSearch">重置</i-button>
            </div>
        </Row>
	    <table id="jqGrid"></table>
	            <Row :gutter="16">
            <div class="buttons-group" style="float:left; margin-bottom: 200px;margin-left: 15px;">
                <div style="display: inline-block; width: 200px">
                    <i-select v-model="printType">
                        <i-option v-for="(item, index) in printTypeList" :value="index"
                                  :key="item.id">{{item.name}}
                        </i-option>
                    </i-select>
                </div>
                <div style="display: inline-block; width: 200px">
                    <i-select v-model="conversionType">
                        <i-option v-for="(item, index) in conversionList" :value="index"
                                  :key="item.id">{{item.name}}
                        </i-option>
                    </i-select>
                </div>

                <i-button type="info" @click="coinCheck" id="coinCheck">审核检测</i-button>
                <i-button type="success" @click="prePrint" disabled="disabled" id="PrintBtn">打印标签</i-button>
                <i-button @click="printWhite">白标打印</i-button>
            </div>
        </Row>
    </div>

    <div v-show="isPrintModel">
        <div>
            <i-form>
                <Form-item>
                    <i-button type="primary" @click="print()">打印</i-button>
                    <i-button type="warning" @click="isPrintModel = false" style="margin-left: 8px">返回</i-button>
                </Form-item>
            </i-form>
        </div>
        <div id="ReportViewerBox"></div>
    </div>
</div>
<script src="/platformFramework/js/print/CreateControl.js?_1749996682638"></script>
<script src="/platformFramework/js/sys/pldycoin.js?_1749996682638"></script>
<script src="/platformFramework/js/print/print-style.js?_1749996682638"></script>

</body>
</html>