<!DOCTYPE html>
<html>
<head>
	<title>创建送评单</title>
	<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<!--bootstrap-->
<link rel="stylesheet" href="/platformFramework/statics/css/bootstrap.min.css">
<link rel="stylesheet" href="/platformFramework/statics/css/font-awesome.min.css">
<!--jqgrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="/platformFramework/statics/plugins/ztree/css/metroStyle/metroStyle.css">
<!--main-->
<link rel="stylesheet" href="/platformFramework/statics/css/main.css">
<link rel="stylesheet" href="/platformFramework/statics/css/iview.css">
<link rel="stylesheet" href="/platformFramework/statics/css/style.css">
<!--treegrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/treegrid/jquery.treegrid.css">
<!--富文本-->
<link rel="stylesheet" href='/platformFramework/statics/plugins/froala_editor/css/froala_editor.min.css'/>

<!--jquery-->
<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<!--layer-->
<script src="/platformFramework/statics/plugins/layer/layer.js"></script>
<!--bootstrap-->
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<!--vue-->
<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/iview.min.js"></script>
<!--jqgrid-->
<script src="/platformFramework/statics/plugins/jqgrid/grid.locale-cn.js"></script>
<script src="/platformFramework/statics/plugins/jqgrid/jquery.jqGrid.min.js"></script>
<!--ztree-->
<script src="/platformFramework/statics/plugins/ztree/jquery.ztree.all.min.js"></script>
<!--treegrid-->
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.extension.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.min.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.bootstrap3.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/tree.table.js"></script>

<!--simplemde富文本-->
<script src='/platformFramework/statics/plugins/froala_editor/js/froala_editor.min.js'></script>
<!--[if lt IE 9]>
<script src="/platformFramework/statics/plugins/froala_editor/js/froala_editor_ie8.min.js"></script>
<![endif]-->
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/tables.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/lists.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/colors.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/media_manager.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_family.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_size.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/block_styles.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/video.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/langs/zh_cn.js"></script>

<script src="/platformFramework/statics/libs/jquery-extend.js"></script>
<script src="/platformFramework/js/common.js"></script>


<!--exce-->
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jexcel.css">
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jsuites.css">
<link rel="stylesheet" href="/platformFramework/js/v4/jquery-clockpicker.min.css">
<link rel="stylesheet" href="/platformFramework/js/v4/happy-scroll.css">
<!--<link rel="stylesheet" href="/platformFramework/statics/css/v8/jspreadsheet.css">-->
<script src="/platformFramework/js/v4/jexcel.js"></script>
<script src="/platformFramework/js/v4/jsuites.js"></script>
<script src="/platformFramework/js/v4/jquery-clockpicker.min.js"></script>
<script src="/platformFramework/js/v4/happy-scroll.min.js"></script>
<script src="/platformFramework/js/v4/jquery.cookie.min.js"></script>
<!--<script src="/platformFramework/js/v8/jspreadsheet.js"></script>-->	<style type="text/css">
		.addDiv .ivu-form-item-content{ margin:0 }
		.addColumnBox{clear:both;width:100%;}
		.commonCloBox{width: 240px;float:left}
		.mylabel{
			width: 60px;
			line-height: 32px;
			font-size: 12px;
			float:left;
		}
		#container {
			overflow: hidden;
		}

		#left {
			float: left;
			width: 400px;
			height: 95vh;
			overflow: auto;

		}

		#left_top {
			width: 350px;
			height:500px;
			overflow: auto;
		}

		#left_bottom {
			width: 350px;
		}

		#content {
			margin-left: 400px;
			margin-bottom: -99999px;
			padding-bottom: 99999px;
		}
		#content {
			min-height: 250px;
			height: auto !important;
		}
		.commonCloBoxSma{width: 100px;float:left}

		.sendCloBox{
			width: 125px;
			float: left;
			margin-right: 10px;}
		.left_top_table{
			height:380px;
			overflow: scroll;
		}

		.activeTr{
			background-color:#ddd
		}

		#pjCoinsTb tr{
			cursor:pointer;
		}

		tr:hover{background-color: #F5F5F5}
		.autocomplete-suggestions { border: 1px solid #999; background: #FFF; overflow: auto; }
		.autocomplete-suggestion { padding: 2px 5px; white-space: nowrap; overflow: hidden; font-size: 16px;}
		.autocomplete-selected { background: lightgray; }
		.autocomplete-suggestions strong { font-weight: normal; color: #3399FF; }
		.autocomplete-group { padding: 2px 5px; }
		.autocomplete-group strong { display: block; border-bottom: 1px solid #000; }

		.information-sub{
			color: #1ab394;
			font-size: 18px;
		}
		#thead.visible {visibility:visible}
		#thead.invisible {visibility:hidden}
		#thead.visible1 {display:block}
		#thead.invisible1 {display:none}

	</style>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="!isPrintModel">
		<div v-show="addSendform">
			<p slot="title">{{title}}</p>
			<i-form ref="formValidate" :model="pjOSendform" :rules="ruleValidate" :label-width="80">
				<div  v-show="isShowSendForm">
					<div  class="addColumnBox">
						<Form-item label="联系电话" prop="rphone" class="commonCloBox">
							<i-input v-model="pjOSendform.rphone" placeholder="">
							</i-input>
						</Form-item>
						<Form-item label="加急" prop="urgenttype" class="commonCloBox">
							<Radio-group v-model="pjOSendform.urgenttype">
								<Radio label="0">
									<span>普通</span>
								</Radio>
								<Radio label="1">
									<span>加急</span>
								</Radio>
							</Radio-group>
						</Form-item>
					</div>
					<div  class="addColumnBox">
						<Form-item label="用户名称" prop="rname" class="commonCloBox">
							<i-input v-model="pjOSendform.rname" placeholder="">
							</i-input>
						</Form-item>
						<Form-item label="保价" prop="safemoney" class="commonCloBox">
							<i-input v-model="pjOSendform.safemoney" type="number">
							</i-input>
						</Form-item>
					</div>
					<div  class="addColumnBox">
						<Form-item label="网名" prop="nickname" class="commonCloBox">
							<i-input v-model="pjOSendform.nickname" v-bind:readonly="isReadOnly" placeholder="网名">
							</i-input>
						</Form-item>
						<Form-item label="收货方式" prop="rtype"  class="commonCloBox">
							<Radio-group v-model="pjOSendform.rtype">
								<Radio label="1">
									<span>上门自提</span>
								</Radio>
								<Radio label="2">
									<span>快递邮寄</span>
								</Radio>
							</Radio-group>
						</Form-item>
<!--						<Form-item prop="postfee" class="commonCloBox" v-show="pjOSendform.rtype == 2" >-->
<!--							<i-input v-model="pjOSendform.postfee" type="number" placeholder="待付快递费">-->
<!--							</i-input>-->
<!--						</Form-item>-->
					</div>
					<div  class="addColumnBox">
						<Form-item label="添加收评" prop="stype"  class="commonCloBox">
							<Radio-group v-model="pjOSendform.stype">
								<Radio label="0">
									<span>是</span>
								</Radio>
								<Radio label="1">
									<span>否</span>
								</Radio>
							</Radio-group>
						</Form-item>
						<Form-item label="收货地址" prop="addr" style="width: 600px;float:left">
							<i-input v-model="pjOSendform.addr" placeholder="地址">
							</i-input>
						</Form-item>
					</div>
					<div  class="addColumnBox">
						<Form-item label="内部备注" prop="backweight" class="commonCloBox">
							<i-input v-model="pjOSendform.backweight" placeholder="">
							</i-input>
						</Form-item>
						<Form-item label="用户备注" prop="description" class="commonCloBox">
							<i-input v-model="pjOSendform.description"  >
							</i-input>
						</Form-item>
					</div>
					<div  class="addColumnBox">
						<Form-item label="收评号" prop="accnum" class="commonCloBox">
							<i-input v-model="pjOSendform.accnum" placeholder="">
							</i-input>
						</Form-item>
					</div>
				</div>


				<!-- 送评项目列表 -->
				<!-- 古钱币 02 -->
				<div style="width: 100%; height: 1px; display: inline-block">
					<h3 style="color: red;font-size: 20px;">（只能添加一个类型的钱币，用户昵称备注钱币类型）<span style="color: red;font-size: 25px;">录入过程请使用谷歌64位(Chrome)浏览器</span></h3>
				</div>
				<div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px">古钱币</div>

				<table class="ui-jqgrid-htable ui-common-table table table-bordered">
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 10%;">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 10%;">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%;">年代</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%;">材质</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%;">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%;">数量</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%;">标准价</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%;">国际价</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%;">费用</th>
					<th class="ui-th-column ui-th-ltr" >折扣</th>
					<th class="ui-th-column ui-th-ltr" >盒子费</th>
					<th class="ui-th-column ui-th-ltr" >加急</th>
					<!--					<th class="ui-th-column ui-th-ltr" >其他费</th>-->
					<th class="ui-th-column ui-th-ltr" >等级</th>
					<th class="ui-th-column ui-th-ltr" >版别</th>
					<th class="ui-th-column ui-th-ltr" >对内备注</th>
					<!--					<th class="ui-th-column ui-th-ltr" >对外备注</th>-->
					<th class="ui-th-column ui-th-ltr" >操作</th>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects2" v-show="projects2.length > 0">

						<!--	对香港的账号进行纲目限制	-->
						<td v-show="!outline">
							<i-input v-model="item.name" placeholder="古錢幣名稱1" @click='getGQIndex(index)'/></i-input>
						</td>

						<td v-show="outline">
							<input v-model="item.name" class="ivu-input" placeholder="古钱币名称1" name="GQautoName" type="text"  @click='getGQIndex(index)'/>
						</td>

						<td><i-input v-model="item.name2" placeholder="名称2" name="GQautoName2"></i-input></td>

						<td>
							<input v-model="item.niandai" class="ivu-input" placeholder="年代" name="GQniandai"  type="text" />
						</td>
						<td>
							<i-select v-model="item.ctCaizhi" filterable label-in-value style="width: 100px;">
								<i-option value="铜">铜</i-option>
								<i-option value="银">银</i-option>
								<i-option value="金">金</i-option>
							</i-select>
						</td>
						<td>
							<i-select v-model="item.boxtype" filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id":key="itemb.id">{{itemb.name}}</i-option>
							</i-select>
						</td>
						<td><i-input v-model="item.amount" placeholder="数量"></i-input></td>

						<td>
							<i-input v-model="item.bzfee" placeholder="标准价" id="bz" name="gqbBZJ" ></i-input>
						</td>
						<td>
							<i-input v-model="item.gjfee" placeholder="国际价"  id="gj" name="gqbGJJ"></i-input>
						</td>

						<td><i-input v-model="item.finalfee" :value="getFee(item, index)" type="number" placeholder="费用" readonly="readonly"></i-input></td>
						<td><i-input v-model="item.zk" placeholder="折扣"></i-input></td>
						<td><i-input v-model="item.boxfee"  placeholder="盒子费"></i-input></td>
						<td><i-input v-model="item.jiajifee" placeholder="加急费"></i-input></td>
						<!--						<td><i-input v-model="item.qtfee" type="number" placeholder="其他费"></i-input></td>-->
						<td>
							<input v-model="item.rank" class="ivu-input" placeholder="等级" name="GQgrade"  type="text" />
						</td>
						<td><i-input v-model="item.edition" placeholder="版别"  name="GQautoBanbie"></i-input></td>
						<td><i-input v-model="item.backweight" placeholder="对内备注"></i-input></td>
						<!--						<td><i-input v-model="item.description" placeholder="对外备注"></i-input></td>-->
						<td><a href="javascript:;" @click="deleteProject(item, index,'2')">删除</a></td>
					</tr>
					</tbody>
				</table>
				<div style="text-align:center;width:100%" class="addDiv">
					<div style="text-align:center;width:100%">
						<button type="button" class="ivu-btn ivu-btn-ghost" @click="addProject('2')" name="addBtn"><span>添加</span></button>
					</div>
				</div>
				<hr/>
				<!-- 机制币 03 -->
				<div style="width: 100%; height: 1px; display: inline-block"></div>
				<div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px">机制币</div>

				<table class="ui-jqgrid-htable ui-common-table table table-bordered">
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 9%">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 9%">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">年代</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">面值</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">材质</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">尺寸</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">重量</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">数量</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">标准价</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">国际价</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">费用</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">折扣</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">盒子费</th>
					<!--					<th class="ui-th-column ui-th-ltr" style="width: 6%">加急</th>-->
					<!--					<th class="ui-th-column ui-th-ltr" >其他费</th>-->
					<th class="ui-th-column ui-th-ltr" style="width: 6%">版别</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">对内备注</th>
					<!--					<th class="ui-th-column ui-th-ltr" >对外备注</th>-->
					<th class="ui-th-column ui-th-ltr" style="width: 6%">操作</th>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects3" v-show="projects3.length > 0">

						<td v-show="!outline">
							<i-input v-model="item.name" placeholder="機制幣名稱1" @click='getJZIndex(index)'></i-input>
						</td>

						<td  v-show="outline">
							<input v-model="item.name" class="ivu-input" placeholder="机制币名称1" name="JZautoName"  type="text"  @click='getJZIndex(index)'/>
						</td>

						<td><i-input v-model="item.name2" placeholder="名称2" name="JZautoName2" ></i-input></td>
						<td>
							<input v-model="item.niandai" class="ivu-input" placeholder="年代" name="JZniandai"  type="text" />
						</td>
						<td>
							<input v-model="item.mianzhi" class="ivu-input" placeholder="面值" name="JZmianzhi"  type="text" />
						</td>
						<td>
							<i-select v-model="item.ctCaizhi" filterable label-in-value style="width: 100px;">
								<i-option value="铜">铜</i-option>
								<i-option value="银">银</i-option>
								<i-option value="金">金</i-option>
							</i-select>
						</td>
						<td>
							<input v-model="item.size" class="ivu-input" placeholder="尺寸" name="JZsize"  type="text" />
						</td>
						<td>
							<input v-model="item.weight" class="ivu-input" placeholder="重量" name="JZweight"  type="text" />
						</td>
						<td>
							<i-select v-model="item.boxtype" filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>
						</td>
						<td><i-input v-model="item.amount" placeholder="数量"></i-input></td>
						<td><i-input v-model="item.bzfee"  placeholder="标准价" name="jzbBZJ"></i-input></td>
						<td><i-input v-model="item.gjfee" placeholder="国际价" name="jzbGJJ"></i-input></td>
						<td><i-input v-model="item.finalfee" :value="getFee(item, index)" type="number" placeholder="费用" readonly="readonly"></i-input></td>
						<td><i-input v-model="item.zk" placeholder="折扣"></i-input></td>
						<td><i-input v-model="item.boxfee" placeholder="盒子费"></i-input></td>
						<!--						<td><i-input v-model="item.jiajifee" type="number" placeholder="加急费"></i-input></td>-->
						<!--						<td><i-input v-model="item.qtfee" type="number" placeholder="其他费"></i-input></td>-->
						<td><i-input v-model="item.edition" placeholder="版别" name="JZbanbie"></i-input></td>
						<td><i-input v-model="item.backweight" placeholder="对内备注"></i-input></td>
						<!--						<td><i-input v-model="item.description" placeholder="对外备注"></i-input></td>-->
						<td><a href="javascript:;" @click="deleteProject(item, index,'3')">删除</a></td>
					</tr>
					</tbody>
				</table>
				<div style="text-align:center;width:100%" class="addDiv">
					<div style="text-align:center;width:100%">
						<button type="button" class="ivu-btn ivu-btn-ghost" @click="addProject('3')" name="addBtn"><span>添加</span></button>
					</div>
				</div>
				<hr/>

				<!-- 银锭 04 -->
				<div style="width: 100%; height: 1px; display: inline-block"></div>
				<div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px">银锭</div>

				<table class="ui-jqgrid-htable ui-common-table table table-bordered">
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 10%">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 10%">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年代</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年份</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">地区</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">税种</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">面值</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">材质</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">数量</th>
					<th class="ui-th-column ui-th-ltr" >标准价</th>
					<th class="ui-th-column ui-th-ltr" >国际价</th>
					<th class="ui-th-column ui-th-ltr" >费用</th>
					<th class="ui-th-column ui-th-ltr" >折扣</th>
					<th class="ui-th-column ui-th-ltr" >盒子费</th>
					<th class="ui-th-column ui-th-ltr" >加急</th>
					<!--					<th class="ui-th-column ui-th-ltr" >其他费</th>-->
					<th class="ui-th-column ui-th-ltr" >版别</th>
					<th class="ui-th-column ui-th-ltr" >对内备注</th>
					<!--					<th class="ui-th-column ui-th-ltr" >对外备注</th>-->
					<th class="ui-th-column ui-th-ltr" >操作</th>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects4" v-show="projects4.length > 0">

						<td v-show="!outline">
							<i-input v-model="item.name" placeholder="銀錠名稱1" @click='getYDIndex(index)'></i-input>
						</td>

						<td  v-show="outline">
							<input v-model="item.name" class="ivu-input" placeholder="银锭名称1" name="YDautoName"  type="text"  @click='getYDIndex(index)'/>
						</td>

						<td><i-input v-model="item.name2" placeholder="名称2"  name="YDautoName2"></i-input></td>
						<td>
							<input v-model="item.niandai" class="ivu-input" placeholder="年代" name="YDniandai"  type="text" />
						</td>
						<td><i-input v-model="item.year" placeholder="年份"></i-input></td>
						<td><i-input v-model="item.addr" placeholder="地区"></i-input></td>
						<td><i-input v-model="item.shuizhong" placeholder="税种"></i-input></td>
						<td>
							<input v-model="item.mianzhi" class="ivu-input" placeholder="面值" name="YDmianzhi"  type="text" />
						</td>
						<td>
							<i-select v-model="item.ctCaizhi" filterable label-in-value style="width: 100px;">
								<i-option value="银">银</i-option>
								<i-option value="金">金</i-option>
								<i-option value="铜镀银">铜镀银</i-option>
							</i-select>
						</td>
						<td>
							<i-select v-model="item.boxtype" filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>
						</td>
						<td><i-input v-model="item.amount" placeholder="数量"></i-input></td>
						<td><i-input v-model="item.bzfee" placeholder="标准价" name="ydBZJ"></i-input></td>
						<td><i-input v-model="item.gjfee" placeholder="国际价" name="ydGJJ"></i-input></td>
						<td><i-input v-model="item.finalfee" :value="getFee(item, index)" type="number" placeholder="费用" readonly="readonly"></i-input></td>
						<td><i-input v-model="item.zk" placeholder="折扣"></i-input></td>
						<td><i-input v-model="item.boxfee" placeholder="盒子费"></i-input></td>
						<td><i-input v-model="item.jiajifee" placeholder="加急费"></i-input></td>
						<!--						<td><i-input v-model="item.qtfee" type="number" placeholder="其他费"></i-input></td>-->
						<td><i-input v-model="item.edition" placeholder="版别" name="YDbanbie"></i-input></td>
						<td><i-input v-model="item.backweight" placeholder="对内备注"></i-input></td>
						<!--						<td><i-input v-model="item.description" placeholder="对外备注"></i-input></td>-->
						<td><a href="javascript:;" @click="deleteProject(item, index,'4')">删除</a></td>
					</tr>
					</tbody>
				</table>
				<div style="text-align:center;width:100%" class="addDiv">
					<div style="text-align:center;width:100%">
						<button type="button" class="ivu-btn ivu-btn-ghost" @click="addProject('4')" name="addBtn"><span>添加</span></button>
					</div>
				</div>
				<hr/>

				<!-- 纸币01 -->
				<div style="width: 100%; height: 1px; display: inline-block"></div>
				<div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px">纸币</div>

				<table class="ui-jqgrid-htable ui-common-table table table-bordered" >
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 10%">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年代</th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%">目录</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">银行名称</th>
<!--					<th class="ui-th-column ui-th-ltr" style="width: 6%">纸币编码</th>-->
					<th class="ui-th-column ui-th-ltr" style="width: 6%">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">数量</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">标准价</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">国际价</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">费用</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">折扣</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">盒子费</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">加急</th>
					<!--					<th class="ui-th-column ui-th-ltr" >其他费</th>-->
					<th class="ui-th-column ui-th-ltr" style="width: 6%">版别</th>
					<th class="ui-th-column ui-th-ltr" style="width: 8%;">特殊标签</th>
					<th class="ui-th-column ui-th-ltr" style="width: 8%;">归属公司</th>
					<!--					<th class="ui-th-column ui-th-ltr" >对外备注</th>-->
					<th class="ui-th-column ui-th-ltr" >操作</th>
					</thead>
					<thead id="thead" class="invisible1" >
					<th class="ui-th-column ui-th-ltr" style="width: 10%"><input type="text" class="ivu-input" placeholder="纸币名称1" v-model="projectsAll.name"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="名称2" v-model="projectsAll.name2"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%"><input  type="text" class="ivu-input" placeholder="年代" v-model="projectsAll.niandai"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%"><input  type="text" class="ivu-input" placeholder="目录" v-model="projectsAll.catalog"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%"><input  type="text" class="ivu-input" placeholder="银行名称" v-model="projectsAll.bank"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%"><select class="ivu-btn" v-model="projectsAll.boxtype">
						<option value="0">请选择</option>
						<option value="1">密封盒</option>
						<option value="2">开放式</option>
						<option value="3">证书</option>
					</select></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="数量" v-model="projectsAll.amount"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="标准价" v-model="projectsAll.bzfee"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="国际价" v-model="projectsAll.gjfee"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="费用" v-model="projectsAll.finalfee"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="折扣" v-model="projectsAll.zk"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="盒子费" v-model="projectsAll.boxfee"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%"><input  type="text" class="ivu-input" placeholder="加急" v-model="projectsAll.jiajifee"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 6%"><input  type="text" class="ivu-input" placeholder="版别" v-model="projectsAll.edition"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 8%;"><input type="text" class="ivu-input" placeholder="特殊标签" v-model="projectsAll.live"></th>
					<th class="ui-th-column ui-th-ltr" style="width: 8%;">
					<select class="ivu-btn" v-model="projectsAll.classid">
						<option value="0">请选择</option>
						<option value="1">中乾评级</option>
						<option value="2">宝鑫评级</option>
					</select>
					</th>
					<th class="ui-th-column ui-th-ltr" ><div style="text-align:center;width:100%" class="addDiv">
						<div style="text-align:center;width:100%">
							<button type="button" class="ivu-btn ivu-btn-ghost"  @click="addProjectAll()" > <span>批量修改</span></button>
						</div>
					</div></th>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects1" v-show="projects1.length > 0">

						<td v-show="!outline">
							<i-input v-model="item.name" placeholder="紙幣名稱1" @click='getZBIndex(index)'></i-input>
						</td>

						<td  v-show="outline">
							<input v-model="item.name" class="ivu-input" placeholder="纸币名称1" name="ZBautoName"  type="text"  @click='getZBIndex(index)'/>
						</td>
						<td><i-input v-model="item.name2" placeholder="名称2"></i-input></td>
						<td>
							<input v-model="item.niandai" class="ivu-input" placeholder="年代" name="ZBniandai"  type="text" />
						</td>
						<td>
							<input v-model="item.catalog" class="ivu-input" placeholder="目录" name="ZBmulu"  type="text" />
						</td>
						<td><i-input v-model="item.bank" placeholder="银行名称"></i-input></td>
<!--						<td><i-input v-model="item.zbnum" placeholder="纸币编码"></i-input></td>-->
						<td>
							<!--<i-select v-model="item.boxtype" filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>-->
							<select v-model="item.boxtype" class="ivu-btn">
								<option v-for="itemb in packTypeList" :value="itemb.id">
									{{itemb.name}}
								</option>
							</select>



						</td>
						<td><i-input v-model="item.amount" placeholder="数量"></i-input></td>
						<td><i-input v-model="item.bzfee"  placeholder="标准价" name="zbBZJ"></i-input></td>
						<td><i-input v-model="item.gjfee"  placeholder="国际价" name="zbGZJ"></i-input></td>
						<td><i-input v-model="item.finalfee" :value="getFee(item, index)" type="number" placeholder="费用" readonly="readonly"></i-input></td>
						<td><i-input v-model="item.zk" placeholder="折扣"></i-input></td>
						<td><i-input v-model="item.boxfee" placeholder="盒子费"></i-input></td>
						<td><i-input v-model="item.jiajifee" placeholder="加急费"></i-input></td>
						<!--						<td><i-input v-model="item.qtfee" type="number" placeholder="其他费"></i-input></td>-->
						<td><i-input v-model="item.edition" placeholder="版别" name="ZBbanbie" ></i-input></td>
						<td><i-input v-model="item.live" placeholder="特殊标签"></i-input></td>
<!--						<td><i-input v-model="item.backweight" placeholder="对内备注"></i-input></td>-->
						<td>
							<!--<i-select v-model="item.classid" filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in companyList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>-->

							<select v-model="item.classid" class="ivu-btn">
								<option v-for="itemb in companyList" :value="itemb.id">
									{{itemb.name}}
								</option>
							</select>

						</td>
						<!--						<td><i-input v-model="item.description" placeholder="对外备注"></i-input></td>-->
						<td><a href="javascript:;" @click="deleteProject(item, index,'1')">删除</a></td>
					</tr>
					</tbody>
				</table>
				<div style="text-align:center;width:100%" class="addDiv">
					<div style="text-align:center;width:100%">
						<button type="button" class="ivu-btn ivu-btn-ghost"  @click="addProject('1')" name="addBtn"> <span>添加</span></button>
						<button type="button" class="ivu-btn ivu-btn-ghost"  @click="addProjectNumber('10')" name="addBtn"> <span>添加10行</span></button>
						<button type="button" class="ivu-btn ivu-btn-ghost"  @click="addProjectNumber('50')" name="addBtn"> <span>添加50行</span></button>
					</div>
				</div>
				<hr/>
				<i-form :label-width="80">
					<Form-item style="margin-top:40px">
						<i-button type="primary" @click="handleSubmitSms('formValidate')" v-show="(isShowSendForm)">
							<i class="fa fa-floppy-o" aria-hidden="true"></i>&nbsp;提交送评单
						</i-button>

						<i-button type="primary" @click="handleSubmit('formValidate')" v-show="(!isShowSendForm)">保存补录数据</i-button>

						<i-button type="warning" @click="exitDataMore" style="margin-left: 8px" v-show="(!isShowSendForm)">返回</i-button>
					</Form-item>
				</i-form>
			</i-form>
		</div>

		<!--		编辑送评单模块-->
		<div v-show="changePjCoin">
			<div id="container">
				<div id="left" class="aside">
					<div class="left_top">
						<i-form>
							<Form-item >
								<i-button type="ghost" style="margin-left: 8px" @click="prePrint">打印送评条码</i-button>
								<i-button type="ghost" @click="addDataMore('formValidate')" style="margin-left: 8px">补录</i-button>
								<i-button type="success" style="margin-left: 8px" @click="replacementSMS">补发短信</i-button>
								<span>共有{{pjCoins.length}}件</span>
							</Form-item>
						</i-form>
						<div  class="left_top_table">
							<table id="pjCoinsTb" class="ui-jqgrid-htable ui-common-table table table-bordered" >
								<tbody>
								<tr :id="('coin'+index)" v-for="(item, index) in pjCoins" :class="getTrClass(index)" @click="changePjCoinFun(item, index)">
									<!-- <td>{item.id}</td> -->
									<td style="width:20%">{{item.nummber}}</td>
									<td style="width:60%">{{item.name}}</td>
									<td style="width:20%">
										<span v-if="(item.pic != null && item.pic !='')">图</span><span style="color:red" v-else>图</span>,
										<span v-if="(item.size != null && item.weight != null)">文</span><span style="color:red" v-else>文</span>,
										<span v-for="sitem in sresList"><font v-if="item.result == sitem.id">{{sitem.name}}</font></span>
										,<span v-if="(item.scoreNum != null && item.scoreNum !='')">分</span><span style="color:red" v-else>分</span>
								</tr>
								</tbody>
							</table>
						</div>
					</div>
					<div class="left_bottom" style="margin-bottom:100px">
						<div class="addColumnBox">
							<label class="mylabel">时间</label>
							<input :value="transDate(pjOSendform.inupttime)" class="sendCloBox" readonly="readonly"/>
							<label class="mylabel">保价</label><input v-model="pjOSendform.safemoney" type="number" class="sendCloBox" />
						</div>
						<div class="addColumnBox">
							<label class="mylabel">送评人</label><input v-model="pjOSendform.rname"  class="sendCloBox"/>
							<label class="mylabel">快递费</label><input v-model="pjOSendform.postfee" type="number" class="sendCloBox" />
						</div>
						<div class="addColumnBox">
							<label class="mylabel">网名</label><input v-model="pjOSendform.nickname"  class="sendCloBox"/>
							<label class="mylabel">其他费用</label><input v-model="pjOSendform.sumFinalFee" type="number" class="sendCloBox" readonly="readonly"/>
						</div>
						<div class="addColumnBox">
							<label class="mylabel">电话</label><input v-model="pjOSendform.rphone"  class="sendCloBox"/>
							<label class="mylabel">总费用</label><input :value="Number(pjOSendform.safemoney)+Number(pjOSendform.postfee)+Number(pjOSendform.sumFinalFee)" type="number" readonly="readonly" class="sendCloBox" />
						</div>
						<div class="addColumnBox">
							<label class="mylabel">收评号</label><input v-model="pjOSendform.accnum"  class="sendCloBox"/>
						</div>
						<div class="addColumnBox">
							<label class="mylabel">财务核对</label>
							<Radio-group v-model="pjOSendform.ifyou">
								<Radio label="0">
									<span>否</span>
								</Radio>
								<Radio label="1">
									<span>已核对</span>
								</Radio>
							</Radio-group>
						</div>
						<div class="addColumnBox">
							<label class="mylabel">收货方式</label>
							<Radio-group v-model="pjOSendform.rtype">
								<Radio label="1">
									<span>上门自提</span>
								</Radio>
								<Radio label="2">
									<span>快递邮寄</span>
								</Radio>
							</Radio-group>
						</div>
						<div class="addColumnBox">
							<label class="mylabel">收货地址</label>
							<input v-model="pjOSendform.addr"  style="width: 350px;float:left" />
						</div>
					</div>
				</div>
				<div id="content" class="section" style="height:100%">
					<i-button type="primary"  @click="reloadPage()">返回</i-button>
					<i-button type="primary" @click="update">保存送评单</i-button>
					<iframe style="border: none; height: 90vh;width: 100%;" :src="frameLink"></iframe>
				</div>
			</div>
		</div>
	</div>

	<!--	打印白标-->
	<div v-show="isPrintModel">
		<div>
			<i-form>
				<Form-item>
					<i-button type="primary" @click="print()">打印</i-button>


					<i-button type="warning" @click="isPrintModel = false" style="margin-left: 8px">返回</i-button>
				</Form-item>
			</i-form>
		</div>
		<div id="ReportViewerBox"></div>

	</div>
</div>

<script src="/platformFramework/js/sys/addsendform.js?_1749996640321"></script>
<script src="/platformFramework/statics/libs/jquery.autocomplete.min.js?_1749996640321"></script>
<script src="/platformFramework/js/print/CreateControl.js?_1749996640321"></script>
<script src="/platformFramework/js/print/print-style.js?_1749996640321"></script>

</body>
</html>