<!DOCTYPE html>
<html>
<head>
	<title></title>
	<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<!--bootstrap-->
<link rel="stylesheet" href="/platformFramework/statics/css/bootstrap.min.css">
<link rel="stylesheet" href="/platformFramework/statics/css/font-awesome.min.css">
<!--jqgrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="/platformFramework/statics/plugins/ztree/css/metroStyle/metroStyle.css">
<!--main-->
<link rel="stylesheet" href="/platformFramework/statics/css/main.css">
<link rel="stylesheet" href="/platformFramework/statics/css/iview.css">
<link rel="stylesheet" href="/platformFramework/statics/css/style.css">
<!--treegrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/treegrid/jquery.treegrid.css">
<!--富文本-->
<link rel="stylesheet" href='/platformFramework/statics/plugins/froala_editor/css/froala_editor.min.css'/>

<!--jquery-->
<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<!--layer-->
<script src="/platformFramework/statics/plugins/layer/layer.js"></script>
<!--bootstrap-->
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<!--vue-->
<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/iview.min.js"></script>
<!--jqgrid-->
<script src="/platformFramework/statics/plugins/jqgrid/grid.locale-cn.js"></script>
<script src="/platformFramework/statics/plugins/jqgrid/jquery.jqGrid.min.js"></script>
<!--ztree-->
<script src="/platformFramework/statics/plugins/ztree/jquery.ztree.all.min.js"></script>
<!--treegrid-->
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.extension.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.min.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.bootstrap3.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/tree.table.js"></script>

<!--simplemde富文本-->
<script src='/platformFramework/statics/plugins/froala_editor/js/froala_editor.min.js'></script>
<!--[if lt IE 9]>
<script src="/platformFramework/statics/plugins/froala_editor/js/froala_editor_ie8.min.js"></script>
<![endif]-->
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/tables.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/lists.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/colors.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/media_manager.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_family.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_size.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/block_styles.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/video.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/langs/zh_cn.js"></script>

<script src="/platformFramework/statics/libs/jquery-extend.js"></script>
<script src="/platformFramework/js/common.js"></script>


<!--exce-->
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jexcel.css">
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jsuites.css">
<link rel="stylesheet" href="/platformFramework/js/v4/jquery-clockpicker.min.css">
<link rel="stylesheet" href="/platformFramework/js/v4/happy-scroll.css">
<!--<link rel="stylesheet" href="/platformFramework/statics/css/v8/jspreadsheet.css">-->
<script src="/platformFramework/js/v4/jexcel.js"></script>
<script src="/platformFramework/js/v4/jsuites.js"></script>
<script src="/platformFramework/js/v4/jquery-clockpicker.min.js"></script>
<script src="/platformFramework/js/v4/happy-scroll.min.js"></script>
<script src="/platformFramework/js/v4/jquery.cookie.min.js"></script>
<!--<script src="/platformFramework/js/v8/jspreadsheet.js"></script>-->	<style>
		.upload-list {
			display: inline-block;
			width: 60px;
			height: 60px;
			text-align: center;
			line-height: 60px;
			border: 1px solid transparent;
			border-radius: 4px;
			overflow: hidden;
			background: #fff;
			position: relative;
			box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
			margin-right: 4px;
		}

		.upload-list img {
			width: 100%;
			height: 100%;
		}

		.upload-list-cover {
			display: none;
			position: absolute;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			background: rgba(0, 0, 0, .6);
		}

		.upload-list:hover .upload-list-cover {
			display: block;
		}

		.upload-list-cover i {
			color: #fff;
			font-size: 20px;
			cursor: pointer;
			margin: 0 2px;
		}
		.ivu-tabs .ivu-tabs-tabpane {
			-ms-flex-negative: 0;
			flex-shrink: 0;
			width: 100%;
			transition: opacity .3s;
			opacity: 1;
			/*min-height: 1000px;*/
			min-height: 700px;
		}
	</style>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
		<Row :gutter="16">
			<div class="search-group">
				<i-col span="3">
					<textarea id="nummbers" style="display:none" v-model="nummbers" placeholder="条码（换行隔开）" style="width:200px" rows="2" class="ivu-input"></textarea>
				</i-col>
				<i-col span="4">
					<i-input  v-model="q.nummber" @on-enter="sm" placeholder="条形码">
					</i-input>
				</i-col>
				<Radio-group v-model="q.addType">
					<Radio label="0">
						<span>无</span>
					</Radio>
					<Radio label="1">
						<span>追加该送评单的所有订单</span>
					</Radio>
					<Radio label="2">
						<span>追加该鉴定单的所有订单</span>
					</Radio>
				</Radio-group>
				<i-button @click="sm">查询</i-button>
				<i-button @click="reloadSearch">复制</i-button>
				<i-button @click="saveAll">保存</i-button>
				<span style="color: red;font-size: 25px;">(录入过程请使用谷歌64位(Chrome)浏览器)</span>
				<!--                 <i-button @click="reloadSearch">重置</i-button> -->
			</div>
			<!--             <div class="buttons-group">
                                                        <i-button type="warning" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</i-button>
                                                    </div> -->
		</Row>
		<Tabs @on-click="setActiveGrid">
			<Tab-Pane label="古钱币" name="#jqGrid1">
				<table class="ui-jqgrid-htable ui-common-table table table-bordered">
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">订单号</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">版别</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年代</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年份</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">等级</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">尺寸</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">重量</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">材质</th>
<!--					<th class="ui-th-column ui-th-ltr" style="width: 10%">品相分数</th>-->
<!--					<th class="ui-th-column ui-th-ltr" >缺陷代码</th>-->
<!--					<th class="ui-th-column ui-th-ltr" style="width: 10%">真伪</th>-->
					<!-- 						<th class="ui-th-column ui-th-ltr" >标准价</th>
                                            <th class="ui-th-column ui-th-ltr" >国际价</th>
                                            <th class="ui-th-column ui-th-ltr" >费用</th>
                                            <th class="ui-th-column ui-th-ltr" >折扣</th> -->
					<!-- 						<th class="ui-th-column ui-th-ltr" >盒子费</th>
                                            <th class="ui-th-column ui-th-ltr" >加急</th>
                                            <th class="ui-th-column ui-th-ltr" >其他费</th> -->
					<th class="ui-th-column ui-th-ltr">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" >对内备注</th>
					<th class="ui-th-column ui-th-ltr" >对外备注</th>
					<th class="ui-th-column ui-th-ltr" >操作</th>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects1" v-show="projects1.length > 0">
						<td>{{item.nummber}}</td>
						<td><i-input v-model="item.name" @on-change="getDepts(item,index,'name',item.name)" placeholder="名称1"/></td>
						<td><i-input v-model="item.name2" @on-change="getDepts(item,index,'name2',item.name2)"  placeholder="名称2"/></td>
						<td><i-input v-model="item.edition" @on-change="getDepts(item,index,'edition',item.edition)"  placeholder="版别"/></td>
						<td><i-input v-model="item.niandai" @on-change="getDepts(item,index,'niandai',item.niandai)" placeholder="年代"/></td>
						<td><i-input v-model="item.year" @on-change="getDepts(item,index,'year',item.year)" placeholder="年份"/></td>
						<td><i-input v-model="item.rank" @on-change="getDepts(item,index,'rank',item.rank)" placeholder="等级"/></td>
						<td><i-input v-model="item.size" @on-change="getDepts(item,index,'size',item.size)" placeholder="尺寸"/></td>
						<td><i-input v-model="item.weight" placeholder="重量"  @on-change="getDepts(item,index,'weight',item.weight)"/></td>
						<td><i-input v-model="item.ctCaizhi" @on-change="getDepts(item,index,'ctCaizhi',item.ctCaizhi)" placeholder="材质"/></td>
<!--						<td>-->
<!--							<i-select v-model="item.scoreshow" @on-change="getDept(item,index)" filterable label-in-value>-->
<!--								<i-option v-for="statusItem in gqbScoreList" :value="statusItem.name"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
<!--						<td>-->
<!--							<i-select v-model="item.qxshow"  @on-change="getDept(item,index)" filterable label-in-value>-->
<!--								<i-option v-for="statusItem in gqbQXList" :value="statusItem.name"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
<!--						<td>-->
<!--							<i-select v-model="item.result"  @on-change="getDepts(item,index,'result',item.result)" filterable label-in-value>-->
<!--								<i-option v-for="statusItem in statusList" :value="statusItem.id"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
						<!-- 							<td><i-input v-model="item.bzfee" type="number" placeholder="标准价"/></td>
                                                    <td><i-input v-model="item.gjfee" type="number" placeholder="国际价"/></td>
                                                    <td><i-input v-model="item.finalfee" :value="getFee(item)" type="number" placeholder="费用" readonly="readonly"/></td>
                                                    <td><i-input v-model="item.zk"  type="number" placeholder="折扣"/></td> -->
						<!-- 							<td><i-input v-model="item.boxfee" type="number" placeholder="盒子费"/></td>
                                                    <td><i-input v-model="item.jiajifee" type="number" placeholder="加急费"/></td>
                                                    <td><i-input v-model="item.qtfee" type="number" placeholder="其他费"/></td> -->
						<td>
							<i-select v-model="item.boxtype" @on-change="getDepts(item,index,'boxtype',item.boxtype)"  filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>
						</td>
						<td><i-input v-model="item.backweight"  @on-change="getDepts(item,index,'backweight',item.backweight)" placeholder="对内备注"/></td>
						<td><i-input v-model="item.description" @on-change="getDepts(item,index,'description',item.description)" placeholder="对外备注"/></td>
						<td><a href="javascript:;" @click="deleteProject(item, index,'1')" v-if="index!=0">删除</a></td>
					</tr>
					</tbody>
				</table>
			</Tab-Pane>
			<Tab-Pane label="银锭" name="#jqGrid2"  >
				<table class="ui-jqgrid-htable ui-common-table table table-bordered">
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">订单号</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">版别</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年代</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年份</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">地区</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">尺寸</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">重量</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">材质</th>
<!--					<th class="ui-th-column ui-th-ltr" style="width: 10%">品相分数</th>-->
<!--					<th class="ui-th-column ui-th-ltr" >缺陷代码</th>-->
<!--					<th class="ui-th-column ui-th-ltr" style="width: 10%">真伪</th>-->
					<!-- 						<th class="ui-th-column ui-th-ltr" >标准价</th>
                                            <th class="ui-th-column ui-th-ltr" >国际价</th>
                                            <th class="ui-th-column ui-th-ltr" >费用</th>
                                            <th class="ui-th-column ui-th-ltr" >折扣</th> -->
					<!-- 						<th class="ui-th-column ui-th-ltr" >盒子费</th>
                                            <th class="ui-th-column ui-th-ltr" >加急</th>
                                            <th class="ui-th-column ui-th-ltr" >其他费</th> -->
					<th class="ui-th-column ui-th-ltr">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" >对内备注</th>
					<th class="ui-th-column ui-th-ltr" >对外备注</th>
					<th class="ui-th-column ui-th-ltr" >操作</th>
					</thead>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects2" v-show="projects2.length > 0">
						<td>{{item.nummber}}</td>
						<td><i-input v-model="item.name" @on-change="getDepts(item,index,'name',item.name)"  placeholder="名称1"/></td>
						<td><i-input v-model="item.name2" @on-change="getDepts(item,index,'name2',item.name2)" placeholder="名称2"/></td>
						<td><i-input v-model="item.edition" @on-change="getDepts(item,index,'edition',item.edition)"  placeholder="版别"/></td>
						<td><i-input v-model="item.niandai" @on-change="getDepts(item,index,'niandai',item.niandai)" placeholder="年代"/></td>
						<td><i-input v-model="item.year" @on-change="getDepts(item,index,'year',item.year)" placeholder="年份"/></td>
						<td><i-input v-model="item.addr" @on-change="getDepts(item,index,'addr',item.addr)"  placeholder="地区"/></td>
						<td><i-input v-model="item.size" @on-change="getDepts(item,index,'size',item.size)" placeholder="尺寸"/></td>
						<td><i-input v-model="item.weight" placeholder="重量"  @on-change="getDepts(item,index,'weight',item.weight)"/></td>
						<td><i-input v-model="item.ctCaizhi" @on-change="getDepts(item,index,'ctCaizhi',item.ctCaizhi)" placeholder="材质"/></td>
<!--						<td>-->
<!--							<i-select v-model="item.scoreshow" @on-change="getDept(item,index)" filterable label-in-value>-->
<!--								<i-option v-for="statusItem in ydScoreList" :value="statusItem.name"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
<!--						<td>-->
<!--							<i-select v-model="item.qxshow" @on-change="getDept(item,index)"  filterable label-in-value>-->
<!--								<i-option v-for="statusItem in ydQXList" :value="statusItem.name"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
<!--						<td>-->
<!--							<i-select v-model="item.result" @on-change="getDepts(item,index,'result',item.result)" filterable label-in-value>-->
<!--								<i-option v-for="statusItem in statusList" :value="statusItem.id"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
						<!-- 							<td><i-input v-model="item.bzfee" type="number" placeholder="标准价"/></td>
                                                    <td><i-input v-model="item.gjfee" type="number" placeholder="国际价"/></td>
                                                    <td><i-input v-model="item.finalfee" :value="getFee(item)" type="number" placeholder="费用" readonly="readonly"/></td>
                                                    <td><i-input v-model="item.zk"  type="number" placeholder="折扣"/></td> -->
						<!-- 							<td><i-input v-model="item.boxfee" type="number" placeholder="盒子费"/></td>
                                                    <td><i-input v-model="item.jiajifee" type="number" placeholder="加急费"/></td>
                                                    <td><i-input v-model="item.qtfee" type="number" placeholder="其他费"/></td> -->
						<td>
							<i-select v-model="item.boxtype" @on-change="getDepts(item,index,'boxtype',item.boxtype)"  filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>
						</td>
						<td><i-input v-model="item.backweight"  @on-change="getDepts(item,index,'backweight',item.backweight)" placeholder="对内备注"/></td>
						<td><i-input v-model="item.description" @on-change="getDepts(item,index,'description',item.description)" placeholder="对外备注"/></td>
						<td><a href="javascript:;" @click="deleteProject(item, index,'2')"  v-if="index!=0">删除</a></td>
					</tr>
					</tbody>
				</table>
			</Tab-Pane>
			<Tab-Pane label="机制币" name="#jqGrid3">
				<table class="ui-jqgrid-htable ui-common-table table table-bordered">
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">订单号</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">版别</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年代</th>
					<!-- <th class="ui-th-column ui-th-ltr" style="width: 4%">年份</th> -->
					<th class="ui-th-column ui-th-ltr" style="width: 5%">面值</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">尺寸</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">重量</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">材质</th>
<!--					<th class="ui-th-column ui-th-ltr" style="width: 10%">品相分数</th>-->
<!--					<th class="ui-th-column ui-th-ltr" >缺陷代码</th>-->
<!--					<th class="ui-th-column ui-th-ltr" style="width: 10%">真伪</th>-->
					<!-- 						<th class="ui-th-column ui-th-ltr" >标准价</th>
                                            <th class="ui-th-column ui-th-ltr" >国际价</th>
                                            <th class="ui-th-column ui-th-ltr" >费用</th>
                                            <th class="ui-th-column ui-th-ltr" >折扣</th> -->
					<!-- 						<th class="ui-th-column ui-th-ltr" >盒子费</th>
                                            <th class="ui-th-column ui-th-ltr" >加急</th>
                                            <th class="ui-th-column ui-th-ltr" >其他费</th> -->
					<th class="ui-th-column ui-th-ltr">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" >对内备注</th>
					<th class="ui-th-column ui-th-ltr" >对外备注</th>
					<th class="ui-th-column ui-th-ltr" >操作</th>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects3" v-show="projects3.length > 0">
						<td>{{item.nummber}}</td>
						<td><i-input v-model="item.name" @on-change="getDepts(item,index,'name',item.name)"  placeholder="名称1"/></td>
						<td><i-input v-model="item.name2" @on-change="getDepts(item,index,'name2',item.name2)" placeholder="名称2"/></td>
						<td><i-input v-model="item.edition" @on-change="getDepts(item,index,'edition',item.edition)"  placeholder="版别"/></td>
						<td><i-input v-model="item.niandai" @on-change="getDepts(item,index,'niandai',item.niandai)" placeholder="年代"/></td>
						<!-- <td><i-input v-model="item.year" @on-change="getDepts(item,index,'year',item.year)" placeholder="年份"/></td> -->
						<td><i-input v-model="item.mianzhi" @on-change="getDepts(item,index,'mianzhi',item.mianzhi)" placeholder="面值"/></td>
						<td><i-input v-model="item.size" @on-change="getDepts(item,index,'size',item.size)" placeholder="尺寸"/></td>
                        <td><i-input v-model="item.weight" placeholder="重量"  @on-change="getDepts(item,index,'weight',item.weight)"/></td>
						<td><i-input v-model="item.ctCaizhi" @on-change="getDepts(item,index,'ctCaizhi',item.ctCaizhi)" placeholder="材质"/></td>
<!--						<td>-->
<!--							<i-select v-model="item.scoreshow" @on-change="getDept(item,index)" filterable label-in-value>-->
<!--								<i-option v-for="statusItem in jzbScoreList" :value="statusItem.name"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
<!--						<td>-->
<!--							<i-select v-model="item.qxshow" @on-change="getDept(item,index)"  filterable label-in-value>-->
<!--								<i-option v-for="statusItem in jzbQXList" :value="statusItem.name"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
<!--						<td>-->
<!--							<i-select v-model="item.result" @on-change="getDepts(item,index,'result',item.result)" filterable label-in-value>-->
<!--								<i-option v-for="statusItem in statusList" :value="statusItem.id"-->
<!--										  :key="statusItem.id">{{statusItem.name}}-->
<!--								</i-option>-->
<!--							</i-select>-->
<!--						</td>-->
						<!-- 							<td><i-input v-model="item.bzfee" type="number" placeholder="标准价"/></td>
                                                    <td><i-input v-model="item.gjfee" type="number" placeholder="国际价"/></td>
                                                    <td><i-input v-model="item.finalfee" :value="getFee(item)" type="number" placeholder="费用" readonly="readonly"/></td>
                                                    <td><i-input v-model="item.zk"  type="number" placeholder="折扣"/></td> -->
						<!-- 							<td><i-input v-model="item.boxfee" type="number" placeholder="盒子费"/></td>
                                                    <td><i-input v-model="item.jiajifee" type="number" placeholder="加急费"/></td>
                                                    <td><i-input v-model="item.qtfee" type="number" placeholder="其他费"/></td> -->
						<td>
							<i-select v-model="item.boxtype" @on-change="getDepts(item,index,'boxtype',item.boxtype)"  filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>
						</td>
						<td><i-input v-model="item.backweight"  @on-change="getDepts(item,index,'backweight',item.backweight)" placeholder="对内备注"/></td>
						<td><i-input v-model="item.description" @on-change="getDepts(item,index,'description',item.description)" placeholder="对外备注"/></td>
						<td><a href="javascript:;" @click="deleteProject(item, index,'3')"  v-if="index!=0">删除</a></td>
					</tr>
					</tbody>
				</table>
			</Tab-Pane>
			<Tab-Pane label="纸币" name="#jqGrid4">
				<div class="ivu-row" style="margin-left: -8px; margin-right: -8px;float: right">
					<div class="search-group">
						<div class="ivu-col ivu-col-span-3" style="padding-left: 8px; padding-right: 8px;"></div>
						<button type="button" class="ivu-btn"><!----> <!----> <span id="spanNumBack" @click='spanNumOS(0)'>上一页</span></button>
						<button type="button" class="ivu-btn"><!----> <!----> <span id="spanNum">0</span></button>
						<button type="button" class="ivu-btn"><!----> <!----> <span >/</span></button>
						<button type="button" class="ivu-btn"><!----> <!----> <span id="spanNum2">0</span></button>
						<button type="button" class="ivu-btn"><!----> <!----> <span id="spanNumNext" @click='spanNumOS(1)'>下一页</span></button>
					</div></div>


				<table class="ui-jqgrid-htable ui-common-table table table-bordered" >
					<thead>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">订单号</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称1</th>
					<th class="ui-th-column ui-th-ltr" style="width: 7%">名称2</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">版别</th>
					<th class="ui-th-column ui-th-ltr" style="width: 4%">年代</th>
					<!-- <th class="ui-th-column ui-th-ltr" style="width: 4%">年份</th> -->
					<th class="ui-th-column ui-th-ltr" style="width: 5%">冠号</th>
					<th class="ui-th-column ui-th-ltr" style="width: 5%">银行</th>
					<th class="ui-th-column ui-th-ltr" style="width: 10%">品相分数</th>
					<th class="ui-th-column ui-th-ltr" style="width: 10%"> ★ /EPQ/NET</th>
					<th class="ui-th-column ui-th-ltr" style="width: 10%">真伪</th>
					<!-- 						<th class="ui-th-column ui-th-ltr" >标准价</th>
                                            <th class="ui-th-column ui-th-ltr" >国际价</th>
                                            <th class="ui-th-column ui-th-ltr" >费用</th>
                                            <th class="ui-th-column ui-th-ltr" >折扣</th> -->
					<!-- 					<th class="ui-th-column ui-th-ltr" >盒子费</th>
                                            <th class="ui-th-column ui-th-ltr" >加急</th>
                                            <th class="ui-th-column ui-th-ltr" >其他费</th> -->
					<th class="ui-th-column ui-th-ltr">地区</th>
					<th class="ui-th-column ui-th-ltr">目录</th>
					<th class="ui-th-column ui-th-ltr">钱币备注</th>
					<th class="ui-th-column ui-th-ltr">盒子类型</th>
					<th class="ui-th-column ui-th-ltr" >对内备注</th>
					<th class="ui-th-column ui-th-ltr" >对外备注</th>
					<th class="ui-th-column ui-th-ltr" >操作</th>
					</thead>
					<tbody>
					<tr v-for="(item, index) in projects4" v-show="projects4.length > 0">
						<td>{{item.nummber}}</td>
						<td><i-input v-model="item.name" @on-change="getDepts(item,index,'name',item.name)"  placeholder="名称1"/></td>
						<td><i-input v-model="item.name2" @on-change="getDepts(item,index,'name2',item.name2)" placeholder="名称2"/></td>
						<td><i-input v-model="item.edition" @on-change="getDepts(item,index,'edition',item.edition)"  placeholder="版别"/></td>
						<td><i-input v-model="item.niandai" @on-change="getDepts(item,index,'niandai',item.niandai)" placeholder="年代"/></td>
						<!-- <td><i-input v-model="item.year" @on-change="getDepts(item,index,'year',item.year)" placeholder="年份"/></td> -->
						<td><i-input v-model="item.zbnum" @on-change="getDepts(item,index,'zbnum',item.zbnum)" placeholder="冠号"/></td>
						<td><i-input v-model="item.bank"  @on-change="getDepts(item,index,'bank',item.bank)" placeholder="银行"/></td>
						<td>
							<i-select v-model="item.scoreshow2" @on-change="getDept(item,index)"  filterable label-in-value>
								<i-option v-for="statusItem in zbScoreList" :value="statusItem.name"
										  :key="statusItem.id">{{statusItem.name}}
								</i-option>
							</i-select>
						</td>
						<td>
							<Checkbox-group @on-change="getDepts(item,index,'extbqs',item.extbqs)" v-model="item.extbqs">
								<Checkbox :label="extbq.id" v-for="extbq in extbqList">{{extbq.name}}</Checkbox>
							</Checkbox-group>
						</td>
						<td>
							<i-select v-model="item.result" @on-change="getDepts(item,index,'result',item.result)" filterable label-in-value>
								<i-option v-for="statusItem in statusList" :value="statusItem.id"
										  :key="statusItem.id">{{statusItem.name}}
								</i-option>
							</i-select>
						</td>
						<!-- 							<td><i-input v-model="item.bzfee" type="number" placeholder="标准价"/></td>
                                                    <td><i-input v-model="item.gjfee" type="number" placeholder="国际价"/></td>
                                                    <td><i-input v-model="item.finalfee" :value="getFee(item)" type="number" placeholder="费用" readonly="readonly"/></td>
                                                    <td><i-input v-model="item.zk"  type="number" placeholder="折扣"/></td> -->
						<!-- 							<td><i-input v-model="item.boxfee" type="number" placeholder="盒子费"/></td>
                                                    <td><i-input v-model="item.jiajifee" type="number" placeholder="加急费"/></td>
                                                    <td><i-input v-model="item.qtfee" type="number" placeholder="其他费"/></td> -->
						<td><i-input v-model="item.addr" @on-change="getDepts(item,index,'addr',item.addr)"  placeholder="地区"/></td>
						<td><i-input v-model="item.catalog"  @on-change="getDepts(item,index,'catalog',item.catalog)"  placeholder="目录"/></td>
						<td><i-input v-model="item.coindesc" @on-change="getDepts(item,index,'coindesc',item.coindesc)" placeholder="钱币备注"/></td>
						<td>
							<i-select v-model="item.boxtype" @on-change="getDepts(item,index,'boxtype',item.boxtype)"  filterable label-in-value style="width: 100px;">
								<i-option v-for="itemb in packTypeList" :value="itemb.id"
										  :key="itemb.id">{{itemb.name}}
								</i-option>
							</i-select>
						</td>
						<td><i-input v-model="item.backweight"  @on-change="getDepts(item,index,'backweight',item.backweight)" placeholder="对内备注"/></td>
						<td><i-input v-model="item.description" @on-change="getDepts(item,index,'description',item.description)" placeholder="对外备注"/></td>
						<td><a href="javascript:;" @click="deleteProject(item, index,'4')"  v-if="index!=0">删除</a></td>
					</tr>

					</tbody>

				</table>


			</Tab-Pane>
			<Tab-Pane label="纸币excel" name="#jqGrid5">
						　　 <!-- 外层盒子 -->
						<div style="height:700px;width:100%;">
							<!-- 这里的标签名称要和main.js文件中定义的组件名称保持一致 -->
							<happy-scroll color="rgba(0,0,0,0.5)" size="5">
								<!-- 内层盒子——内容区 -->
								<div class="con">
									<!--				<div class="ivu-row" style="margin-left: -8px; margin-right: -8px;float: left;align-content: center">-->
									<!--					<div class="search-group">-->
									<div id="spreadsheet1" style="width: 100% ;"   @contextmenu.prevent.stop="rightClick()"></div>
									<!--					</div>-->
									<!--				</div>-->



								</div>
							</happy-scroll>
						</div>






			</Tab-Pane>
		</Tabs>
	</div>

	<Card v-show="!showList">
		<p slot="title">{{title}}</p>
		<i-form ref="formValidate" :model="pjClCoin" :rules="ruleValidate" :label-width="80">
			<Form-item label="编号" prop="serialNumber" >
				<i-input v-model="pjClCoin.serialNumber" type="number" placeholder="不填写时则自动生成" readonly="readonly">
				</i-input>
			</Form-item>
			<Form-item label="纲目" prop="parentName">
				<i-input type="text" v-model="parentName" icon="eye" readonly="readonly"
						 @on-click="deptTree" readonly="readonly" placeholder="纲目">
				</i-input>
			</Form-item>
			<Form-item label="名称" prop="name">
				<i-input v-model="pjClCoin.name" placeholder="名称">
				</i-input>
			</Form-item>
			<Form-item label="年代" prop="niandai">
				<i-input v-model="pjClCoin.niandai" placeholder="年代">
				</i-input>
			</Form-item>
			<Form-item label="尺寸" prop="size">
				<i-input v-model="pjClCoin.size" placeholder="尺寸">
				</i-input>
			</Form-item>
			<Form-item label="重量" prop="weight">
				<i-input v-model="pjClCoin.weight" placeholder="重量">
				</i-input>
			</Form-item>
			<Form-item label="材质" prop="ctCaizhi">
				<i-input v-model="pjClCoin.ctCaizhi" placeholder="材质">
				</i-input>
			</Form-item>
			<Form-item label="缺陷代码" prop="cldefNum">
				<i-input v-model="pjClCoin.cldefNum" placeholder="缺陷代码">
				</i-input>
			</Form-item>
			<Form-item label="分数代码" prop="scoreNum">
				<i-input v-model="pjClCoin.scoreNum" placeholder="分数代码">
				</i-input>
			</Form-item>
			<Form-item label="分数名称" prop="scoreName">
				<i-input v-model="pjClCoin.scoreName" placeholder="分数名称">
				</i-input>
			</Form-item>
			<Form-item label="分数" prop="score">
				<i-input v-model="pjClCoin.score" placeholder="分数">
				</i-input>
			</Form-item>
			<Form-item label="分数备注" prop="scoreDesc">
				<i-input v-model="pjClCoin.scoreDesc" placeholder="分数备注">
				</i-input>
			</Form-item>
			<Form-item label="鉴定结果" prop="result" style="width: 268px;">
				<i-select v-model="pjClCoin.result" filterable label-in-value>
					<i-option v-for="statusItem in statusList" :value="statusItem.id"
							  :key="statusItem.id">{{statusItem.name}}
					</i-option>
				</i-select>
			</Form-item>
			<Form-item label="年份" prop="year">
				<i-input v-model="pjClCoin.year" placeholder="年份">
				</i-input>
			</Form-item>
			<Form-item label="地区" prop="addr">
				<i-input v-model="pjClCoin.addr" placeholder="地区">
				</i-input>
			</Form-item>
			<Form-item label="银行名称" prop="bank">
				<i-input v-model="pjClCoin.bank" placeholder="地区">
				</i-input>
			</Form-item>
			<Form-item label="纸币编号" prop="zbnum">
				<i-input v-model="pjClCoin.zbnum" placeholder="纸币编号">
				</i-input>
			</Form-item>
			<Form-item label="预估价" prop="ctValue">
				<i-input v-model="pjClCoin.ctValue" placeholder="预估价">
				</i-input>
			</Form-item>
			<Form-item label="对内备注" prop="backweight">
				<i-input v-model="pjClCoin.backweight" placeholder="对内备注">
				</i-input>
			</Form-item>
			<!--             <Form-item label="图片" prop="pic">
                            <i-input v-model="pjClCoin.pic" placeholder="图片"/>
                        </Form-item>
                            -->
			<Form-item label="图片" prop="uploadList">
				<template>
					<div class="upload-list" v-for="item in uploadList">
						<template>
							<img :src="item.imgUrl"/>
							<div class="upload-list-cover">
								<Icon type="ios-eye-outline" @click.native="handleView(item.imgUrl)"></Icon>
								<Icon type="ios-trash-outline" @click.native="handleRemove(item)"></Icon>
							</div>
						</template>
						<template v-else>
							<Progress v-if="item.showProgress" :percent="item.percentage" hide-info></Progress>
						</template>
					</div>
					<Upload
							ref="upload"
							:show-upload-list="false"
							:default-file-list="uploadList"
							:on-success="handleSuccess"
							:format="['jpg','jpeg','png']"
							:max-size="10480"
							:on-format-error="handleFormatError"
							:on-exceeded-size="handleMaxSize"
							:before-upload="handleBeforeUpload"
							multiple
							type="drag"
							action="../sys/oss/upload"
							style="display: inline-block;width:58px;">
						<div style="width: 58px;height:58px;line-height: 58px;">
							<Icon type="camera" size="20"></Icon>
						</div>
					</Upload>
					<Modal title="查看图片" v-model="visible">
						<img :src="imgName" v-if="visible" style="width: 100%"/>
					</Modal>
				</template>
			</Form-item>

			</Form-item>
			<i-button type="primary" v-if="read==0"  @click="handleSubmit('formValidate')">提交</i-button>
			<i-button type="warning" @click="reload" style="margin-left: 8px"/>返回</i-button>
			<i-button type="ghost" v-if="read==0"  @click="handleReset('formValidate')" style="margin-left: 8px">重置</i-button>
			</Form-item>
		</i-form>
	</Card>
</div>
<!-- 选择纲目 -->
<div id="deptLayer" style="display: none;padding:10px;">
	<ul id="deptTree" class="ztree"></ul>
</div>




<script src="/platformFramework/js/sys/coinsm.js?_1749996672920"></script>
<!--<script src="js/v4/jexcel.js"></script>-->
<!--<script src="js/v4/jsuites.js"></script>-->
<!--<link rel="stylesheet" href="css/v4/jexcel.css" type="text/css" />-->
<!--<link rel="stylesheet" href="css/v4/jsuites.css" type="text/css" />-->
<script>
	var usernames =null;
	var numberRow;
	var numberRowOk=0;

	var userIdssss = null;
	// function getIntArr(str){
	// 	return  str.replace(/[^0-9]/ig, ' ').trim().split(/\s+/);
	// }

	var onpa = function(el, data) {//粘贴数据后
		// console.log("onpa");
		// console.log( el );
		// console.log( data );


		console.log("改变后");


	}


	var onbefor = function(el, data, x, y) {//它发生在将数据粘贴到电子表格之前，可用于拦截、更改或取消用户操作。



		 let arrdata=data.split('\n');//行数
		arrdata=arrdata[0].trim().split('\t');
		let col=table1.getSelectedColumns();//x
		let row=table1.getSelectedRows();//y
		if(arrdata.length>1){

			if((arrdata.length==1&&arrdata.length==col.length)||row.length>1){

				for (let i = 0; i < arrdata.length; i++) {
					for (let j = 0; j < row.length; j++) {
						table1.setValueFromCoords(col[i],row[j].rowIndex-1,arrdata[i]);
					}

				}
				return false;
			}


		}


		//table1.setValueFromCoords(table1.getSelectedColumns()[0],table1.getSelectedRows()[0].rowIndex-1,"111111111");
		//console.log(table1.getSelectedColumns());//列
		//console.log(table1.getSelectedRows());//行
		// console.log(table1.getSelectedColumns());//x坐标取值
		// console.log(table1.getSelectedRows());//y坐标取值




	}



	var onediti = function(el, cell, x, y) {//选择改变

		// console.log("onediti");
		// console.log( el );
		// console.log( cell );
		// console.log( x );
		// console.log( y );
		// console.log("onediti");


	}


	var changed1 = function(el, records) {//单元格改变事件

		// console.log("改变后");
		// console.log( el );
		// console.log(records);
		// for (let i = 0; i < records.length; i++) {
		//
		// 	console.log(records[i].x);
		// 	console.log(records[i].y);
		//
		//
		// }

	}


	var changed = function(instance, cell, x, y, value) {
		var cellName = jspreadsheet.getColumnNameFromId([x,y]);
		 //console.log('单元格内容' + cellName + ' 更新为: ' + value );
		// table1.setValueFromCoords(x,y,"111111111");
		let  projectData = [];

		let data=table1.getData();
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据
		data.pop();//删掉最后一行空数据


		for (let i = 0; i < data.length; i++) {
			let json={};

			let data1=data[i];

			json.addr=(data1[11]);//addr
			json.bank=(data1[7]); //bank
			json.bzfee=(data1[20]);//bzfee
			json.catalog=(data1[12]);//catalog
			json.classid=(data1[21]);//classid
			json.cldef=(data1[22]);//cldef
			json.cldefNum=(data1[23]);//cldefNum
			json.cldefname=(data1[24]);//cldefname
			json.cointype=(data1[25]);//cointype
			json.createTime=(data1[27]);//createTime
			json.createUser=(data1[28]);//createUser
			json.edition=(data1[4]); //edition
			json.extbq=(extbqPackDe(data1[9])); //extbq
			json.finalfee=(data1[38]);//finalfee
			json.gjfee=(data1[39]);//gjfee
			json.gm=(data1[40]);//gm
			json.gmname=(data1[41]);//gmname
			json.id=(data1[43]);//id
			json.jiaji=(data1[50]);//jiaji
			json.live=(data1[3]);//live
			json.name=(data1[1]); //name
			json.niandai=(data1[5]); //niandai
			json.nummber=(data1[0]); //nummber
			json.pic=(data1[58]);//pic
			json.qxshow=(data1[60]);//qxshow
			json.result=(resultPackDe(data1[10])); //result
			json.score=(scoreshow2Pack2(data1[8]));//score 分数data1[64]
			json.scoreName=(data1[66]);//scoreName
			json.scoreNum=(scoreshow2Pack1(data1[8]));//scoreNum 分数表现data1[67]
			json.scoreshow=(data1[69]);//scoreshow
			json.scoreshow2=(data1[8]); //scoreshow2
			json.sendnum=(data1[70]);//sendnum
			json.subclassid=(data1[76]);//subclassid
			json.thumb=(data1[78]);//thumb
			json.updateTime=(data1[79]);//updateTime
			json.updateUser=(data1[80]);//updateUser
			json.zbnum=(data1[6]); //zbnum
			json.zk=(data1[84]);//zk
			json.boxtype=(boxtypePackDe(data1[14]));//boxtype
			json.backweight=(data1[15]);//backweight
			json.description=(data1[16]);//description
			json.coindesc=(data1[13]);//coindesc
			json.name2=(data1[2]);//name2

			userIdssss=json.createUser
			projectData.push(json);//boxtypePackDe



		}



		// let url = "../pjclcoin/bufferAll";
		// Ajax.request({
		// 	url: url,
		// 	params: JSON.stringify(projectData),
		// 	type: "POST",
		// 	contentType: "application/json",
		// 	successCallback: function (r) {
		//
		// 		// console.log(r);
		//
		// 	}
		// });




	}

	var beforeChange =  function(instance, cell, x, y, value) {
		/*// 检查是否满足序列化的条件
		if (/^[A-Z]+\d+$/.test(value)) { // 正则表达式匹配以字母开头并且带数字结尾的字符串
			var match = value.match(/([A-Z]+)(\d+)$/);
			var prefix = match[1]; // 字母部分
			var number = parseInt(match[2]); // 数字部分
			var colIndex = x; // 当前列
			var rowIndex = y; // 当前行

			// 获取下拉的范围
			var nextRows = instance.getSelectedRows();

			for (var i = 0; i < nextRows.length; i++) {
				// 增加序号
				var newValue = prefix + (number + i + 1);
				instance.setValueFromCoords(colIndex, nextRows[i], newValue);
			}

			// 阻止默认的值更改
			return false;
		}*/
	}


	var insertedRow = function(instance) {
		console.log('Row added');
	}

	var insertedColumn = function(instance) {
		console.log('Column added');
	}

	var deletedRow = function(instance) {
		console.log('Row deleted');
	}

	var deletedColumn = function(instance) {
		console.log('Column deleted');
	}

	var sort = function(instance, cellNum, order) {
		var order = (order) ? 'desc' : 'asc';
		//console.log('The column  ' + cellNum + ' sorted by ' + order + '');
	}


	var beforesort = function(el, column,  direction,  newOrderValues) {
		//console.log('排序前');
		// return false;
		// var order = (order) ? 'desc' : 'asc';
		//console.log('The column  ' + cellNum + ' sorted by ' + order + '');
	}

	var resizeColumn = function(instance, cell, width) {
		//console.log('The column  ' + cell + ' resized to width ' + width + ' px');
	}

	var resizeRow = function(instance, cell, height) {
		//console.log('The row  ' + cell + ' resized to height ' + height + ' px');
	}

	var selectionActive = function(instance, x1, y1, x2, y2, origin) {
		var cellName1 = jspreadsheet.getColumnNameFromId([x1, y1]);
		var cellName2 = jspreadsheet.getColumnNameFromId([x2, y2]);

		 numberRow=table1.getValue(cellName1);
		 numberRowOk=0;


		// console.log(x1);

		// console.log(table1.getValue(cellName1));


		 //console.log('实时选择区 ' + cellName1 + ' 到 ' + cellName2 + '');
	}

	var loaded = function(instance) {
		console.log('New data is loaded');
	}

	var moveRow = function(instance, from, to) {
		console.log('The row ' + from + ' was move to the position of ' + to + ' ');
	}

	var moveColumn = function(instance, from, to) {
		console.log('The col ' + from + ' was move to the position of ' + to + ' ');
	}

	var blur = function(instance) {
		console.log('The table ' + $(instance).prop('id') + ' is blur');
	}

	var focus = function(instance) {
		console.log('The table ' + $(instance).prop('id') + ' is focus');
	}




	var dataauto = ['1|汽车','2|飞机','3|轮船','4|民族人物头像','5|纺织','6|女拖拉机手','7|大团结','8|长江大桥','9|毛泽东头像','10|建国纪念钞','11|拖拉机','12|车床工人','13|炼钢工人','14|工农知识分子头像','15|教育与生产劳动相结合','16|各民族大团结','17|龙钞纪念币','18|水电站','19|火车','20|井冈山','21|天安门','22|宝塔山','23|四位领袖浮雕头像','24|航天纪念钞','25|迎接新世纪纪念钞','26|塑料钞'];
	var data1 = [
		// [ '名称1','名称2','年代','目录','银行名称','盒子类型','数量','标准价','国际价','费用','折扣','盒子费','加急','版别','特殊标签','归属公司'],
		// [ '订单号','名称1','名称2','版别','年代','冠号','银行','品相分数','★ /EPQ/NET','真伪','地区','目录','钱币备注','盒子类型','对内备注','对外备注'],
		// [ '订单号','名称1','名称2','版别','年代','冠号','银行','品相分数','★ /EPQ/NET','真伪','地区','目录','钱币备注','盒子类型','对内备注','对外备注'],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
		// [ '','','','','','','','','','','','','','','',''],
	];

	// console.log(data1);
	var table1 = jspreadsheet(document.getElementById('spreadsheet1'), {
		// data:data1,
		columns: [

			{
				title: '订单号',
				type: 'text',
				width:'82px',
				readOnly:true,

			},
			{
				title: '名称1',
				type: 'text',
				width:'100px',
			},
			{
				title: '名称2',
				type: 'text',
				width:'80px',
			},
			{
				title: '附加',
				type: 'text',
				width:'80px',
			},

			{
				title: '版别',
				type: 'autocomplete',//autocomplete

				source:['汽车','南昌','6张纪念钞组合','飞机','轮船','拾圆伍圆贰圆壹圆贰角（荧光钞）','民族人物头像','纺织','女拖拉机手','大团结','长江大桥','毛泽东头像','建国纪念钞',
					    '拖拉机','工人农民','龙年纪念钞','大黑拾','红库印','车床工人','币签合一','炼钢工人','工农知识分子头像','教育与生产劳动相结合','各民族大团结','龙钞纪念币','水坝',
					    '水电站','建设公债','斐济财神钞','第四套套装 分币组合','银元券','航天钞 2018世界杯 索契冬奥纪念币组合','省立、兑换劵','老假','双马耕地','永泰县','福建','火车','兑换劵','井冈山','天安门','（广东省集藏投资协会监制）','宝塔山','四位领袖浮雕头像','航天纪念钞','迎接新世纪纪念钞','塑料钞',
					    '上海','花版','地方流通劵','鸟版','保值公债','黄钥匙','罗马兵','帆船','小棉胎','蓝屋','吉林','寿光','东三省','汇兑券','省立','兑米票','三明市企业内部债券','昌黎','重庆、银元辅币券','琼崖区流通券','银元辅币券','私人印制','大汉四川军政府军用银票','山西','兑换券（纪念券）','左右号不同','Bank of China','预备乘车券','甲辰龙年贺岁纪念钞','船版','纸币套装 伍圆 贰圆 壹圆','本票','益阳二里','太原','革命战争共债劵','2024年斐济龙年塑料钞','四版贰圆贰角/三版贰角/飞机','广州、银元券','合作券','东北九省流通券','天津','三罗码','金圆券','国库券','外汇兑换券','香港','重庆','张家口','捆刀封签','银毫券','北京',
					    '厦门','东三省、小银元券','河南','杭州','山东、兑换券','哈尔滨','股权证','股票','兑换券','张家口丰镇','梧州','福州','京兆','节约建国储蓄券','牌坊','北平','济南','第四版人民币 伍圆 贰圆 壹圆','第24届北京冬季奥林匹克运动会纪念钞','奥运会纪念钞','成立一百五十周年纪念','70周年纪念钞','冬奥会纪念钞','关金券','经济建设公债','广东','广州',
					    '汽车、飞机、轮船','湘赣省分行','革命战争公债券','票样正面','八同号','票样反面','甘肃','辽东','大洋票','山东','驮运','银毫劵',"蛇年纪念钞",
					    '纪念中国银行成立一百周年','汕头','地方经济建设公债','支票','第四套套装','七十周年纪念钞','第三套18.88套装','小小三套装 粮票组合',
				        '人民代表步出大会堂','521套装','1953年分币套装',  '冰上运动雪上运动组合',
						'第24届冬奥会纪念钞',  '退市第四套人民币套装组合','中华民国三十八年','关金伍仟圆', '四版贰圆贰角/三版贰角 三枚组合', '维吾尔族、彝族 长江大桥组合',
						'第四套套装 十全十美',  '粮票组合套装',  '布依族、朝鲜族,长江大桥组合',  '伍分轮船 贰分飞机 壹分汽车',
						'小小三套装','男皇蓝色版','男皇紫色版','小圣书','大棉胎','七小福套装','第三套18.88套装 粮票组合','东北九省流通券','第四版人民币 壹佰圆 索契冬奥钞','70周年纪念钞 世界杯纪念钞 北京冬奥会纪念钞','四版贰圆贰角/贰分','第三版人民币组合（综合分）','三版伍圆/四版伍圆组合','第三套/第四套拾圆组合','纤云.金光星辉','中华民国三十六年','第四套套装 粮票组合','庆祝中华人民共和国成立50周年','小四套装'],
				width:'150px',
				align: 'left',

			},
			{
				title: '年代',
				type: 'text',
				width:'50px',
			},
			{
				title: '冠号',
				type: 'text',
				width:'120px',
			},
			{
				title: '银行',
				type: 'text',
				width:'100px',
			},
			{
				title: '品相分数',
				type: 'autocomplete',
				// source: qualityOptions,
				source:[ 'Superb Gem Unc70','Superb Gem Unc69','Superb Gem Unc68','Superb Gem Unc67','Gem Uncirculated66','Gem Uncirculated65','Choice Uncirculated64','Choice Uncirculated63','Uncirculated62','Uncirculated61','Uncirculated60','Choice About Unc58','About Uncirculated55','About Uncirculated53','About Uncirculated50','Choice Extremely Fine45','Extremely Fine40','Choice Very Fine35','Very Fine30','Very Fine25','Genuine','Genuine真品','极美品','精美品','美品','上美品'],
				width:'150px',
			},
			{
				title: '★ /EPQ/NET',
				autocomplete:true,
				multiple:true,
				type: 'dropdown',
				source:[ '★','EPQ','NET'],
				width:'110px',
				align: 'left',
			},

			{
				title: '真伪',
				type: 'autocomplete',
				source:[ '0|未鉴定','1|真','2|赝品','3|存疑','4|不提供服务','5|不适合评级(赝品)','6|不适合评级(锭体、铭文修复超出规定)','9|不适合评级(老假、老仿、臆造)','8|不适合评级','7|撤评'],
				width:'150px',
				align: 'left',
			},{
				title: '地区',
				type: 'text',
				width:'50px',
			},{
				title: '目录',
				type: 'text',
				width:'100px',
			},
			{
				title: '钱币备注',
				type: 'autocomplete',
				source:['1000以内' ,
				'2000以内' ,
				'3000以内' ,
				'5000以内' ,
				'8000以内' ,
				'10000以内',
				'20000以内',
				'30000以内',
				'50000以内'],
				width:'100px',
			},
			{
				title: '星级',
				type: 'autocomplete',
				source:[ '五星','四星','三星'],
				width:'70px',
				align: 'left',
			},
			{
				title: '对内备注',
				type: 'text',
				width:'100px',
			},
			{
				title: '对外备注',
				type: 'text',
				width:'100px',
			},


			//	------------------


			{
				title: 'addType',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//11
			// 	title: 'addr',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'amount',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//15
			// 	title: 'backweight',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {//7
			// 	title: 'bank',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'boxfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//14
			// 	title: 'boxtype',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'bzfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//12
			// 	title: 'catalog',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'classid',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'cldef',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'cldefNum',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'cldefname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//13
			// 	title: 'coindesc',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'cointype',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'corder',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'createTime',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'createUser',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctCaizhi',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctJiyuan',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctPrint',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctValue',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ctZhuzb',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//16
			// 	title: 'description',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {//4
			// 	title: 'edition',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {//9
			// 	title: 'extbq',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'extbqs',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'extbqs1',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'extbqs2',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'feeex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'finalfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'gjfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'gm',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'gmname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'grade',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'id',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ids',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'imgList',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'indate',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'intro',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'ispack',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'issm',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'jiaji',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'jiajifee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {
			// 	title: 'live',
			// 	type: 'text',
			// 	width:'0px',
			// 	height:'0px',
			// 	readOnly:true,
			// },
			{
				title: 'marker',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'mianzhi',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {
			// 	title: 'name',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			// {
			// 	title: 'name2',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'name3',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'nameex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//5
			// 	title: 'niandai',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'nickname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'numbers',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {
			// 	title: 'nummber',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'pic',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'qtfee',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'qxshow',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'rank',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//10
			// 	title: 'result',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'rname',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'rnameex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'score',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreDesc',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreName',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreNum',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'scoreshow',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//8
			// 	title: 'scoreshow2',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'sendnum',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'shape',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'shuizhong',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'size',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'smtime',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'status',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'subclassid',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'thirdclassid',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'thumb',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'updateTime',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'updateUser',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'weight',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'year',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			{
				title: 'yearex',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},
			// {//6
			// 	title: 'zbnum',
			// 	type: 'text',
			// 	width:'0px',

			// 	readOnly:true,
			// },
			{
				title: 'zk',
				type: 'text',
				width:'0px',
				height:'0px',
				readOnly:true,
			},

		],
		rowResize: true,
		columnDrag: true,
		allowInsertColumn:false,//插入列
		allowManualInsertColumn:false,
		allowManualInsertRow:false,
		allowManualInsertRow:false,
		tableOverflowResizable:true,
		tableOverflow:false,//有滚轮
		// tableHeight:'100%',
		// tableWidth:"100%",
		autoCasting:false,//
		editorFormulas:false,
		loadingSpin:true,

		autoIncrement:false,//自增

		//parseFormulas	Enable execution of formulas inside the table
		//autoIncrement	Auto increment actions when using the dragging corner
		//autoCastings	Convert strings into numbers when is possible


		// autoIncrement：
		onpaste:onpa,
		onbeforepaste:onbefor,
		oneditionstart:onediti,
				onafterchanges:changed1,
				onchange: changed,
				onbeforechange: beforeChange,
				oninsertrow: insertedRow,
				oninsertcolumn: insertedColumn,
				ondeleterow: deletedRow,
				ondeletecolumn: deletedColumn,
				onselection: selectionActive,
				onsort: sort,
				onbeforesort:beforesort,
				onresizerow: resizeRow,
				onresizecolumn: resizeColumn,
				onmoverow: moveRow,
				onmovecolumn: moveColumn,
				onload: loaded,
				onblur: blur,
				onfocus: focus,
		        license: 'MWEzMTE4MGFkNWY5YzQzNjE4NjZiNmE1NThhM2M0Yjc1NmUyNGM2N2YzZjU2NDQ5ZjM1MGFiYWNmOTFkNTkwODFiYmYwNDE1YjhhM2ViNGUyMzM2YjYzY2Q4NTcyMWE4MGQ4YjVjNjI2NWY4NWYyMTBjMWU5M2ZmNTU4OGI1MDQsZXlKdVlXMWxJam9pY0dGMWJDNW9iMlJsYkNJc0ltUmhkR1VpT2pFMk5UZzVOakk0TURBc0ltUnZiV0ZwYmlJNld5SnFjM0J5WldGa2MyaGxaWFF1WTI5dElpd2lZM05pTG1Gd2NDSXNJbXB6YUdWc2JDNXVaWFFpTENKc2IyTmhiR2h2YzNRaVhTd2ljR3hoYmlJNklqSWlMQ0p6WTI5d1pTSTZXeUoyTnlJc0luWTRJaXdpY0dGeWMyVnlJaXdpYzJobFpYUnpJaXdpWm05eWJYTWlMQ0p5Wlc1a1pYSWlMQ0ptYjNKdGRXeGhJbDE5',



	});

	function updateTab(y1Tab) {
		let value01 = $("#value01").val(); // x
		let value02 = $("#value02").val(); // 开始 y
		let value03 = $("#value03").val(); // 末尾 y
		let value1 = $("#value1").val(); // 末尾 y 用户自选
		value1 = Number(value1) + Number(y1Tab) - 1;
		if (value1 !== "") {
			value1 = value1 > table1.rows.length ? table1.rows.length : value1;
		} else {
			value1 = value03;
			value1 = value1 > table1.rows.length ? table1.rows.length : value1;
		}
		let value2 = $("#value2").val(); // 固定文本

		// Extract the numeric part from value2
		let reg = /\d+/g;
		let result = value2.match(reg);

		// Define a regex to remove leading zeros
		let reg1 = new RegExp("([0]*)([1-9]+[0-9]+)", "g");

		let num = ""; // Prepare to handle leading zeros
		if (result !== null && result.length > 0) {
			// Remove leading zeros
			let result1 = result[0].replace(reg1, "$2");
			let result1Length = result1.length;
			for (let i = 0; i < result[0].length - result1Length; i++) {
				num += "0";
			}
			console.log(num);
		} else {
			console.error("没有匹配到任何内容");
		}

		let value3 = $("#value3").val(); // 自增位置
		if (value3 === "") {
			value3 = 0;
		}

	/*	if (result != null) {
			value3 = result[0];
			value2 = value2.replace(value3, '');
		}*/

		let prefix = ""  // "HY"
		let number = ""  // "99987378"
		let suffix = ""  // "（豹子头）"
			// 使用正则表达式捕获各部分
		let regs = /^(.*?)(\d+)(.*)$/;
		let matches = value2.match(regs);

		if (matches) {
		prefix = matches[1];   // "HY"
		number = matches[2];   // "99987378"
		suffix = matches[3];   // "（豹子头）"
		}



		if (value01 > 0 && value02 > 0 && value03 > 0 && value1 > 0) {
			let numberLength = result[0].length; // 假设 result[0] 是从 value2 中提取的数字部分

			for (let i = value02 - 1; i < value1; i++) {
				let newNumber = String(parseInt(number, 10) + 1).padStart(numberLength, '0');
				let newValue = prefix + newNumber + suffix;
				table1.setValueFromCoords((value01 - 1), i, newValue);
				number = newNumber; // 更新 number 以供下一次迭代使用
			}
		} else {
			alert("输入索引异常");
		}
	}




	function updateTab1(y1Tab){


		let value01=$("#value01").val();//x
		let value02=$("#value02").val();//开始y
		let value03=$("#value03").val();//末尾y
		let value1=$("#tab2Val1").val();//末尾y用户自选
		value1 = Number(value1)+Number(y1Tab)-1;
		if(value1 != ""){
			value1=value1>table1.rows.length?table1.rows.length:value1;
		}else {
			value1=value03;
			value1=value1>table1.rows.length?table1.rows.length:value1;
		}
		let value2=$("#value2").val();//固定文本
		let reg = /\d+\.*\d*$/g;

		let result = value2.match(reg);



		let value3=$("#value3").val();//自增位置
		if(value3==""){
			value3=0;
		}

		if(result!=null){
			value3=result[0];
		}


		if(value01>0&&value02>0&&(value03>0)&&value1>0){

			for (let i = value02-1; i < value1; i++) {

					table1.setValueFromCoords((value01 - 1), i, value2);

			}

		}else {

			alert("输入索引异常");

		}





	};







</script>


<script>
	$(function (){

		let url = "../pjclcoin/queryAll";
		Ajax.request({
			url: url,
			type: "POST",
			contentType: "application/json",
			successCallback: function (r) {


				if(r.list!=null){
					//console.log(r.list);

					$.each(r.list, function(i, item){

							let data1T =[item.nummber,
								item.name,
								item.name2,
								item.live,
								item.edition,
								//editionPack(item.edition),
								item.niandai,
								item.zbnum,
								item.bank,
								item.scoreshow2,
								extbqPack(item.extbq),
								resultPack(item.result),
								item.addr,
								item.catalog,
								item.coindesc,
								boxtypePack(item.boxtype),
								item.backweight,
								item.description];//
							data1T.push(item.addType);
							data1T.push(item.amount);
							data1T.push(item.boxfee);
							data1T.push(item.bzfee);
							data1T.push(item.classid);
							data1T.push(item.cldef);
							data1T.push(item.cldefNum);
							data1T.push(item.cldefname);
							data1T.push(item.cointype);
							data1T.push(item.corder);
							data1T.push(item.createTime);
							data1T.push(item.createUser);
							data1T.push(item.ctCaizhi);
							data1T.push(item.ctJiyuan);
							data1T.push(item.ctPrint);
							data1T.push(item.ctValue);
							data1T.push(item.ctZhuzb);
							data1T.push(item.extbqs);
							data1T.push(item.extbqs1);
							data1T.push(item.extbqs2);
							data1T.push(item.feeex);
							data1T.push(item.finalfee);
							data1T.push(item.gjfee);
							data1T.push(item.gm);
							data1T.push(item.gmname);
							data1T.push(item.grade);
							data1T.push(item.id);
							data1T.push(item.ids);
							data1T.push(item.imgList);
							data1T.push(item.indate);
							data1T.push(item.intro);
							data1T.push(item.ispack);
							data1T.push(item.issm);
							data1T.push(item.jiaji);
							data1T.push(item.jiajifee);

							data1T.push(item.marker);
							data1T.push(item.mianzhi);
							data1T.push(item.name3);
							data1T.push(item.nameex);
							data1T.push(item.nickname);
							data1T.push(item.numbers);
							data1T.push(item.pic);
							data1T.push(item.qtfee);
							data1T.push(item.qxshow);
							data1T.push(item.rank);
							data1T.push(item.rname);
							data1T.push(item.rnameex);
							data1T.push(item.score);
							data1T.push(item.scoreDesc);
							data1T.push(item.scoreName);
							data1T.push(item.scoreNum);
							data1T.push(item.scoreex);
							data1T.push(item.scoreshow);
							data1T.push(item.sendnum);
							data1T.push(item.shape);
							data1T.push(item.shuizhong);
							data1T.push(item.size);
							data1T.push(item.smtime);
							data1T.push(item.status);
							data1T.push(item.subclassid);
							data1T.push(item.thirdclassid);
							data1T.push(item.thumb);
							data1T.push(item.updateTime);
							data1T.push(item.updateUser);
							data1T.push(item.weight);
							data1T.push(item.year);
							data1T.push(item.yearex);
							data1T.push(item.zk);
							vm.projects5Ve.push(data1T);

						    vm.projects4Ve1.push(item);





					});



					if (vm.projects5Ve.length>1){
						vm.projects5Ve.pop();//删掉最后一行空数据
					}

					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);
					vm.projects5Ve.push([]);

					table1.setData(vm.projects5Ve);


				}



			}
		});

	})
</script>




</body>
</html>