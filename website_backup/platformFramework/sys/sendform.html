 <!DOCTYPE html>
<html>
<head>
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache, must-revalidate">
    <meta http-equiv="expires" content="0">
    <title>送评单列表</title>
    <meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<!--bootstrap-->
<link rel="stylesheet" href="/platformFramework/statics/css/bootstrap.min.css">
<link rel="stylesheet" href="/platformFramework/statics/css/font-awesome.min.css">
<!--jqgrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="/platformFramework/statics/plugins/ztree/css/metroStyle/metroStyle.css">
<!--main-->
<link rel="stylesheet" href="/platformFramework/statics/css/main.css">
<link rel="stylesheet" href="/platformFramework/statics/css/iview.css">
<link rel="stylesheet" href="/platformFramework/statics/css/style.css">
<!--treegrid-->
<link rel="stylesheet" href="/platformFramework/statics/plugins/treegrid/jquery.treegrid.css">
<!--富文本-->
<link rel="stylesheet" href='/platformFramework/statics/plugins/froala_editor/css/froala_editor.min.css'/>

<!--jquery-->
<script src="/platformFramework/statics/libs/jquery.min.js"></script>
<!--layer-->
<script src="/platformFramework/statics/plugins/layer/layer.js"></script>
<!--bootstrap-->
<script src="/platformFramework/statics/libs/bootstrap.min.js"></script>
<!--vue-->
<script src="/platformFramework/statics/libs/vue.min.js"></script>
<script src="/platformFramework/statics/libs/iview.min.js"></script>
<!--jqgrid-->
<script src="/platformFramework/statics/plugins/jqgrid/grid.locale-cn.js"></script>
<script src="/platformFramework/statics/plugins/jqgrid/jquery.jqGrid.min.js"></script>
<!--ztree-->
<script src="/platformFramework/statics/plugins/ztree/jquery.ztree.all.min.js"></script>
<!--treegrid-->
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.extension.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.min.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/jquery.treegrid.bootstrap3.js"></script>
<script src="/platformFramework/statics/plugins/treegrid/tree.table.js"></script>

<!--simplemde富文本-->
<script src='/platformFramework/statics/plugins/froala_editor/js/froala_editor.min.js'></script>
<!--[if lt IE 9]>
<script src="/platformFramework/statics/plugins/froala_editor/js/froala_editor_ie8.min.js"></script>
<![endif]-->
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/tables.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/lists.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/colors.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/media_manager.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_family.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/font_size.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/block_styles.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/plugins/video.min.js"></script>
<script src="/platformFramework/statics/plugins/froala_editor/js/langs/zh_cn.js"></script>

<script src="/platformFramework/statics/libs/jquery-extend.js"></script>
<script src="/platformFramework/js/common.js"></script>


<!--exce-->
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jexcel.css">
<link rel="stylesheet" href="/platformFramework/statics/css/v4/jsuites.css">
<link rel="stylesheet" href="/platformFramework/js/v4/jquery-clockpicker.min.css">
<link rel="stylesheet" href="/platformFramework/js/v4/happy-scroll.css">
<!--<link rel="stylesheet" href="/platformFramework/statics/css/v8/jspreadsheet.css">-->
<script src="/platformFramework/js/v4/jexcel.js"></script>
<script src="/platformFramework/js/v4/jsuites.js"></script>
<script src="/platformFramework/js/v4/jquery-clockpicker.min.js"></script>
<script src="/platformFramework/js/v4/happy-scroll.min.js"></script>
<script src="/platformFramework/js/v4/jquery.cookie.min.js"></script>
<!--<script src="/platformFramework/js/v8/jspreadsheet.js"></script>-->    <style type="text/css">
        .upload-list {
            display: inline-block;
            width: 60px;
            height: 60px;
            text-align: center;
            line-height: 60px;
            border: 1px solid transparent;
            border-radius: 4px;
            overflow: hidden;
            background: #fff;
            position: relative;
            box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
            margin-right: 4px;
        }

        .upload-list img {
            width: 100%;
            height: 100%;
        }

        .upload-list-cover {
            display: none;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, .6);
        }

        .upload-list:hover .upload-list-cover {
            display: block;
        }

        .upload-list-cover i {
            color: #fff;
            font-size: 20px;
            cursor: pointer;
            margin: 0 2px;
        }

        .information-sub {
            width: 100%;
            clear: both;
            background-color: #ececec;
            font-size: 14px;
            font-weight: bold;
            color: #495060;
            height: 25px;
            line-height: 25px;
            padding-left: 10px;
        }

        .addColumnBox {
            clear: both;
            width: 100%;
        }

        .coinClo {
            width: 268px;
            float: left
        }

        #log{
            width: 450px;height: 300px;background-color: lightgray;float: right;position: fixed;right: 15%; padding-left: 10px;top: 8%;
            overflow: scroll;
        }
        .addDiv .ivu-form-item-content{margin:0;}
        .myLabel{
            width: 100px;
            line-height: 32px;
            font-size: 12px;
        }
        .diy{
            display: inline-block;
            width: 100%;
            height: 32px;
            line-height: 1.5;
            padding: 4px 7px;
            font-size: 12px;
            border: 1px solid #dddee1;
            border-radius: 4px;
            color: #495060;
            background-color: #fff;
            background-image: none;
            position: relative;
            cursor: text;
            transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
        }
        .autocomplete-suggestions { border: 1px solid #999; background: #FFF; overflow: auto; }
        .autocomplete-suggestion { padding: 2px 5px; white-space: nowrap; overflow: hidden; font-size: 16px;}
        .autocomplete-selected { background: lightgray; }
        .autocomplete-suggestions strong { font-weight: normal; color: #3399FF; }
        .autocomplete-group { padding: 2px 5px; }
        .autocomplete-group strong { display: block; border-bottom: 1px solid #000; }
    </style>
</head>
<body>
<div id="vue-table" v-cloak>
    <div v-show="showList">
        <Row :gutter="16">
            <div class="search-group">
                <i-col span="4">
                    <i-input v-model="q.sendnum" @on-enter="query" placeholder="送评单号">
                    </i-input>
                </i-col>
                <i-col span="4">
                    <i-input v-model="q.nummber" @on-enter="query" placeholder="编号">
                    </i-input>
                </i-col>
                <i-col span="4">
                    <i-input v-model="q.rname" @on-enter="query" placeholder="姓名">
                    </i-input>
                </i-col>
                <i-col span="4">
                    <i-input v-model="q.nickname" @on-enter="query" placeholder="网名">
                    </i-input>
                </i-col>
                <i-button @click="query">查询</i-button>
                <i-button @click="reloadSearch">重置</i-button>
            </div>
            <div class="buttons-group">
                <!-- <i-button type="primary" @click="getItemInfo"><i class="fa fa-hand-o-up"></i>&nbsp;查询</i-button> -->
                                                <i-button type="button" @click="add" class="btn btn-primary">&nbsp;增加钱币</i-button>
                
                
<!--                -->
                                <i-button type="warning" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</i-button>
                
            </div>
        </Row>
        <table id="jqGrid"></table>
    </div>

    <Card v-show="ChangeState">
        <iframe style="height: 90vh;width: 100%;" :src="frameLink"></iframe>
    </Card>

    <Card v-show="showDiyList">
        <h2>录入该送评单号内：
            <label style="color: #d9534f;">{{sendnum}}</label></h2>
        <div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px;margin-bottom: 5px;">钱币基本信息
        </div>

        <i-form ref="formValidate" :model="pjClCoin" :rules="ruleValidate" :label-width="80">
            <div class="addColumnBox">
                <Form-item label="DIY编号" prop="nummber" class="coinClo">
                    <input v-model="pjClCoin.nummber" type="number" placeholder="请输入编号" name="nummber" @blur="come_home" class="diy"/>
                </Form-item>
                <span style="padding-left: 24px;font-size: 16px;}" id="tips">{{tips}}</span>
            </div>
            <div class="addColumnBox">
                <Form-item label="类型" prop="cointype" class="coinClo">
                    <i-select v-model="pjClCoin.cointype" filterable label-in-value style="width: 200px;float:left;" disabled="disabled">
                        <i-option v-for="statusItem in gmList" :value="statusItem.id"
                                  :key ="statusItem.id">{{statusItem.name}}
                        </i-option>
                    </i-select>
                </Form-item>
            </div>

            <div class="addColumnBox">

                <Form-item label="名称1" prop="name" class="coinClo" v-show="outline">
                    <input id="name" class="ivu-input"  v-model="pjClCoin.name" placeholder="名称1" name="name" type="text"/>
                </Form-item>

                <Form-item label="名称2" prop="name2" class="coinClo">
                    <i-input v-model="pjClCoin.name2" placeholder="名称2">
                    </i-input>
                </Form-item>
                <Form-item label="名称3" prop="name3" class="coinClo"  v-show="pjClCoin.cointype=='04'">
                    <i-input v-model="pjClCoin.name3" placeholder="名称3">
                    </i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="版别" prop="edition" class="coinClo" v-show="pjClCoin.cointype!='04'">
                    <i-input v-model="pjClCoin.edition" placeholder="版别"/>
                    </i-input>
                </Form-item>
                <Form-item label="特殊标签"  prop="live" class="coinClo"   v-show="pjClCoin.cointype=='01'" >
                    <i-input v-model="pjClCoin.live" placeholder="特殊标签" >
                    </i-input>
                </Form-item>
                <Form-item label="等级" prop="rank" class="coinClo" v-show="pjClCoin.cointype=='02'">
                    <i-input v-model="pjClCoin.rank" placeholder="等级">
                    </i-input>
                </Form-item>

                <Form-item label="面值" prop="mianzhi" class="coinClo"
                           v-show="(pjClCoin.cointype=='04'||pjClCoin.cointype=='03')">
                    <i-input v-model="pjClCoin.mianzhi" placeholder="面值">
                    </i-input>
                </Form-item>
                <Form-item label="所属县"  prop="live" class="coinClo" v-show="pjClCoin.cointype=='04'" >
                    <i-input v-model="pjClCoin.live" placeholder="所属县" >
                    </i-input>
                </Form-item>
                <Form-item label="集藏名称"  prop="catalog" class="coinClo" v-show="pjClCoin.cointype=='04'" >
                    <i-input v-model="pjClCoin.catalog" placeholder="集藏名称" >
                    </i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="年代" prop="niandai" class="coinClo">
                    <input id="niandai" class="ivu-input" v-model="pjClCoin.niandai" name="niandai" type="text" placeholder="年代">
                </Form-item>

                <Form-item label="材质" prop="ctCaizhi" class="coinClo">
                    <i-input v-model="pjClCoin.ctCaizhi" placeholder="材质">
                    </i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="尺寸" prop="size" class="coinClo">
                    <i-input v-model="pjClCoin.size" placeholder="尺寸">
                    </i-input>
                </Form-item>

                <Form-item label="重量" prop="weight" class="coinClo">
                    <i-input v-model="pjClCoin.weight" placeholder="重量">
                    </i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="年份" prop="year" class="coinClo" v-show="(pjClCoin.cointype=='04')">
                    <i-input v-model="pjClCoin.year" placeholder="年份"></i-input>
                </Form-item>
                <Form-item label="地区" prop="addr" class="coinClo" v-show="(pjClCoin.cointype=='04' || pjClCoin.cointype=='01')">
                    <i-input v-model="pjClCoin.addr" placeholder="地区"></i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="目录" prop="catalog" class="coinClo" v-show="pjClCoin.cointype=='01'">
                    <i-input v-model="pjClCoin.catalog" placeholder="目录"></i-input>
                </Form-item>

                <Form-item label="钱币备注" prop="coindesc" class="coinClo" v-show="pjClCoin.cointype=='01'">
                    <i-input v-model="pjClCoin.coindesc" placeholder="钱币备注"></i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="盒子费用" prop="boxfee" class="coinClo">
                    <i-input v-model="pjClCoin.boxfee" placeholder="盒子费用" type="number"></i-input>
                </Form-item>
            </div>

            <div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px;color: #d9534f;">真伪鉴定（存档）</div>
            <div class="addColumnBox">
                <Form-item label="真伪" prop="result" class="coinClo">
                    <i-select v-model="pjClCoin.result" filterable label-in-value @on-change="clearPrice">
                        <i-option v-for="statusItem in statusList" :value="statusItem.id":key="statusItem.id">{{statusItem.name}}
                        </i-option>
                    </i-select>
                </Form-item>
            </div>

            <div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px;color: #d9534f;">评级打分（存档）</div>
            <div class="addColumnBox" v-show="pjClCoin.cointype=='03'"><!-- 机制币 -->
                <Form-item label="品相类型" prop="jzbScoretype" class="coinClo">
                    <Radio-group v-model="jzbScoretype">
                        <Radio label="1">
                            <span>精致币</span>
                        </Radio>
                        <Radio label="2">
                            <span>样币</span>
                        </Radio>
                        <Radio label="3">
                            <span>其他</span>
                        </Radio>
                    </Radio-group>
                </Form-item>
            </div>

            <!--     品相      start   -->
            <div class="addColumnBox" v-show="pjClCoin.cointype=='03' && jzbScoretype == 1"><!-- 机制币 精制币【品相】 -->
                <Form-item label="品相" prop="score" class="coinClo">
                    <i-select v-model="pjClCoin.scoreshow2" filterable label-in-value>
                        <i-option v-for="statusItem in jzbAllScoreList" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>
            </div>


            <div class="addColumnBox" v-show="pjClCoin.cointype=='03' && jzbScoretype == 2"><!-- 机制币 样币【品相】-->
                <Form-item label="品相" prop="score" class="coinClo">
                    <i-select v-model="pjClCoin.scoreshow2" filterable label-in-value>
                        <i-option v-for="statusItem in ybAllScoreArray" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>
            </div>

            <div class="addColumnBox" v-show="pjClCoin.cointype=='03'  && jzbScoretype == 3"><!-- 机制币 其他【品相】 -->
                <Form-item label="品相" prop="scoreshow" class="coinClo">
                    <i-select v-model="pjClCoin.scoreshow" filterable label-in-value>
                        <i-option v-for="statusItem in jzbScoreList" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>
            </div>

            <div class="addColumnBox" v-show="pjClCoin.cointype=='02'"> <!-- 古钱币【品相】 -->
                <Form-item label="品相" prop="scoreshow" class="coinClo">
                    <i-select v-model="pjClCoin.scoreshow" filterable label-in-value>
                        <i-option v-for="statusItem in gqbScoreList" :value="statusItem.name" :key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>
            </div>

            <!--       银锭        -->

            <div class="addColumnBox" v-show="pjClCoin.cointype=='04'"><!-- 银锭 -->
                <Form-item label="品相" prop="scoreshow" class="coinClo">
                    <i-select v-model="pjClCoin.scoreshow" filterable label-in-value>
                        <i-option v-for="statusItem in ydScoreList" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>


                <Form-item label="铭文" prop="marker" class="coinClo">
                    <i-select v-model="pjClCoin.marker" filterable label-in-value>
                        <i-option v-for="statusItem in yd20List" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>

                <Form-item label="型制" prop="shape" class="coinClo">
                    <i-select v-model="pjClCoin.shape" filterable label-in-value>
                        <i-option v-for="statusItem in yd20List2" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>

            </div>

            <div class="addColumnBox" v-show="pjClCoin.cointype=='01'"><!-- 纸币 -->
                <Form-item label="品相" prop="scoreshow2" class="coinClo">
                    <i-select v-model="pjClCoin.scoreshow2" @on-change="queryzbbq" filterable label-in-value>
                        <!--                    <i-select v-model="pjClCoin.scoreshow2" filterable label-in-value>-->
                        <i-option v-for="statusItem in zbScoreList" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>

                <Form-item prop="extbqs" class="coinClo">
                    <Checkbox-group v-model="pjClCoin.extbqs">
                        <Checkbox :label="extbq.id" v-for="extbq in extbqList">{{extbq.name}}</Checkbox>
                    </Checkbox-group>
                </Form-item>
            </div>
            <!--     品相      end   -->


            <div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px;color: #d9534f;">缺陷修补（存档）</div>
            <div class="addColumnBox">
                <Form-item label="评分备注" prop="scoreDesc" class="coinClo">
                    <i-input v-model="pjClCoin.scoreDesc" placeholder="分数备注"></i-input>
                </Form-item>

                <Form-item label="缺陷代码" prop="qxshow" class="coinClo" v-show="pjClCoin.cointype=='03'">
                    <i-select v-model="pjClCoin.qxshow" filterable label-in-value>
                        <i-option v-for="statusItem in jzbQXList" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>

                <Form-item label="缺陷代码" prop="qxshow" class="coinClo" v-show="pjClCoin.cointype=='02'">
                    <i-select v-model="pjClCoin.qxshow" filterable label-in-value>
                        <i-option v-for="statusItem in gqbQXList" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>

                <Form-item label="缺陷代码" prop="qxshow" class="coinClo" v-show="pjClCoin.cointype=='04'">
                    <i-select v-model="pjClCoin.qxshow" filterable label-in-value>
                        <i-option v-for="statusItem in ydQXList" :value="statusItem.name":key="statusItem.id">{{statusItem.name}}</i-option>
                    </i-select>
                </Form-item>
            </div>

            <div class="information-sub" data-locale="review_the_coin_project" style="margin-top: 10px">其他信息</div>
            <div class="addColumnBox">
                <Form-item label="标准价" prop="bzfee" class="coinClo">
                    <i-input v-model="pjClCoin.bzfee" type="number" placeholder="标准价"></i-input>
                </Form-item>
                <Form-item label="国际价" prop="gjfee" class="coinClo">
                    <i-input v-model="pjClCoin.gjfee" type="number" placeholder="国际价">
                    </i-input>
                </Form-item>
                <Form-item label="折扣" prop="zk" class="coinClo">
                    <i-input v-model="pjClCoin.zk" type="number" placeholder="折扣">
                    </i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="费用" prop="finalfee" class="coinClo">
                    <i-input v-model="pjClCoin.finalfee" :value="getFee(pjClCoin)" type="number" placeholder="费用"readonly="readonly"/></i-input>
                </Form-item>
            </div>

            <div class="addColumnBox">
                <Form-item label="纸币编号" prop="zbnum" class="coinClo" v-show="pjClCoin.cointype=='01'">
                    <i-input v-model="pjClCoin.zbnum" placeholder="纸币编号"></i-input>
                </Form-item>

                <Form-item label="银行名称" prop="bank" class="coinClo" v-show="pjClCoin.cointype=='01'">
                    <i-input v-model="pjClCoin.bank" placeholder="地区"></i-input>
                </Form-item>
            </div>
            <div class="addColumnBox">
                <Form-item label="对外备注" prop="description" class="coinClo">
                    <i-input v-model="pjClCoin.description" placeholder="对外备注"></i-input>
                </Form-item>

                <Form-item label="对内备注" prop="backweight" class="coinClo">
                    <i-input v-model="pjClCoin.backweight" placeholder="对内备注"></i-input>
                </Form-item>
            </div>
            <div class="addColumnBox">
                <Form-item label="藏品简介" prop="intro" class="coinClo" v-show="pjClCoin.cointype=='04'">
                    <textarea rows="10" cols="75"  v-model="pjClCoin.intro" placeholder="请填写..." style="line-height: normal;font-size: 14px;"></textarea>
                </Form-item>
            </div>
            <div class="addColumnBox">
                <Form-item label="图片" prop="uploadList">
                    <template>
                        <div class="upload-list" v-for="item in uploadList">
                            <template>
                                <img :src="item.imgUrl"/>
                                <div class="upload-list-cover">
                                    <Icon type="ios-eye-outline" @click.native="handleView(item.imgUrl)"></Icon>
                                    <Icon type="ios-trash-outline" @click.native="handleRemove(item)"></Icon>
                                </div>
                            </template>
                            <template v-else>
                                <Progress v-if="item.showProgress" :percent="item.percentage" hide-info></Progress>
                            </template>
                        </div>
                        <Upload
                                ref="upload"
                                :show-upload-list="false"
                                :default-file-list="uploadList2"
                                :on-success="handleSuccess"
                                :format="['jpg','jpeg','png']"
                                :max-size="10480"
                                :on-format-error="handleFormatError"
                                :on-exceeded-size="handleMaxSize"
                                :before-upload="handleBeforeUpload"
                                multiple
                                type="drag"
                                action="../sys/oss/upload"
                                style="display: inline-block;width:58px;">

                            <div style="width: 58px;height:58px;line-height: 58px;">
                                <Icon type="camera" size="20"></Icon>
                            </div>

                        </Upload>

                        <Modal title="查看图片" v-model="visible">
                            <img :src="imgName" v-if="visible" style="width: 100%"/>
                        </Modal>

                    </template>
                </Form-item>

                <Form-item>
                    <i-button type="primary" v-if="read==0" @click="handleSubmit('formValidate')" id="submit"><i class="fa fa-check"></i>&nbsp;提交</i-button>
                    <i-button type="success" @click="reload" style="margin-left: 8px"/><i class="fa fa-arrow-left"></i>&nbsp;返回</i-button>
                </Form-item>
            </div>
        </i-form>
    </Card>
</div>

<script src="/platformFramework/js/sys/sendform.js?_1749996654353"></script>
<script src="/platformFramework/statics/libs/jquery.autocomplete2017.js?_1749996654353"></script>
</body>
</html>