[2m2025-08-05 00:06:21.344[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,CANVAS_CONFIG,CANVAS_DATA,ELEMENTS,PREVIEW_IMAGE,IS_DEFAULT,STATUS,DESCRIPTION,USE_COUNT,LAST_USED_TIME,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,VERSION,DELETED,EXT_FIELD1,EXT_FIELD2,EXT_FIELD3    FROM  COLOR_LABEL_TEMPLATE     WHERE DELETED=0
[2m2025-08-05 00:06:21.425[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,CANVAS_CONFIG,CANVAS_DATA,ELEMENTS,PREVIEW_IMAGE,IS_DEFAULT,STATUS,DESCRIPTION,USE_COUNT,LAST_USED_TIME,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,VERSION,DELETED,EXT_FIELD1,EXT_FIELD2,EXT_FIELD3    FROM  COLOR_LABEL_TEMPLATE     WHERE DELETED=0
[2m2025-08-05 00:06:21.426[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, CANVAS_CONFIG, CANVAS_DATA, ELEMENTS, PREVIEW_IMAGE, IS_DEFAULT, STATUS, DESCRIPTION, USE_COUNT, LAST_USED_TIME, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, VERSION, DELETED, EXT_FIELD1, EXT_FIELD2, EXT_FIELD3 FROM COLOR_LABEL_TEMPLATE WHERE DELETED = 0
[2m2025-08-05 00:06:21.445[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM COLOR_LABEL_TEMPLATE WHERE DELETED = 0
[2m2025-08-05 00:06:21.445[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-05 00:06:21.450[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-05 00:06:21.459[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, CANVAS_CONFIG, CANVAS_DATA, ELEMENTS, PREVIEW_IMAGE, IS_DEFAULT, STATUS, DESCRIPTION, USE_COUNT, LAST_USED_TIME, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, VERSION, DELETED, EXT_FIELD1, EXT_FIELD2, EXT_FIELD3 FROM COLOR_LABEL_TEMPLATE WHERE DELETED = 0 ORDER BY create_time DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-05 00:06:21.459[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectList                  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-05 00:06:21.471[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-8][0;39m [36mc.p.s.b.m.C.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-05 00:06:30.382[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-05 00:06:30.397[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-05 00:06:30.398[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-05 00:06:30.417[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-05 00:06:30.418[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-05 00:06:30.423[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-05 00:06:30.441[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-05 00:06:30.442[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-05 00:06:30.458[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-2][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-05 00:06:31.023[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,CANVAS_CONFIG,CANVAS_DATA,ELEMENTS,PREVIEW_IMAGE,IS_DEFAULT,STATUS,DESCRIPTION,USE_COUNT,LAST_USED_TIME,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,VERSION,DELETED,EXT_FIELD1,EXT_FIELD2,EXT_FIELD3    FROM  COLOR_LABEL_TEMPLATE     WHERE DELETED=0
[2m2025-08-05 00:06:31.032[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,TEMPLATE_NAME,TEMPLATE_TYPE,CANVAS_CONFIG,CANVAS_DATA,ELEMENTS,PREVIEW_IMAGE,IS_DEFAULT,STATUS,DESCRIPTION,USE_COUNT,LAST_USED_TIME,CREATE_USER,CREATE_TIME,UPDATE_USER,UPDATE_TIME,VERSION,DELETED,EXT_FIELD1,EXT_FIELD2,EXT_FIELD3    FROM  COLOR_LABEL_TEMPLATE     WHERE DELETED=0
[2m2025-08-05 00:06:31.033[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, CANVAS_CONFIG, CANVAS_DATA, ELEMENTS, PREVIEW_IMAGE, IS_DEFAULT, STATUS, DESCRIPTION, USE_COUNT, LAST_USED_TIME, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, VERSION, DELETED, EXT_FIELD1, EXT_FIELD2, EXT_FIELD3 FROM COLOR_LABEL_TEMPLATE WHERE DELETED = 0
[2m2025-08-05 00:06:31.046[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.C.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM COLOR_LABEL_TEMPLATE WHERE DELETED = 0
[2m2025-08-05 00:06:31.046[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.C.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-05 00:06:31.051[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.C.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-05 00:06:31.063[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.C.selectList                  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, TEMPLATE_NAME, TEMPLATE_TYPE, CANVAS_CONFIG, CANVAS_DATA, ELEMENTS, PREVIEW_IMAGE, IS_DEFAULT, STATUS, DESCRIPTION, USE_COUNT, LAST_USED_TIME, CREATE_USER, CREATE_TIME, UPDATE_USER, UPDATE_TIME, VERSION, DELETED, EXT_FIELD1, EXT_FIELD2, EXT_FIELD3 FROM COLOR_LABEL_TEMPLATE WHERE DELETED = 0 ORDER BY create_time DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-05 00:06:31.063[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.C.selectList                  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-05 00:06:31.305[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-5][0;39m [36mc.p.s.b.m.C.selectList                  [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-05 00:06:33.073[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m original SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-05 00:06:33.091[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m SQL to parse, SQL: SELECT    ID,SENDNUM,STATUS,STATUS_VALUE,CREATOR,DEPT_NAME,INUPTTIME,UPDATETIME,MEMBERID,ADDR,RNAME,RPHONE,RTYPE,SAFEMONEY,POSTFEE,INITFEE,POST,RETURNFEE,ISDOWNLINE,SUM_FINAL_FEE,QBTOTAL,NICKNAME,PAYMENTSTAT,GQBSTEP,PAYMENTTYPE,APPENDFEE,RETURNFEEOPTION,WAITPAYFEE,RETURNFEEOPTION2,JZBSTEP,URGENTTYPE,BATCHTYPE,PROJECTS1,PROJECTS2,PROJECTS3,PROJECTS4,SENDMEMO,ID_PICS,TC_CREATE_AGENT,TC_CREATE_DATE,RCODE,PAYEDFEE,AGENTCODE,PAYEDDESC,ADDR_PRO,ADDR_CITY,ADDR_CUN,ADDR_BUD,COUNTRY,SENDREMARK,SUMFEE,COUNT_ITEM,ISSMS,BACKWEIGHT,DESCRIPTION,STYPE,SUMFEETW,COIN_COUNT,SUM_BOX_FEE,ACCNUM,IFYOU,CHECK_STATUS,CHECKER,CHECK_TIME,CHECK_REMARK,FULLY_OPEN    FROM  PJ_O_SENDFORM
[2m2025-08-05 00:06:33.094[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.u.h.CustomDataPermissionInterceptor [0;39m [2m:[0;39m parse the finished SQL: SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM
[2m2025-08-05 00:06:33.114[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM PJ_O_SENDFORM
[2m2025-08-05 00:06:33.114[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-05 00:06:33.182[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.P.selectList_mpCount          [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-05 00:06:33.202[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT ID, SENDNUM, STATUS, STATUS_VALUE, CREATOR, DEPT_NAME, INUPTTIME, UPDATETIME, MEMBERID, ADDR, RNAME, RPHONE, RTYPE, SAFEMONEY, POSTFEE, INITFEE, POST, RETURNFEE, ISDOWNLINE, SUM_FINAL_FEE, QBTOTAL, NICKNAME, PAYMENTSTAT, GQBSTEP, PAYMENTTYPE, APPENDFEE, RETURNFEEOPTION, WAITPAYFEE, RETURNFEEOPTION2, JZBSTEP, URGENTTYPE, BATCHTYPE, PROJECTS1, PROJECTS2, PROJECTS3, PROJECTS4, SENDMEMO, ID_PICS, TC_CREATE_AGENT, TC_CREATE_DATE, RCODE, PAYEDFEE, AGENTCODE, PAYEDDESC, ADDR_PRO, ADDR_CITY, ADDR_CUN, ADDR_BUD, COUNTRY, SENDREMARK, SUMFEE, COUNT_ITEM, ISSMS, BACKWEIGHT, DESCRIPTION, STYPE, SUMFEETW, COIN_COUNT, SUM_BOX_FEE, ACCNUM, IFYOU, CHECK_STATUS, CHECKER, CHECK_TIME, CHECK_REMARK, FULLY_OPEN FROM PJ_O_SENDFORM ORDER BY inupttime DESC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-05 00:06:33.203[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 10(Long), 0(Long)
[2m2025-08-05 00:06:33.271[0;39m [32mDEBUG[0;39m [35m31967[0;39m [2m---[0;39m [2m[nio-7070-exec-6][0;39m [36mc.p.s.b.m.PjOSendformMapper.selectList  [0;39m [2m:[0;39m <==      Total: 1
