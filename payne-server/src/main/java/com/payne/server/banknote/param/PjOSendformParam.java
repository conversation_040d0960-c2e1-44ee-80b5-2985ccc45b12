package com.payne.server.banknote.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.payne.core.annotation.QueryField;
import com.payne.core.annotation.QueryType;
import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 送评单查询参数
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PjOSendformParam extends BaseParam {
    
    private static final long serialVersionUID = 1L;

    /**
     * 送评单号
     */
    @QueryField(type = QueryType.LIKE)
    private String sendnum;

    /**
     * 编号
     */
    @QueryField(value = "sendnum", type = QueryType.LIKE)
    private String nummber;

    /**
     * 真实姓名
     */
    @QueryField(type = QueryType.LIKE)
    private String rname;

    /**
     * 网名
     */
    @QueryField(type = QueryType.LIKE)
    private String nickname;

    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建公司
     */
    private String deptName;

    /**
     * 状态
     */
    @QueryField(type = QueryType.EQ)
    private Integer status;

    /**
     * 财务核对状态
     */
    @QueryField(type = QueryType.EQ)
    private Integer ifyou;

    /**
     * 评单审核状态
     */
    @QueryField(type = QueryType.EQ)
    private Integer checkStatus;

    /**
     * 扫码审核状态
     */
    @QueryField(type = QueryType.EQ)
    private Integer fullyOpen;

    /**
     * 支付状态
     */
    @QueryField(type = QueryType.EQ)
    private Integer paymentstat;

    /**
     * 项目类型
     */
    @QueryField(type = QueryType.EQ)
    private Integer stype;
} 