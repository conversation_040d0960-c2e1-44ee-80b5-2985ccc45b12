<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payne.server.banknote.mapper.PjOSendformMapper">

    <!-- 查询指定前缀的送评单号最大序号 -->
    <select id="getMaxSendnumSequence" resultType="java.lang.Integer">
        SELECT NVL(MAX(
            CASE
                WHEN SENDNUM LIKE #{datePrefix} || '%'
                THEN TO_NUMBER(SUBSTR(SENDNUM, LENGTH(#{datePrefix}) + 1))
                ELSE 0
            END
        ), 0) AS maxSeq
        FROM PJ_O_SENDFORM
        WHERE SENDNUM LIKE #{datePrefix} || '%'
    </select>

</mapper>
