package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 送评单实体类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Entity
@Table(name = "PJ_O_SENDFORM")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("PJ_O_SENDFORM")
public class PjOSendform implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 送评单号
     */
    @Column(name = "SENDNUM", columnDefinition = "VARCHAR2(50)")
    @TableField("SENDNUM")
    private String sendnum;
    
    /**
     * 状态
     */
    @Column(name = "STATUS", columnDefinition = "NUMBER(2)")
    @TableField("STATUS")
    private Integer status;
    
    /**
     * 状态值
     */
    @Column(name = "STATUS_VALUE", columnDefinition = "NUMBER(2)")
    @TableField("STATUS_VALUE")
    private Integer statusValue;
    
    /**
     * 创建人
     */
    @Column(name = "CREATOR",columnDefinition = "VARCHAR2(100)")
    @TableField(value = "CREATOR")
    private String creator;
    /**
     * 创建公司
     */
    @Column(name = "DEPT_NAME",columnDefinition = "VARCHAR2(255)")
    @TableField(value = "DEPT_NAME")
    private String deptName;
    /**
     * 创建时间
     */
    @Column(name = "INUPTTIME")
    @TableField(value = "INUPTTIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime inupttime;

    /**
     * 更新时间
     */
    @Column(name = "UPDATETIME")
    @TableField(value = "UPDATETIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatetime;
    
    /**
     * 会员ID
     */
    @Column(name = "MEMBERID", columnDefinition = "VARCHAR2(50)")
    @TableField("MEMBERID")
    private String memberid;
    
    /**
     * 地址
     */
    @Column(name = "ADDR", columnDefinition = "VARCHAR2(500)")
    @TableField("ADDR")
    private String addr;
    
    /**
     * 真实姓名
     */
    @Column(name = "RNAME", columnDefinition = "VARCHAR2(100)")
    @TableField("RNAME")
    private String rname;
    
    /**
     * 联系电话
     */
    @Column(name = "RPHONE", columnDefinition = "VARCHAR2(20)")
    @TableField("RPHONE")
    private String rphone;
    
    /**
     * 收货类型
     */
    @Column(name = "RTYPE", columnDefinition = "NUMBER(2)")
    @TableField("RTYPE")
    private Integer rtype;
    
    /**
     * 保价金额
     */
    @Column(name = "SAFEMONEY", columnDefinition = "NUMBER(10,2)")
    @TableField("SAFEMONEY")
    private BigDecimal safemoney;
    
    /**
     * 邮费
     */
    @Column(name = "POSTFEE", columnDefinition = "NUMBER(10,2)")
    @TableField("POSTFEE")
    private BigDecimal postfee;
    
    /**
     * 初始费用
     */
    @Column(name = "INITFEE", columnDefinition = "NUMBER(10,2)")
    @TableField("INITFEE")
    private BigDecimal initfee;
    
    /**
     * 邮寄信息
     */
    @Column(name = "POST", columnDefinition = "VARCHAR2(50)")
    @TableField("POST")
    private String post;
    
    /**
     * 退回费用
     */
    @Column(name = "RETURNFEE", columnDefinition = "NUMBER(10,2)")
    @TableField("RETURNFEE")
    private BigDecimal returnfee;
    
    /**
     * 是否线下
     */
    @Column(name = "ISDOWNLINE", columnDefinition = "NUMBER(2)")
    @TableField("ISDOWNLINE")
    private Integer isdownline;
    
    /**
     * 最终费用总计
     */
    @Column(name = "SUM_FINAL_FEE", columnDefinition = "NUMBER(10,2)")
    @TableField("SUM_FINAL_FEE")
    private BigDecimal sumFinalFee;
    
    /**
     * 钱币总数
     */
    @Column(name = "QBTOTAL", columnDefinition = "NUMBER(10,2)")
    @TableField("QBTOTAL")
    private BigDecimal qbtotal;
    
    /**
     * 网名
     */
    @Column(name = "NICKNAME", columnDefinition = "VARCHAR2(100)")
    @TableField("NICKNAME")
    private String nickname;
    
    /**
     * 支付状态
     */
    @Column(name = "PAYMENTSTAT", columnDefinition = "NUMBER(2)")
    @TableField("PAYMENTSTAT")
    private Integer paymentstat;
    
    /**
     * 古钱币步骤
     */
    @Column(name = "GQBSTEP", columnDefinition = "VARCHAR2(50)")
    @TableField("GQBSTEP")
    private String gqbstep;
    
    /**
     * 支付类型
     */
    @Column(name = "PAYMENTTYPE", columnDefinition = "NUMBER(2)")
    @TableField("PAYMENTTYPE")
    private Integer paymenttype;
    
    /**
     * 附加费用
     */
    @Column(name = "APPENDFEE", columnDefinition = "NUMBER(10,2)")
    @TableField("APPENDFEE")
    private BigDecimal appendfee;
    
    /**
     * 退费选项
     */
    @Column(name = "RETURNFEEOPTION", columnDefinition = "NUMBER(2)")
    @TableField("RETURNFEEOPTION")
    private Integer returnfeeoption;
    
    /**
     * 待支付费用
     */
    @Column(name = "WAITPAYFEE", columnDefinition = "NUMBER(10,2)")
    @TableField("WAITPAYFEE")
    private BigDecimal waitpayfee;
    
    /**
     * 退费选项2
     */
    @Column(name = "RETURNFEEOPTION2", columnDefinition = "NUMBER(2)")
    @TableField("RETURNFEEOPTION2")
    private Integer returnfeeoption2;
    
    /**
     * 机制币步骤
     */
    @Column(name = "JZBSTEP", columnDefinition = "VARCHAR2(50)")
    @TableField("JZBSTEP")
    private String jzbstep;
    

    
    /**
     * 加急类型
     */
    @Column(name = "URGENTTYPE", columnDefinition = "NUMBER(2)")
    @TableField("URGENTTYPE")
    private Integer urgenttype;
    
    /**
     * 批次类型
     */
    @Column(name = "BATCHTYPE", columnDefinition = "NUMBER(2)")
    @TableField("BATCHTYPE")
    private Integer batchtype;
    
    /**
     * 项目1
     */
    @Column(name = "PROJECTS1", columnDefinition = "VARCHAR2(500)")
    @TableField("PROJECTS1")
    private String projects1;
    
    /**
     * 项目2
     */
    @Column(name = "PROJECTS2", columnDefinition = "VARCHAR2(500)")
    @TableField("PROJECTS2")
    private String projects2;
    
    /**
     * 项目3
     */
    @Column(name = "PROJECTS3", columnDefinition = "VARCHAR2(500)")
    @TableField("PROJECTS3")
    private String projects3;
    
    /**
     * 项目4
     */
    @Column(name = "PROJECTS4", columnDefinition = "VARCHAR2(500)")
    @TableField("PROJECTS4")
    private String projects4;
    
    /**
     * 发送备注
     */
    @Column(name = "SENDMEMO", columnDefinition = "VARCHAR2(1000)")
    @TableField("SENDMEMO")
    private String sendmemo;
    
    /**
     * 身份证照片
     */
    @Column(name = "ID_PICS", columnDefinition = "VARCHAR2(500)")
    @TableField("ID_PICS")
    private String idPics;
    
    /**
     * 创建代理
     */
    @Column(name = "TC_CREATE_AGENT", columnDefinition = "VARCHAR2(100)")
    @TableField("TC_CREATE_AGENT")
    private String tcCreateAgent;
    
    /**
     * 创建日期
     */
    @Column(name = "TC_CREATE_DATE")
    @TableField("TC_CREATE_DATE")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date tcCreateDate;
    
    /**
     * 收货代码
     */
    @Column(name = "RCODE", columnDefinition = "VARCHAR2(50)")
    @TableField("RCODE")
    private String rcode;
    
    /**
     * 已支付费用
     */
    @Column(name = "PAYEDFEE", columnDefinition = "NUMBER(10,2)")
    @TableField("PAYEDFEE")
    private BigDecimal payedfee;
    
    /**
     * 代理代码
     */
    @Column(name = "AGENTCODE", columnDefinition = "VARCHAR2(50)")
    @TableField("AGENTCODE")
    private String agentcode;
    
    /**
     * 支付描述
     */
    @Column(name = "PAYEDDESC", columnDefinition = "VARCHAR2(500)")
    @TableField("PAYEDDESC")
    private String payeddesc;
    
    /**
     * 省份
     */
    @Column(name = "ADDR_PRO", columnDefinition = "VARCHAR2(50)")
    @TableField("ADDR_PRO")
    private String addrPro;
    
    /**
     * 城市
     */
    @Column(name = "ADDR_CITY", columnDefinition = "VARCHAR2(50)")
    @TableField("ADDR_CITY")
    private String addrCity;
    
    /**
     * 县区
     */
    @Column(name = "ADDR_CUN", columnDefinition = "VARCHAR2(50)")
    @TableField("ADDR_CUN")
    private String addrCun;
    
    /**
     * 详细地址
     */
    @Column(name = "ADDR_BUD", columnDefinition = "VARCHAR2(200)")
    @TableField("ADDR_BUD")
    private String addrBud;
    
    /**
     * 国家
     */
    @Column(name = "COUNTRY", columnDefinition = "VARCHAR2(50)")
    @TableField("COUNTRY")
    private String country;
    
    /**
     * 发送备注
     */
    @Column(name = "SENDREMARK", columnDefinition = "VARCHAR2(1000)")
    @TableField("SENDREMARK")
    private String sendremark;
    
    /**
     * 费用总计
     */
    @Column(name = "SUMFEE", columnDefinition = "NUMBER(10,2)")
    @TableField("SUMFEE")
    private BigDecimal sumfee;
    
    /**
     * 项目数量
     */
    @Column(name = "COUNT_ITEM", columnDefinition = "NUMBER(10)")
    @TableField("COUNT_ITEM")
    private Integer countItem;
    
    /**
     * 是否短信
     */
    @Column(name = "ISSMS", columnDefinition = "NUMBER(2)")
    @TableField("ISSMS")
    private Integer issms;
    
    /**
     * 退回重量
     */
    @Column(name = "BACKWEIGHT", columnDefinition = "VARCHAR2(50)")
    @TableField("BACKWEIGHT")
    private String backweight;
    
    /**
     * 描述
     */
    @Column(name = "DESCRIPTION", columnDefinition = "VARCHAR2(1000)")
    @TableField("DESCRIPTION")
    private String description;
    
    /**
     * 类型
     */
    @Column(name = "STYPE", columnDefinition = "NUMBER(2)")
    @TableField("STYPE")
    private Integer stype;
    
    /**
     * 总费用(台币)
     */
    @Column(name = "SUMFEETW", columnDefinition = "NUMBER(10,2)")
    @TableField("SUMFEETW")
    private BigDecimal sumfeetw;
    
    /**
     * 钱币数量
     */
    @Column(name = "COIN_COUNT", columnDefinition = "NUMBER(10)")
    @TableField("COIN_COUNT")
    private Integer coinCount;
    
    /**
     * 盒子费用总计
     */
    @Column(name = "SUM_BOX_FEE", columnDefinition = "NUMBER(10,2)")
    @TableField("SUM_BOX_FEE")
    private BigDecimal sumBoxFee;
    
    /**
     * 账号
     */
    @Column(name = "ACCNUM", columnDefinition = "VARCHAR2(50)")
    @TableField("ACCNUM")
    private String accnum;
    
    /**
     * 财务核对 0-否 1-已核对
     */
    @Column(name = "IFYOU", columnDefinition = "NUMBER(2)")
    @TableField("IFYOU")
    private Integer ifyou;
    
    /**
     * 评单审核状态 0-未审核 1-审核通过 2-审核驳回
     */
    @Column(name = "CHECK_STATUS", columnDefinition = "NUMBER(2)")
    @TableField("CHECK_STATUS")
    private Integer checkStatus;

    /**
     * 审核人
     */
    @Column(name = "CHECKER", columnDefinition = "VARCHAR2(100)")
    @TableField("CHECKER")
    private String checker;

    /**
     * 审核时间
     */
    @Column(name = "CHECK_TIME")
    @TableField("CHECK_TIME")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date checkTime;

    /**
     * 审核意见/驳回原因
     */
    @Column(name = "CHECK_REMARK", columnDefinition = "VARCHAR2(1000)")
    @TableField("CHECK_REMARK")
    private String checkRemark;

    /**
     * 扫码审核 0-未开启 1-已开启
     */
    @Column(name = "FULLY_OPEN", columnDefinition = "NUMBER(2)")
    @TableField("FULLY_OPEN")
    private Integer fullyOpen;
    
    /**
     * 送评单明细列表
     */
    @Transient
    @TableField(exist = false)
    private List<PjOSendformItem> pjOSendformItemList;
} 