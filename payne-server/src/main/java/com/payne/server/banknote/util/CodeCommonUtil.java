package com.payne.server.banknote.util;

import java.util.*;

/**
 * 临时代码工具类
 * 用于替代缺失的 com.payne.upms.code.util.CodeCommonUtil
 * 
 * <AUTHOR>
 * @since 2025-01-28
 */
public class CodeCommonUtil {

    /**
     * 根据代码获取钱币类型名称
     * @param code 钱币类型代码
     * @return 钱币类型名称
     */
    public static String getCoinTypeNameByCode(String code) {
        if (code == null) {
            return "";
        }
        
        switch (code) {
            case "ancientCoin":
                return "古钱币";
            case "machineCoin":
                return "机制币";
            case "silverIngot":
                return "银锭";
            case "banknote":
                return "纸币";
            default:
                return code;
        }
    }

    /**
     * 获取钱币类型列表
     * @return 钱币类型列表
     */
    public static List<Map<String, Object>> getCoinTypes() {
        List<Map<String, Object>> coinTypes = new ArrayList<>();
        
        Map<String, Object> ancientCoin = new HashMap<>();
        ancientCoin.put("code", "ancientCoin");
        ancientCoin.put("name", "古钱币");
        coinTypes.add(ancientCoin);
        
        Map<String, Object> machineCoin = new HashMap<>();
        machineCoin.put("code", "machineCoin");
        machineCoin.put("name", "机制币");
        coinTypes.add(machineCoin);
        
        Map<String, Object> silverIngot = new HashMap<>();
        silverIngot.put("code", "silverIngot");
        silverIngot.put("name", "银锭");
        coinTypes.add(silverIngot);
        
        Map<String, Object> banknote = new HashMap<>();
        banknote.put("code", "banknote");
        banknote.put("name", "纸币");
        coinTypes.add(banknote);
        
        return coinTypes;
    }

    /**
     * 生成序列号
     * @return 生成的序列号
     */
    public static String generateSerialNumber() {
        return "SN" + System.currentTimeMillis();
    }

    /**
     * 验证代码格式
     * @param code 代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return code != null && !code.trim().isEmpty();
    }
}
