package com.payne.server.banknote.enums;

/**
 * 扫码录入常量配置
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public class ScanInputConstants {
    
    /**
     * 钱币类型代码映射
     */
    public static final class CoinTypeCode {
        /** 纸币 */
        public static final String BANKNOTE = "01";
        /** 古钱币 */
        public static final String ANCIENT_COIN = "02";
        /** 机制币 */
        public static final String MACHINE_COIN = "03";
        /** 银锭 */
        public static final String SILVER_INGOT = "04";
    }
    
    /**
     * 条形码前缀
     */
    public static final class BarcodePrefix {
        /** 纸币 */
        public static final String BANKNOTE = "ZK";
        /** 古钱币 */
        public static final String ANCIENT_COIN = "GQ";
        /** 机制币 */
        public static final String MACHINE_COIN = "JZ";
        /** 银锭 */
        public static final String SILVER_INGOT = "YD";
    }
    
    /**
     * 真伪鉴定结果
     */
    public static final class Authenticity {
        /** 真 */
        public static final String GENUINE = "真";
        /** 假 */
        public static final String FAKE = "假";
        /** 不确定 */
        public static final String UNCERTAIN = "不确定";
    }
    
    /**
     * 盒子类型
     */
    public static final class BoxType {
        /** 标准盒 */
        public static final String STANDARD = "标准盒";
        /** 加厚盒 */
        public static final String THICK = "加厚盒";
        /** 超厚盒 */
        public static final String SUPER_THICK = "超厚盒";
        /** 特制盒 */
        public static final String SPECIAL = "特制盒";
        /** 不装盒 */
        public static final String NO_BOX = "不装盒";
        /** 自定义 */
        public static final String CUSTOM = "自定义";
    }
    
    /**
     * 追加类型
     */
    public static final class AddType {
        /** 无追加 */
        public static final Integer NONE = 0;
        /** 追加该送评单所有订单 */
        public static final Integer SENDFORM_ALL = 1;
        /** 追加该鉴定单所有订单 */
        public static final Integer APPRAISAL_ALL = 2;
    }
    
    /**
     * 操作类型
     */
    public static final class OperationType {
        /** 扫码查询 */
        public static final String SCAN_QUERY = "scan_query";
        /** 批量更新 */
        public static final String BATCH_UPDATE = "batch_update";
        /** 数据导出 */
        public static final String DATA_EXPORT = "data_export";
        /** 条码验证 */
        public static final String BARCODE_VALIDATE = "barcode_validate";
        /** 统计查询 */
        public static final String STATISTICS_QUERY = "statistics_query";
    }
    
    /**
     * 导出格式
     */
    public static final class ExportFormat {
        /** CSV格式 */
        public static final String CSV = "csv";
        /** Excel格式 */
        public static final String EXCEL = "excel";
        /** PDF格式 */
        public static final String PDF = "pdf";
    }
    
    /**
     * 默认配置值
     */
    public static final class DefaultConfig {
        /** 默认页面大小 */
        public static final Integer PAGE_SIZE = 20;
        /** 默认批量操作限制 */
        public static final Integer BATCH_LIMIT = 100;
        /** 默认条码生成限制 */
        public static final Integer GENERATE_LIMIT = 100;
        /** 默认导出文件名前缀 */
        public static final String EXPORT_FILENAME_PREFIX = "scan_data_";
        /** 默认等级 */
        public static final String DEFAULT_GRADE = "Superb Gem Unc68";
        /** 默认真伪 */
        public static final String DEFAULT_AUTHENTICITY = "真";
    }
    
    /**
     * 响应状态
     */
    public static final class ResponseStatus {
        /** 成功 */
        public static final Boolean SUCCESS = true;
        /** 失败 */
        public static final Boolean FAILED = false;
    }
    
    /**
     * 消息提示
     */
    public static final class Message {
        /** 查询成功 */
        public static final String QUERY_SUCCESS = "查询成功";
        /** 查询失败 */
        public static final String QUERY_FAILED = "查询失败";
        /** 未找到数据 */
        public static final String DATA_NOT_FOUND = "未找到对应的钱币信息";
        /** 批量更新成功 */
        public static final String BATCH_UPDATE_SUCCESS = "批量更新成功";
        /** 批量更新失败 */
        public static final String BATCH_UPDATE_FAILED = "批量更新失败";
        /** 删除成功 */
        public static final String DELETE_SUCCESS = "删除成功";
        /** 删除失败 */
        public static final String DELETE_FAILED = "删除失败";
        /** 导出成功 */
        public static final String EXPORT_SUCCESS = "导出成功";
        /** 导出失败 */
        public static final String EXPORT_FAILED = "导出失败";
        /** 验证成功 */
        public static final String VALIDATE_SUCCESS = "验证成功";
        /** 验证失败 */
        public static final String VALIDATE_FAILED = "验证失败";
        /** 生成成功 */
        public static final String GENERATE_SUCCESS = "生成成功";
        /** 生成失败 */
        public static final String GENERATE_FAILED = "生成失败";
    }
} 