package com.payne.server.banknote.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payne.server.banknote.entity.PjOSendformItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 送评单明细Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface PjOSendformItemMapper extends BaseMapper<PjOSendformItem> {
    
    /**
     * 根据送评单号查询明细列表
     */
    List<PjOSendformItem> selectBySendnum(@Param("sendnum") String sendnum);
    
    /**
     * 根据送评单号删除明细
     */
    int deleteBySendnum(@Param("sendnum") String sendnum);

    /**
     * 查询指定前缀和日期的最大序号
     * @param prefix 钱币类型前缀（如ZK、GQ等）
     * @param dateStr 日期字符串（yyMM格式）
     * @return 最大序号，如果没有记录则返回0
     */
    Integer getMaxSequenceNumber(@Param("prefix") String prefix, @Param("dateStr") String dateStr);
} 