package com.payne.server.banknote.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.server.banknote.dto.ScanQueryDto;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.service.ScanInputService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 扫码录入控制器
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/api/banknote/scan")
public class ScanInputController extends BaseController {
    
    @Resource
    private ScanInputService scanInputService;

    /**
     * 扫码查询钱币信息（支持送评单号、送评条码和钱币编号）
     */
    @PreAuthorize("hasAuthority('banknote:scan:query')")
    @OperationLog
    @PostMapping("/query")
    public ApiResult<?> queryCoinsByScan(@RequestBody ScanQueryDto queryDto) {
        try {
            List<PjOSendformItem> list = scanInputService.queryCoinsByScan(queryDto);
            return success(list);
        } catch (Exception e) {
            return fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 通用扫码查询接口（GET方式，支持送评条码或钱币编号）
     */
    @PreAuthorize("hasAuthority('banknote:scan:query')")
    @OperationLog
    @GetMapping("/scan")
    public ApiResult<?> scanCode(@RequestParam(required = false) String diyCode,
                                 @RequestParam(required = false) String serialNumber) {
        try {
            ScanQueryDto queryDto = new ScanQueryDto();
            queryDto.setDiyCode(diyCode);
            queryDto.setSerialNumber(serialNumber);
            List<PjOSendformItem> list = scanInputService.queryCoinsByScan(queryDto);
            return success(list);
        } catch (Exception e) {
            return fail("扫码查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新钱币信息
     */
    @PreAuthorize("hasAuthority('banknote:scan:update')")
    @OperationLog
    @PostMapping("/batchUpdate")
    public ApiResult<?> batchUpdateCoins(@RequestBody List<PjOSendformItem> coinList) {
        try {
            boolean result = scanInputService.batchUpdateCoins(coinList);
            if (result) {
                return success("批量更新成功");
            } else {
                return fail("批量更新失败");
            }
        } catch (Exception e) {
            return fail("批量更新失败：" + e.getMessage());
        }
    }

    /**
     * 根据送评单号查询钱币列表
     */
    @PreAuthorize("hasAuthority('banknote:scan:list')")
    @OperationLog
    @GetMapping("/getBySendnum")
    public ApiResult<?> getCoinsBySendnum(@RequestParam String sendnum) {
        try {
            List<PjOSendformItem> result = scanInputService.getCoinsBySendnum(sendnum);
            return success(result);
        } catch (Exception e) {
            return fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据钱币ID删除钱币
     */
    @PreAuthorize("hasAuthority('banknote:scan:remove')")
    @OperationLog
    @PostMapping("/remove")
    public ApiResult<?> removeCoins(@RequestBody List<String> ids) {
        try {
            boolean result = scanInputService.removeCoins(ids);
            if (result) {
                return success("删除成功");
            } else {
                return fail("删除失败");
            }
        } catch (Exception e) {
            return fail("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取钱币类型字典
     */
    @GetMapping("/coinTypes")
    public ApiResult<?> getCoinTypes() {
        try {
            List<Map<String, Object>> result = scanInputService.getCoinTypes();
            return success(result);
        } catch (Exception e) {
            return fail("获取钱币类型失败：" + e.getMessage());
        }
    }

    /**
     * 获取真伪选项字典
     */
    @GetMapping("/resultOptions")
    public ApiResult<?> getResultOptions() {
        try {
            List<Map<String, Object>> result = scanInputService.getResultOptions();
            return success(result);
        } catch (Exception e) {
            return fail("获取真伪选项失败：" + e.getMessage());
        }
    }

    /**
     * 获取盒子类型字典
     */
    @GetMapping("/boxTypes")
    public ApiResult<?> getBoxTypes() {
        try {
            List<Map<String, Object>> result = scanInputService.getBoxTypes();
            return success(result);
        } catch (Exception e) {
            return fail("获取盒子类型失败：" + e.getMessage());
        }
    }

    /**
     * 根据送评条码获取钱币详情
     */
    @PreAuthorize("hasAuthority('banknote:scan:detail')")
    @OperationLog
    @GetMapping("/detail/diyCode")
    public ApiResult<?> getCoinDetailByDiyCode(@RequestParam String diyCode) {
        try {
            PjOSendformItem result = scanInputService.getCoinDetailByDiyCode(diyCode);
            if (result != null) {
                return success(result);
            } else {
                return fail("未找到对应的钱币信息");
            }
        } catch (Exception e) {
            return fail("查询详情失败：" + e.getMessage());
        }
    }

    /**
     * 根据钱币编号获取钱币详情
     */
    @PreAuthorize("hasAuthority('banknote:scan:detail')")
    @OperationLog
    @GetMapping("/detail/number")
    public ApiResult<?> getCoinDetailByNumber(@RequestParam String nummber) {
        try {
            PjOSendformItem result = scanInputService.getCoinDetailByNumber(nummber);
            if (result != null) {
                return success(result);
            } else {
                return fail("未找到对应的钱币信息");
            }
        } catch (Exception e) {
            return fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 验证送评条码是否存在
     */
    @PreAuthorize("hasAuthority('banknote:scan:validate')")
    @OperationLog
    @GetMapping("/validate/diyCode")
    public ApiResult<?> validateDiyCode(@RequestParam String diyCode) {
        try {
            Map<String, Object> result = scanInputService.validateDiyCode(diyCode);
            return success(result);
        } catch (Exception e) {
            return fail("验证失败：" + e.getMessage());
        }
    }

    /**
     * 验证钱币编号是否存在
     */
    @PreAuthorize("hasAuthority('banknote:scan:validate')")
    @OperationLog
    @GetMapping("/validate/number")
    public ApiResult<?> validateNumber(@RequestParam String nummber) {
        try {
            Map<String, Object> result = scanInputService.validateNumber(nummber);
            return success(result);
        } catch (Exception e) {
            return fail("验证失败：" + e.getMessage());
        }
    }

    /**
     * 导出扫码录入数据
     */
    @PreAuthorize("hasAuthority('banknote:scan:export')")
    @OperationLog
    @PostMapping("/export")
    public void exportScanData(@RequestBody Map<String, Object> params, HttpServletResponse response) {
        try {
            scanInputService.exportScanData(params, response);
        } catch (Exception e) {
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 根据条形码查找同一送评单下的其他钱币
     */
    @PreAuthorize("hasAuthority('banknote:scan:query')")
    @OperationLog
    @GetMapping("/findSameSendform")
    public ApiResult<?> findCoinsBySameSendform(@RequestParam String nummber) {
        try {
            List<PjOSendformItem> result = scanInputService.findCoinsBySameSendform(nummber);
            return success(result);
        } catch (Exception e) {
            return fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 批量生成条形码
     */
    @PreAuthorize("hasAuthority('banknote:scan:generate')")
    @OperationLog
    @PostMapping("/generateBarcodes")
    public ApiResult<?> generateBarcodes(@RequestBody Map<String, Object> params) {
        try {
            String coinType = (String) params.get("coinType");
            Integer count = (Integer) params.get("count");
            if (count == null || count <= 0 || count > 100) {
                return fail("生成数量必须在1-100之间");
            }

            List<String> result = scanInputService.generateBarcodes(coinType, count);
            return success(result);
        } catch (Exception e) {
            return fail("生成条形码失败：" + e.getMessage());
        }
    }
}