package com.payne.server.banknote.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.server.banknote.dto.SendformCheckDto;
import com.payne.server.banknote.entity.PjOSendform;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.enums.CheckStatusEnum;
import com.payne.server.banknote.mapper.PjOSendformItemMapper;
import com.payne.server.banknote.mapper.PjOSendformMapper;
import com.payne.server.banknote.service.PjOSendformItemService;
import com.payne.server.banknote.service.PjOSendformService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 送评单Service实现类
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@AllArgsConstructor
@Service
public class PjOSendformServiceImpl extends ServiceImpl<PjOSendformMapper, PjOSendform> implements PjOSendformService {

    private final PjOSendformMapper pjOSendformMapper;
    private final PjOSendformItemService pjOSendformItemService;
    private final PjOSendformItemMapper pjOSendformItemMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeAndItemsByIds(List<String> ids) {
        try {
            // 删除送评单明细
            for (String id : ids) {
                PjOSendform sendform = getById(id);
                if (sendform != null) {
                    pjOSendformItemService.deleteBySendnum(sendform.getSendnum());
                }
            }
            // 删除送评单
            return removeByIds(ids);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchCheck(SendformCheckDto checkDto) {
        try {
            if (checkDto == null || checkDto.getSendnums() == null || checkDto.getSendnums().isEmpty()) {
                return false;
            }

            CheckStatusEnum checkStatus = CheckStatusEnum.getByCode(checkDto.getCheckStatus());
            if (checkStatus == null) {
                return false;
            }

            // 如果是审核通过，需要检查钱币是否都已打分
            if (CheckStatusEnum.APPROVED.equals(checkStatus)) {
                validateCoinsScored(checkDto.getSendnums());
            }

            Date checkTime = new Date();
            String checker = checkDto.getChecker();
            String checkRemark = checkDto.getCheckRemark();

            // 数据验证和处理
            if (checker != null && checker.length() > 100) {
                checker = checker.substring(0, 100);
            }

            if (checkRemark != null && checkRemark.length() > 1000) {
                checkRemark = checkRemark.substring(0, 1000);
            }

            // 批量更新送评单审核状态
            int processedCount = 0;

            for (String sendnum : checkDto.getSendnums()) {
                try {
                    PjOSendform sendform = lambdaQuery()
                            .eq(PjOSendform::getSendnum, sendnum)
                            .one();

                    if (sendform == null) {
                        continue;
                    }

                    // 如果已经是目标状态，视为成功处理
                    if (sendform.getCheckStatus() != null && checkStatus.getCode().equals(sendform.getCheckStatus())) {
                        processedCount++;
                        continue;
                    }

                    // 只有待审核状态才能进行审核
                    if (sendform.getCheckStatus() != null && !CheckStatusEnum.PENDING.getCode().equals(sendform.getCheckStatus())) {
                        continue;
                    }

                    // 使用安全更新方法
                    boolean updateResult = safeUpdateCheckStatus(sendnum, checkStatus.getCode(), checker, checkTime, checkRemark);

                    if (updateResult) {
                        processedCount++;
                    }

                } catch (Exception e) {
                    // 继续处理其他送评单，不中断整个批量操作
                }
            }

            // 只要处理了记录就返回true
            return processedCount > 0;

        } catch (Exception e) {
            log.error("批量审核送评单失败", e);
            throw e;
        }
    }

    @Override
    public boolean approveByBatch(List<String> sendnums, String checker, String checkRemark) {
        SendformCheckDto checkDto = new SendformCheckDto();
        checkDto.setSendnums(sendnums);
        checkDto.setCheckStatus(CheckStatusEnum.APPROVED.getCode());
        checkDto.setChecker(checker);
        checkDto.setCheckRemark(StringUtils.hasText(checkRemark) ? checkRemark : "审核通过");

        return batchCheck(checkDto);
    }

    @Override
    public boolean rejectByBatch(List<String> sendnums, String checker, String checkRemark) {
        SendformCheckDto checkDto = new SendformCheckDto();
        checkDto.setSendnums(sendnums);
        checkDto.setCheckStatus(CheckStatusEnum.REJECTED.getCode());
        checkDto.setChecker(checker);
        checkDto.setCheckRemark(StringUtils.hasText(checkRemark) ? checkRemark : "审核驳回");

        return batchCheck(checkDto);
    }

    @Override
    public List<PjOSendformItem> checkUnScoredCoins(String sendnum) {
        if (!StringUtils.hasText(sendnum)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<PjOSendformItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PjOSendformItem::getSendnum, sendnum)
               .and(w -> w.isNull(PjOSendformItem::getGradeScore)
                         .or()
                         .eq(PjOSendformItem::getGradeScore, ""));

        List<PjOSendformItem> unScoredCoins = pjOSendformItemMapper.selectList(wrapper);
        log.debug("送评单{}检查结果：共{}个未打分钱币", sendnum, unScoredCoins.size());

        return unScoredCoins;
    }

    @Override
    public Map<String, List<PjOSendformItem>> batchCheckUnScoredCoins(List<String> sendnums) {
        if (sendnums == null || sendnums.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, List<PjOSendformItem>> result = new HashMap<>();

        for (String sendnum : sendnums) {
            List<PjOSendformItem> unScoredCoins = checkUnScoredCoins(sendnum);
            if (!unScoredCoins.isEmpty()) {
                result.put(sendnum, unScoredCoins);
            }
        }

        return result;
    }

    @Override
    public void validateCoinsScored(List<String> sendnums) {
        Map<String, List<PjOSendformItem>> unScoredCoins = batchCheckUnScoredCoins(sendnums);

        if (!unScoredCoins.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("以下送评单含有未打分钱币，请先完成品相评分：\n");

            unScoredCoins.forEach((sendnum, coins) -> {
                errorMsg.append(String.format("• 送评单 %s: %d 个未打分钱币", sendnum, coins.size()));

                // 显示前3个未打分钱币的详细信息
                List<String> coinDetails = coins.stream()
                        .limit(3)
                        .map(coin -> {
                            String coinName = StringUtils.hasText(coin.getCoinName1()) ? coin.getCoinName1() : "未命名";
                            String nummber = StringUtils.hasText(coin.getSerialNumber()) ? coin.getSerialNumber() : "无编号";
                            return String.format("钱币编号:%s(%s)", nummber, coinName);
                        })
                        .collect(Collectors.toList());

                if (!coinDetails.isEmpty()) {
                    errorMsg.append(" [").append(String.join(", ", coinDetails));
                    if (coins.size() > 3) {
                        errorMsg.append(String.format(" 等%d个", coins.size()));
                    }
                    errorMsg.append("]");
                }
                errorMsg.append("\n");
            });

            throw new RuntimeException(errorMsg.toString());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean enableScanAudit(List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return false;
            }

            int successCount = 0;
            for (String sendnum : sendnums) {
                PjOSendform sendform = lambdaQuery()
                        .eq(PjOSendform::getSendnum, sendnum)
                        .one();

                if (sendform == null) {
                    continue;
                }

                // 检查是否已经开启扫码审核
                if (Integer.valueOf(1).equals(sendform.getFullyOpen())) {
                    successCount++;
                    continue;
                }

                // 开启扫码审核
                sendform.setFullyOpen(1);
                sendform.setUpdatetime(java.time.LocalDateTime.now());
                if (updateById(sendform)) {
                    successCount++;
                }
            }

            return successCount > 0;
        } catch (Exception e) {
            throw e;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean disableScanAudit(List<String> sendnums) {
        try {
            if (sendnums == null || sendnums.isEmpty()) {
                return false;
            }

            int successCount = 0;
            for (String sendnum : sendnums) {
                PjOSendform sendform = lambdaQuery()
                        .eq(PjOSendform::getSendnum, sendnum)
                        .one();

                if (sendform == null) {
                    continue;
                }

                // 检查是否已经关闭扫码审核
                if (!Integer.valueOf(1).equals(sendform.getFullyOpen())) {
                    successCount++;
                    continue;
                }

                // 关闭扫码审核
                sendform.setFullyOpen(0);
                sendform.setUpdatetime(java.time.LocalDateTime.now());
                if (updateById(sendform)) {
                    successCount++;
                }
            }

            return successCount > 0;
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public Map<String, Object> autoEnableScanAudit(List<String> sendnums) {
        Map<String, Object> result = new HashMap<>();
        List<String> successList = new ArrayList<>();
        List<String> failedList = new ArrayList<>();
        List<String> skippedList = new ArrayList<>();

        try {
            if (sendnums == null || sendnums.isEmpty()) {
                result.put("success", false);
                result.put("message", "送评单号列表为空");
                return result;
            }

            for (String sendnum : sendnums) {
                try {
                    PjOSendform sendform = lambdaQuery()
                            .eq(PjOSendform::getSendnum, sendnum)
                            .one();

                    if (sendform == null) {
                        failedList.add(sendnum + "(送评单不存在)");
                        continue;
                    }

                    // 检查是否满足自动开启条件
                    if (!checkAutoEnableConditions(sendform)) {
                        skippedList.add(sendnum + "(不满足开启条件)");
                        continue;
                    }

                    // 检查是否已经开启
                    if (Integer.valueOf(1).equals(sendform.getFullyOpen())) {
                        skippedList.add(sendnum + "(已开启)");
                        continue;
                    }

                    // 开启扫码审核
                    sendform.setFullyOpen(1);
                    sendform.setUpdatetime(java.time.LocalDateTime.now());
                    if (updateById(sendform)) {
                        successList.add(sendnum);
                    } else {
                        failedList.add(sendnum + "(更新失败)");
                    }

                } catch (Exception e) {
                    failedList.add(sendnum + "(异常: " + e.getMessage() + ")");
                }
            }

            result.put("success", true);
            result.put("total", sendnums.size());
            result.put("successCount", successList.size());
            result.put("failedCount", failedList.size());
            result.put("skippedCount", skippedList.size());
            result.put("successList", successList);
            result.put("failedList", failedList);
            result.put("skippedList", skippedList);

            String message = String.format("自动开启扫码审核完成：成功 %d 个，失败 %d 个，跳过 %d 个",
                    successList.size(), failedList.size(), skippedList.size());
            result.put("message", message);

            return result;

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "自动开启扫码审核失败：" + e.getMessage());
            return result;
        }
    }

    /**
     * 检查是否满足自动开启扫码审核的条件
     */
    private boolean checkAutoEnableConditions(PjOSendform sendform) {
        // 条件1：送评单必须已审核通过
        if (!CheckStatusEnum.APPROVED.getCode().equals(sendform.getCheckStatus())) {
            return false;
        }

        // 条件2：财务必须已核对
        if (!Integer.valueOf(1).equals(sendform.getIfyou())) {
            return false;
        }

        // 条件3：所有钱币必须已完成打分
        try {
            List<PjOSendformItem> unScoredCoins = checkUnScoredCoins(sendform.getSendnum());
            if (!unScoredCoins.isEmpty()) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }

        return true;
    }

    @Override
    public Map<String, Object> checkAutoEnableConditions(List<String> sendnums) {
        Map<String, Object> result = new HashMap<>();
        List<String> canEnableList = new ArrayList<>();
        List<String> cannotEnableList = new ArrayList<>();
        List<String> alreadyEnabledList = new ArrayList<>();
        List<String> notFoundList = new ArrayList<>();

        try {
            if (sendnums == null || sendnums.isEmpty()) {
                result.put("success", false);
                result.put("message", "送评单号列表为空");
                return result;
            }

            for (String sendnum : sendnums) {
                try {
                    PjOSendform sendform = lambdaQuery()
                            .eq(PjOSendform::getSendnum, sendnum)
                            .one();

                    if (sendform == null) {
                        notFoundList.add(sendnum);
                        continue;
                    }

                    // 检查是否已经开启
                    if (Integer.valueOf(1).equals(sendform.getFullyOpen())) {
                        alreadyEnabledList.add(sendnum);
                        continue;
                    }

                    // 检查是否满足自动开启条件
                    if (checkAutoEnableConditions(sendform)) {
                        canEnableList.add(sendnum);
                    } else {
                        cannotEnableList.add(sendnum);
                    }

                } catch (Exception e) {
                    log.error("检查送评单{}自动开启条件异常", sendnum, e);
                    cannotEnableList.add(sendnum + "(检查异常)");
                }
            }

            result.put("success", true);
            result.put("message", "条件检查完成");
            result.put("canEnableList", canEnableList);
            result.put("cannotEnableList", cannotEnableList);
            result.put("alreadyEnabledList", alreadyEnabledList);
            result.put("notFoundList", notFoundList);
            result.put("canEnableCount", canEnableList.size());
            result.put("totalCount", sendnums.size());

            log.info("扫码审核条件检查完成，总数: {}, 可开启: {}, 不可开启: {}, 已开启: {}, 未找到: {}",
                    sendnums.size(), canEnableList.size(), cannotEnableList.size(),
                    alreadyEnabledList.size(), notFoundList.size());

        } catch (Exception e) {
            log.error("检查自动开启扫码审核条件失败", e);
            result.put("success", false);
            result.put("message", "检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 验证审核数据的有效性
     */
    private void validateCheckData(String checker, String checkRemark) {
        if (checker == null || checker.trim().isEmpty()) {
            throw new IllegalArgumentException("审核人不能为空");
        }

        if (checker.length() > 100) {
            throw new IllegalArgumentException("审核人名称长度不能超过100个字符");
        }

        if (checkRemark != null && checkRemark.length() > 1000) {
            throw new IllegalArgumentException("审核意见长度不能超过1000个字符");
        }
    }

    /**
     * 安全地更新送评单审核状态
     */
    private boolean safeUpdateCheckStatus(String sendnum, Integer checkStatus, String checker, Date checkTime, String checkRemark) {
        try {
            // 验证数据
            validateCheckData(checker, checkRemark);

            // 使用LambdaUpdateWrapper进行更新
            return lambdaUpdate()
                    .eq(PjOSendform::getSendnum, sendnum)
                    .set(PjOSendform::getCheckStatus, checkStatus)
                    .set(PjOSendform::getChecker, checker)
                    .set(PjOSendform::getCheckTime, checkTime)
                    .set(checkRemark != null, PjOSendform::getCheckRemark, checkRemark)
                    .update();

        } catch (Exception e) {
            return false;
        }
    }
}