package com.payne.server.banknote.service;

import com.payne.server.banknote.dto.ScanQueryDto;
import com.payne.server.banknote.entity.PjOSendformItem;
import jakarta.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.Map;

/**
 * 扫码录入Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface ScanInputService {

    /**
     * 根据条形码扫描查询钱币信息（支持送评单号、送评条码、钱币编号）
     *
     * @param queryDto 查询条件
     * @return 查询结果
     */
    List<PjOSendformItem> queryCoinsByScan(ScanQueryDto queryDto);

    /**
     * 批量更新钱币信息
     * 
     * @param coinList 钱币列表
     * @return 是否成功
     */
    boolean batchUpdateCoins(List<PjOSendformItem> coinList);

    /**
     * 根据送评单号查询钱币列表
     * 
     * @param sendnum 送评单号
     * @return 钱币列表
     */
    List<PjOSendformItem> getCoinsBySendnum(String sendnum);

    /**
     * 根据钱币ID删除钱币
     * 
     * @param ids 钱币ID列表
     * @return 是否成功
     */
    boolean removeCoins(List<String> ids);

    /**
     * 获取钱币类型字典
     * 
     * @return 钱币类型列表
     */
    List<Map<String, Object>> getCoinTypes();

    /**
     * 获取真伪选项字典
     * 
     * @return 真伪选项列表
     */
    List<Map<String, Object>> getResultOptions();

    /**
     * 获取盒子类型字典
     * 
     * @return 盒子类型列表
     */
    List<Map<String, Object>> getBoxTypes();

    /**
     * 根据送评条码获取钱币详情
     *
     * @param diyCode 送评条码
     * @return 钱币详情
     */
    PjOSendformItem getCoinDetailByDiyCode(String diyCode);

    /**
     * 根据钱币编号获取钱币详情
     *
     * @param nummber 钱币编号
     * @return 钱币详情
     */
    PjOSendformItem getCoinDetailByNumber(String nummber);

    /**
     * 验证送评条码是否存在
     *
     * @param diyCode 送评条码
     * @return 验证结果
     */
    Map<String, Object> validateDiyCode(String diyCode);

    /**
     * 验证钱币编号是否存在
     *
     * @param nummber 钱币编号
     * @return 验证结果
     */
    Map<String, Object> validateNumber(String nummber);

    /**
     * 导出扫码录入数据
     * 
     * @param params 导出参数
     * @param response HTTP响应
     */
    void exportScanData(Map<String, Object> params, HttpServletResponse response);

    /**
     * 根据送评条码查找同一送评单下的其他钱币
     *
     * @param diyCode 送评条码
     * @return 钱币列表
     */
    List<PjOSendformItem> findCoinsBySameSendformByDiyCode(String diyCode);

    /**
     * 根据钱币编号查找同一送评单下的其他钱币
     *
     * @param nummber 钱币编号
     * @return 钱币列表
     */
    List<PjOSendformItem> findCoinsBySameSendformByNumber(String nummber);

    /**
     * 根据条形码查找同一送评单下的其他钱币（兼容方法，优先使用钱币编号）
     *
     * @param nummber 钱币编号
     * @return 钱币列表
     * @deprecated 建议使用 findCoinsBySameSendformByNumber 或 findCoinsBySameSendformByDiyCode
     */
    @Deprecated
    List<PjOSendformItem> findCoinsBySameSendform(String nummber);

    /**
     * 根据钱币详情获取钱币信息（兼容方法）
     *
     * @param nummber 钱币编号
     * @return 钱币详情
     * @deprecated 建议使用 getCoinDetailByNumber 或 getCoinDetailByDiyCode
     */
    @Deprecated
    PjOSendformItem getCoinDetail(String nummber);

    /**
     * 验证条形码是否存在（兼容方法）
     *
     * @param nummber 钱币编号
     * @return 验证结果
     * @deprecated 建议使用 validateNumber 或 validateDiyCode
     */
    @Deprecated
    Map<String, Object> validateBarcode(String nummber);

    /**
     * 批量生成条形码
     * 
     * @param coinType 钱币类型
     * @param count 生成数量
     * @return 生成的条形码列表
     */
    List<String> generateBarcodes(String coinType, Integer count);
} 