package com.payne.server.banknote.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 钱币图片字段自定义反序列化器
 * 
 * 处理前端发送的多种数据格式：
 * 1. 字符串格式：直接返回
 * 2. 数组格式：提取图片ID并转换为逗号分隔的字符串
 * 3. 嵌套JSON字符串：解析后提取ID
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
public class CoinImagesDeserializer extends JsonDeserializer<String> {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        JsonToken token = parser.getCurrentToken();
        
        try {
            // 如果是字符串，直接返回
            if (token == JsonToken.VALUE_STRING) {
                String value = parser.getValueAsString();
                log.debug("处理字符串格式的coinImages: {}", value);
                return value;
            }
            
            // 如果是数组，处理数组中的元素
            if (token == JsonToken.START_ARRAY) {
                JsonNode arrayNode = parser.readValueAsTree();
                List<String> imageIds = new ArrayList<>();
                
                for (JsonNode element : arrayNode) {
                    if (element.isTextual()) {
                        String elementText = element.asText();
                        
                        // 检查是否是JSON字符串
                        if (elementText.trim().startsWith("[") || elementText.trim().startsWith("{")) {
                            try {
                                // 尝试解析为JSON
                                JsonNode jsonNode = objectMapper.readTree(elementText);
                                extractImageIds(jsonNode, imageIds);
                            } catch (Exception e) {
                                log.warn("无法解析JSON字符串: {}, 将作为普通字符串处理", elementText);
                                if (!elementText.trim().isEmpty()) {
                                    imageIds.add(elementText.trim());
                                }
                            }
                        } else {
                            // 普通字符串，直接添加
                            if (!elementText.trim().isEmpty()) {
                                imageIds.add(elementText.trim());
                            }
                        }
                    } else if (element.isObject()) {
                        // 直接是对象，提取ID
                        extractImageIds(element, imageIds);
                    }
                }
                
                String result = String.join(",", imageIds);
                log.debug("处理数组格式的coinImages，提取到的ID: {}", result);
                return result;
            }
            
            // 如果是对象，尝试提取ID
            if (token == JsonToken.START_OBJECT) {
                JsonNode objectNode = parser.readValueAsTree();
                List<String> imageIds = new ArrayList<>();
                extractImageIds(objectNode, imageIds);
                
                String result = String.join(",", imageIds);
                log.debug("处理对象格式的coinImages，提取到的ID: {}", result);
                return result;
            }
            
            // 其他情况返回空字符串
            log.warn("未知的coinImages数据格式，token类型: {}", token);
            return "";
            
        } catch (Exception e) {
            log.error("反序列化coinImages时发生错误", e);
            return "";
        }
    }
    
    /**
     * 从JSON节点中提取图片ID
     */
    private void extractImageIds(JsonNode node, List<String> imageIds) {
        if (node.isArray()) {
            for (JsonNode item : node) {
                extractImageIds(item, imageIds);
            }
        } else if (node.isObject()) {
            // 尝试提取常见的ID字段
            JsonNode idNode = node.get("id");
            if (idNode != null && idNode.isTextual()) {
                String id = idNode.asText().trim();
                if (!id.isEmpty() && !imageIds.contains(id)) {
                    imageIds.add(id);
                }
            }
            
            // 尝试提取key字段（前端可能使用key作为ID）
            JsonNode keyNode = node.get("key");
            if (keyNode != null && keyNode.isTextual()) {
                String key = keyNode.asText().trim();
                if (!key.isEmpty() && !imageIds.contains(key)) {
                    imageIds.add(key);
                }
            }
        } else if (node.isTextual()) {
            String text = node.asText().trim();
            if (!text.isEmpty() && !imageIds.contains(text)) {
                imageIds.add(text);
            }
        }
    }
}
