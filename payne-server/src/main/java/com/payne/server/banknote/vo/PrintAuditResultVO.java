package com.payne.server.banknote.vo;

import java.util.List;

/**
 * 打印审核结果视图对象
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
public class PrintAuditResultVO {

    private Integer unauditedCount;
    private List<String> unauditedSendforms;
    private Integer totalCount;
    private Integer auditedCount;

    public Integer getUnauditedCount() {
        return unauditedCount;
    }

    public void setUnauditedCount(Integer unauditedCount) {
        this.unauditedCount = unauditedCount;
    }

    public List<String> getUnauditedSendforms() {
        return unauditedSendforms;
    }

    public void setUnauditedSendforms(List<String> unauditedSendforms) {
        this.unauditedSendforms = unauditedSendforms;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getAuditedCount() {
        return auditedCount;
    }

    public void setAuditedCount(Integer auditedCount) {
        this.auditedCount = auditedCount;
    }
}
