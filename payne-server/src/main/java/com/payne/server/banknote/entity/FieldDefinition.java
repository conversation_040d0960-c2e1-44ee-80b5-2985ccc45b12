package com.payne.server.banknote.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 字段定义实体类
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Entity
@Table(name = "FIELD_DEFINITION")
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("FIELD_DEFINITION")
public class FieldDefinition implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(50)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    @Column(name = "FIELD_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("FIELD_NAME")
    private String fieldName;
    
    @Column(name = "FIELD_TYPE", columnDefinition = "VARCHAR2(50)")
    @TableField("FIELD_TYPE")
    private String fieldType;
    
    @Column(name = "DISPLAY_NAME", columnDefinition = "VARCHAR2(100)")
    @TableField("DISPLAY_NAME")
    private String displayName;
    
    @Column(name = "CATEGORY", columnDefinition = "VARCHAR2(50)")
    @TableField("CATEGORY")
    private String category;
    
    @Column(name = "SORT_ORDER", columnDefinition = "NUMBER(10)")
    @TableField("SORT_ORDER")
    private Integer sortOrder;
    
    @Column(name = "IS_ENABLED", columnDefinition = "NUMBER(1)")
    @TableField("IS_ENABLED")
    private Boolean isEnabled;
    
    @Column(name = "DESCRIPTION", columnDefinition = "VARCHAR2(500)")
    @TableField("DESCRIPTION")
    private String description;
    
    @Column(name = "CREATE_TIME", columnDefinition = "TIMESTAMP")
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;
    
    @Column(name = "CREATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("CREATE_USER")
    private String createUser;
    
    @Column(name = "UPDATE_TIME", columnDefinition = "TIMESTAMP")
    @TableField("UPDATE_TIME")
    private LocalDateTime updateTime;
    
    @Column(name = "UPDATE_USER", columnDefinition = "VARCHAR2(50)")
    @TableField("UPDATE_USER")
    private String updateUser;
}