package com.payne.server.banknote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 送评单审核状态枚举
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Getter
@AllArgsConstructor
public enum CheckStatusEnum {
    
    /**
     * 未审核
     */
    PENDING(0, "未审核"),
    
    /**
     * 审核通过
     */
    APPROVED(1, "审核通过"),
    
    /**
     * 审核驳回
     */
    REJECTED(2, "审核驳回");
    
    private final Integer code;
    private final String name;
    
    /**
     * 根据代码获取枚举
     */
    public static CheckStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CheckStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
    
    /**
     * 根据名称获取枚举
     */
    public static CheckStatusEnum getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        for (CheckStatusEnum status : values()) {
            if (status.getName().equals(name.trim())) {
                return status;
            }
        }
        return null;
    }
}
