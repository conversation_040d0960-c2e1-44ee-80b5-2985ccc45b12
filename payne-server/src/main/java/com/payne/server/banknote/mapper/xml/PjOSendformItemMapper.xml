<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payne.server.banknote.mapper.PjOSendformItemMapper">

    <!-- 根据送评单号查询明细列表 -->
    <select id="selectBySendnum" resultType="com.payne.server.banknote.entity.PjOSendformItem">
        SELECT * FROM PJ_O_SENDFORM_ITEM
        WHERE SENDNUM = #{sendnum}
        ORDER BY SEQNO ASC
    </select>

    <!-- 根据送评单号删除明细 -->
    <delete id="deleteBySendnum">
        DELETE FROM PJ_O_SENDFORM_ITEM
        WHERE SENDNUM = #{sendnum}
    </delete>

    <!-- 查询指定前缀和日期的最大序号 -->
    <select id="getMaxSequenceNumber" resultType="java.lang.Integer">
        SELECT NVL(MAX(
            CASE
                WHEN DIY_CODE LIKE #{prefix} || #{dateStr} || '%'
                THEN TO_NUMBER(SUBSTR(DIY_CODE, LENGTH(#{prefix} || #{dateStr}) + 1))
                ELSE 0
            END
        ), 0) AS maxSeq
        FROM PJ_O_SENDFORM_ITEM
        WHERE DIY_CODE LIKE #{prefix} || #{dateStr} || '%'
    </select>

</mapper>