package com.payne.server.banknote.util;

import com.payne.server.banknote.entity.PjOSendform;
import com.payne.server.banknote.entity.PjOSendformItem;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 费用计算工具类
 * 基于旧系统逻辑完善的费用计算功能
 * 
 * <AUTHOR>
 * @since 2025-08-03
 */
@Slf4j
public class FeeCalculatorUtil {

    /** 默认折扣（无折扣） */
    public static final BigDecimal DEFAULT_DISCOUNT = new BigDecimal("10");
    
    /** 小数位数 */
    public static final int DECIMAL_PLACES = 2;
    
    /** 零折扣时的结果 */
    public static final BigDecimal ZERO_DISCOUNT_RESULT = BigDecimal.ZERO;

    /**
     * 计算单个钱币的评级费用
     * 
     * @param coin 钱币对象
     * @return 计算后的评级费用
     */
    public static BigDecimal calculateGradeFee(PjOSendformItem coin) {
        if (coin == null) {
            return BigDecimal.ZERO;
        }

        BigDecimal standardPrice = coin.getStandardPrice();
        BigDecimal internationalPrice = coin.getInternationalPrice();
        BigDecimal discount = coin.getDiscount();

        // 处理null值
        if (standardPrice == null) {
            standardPrice = BigDecimal.ZERO;
        }
        if (internationalPrice == null) {
            internationalPrice = BigDecimal.ZERO;
        }
        if (discount == null) {
            discount = DEFAULT_DISCOUNT;
        }

        // 特殊情况：折扣为0时，费用直接为0（与旧系统保持一致）
        if (discount.compareTo(BigDecimal.ZERO) == 0) {
            return ZERO_DISCOUNT_RESULT;
        }

        BigDecimal baseFee = BigDecimal.ZERO;

        // 费用选择优先级：国际价 > 标准价（与旧系统逻辑一致）
        boolean hasStandardPrice = standardPrice.compareTo(BigDecimal.ZERO) > 0;
        boolean hasInternationalPrice = internationalPrice.compareTo(BigDecimal.ZERO) > 0;

        if (hasStandardPrice && hasInternationalPrice) {
            // 同时存在时使用国际价
            baseFee = internationalPrice;
        } else if (hasStandardPrice) {
            // 只有标准价
            baseFee = standardPrice;
        } else if (hasInternationalPrice) {
            // 只有国际价
            baseFee = internationalPrice;
        }

        // 计算最终费用：基础价格 × 折扣 ÷ 10
        BigDecimal finalFee = baseFee.multiply(discount).divide(new BigDecimal("10"), DECIMAL_PLACES, RoundingMode.HALF_UP);

        return finalFee;
    }

    /**
     * 计算钱币列表的总评级费用
     * 
     * @param coinList 钱币列表
     * @return 总评级费用
     */
    public static BigDecimal calculateTotalGradeFee(List<PjOSendformItem> coinList) {
        if (coinList == null || coinList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return coinList.stream()
                .filter(coin -> coin != null)
                .map(coin -> {
                    BigDecimal gradeFee = coin.getGradeFee();
                    if (gradeFee == null) {
                        gradeFee = calculateGradeFee(coin);
                    }
                    return gradeFee;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算钱币列表的总盒子费用
     * 
     * @param coinList 钱币列表
     * @return 总盒子费用
     */
    public static BigDecimal calculateTotalBoxFee(List<PjOSendformItem> coinList) {
        if (coinList == null || coinList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return coinList.stream()
                .filter(coin -> coin != null)
                .map(coin -> coin.getBoxFee() != null ? coin.getBoxFee() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算钱币列表的总加急费用
     * 
     * @param coinList 钱币列表
     * @return 总加急费用
     */
    public static BigDecimal calculateTotalUrgentFee(List<PjOSendformItem> coinList) {
        if (coinList == null || coinList.isEmpty()) {
            return BigDecimal.ZERO;
        }

        return coinList.stream()
                .filter(coin -> coin != null)
                .map(coin -> coin.getUrgentFee() != null ? coin.getUrgentFee() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 计算送评单的总费用
     * 
     * @param sendform 送评单数据
     * @param coinList 钱币列表
     * @return 费用明细对象
     */
    public static FeeDetail calculateSendformTotalFee(PjOSendform sendform, List<PjOSendformItem> coinList) {
        FeeDetail feeDetail = new FeeDetail();

        // 计算钱币相关费用
        if (coinList != null && !coinList.isEmpty()) {
            feeDetail.setGradeFeeTotal(calculateTotalGradeFee(coinList));
            feeDetail.setBoxFeeTotal(calculateTotalBoxFee(coinList));
            feeDetail.setUrgentFeeTotal(calculateTotalUrgentFee(coinList));
        }

        // 获取送评单级别的费用
        if (sendform != null) {
            BigDecimal postfee = sendform.getPostfee() != null ? sendform.getPostfee() : BigDecimal.ZERO;
            BigDecimal appendfee = sendform.getAppendfee() != null ? sendform.getAppendfee() : BigDecimal.ZERO;
            
            feeDetail.setExpressFee(postfee);
            feeDetail.setOtherFee(appendfee);
        }

        // 计算总费用
        BigDecimal totalFee = feeDetail.getGradeFeeTotal()
                .add(feeDetail.getBoxFeeTotal())
                .add(feeDetail.getUrgentFeeTotal())
                .add(feeDetail.getExpressFee())
                .add(feeDetail.getOtherFee());

        feeDetail.setTotalFee(totalFee);

        return feeDetail;
    }

    /**
     * 批量更新钱币列表的评级费用
     * 
     * @param coinList 钱币列表
     */
    public static void batchUpdateGradeFee(List<PjOSendformItem> coinList) {
        if (coinList == null || coinList.isEmpty()) {
            return;
        }

        coinList.forEach(coin -> {
            if (coin != null) {
                BigDecimal gradeFee = calculateGradeFee(coin);
                coin.setGradeFee(gradeFee);
            }
        });
    }

    /**
     * 验证费用数据的有效性
     * 
     * @param coin 钱币对象
     * @return 验证结果
     */
    public static ValidationResult validateFeeData(PjOSendformItem coin) {
        ValidationResult result = new ValidationResult();

        if (coin == null) {
            result.setValid(false);
            result.addError("钱币数据无效");
            return result;
        }

        BigDecimal standardPrice = coin.getStandardPrice();
        BigDecimal internationalPrice = coin.getInternationalPrice();
        BigDecimal discount = coin.getDiscount();

        // 检查价格是否为负数
        if (standardPrice != null && standardPrice.compareTo(BigDecimal.ZERO) < 0) {
            result.addError("标准价不能为负数");
            result.setValid(false);
        }

        if (internationalPrice != null && internationalPrice.compareTo(BigDecimal.ZERO) < 0) {
            result.addError("国际价不能为负数");
            result.setValid(false);
        }

        // 检查折扣范围
        if (discount != null && (discount.compareTo(BigDecimal.ZERO) < 0 || discount.compareTo(new BigDecimal("20")) > 0)) {
            result.addWarning("折扣值异常，建议检查（正常范围：0-20）");
        }

        // 检查是否有价格设置
        boolean hasPrice = (standardPrice != null && standardPrice.compareTo(BigDecimal.ZERO) > 0) ||
                          (internationalPrice != null && internationalPrice.compareTo(BigDecimal.ZERO) > 0);
        if (!hasPrice) {
            result.addWarning("未设置标准价或国际价，费用将为0");
        }

        return result;
    }

    /**
     * 格式化费用显示
     * 
     * @param fee 费用金额
     * @param currency 货币符号
     * @return 格式化后的费用字符串
     */
    public static String formatFee(BigDecimal fee, String currency) {
        if (fee == null) {
            fee = BigDecimal.ZERO;
        }
        if (currency == null) {
            currency = "";
        }
        return currency + fee.setScale(DECIMAL_PLACES, RoundingMode.HALF_UP).toString();
    }

    /**
     * 费用明细类
     */
    public static class FeeDetail {
        private BigDecimal gradeFeeTotal = BigDecimal.ZERO;      // 评级费总计
        private BigDecimal boxFeeTotal = BigDecimal.ZERO;        // 盒子费总计
        private BigDecimal urgentFeeTotal = BigDecimal.ZERO;     // 加急费总计
        private BigDecimal expressFee = BigDecimal.ZERO;         // 快递费
        private BigDecimal otherFee = BigDecimal.ZERO;           // 其他费用
        private BigDecimal totalFee = BigDecimal.ZERO;           // 总费用

        // Getters and Setters
        public BigDecimal getGradeFeeTotal() { return gradeFeeTotal; }
        public void setGradeFeeTotal(BigDecimal gradeFeeTotal) { this.gradeFeeTotal = gradeFeeTotal; }
        
        public BigDecimal getBoxFeeTotal() { return boxFeeTotal; }
        public void setBoxFeeTotal(BigDecimal boxFeeTotal) { this.boxFeeTotal = boxFeeTotal; }
        
        public BigDecimal getUrgentFeeTotal() { return urgentFeeTotal; }
        public void setUrgentFeeTotal(BigDecimal urgentFeeTotal) { this.urgentFeeTotal = urgentFeeTotal; }
        
        public BigDecimal getExpressFee() { return expressFee; }
        public void setExpressFee(BigDecimal expressFee) { this.expressFee = expressFee; }
        
        public BigDecimal getOtherFee() { return otherFee; }
        public void setOtherFee(BigDecimal otherFee) { this.otherFee = otherFee; }
        
        public BigDecimal getTotalFee() { return totalFee; }
        public void setTotalFee(BigDecimal totalFee) { this.totalFee = totalFee; }
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private boolean isValid = true;
        private java.util.List<String> errors = new java.util.ArrayList<>();
        private java.util.List<String> warnings = new java.util.ArrayList<>();

        public boolean isValid() { return isValid; }
        public void setValid(boolean valid) { isValid = valid; }
        
        public java.util.List<String> getErrors() { return errors; }
        public void addError(String error) { this.errors.add(error); }
        
        public java.util.List<String> getWarnings() { return warnings; }
        public void addWarning(String warning) { this.warnings.add(warning); }
    }
}
