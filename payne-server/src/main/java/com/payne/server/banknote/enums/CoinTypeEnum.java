package com.payne.server.banknote.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 钱币类型枚举
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Getter
@AllArgsConstructor
public enum CoinTypeEnum {
    
    /**
     * 古钱币
     */
    ANCIENT_COIN(1, "古钱币"),
    
    /**
     * 机制币
     */
    MACHINE_COIN(2, "机制币"),
    
    /**
     * 银锭
     */
    SILVER_INGOT(3, "银锭"),
    
    /**
     * 纸币
     */
    BANKNOTE(4, "纸币");
    
    private final Integer code;
    private final String name;
    
    public static CoinTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (CoinTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
} 