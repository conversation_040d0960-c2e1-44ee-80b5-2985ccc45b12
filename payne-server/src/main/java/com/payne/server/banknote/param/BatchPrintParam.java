package com.payne.server.banknote.param;

import java.util.List;

/**
 * 批量打印查询参数
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
public class BatchPrintParam {

    private List<String> coinNumbers;
    private List<String> sendformNumbers;
    private String coinType;
    private Long current = 1L;
    private Long size = 20L;

    public List<String> getCoinNumbers() {
        return coinNumbers;
    }

    public void setCoinNumbers(List<String> coinNumbers) {
        this.coinNumbers = coinNumbers;
    }

    public List<String> getSendformNumbers() {
        return sendformNumbers;
    }

    public void setSendformNumbers(List<String> sendformNumbers) {
        this.sendformNumbers = sendformNumbers;
    }

    public String getCoinType() {
        return coinType;
    }

    public void setCoinType(String coinType) {
        this.coinType = coinType;
    }

    public Long getCurrent() {
        return current;
    }

    public void setCurrent(Long current) {
        this.current = current;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }
}
