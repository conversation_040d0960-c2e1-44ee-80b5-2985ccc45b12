package com.payne.server.banknote.dto;

import lombok.Data;

import java.util.List;

/**
 * 扫码查询DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class ScanQueryDto {

    /**
     * 送评条码（扫码时使用的条码）
     */
    private String diyCode;

    /**
     * 钱币编号
     */
    private String serialNumber;

    /**
     * 钱币类型 (01=纸币, 02=古钱币, 03=机制币, 04=银锭)
     */
    private String gm;

    /**
     * 追加类型 (0=无, 1=追加该送评单所有订单, 2=追加该鉴定单所有订单)
     */
    private Integer addType;

    /**
     * 已存在的送评条码数组（用于去重）
     */
    private List<String> diyCodes;

    /**
     * 已存在的钱币编号数组（用于去重）
     */
    private List<String> serialNumbers;

    /**
     * 送评单号（可选，用于特定查询）
     */
    private String sendnum;

    /**
     * 是否包含详细信息
     */
    private Boolean includeDetails;
} 