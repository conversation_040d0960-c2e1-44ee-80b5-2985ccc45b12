package com.payne.server.banknote.dto;

import com.payne.server.banknote.entity.PjOSendform;
import com.payne.server.banknote.entity.PjOSendformItem;
import lombok.Data;

import java.util.List;

/**
 * 创建送评单DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class SendformCreateDto {
    
    /**
     * 送评单基本信息
     */
    private PjOSendform sendform;
    
    /**
     * 古钱币列表
     */
    private List<PjOSendformItem> ancientCoins;
    
    /**
     * 机制币列表
     */
    private List<PjOSendformItem> machineCoins;
    
    /**
     * 银锭列表
     */
    private List<PjOSendformItem> silverIngots;
    
    /**
     * 纸币列表
     */
    private List<PjOSendformItem> banknotes;
} 