package com.payne.server.banknote.vo;

import java.util.List;
import java.util.Map;

/**
 * 打印数据视图对象
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
public class PrintDataVO {

    private Integer totalCount;
    private String labelTypeName;
    private String conversionTypeName;
    private String printType;
    private List<BatchPrintCoinVO> items;
    private Integer labelType;
    private Integer conversionType;
    private List<String> coinIds;
    
    // 自定义模板相关字段
    private String templateId;
    private String templateName;
    private Map<String, Object> layoutConfig;
    private Map<String, List<String>> fieldMapping;



    // 页面设置（包含纸张大小等信息）
    private String pageSettings;

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public String getLabelTypeName() {
        return labelTypeName;
    }

    public void setLabelTypeName(String labelTypeName) {
        this.labelTypeName = labelTypeName;
    }

    public String getConversionTypeName() {
        return conversionTypeName;
    }

    public void setConversionTypeName(String conversionTypeName) {
        this.conversionTypeName = conversionTypeName;
    }

    public String getPrintType() {
        return printType;
    }

    public void setPrintType(String printType) {
        this.printType = printType;
    }

    public List<BatchPrintCoinVO> getItems() {
        return items;
    }

    public void setItems(List<BatchPrintCoinVO> items) {
        this.items = items;
    }

    public Integer getLabelType() {
        return labelType;
    }

    public void setLabelType(Integer labelType) {
        this.labelType = labelType;
    }

    public Integer getConversionType() {
        return conversionType;
    }

    public void setConversionType(Integer conversionType) {
        this.conversionType = conversionType;
    }

    public List<String> getCoinIds() {
        return coinIds;
    }

    public void setCoinIds(List<String> coinIds) {
        this.coinIds = coinIds;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public Map<String, Object> getLayoutConfig() {
        return layoutConfig;
    }

    public void setLayoutConfig(Map<String, Object> layoutConfig) {
        this.layoutConfig = layoutConfig;
    }

    public Map<String, List<String>> getFieldMapping() {
        return fieldMapping;
    }

    public void setFieldMapping(Map<String, List<String>> fieldMapping) {
        this.fieldMapping = fieldMapping;
    }



    public String getPageSettings() {
        return pageSettings;
    }

    public void setPageSettings(String pageSettings) {
        this.pageSettings = pageSettings;
    }
}
