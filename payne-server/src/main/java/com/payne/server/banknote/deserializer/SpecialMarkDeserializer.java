package com.payne.server.banknote.deserializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 特殊标记字段反序列化器
 * 支持将数组类型转换为逗号分隔的字符串
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
public class SpecialMarkDeserializer extends JsonDeserializer<String> {

    @Override
    public String deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        JsonToken token = parser.getCurrentToken();
        
        if (token == JsonToken.VALUE_NULL) {
            return null;
        }
        
        if (token == JsonToken.VALUE_STRING) {
            // 如果是字符串，直接返回
            return parser.getValueAsString();
        }
        
        if (token == JsonToken.START_ARRAY) {
            // 如果是数组，转换为逗号分隔的字符串
            List<String> values = new ArrayList<>();
            
            while (parser.nextToken() != JsonToken.END_ARRAY) {
                if (parser.getCurrentToken() == JsonToken.VALUE_STRING) {
                    String value = parser.getValueAsString();
                    if (value != null && !value.trim().isEmpty()) {
                        values.add(value.trim());
                    }
                }
            }
            
            if (values.isEmpty()) {
                return "";
            }
            
            String result = String.join(",", values);
            log.debug("转换特殊标记数组为字符串: {} -> {}", values, result);
            return result;
        }
        
        // 其他类型尝试转换为字符串
        return parser.getValueAsString();
    }
}
