package com.payne.server.banknote.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.ApiResult;
import com.payne.core.web.BaseController;
import com.payne.server.banknote.dto.LabelTemplateDto;
import com.payne.server.banknote.entity.FieldDefinition;
import com.payne.server.banknote.entity.LabelTemplate;
import com.payne.server.banknote.service.LabelDesignService;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 标签设计控制器
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@RestController
@RequestMapping("/api/label-design")
@RequiredArgsConstructor
public class LabelDesignController extends BaseController {
    
    private final LabelDesignService labelDesignService;
    
    /**
     * 获取可用字段列表
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "获取可用字段列表")
    @GetMapping("/fields")
    public ApiResult<?> getAvailableFields() {
        try {
            List<FieldDefinition> fields = labelDesignService.getAvailableFields();
            return success(fields);
        } catch (Exception e) {
            return fail("获取字段列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 按分类获取字段列表
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "按分类获取字段列表")
    @GetMapping("/fields/by-category")
    public ApiResult<?> getFieldsByCategory() {
        try {
            Map<String, List<FieldDefinition>> fieldMap = labelDesignService.getFieldsByCategory();
            return success(fieldMap);
        } catch (Exception e) {
            return fail("获取字段分类失败：" + e.getMessage());
        }
    }
    
    /**
     * 保存标签模板
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "保存标签模板")
    @PostMapping("/template")
    public ApiResult<?> saveTemplate(@RequestBody LabelTemplateDto templateDto) {
        try {
            LabelTemplate template = labelDesignService.saveTemplate(templateDto);
            return success("模板保存成功", template);
        } catch (Exception e) {
            return fail("保存失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取模板列表
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "获取模板列表")
    @GetMapping("/templates")
    public ApiResult<?> getTemplateList() {
        try {
            List<LabelTemplate> templates = labelDesignService.getTemplateList();
            return success(templates);
        } catch (Exception e) {
            return fail("获取模板列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取默认模板
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "获取默认模板")
    @GetMapping("/template/default")
    public ApiResult<?> getDefaultTemplate() {
        try {
            LabelTemplate template = labelDesignService.getDefaultTemplate();
            return success(template);
        } catch (Exception e) {
            return fail("获取默认模板失败：" + e.getMessage());
        }
    }

    
    /**
     * 获取模板详情
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "获取模板详情")
    @GetMapping("/template/{id}")
    public ApiResult<?> getTemplateById(@PathVariable String id) {
        try {
            LabelTemplate template = labelDesignService.getById(id);
            if (template != null) {
                return success(template);
            } else {
                return fail("模板不存在");
            }
        } catch (Exception e) {
            return fail("获取模板详情失败：" + e.getMessage());
        }
    }

    /**
     * 更新标签模板
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "更新标签模板")
    @PutMapping("/template/{id}")
    public ApiResult<?> updateTemplate(@PathVariable String id, @RequestBody LabelTemplateDto templateDto) {
        try {
            templateDto.setId(id);
            LabelTemplate template = labelDesignService.saveTemplate(templateDto);
            return success("模板更新成功", template);
        } catch (Exception e) {
            return fail("更新失败：" + e.getMessage());
        }
    }

    /**
     * 设置默认模板
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "设置默认模板")
    @PutMapping("/template/{id}/default")
    public ApiResult<?> setDefaultTemplate(@PathVariable String id) {
        try {
            LabelTemplate template = labelDesignService.setDefaultTemplate(id);
            return success("设置默认模板成功", template);
        } catch (Exception e) {
            return fail("设置默认模板失败：" + e.getMessage());
        }
    }

    /**
     * 删除标签模板
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "删除标签模板")
    @DeleteMapping("/template/{id}")
    public ApiResult<?> deleteTemplate(@PathVariable String id) {
        try {
            boolean result = labelDesignService.removeById(id);
            if (result) {
                return success("删除成功");
            } else {
                return fail("删除失败");
            }
        } catch (Exception e) {
            return fail("删除失败：" + e.getMessage());
        }
    }

    /**
     * 复制标签模板
     */
    @PreAuthorize("hasAuthority('banknote:label:design')")
    @OperationLog(module = "标签设计", comments = "复制标签模板")
    @PostMapping("/template/{id}/copy")
    public ApiResult<?> copyTemplate(@PathVariable String id, @RequestBody Map<String, String> request) {
        try {
            String newName = request.get("newName");
            if (newName == null || newName.trim().isEmpty()) {
                return fail("新模板名称不能为空");
            }

            LabelTemplate copiedTemplate = labelDesignService.copyTemplate(id, newName.trim());
            return success("模板复制成功", copiedTemplate);
        } catch (Exception e) {
            return fail("复制失败：" + e.getMessage());
        }
    }
}