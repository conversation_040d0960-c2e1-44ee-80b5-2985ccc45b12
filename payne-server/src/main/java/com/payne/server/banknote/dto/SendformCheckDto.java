package com.payne.server.banknote.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 送评单审核DTO
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@Data
public class SendformCheckDto {
    
    /**
     * 送评单号列表
     */
    @NotEmpty(message = "送评单号列表不能为空")
    private List<String> sendnums;
    
    /**
     * 审核状态 1-通过 2-驳回
     */
    @NotNull(message = "审核状态不能为空")
    private Integer checkStatus;
    
    /**
     * 审核意见/驳回原因
     */
    private String checkRemark;
    
    /**
     * 审核人
     */
    private String checker;
    
    /**
     * 审核时间
     */
    private String checkTime;
}
