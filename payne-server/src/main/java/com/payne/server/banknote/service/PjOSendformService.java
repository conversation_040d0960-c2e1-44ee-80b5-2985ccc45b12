package com.payne.server.banknote.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.payne.server.banknote.dto.SendformCheckDto;
import com.payne.server.banknote.entity.PjOSendform;
import com.payne.server.banknote.entity.PjOSendformItem;

import java.util.List;
import java.util.Map;

/**
 * 送评单Service接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PjOSendformService extends IService<PjOSendform> {

    boolean removeAndItemsByIds(List<String> ids);

    /**
     * 批量审核送评单
     *
     * @param checkDto 审核信息
     * @return 审核结果
     */
    boolean batchCheck(SendformCheckDto checkDto);

    /**
     * 审核通过
     *
     * @param sendnums 送评单号列表
     * @param checker 审核人
     * @param checkRemark 审核意见
     * @return 审核结果
     */
    boolean approveByBatch(List<String> sendnums, String checker, String checkRemark);

    /**
     * 审核驳回
     *
     * @param sendnums 送评单号列表
     * @param checker 审核人
     * @param checkRemark 驳回原因
     * @return 审核结果
     */
    boolean rejectByBatch(List<String> sendnums, String checker, String checkRemark);

    /**
     * 检查送评单下钱币是否都已完成打分（品相评分）
     *
     * @param sendnum 送评单号
     * @return 未打分的钱币列表
     */
    List<PjOSendformItem> checkUnScoredCoins(String sendnum);

    /**
     * 批量检查多个送评单的钱币打分情况
     *
     * @param sendnums 送评单号列表
     * @return 未打分钱币的统计信息，key为送评单号，value为未打分钱币列表
     */
    Map<String, List<PjOSendformItem>> batchCheckUnScoredCoins(List<String> sendnums);

    /**
     * 检查送评单是否可以审核（所有钱币都已打分）
     *
     * @param sendnums 送评单号列表
     * @return 检查结果，如果有未打分钱币则抛出异常
     */
    void validateCoinsScored(List<String> sendnums);

    /**
     * 开启扫码审核
     *
     * @param sendnums 送评单号列表
     * @return 操作结果
     */
    boolean enableScanAudit(List<String> sendnums);

    /**
     * 关闭扫码审核
     *
     * @param sendnums 送评单号列表
     * @return 操作结果
     */
    boolean disableScanAudit(List<String> sendnums);

    /**
     * 自动开启扫码审核（满足条件时）
     *
     * @param sendnums 送评单号列表
     * @return 操作结果，包含成功和失败的统计信息
     */
    Map<String, Object> autoEnableScanAudit(List<String> sendnums);

    /**
     * 检查送评单是否满足自动开启扫码审核的条件
     *
     * @param sendnums 送评单号列表
     * @return 检查结果，包含满足条件和不满足条件的送评单统计
     */
    Map<String, Object> checkAutoEnableConditions(List<String> sendnums);
}