package com.payne.server.banknote.dto;

import lombok.Data;

import java.util.List;

/**
 * 批量更新DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
public class BatchUpdateDto {
    
    /**
     * 要更新的钱币ID列表
     */
    private List<String> coinIds;
    
    /**
     * 要更新的字段数据
     */
    private UpdateFields updateFields;
    
    /**
     * 更新字段数据类
     */
    @Data
    public static class UpdateFields {
        
        /**
         * 真伪鉴定结果
         */
        private String authenticity;
        
        /**
         * 等级评分
         */
        private String gradeScore;
        
        /**
         * 地区
         */
        private String region;
        
        /**
         * 盒子类型
         */
        private String boxType;
        
        /**
         * 对内备注
         */
        private String internalNote;
        
        /**
         * 对外备注
         */
        private String externalNote;
        
        /**
         * 检验备注
         */
        private String inspectionNote;
        
        /**
         * 特殊标记
         */
        private String specialMark;
        
        /**
         * 评分备注
         */
        private String scoreRemarks;
        
        /**
         * 是否强制更新（覆盖现有数据）
         */
        private Boolean forceUpdate = false;
    }
} 