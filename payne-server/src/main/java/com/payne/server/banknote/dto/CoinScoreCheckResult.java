package com.payne.server.banknote.dto;

import com.payne.server.banknote.entity.PjOSendformItem;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 钱币打分检查结果DTO
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class CoinScoreCheckResult {

    /**
     * 是否有未打分的钱币
     */
    private Boolean hasUnScoredCoins;

    /**
     * 未打分钱币的详细信息
     * key: 送评单号
     * value: 该送评单下未打分的钱币列表
     */
    private Map<String, List<PjOSendformItem>> unScoredCoins;

    /**
     * 未打分钱币总数
     */
    private Integer totalUnScoredCount;

    /**
     * 受影响的送评单数量
     */
    private Integer affectedSendformCount;

    /**
     * 检查消息
     */
    private String message;

    /**
     * 详细错误信息（用于前端显示）
     */
    private List<String> errorDetails;

    /**
     * 构造方法 - 检查通过
     */
    public static CoinScoreCheckResult success() {
        CoinScoreCheckResult result = new CoinScoreCheckResult();
        result.setHasUnScoredCoins(false);
        result.setTotalUnScoredCount(0);
        result.setAffectedSendformCount(0);
        result.setMessage("所有钱币都已完成打分，可以进行审核");
        return result;
    }

    /**
     * 构造方法 - 检查失败
     */
    public static CoinScoreCheckResult failure(Map<String, List<PjOSendformItem>> unScoredCoins) {
        CoinScoreCheckResult result = new CoinScoreCheckResult();
        result.setHasUnScoredCoins(true);
        result.setUnScoredCoins(unScoredCoins);
        
        int totalCount = unScoredCoins.values().stream()
                .mapToInt(List::size)
                .sum();
        result.setTotalUnScoredCount(totalCount);
        result.setAffectedSendformCount(unScoredCoins.size());
        
        result.setMessage(String.format("发现 %d 个送评单共 %d 个钱币未完成打分", 
                unScoredCoins.size(), totalCount));
        
        return result;
    }
}
