package com.payne.server.banknote.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.payne.server.banknote.entity.PjOSendform;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 送评单Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface PjOSendformMapper extends BaseMapper<PjOSendform> {

    /**
     * 查询指定前缀的送评单号最大序号
     * @param datePrefix 日期前缀（如***********）
     * @return 最大序号，如果没有记录则返回0
     */
    Integer getMaxSendnumSequence(@Param("datePrefix") String datePrefix);
}