name: 部署demo-beta

on:
  push:
    tags-ignore:
      - 0.*
    branches:
      - main

jobs:
  deploy-beta-gh-pages:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: check args
        run: |
          echo "${{ github.event }}"
          echo "${{ github.event_path }}"
          echo "${{ github.workflow }}"
          echo "${{ github.job }}"
          echo "${{ github.run_id }}"
          echo "${{ github.repository }}"
          echo "${{ github.repository_owner }}"
          echo "${{ github.ref }}"

      - name: check diff
        id: check_diff
        uses: technote-space/get-diff-action@v6
        with:
          PATTERNS: |
            src/hiprint/**

      - name: check diff2
        id: check_diff2
        uses: technote-space/get-diff-action@v6
        with:
          PATTERNS: |
            src/demo/**
            public/**

      - name: 设置 Node.js
        if: ${{ steps.check_diff.outputs.diff || steps.check_diff2.outputs.diff }}
        uses: actions/setup-node@v3
        with:
          node-version: 16
          registry-url: 'https://registry.npmjs.org'

      - name: 安装依赖
        if: ${{ steps.check_diff.outputs.diff || steps.check_diff2.outputs.diff }}
        run: |
          echo "${{ github.ref }}"
          npm install

      - name: up-version
        if: ${{ steps.check_diff.outputs.diff && !startsWith(github.ref, 'refs/tags/0.') }}
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "github-actions[bot]"
          npm run up-version

      - name: push beta changes
        if: ${{ steps.check_diff.outputs.diff && !startsWith(github.ref, 'refs/tags/0.') }}
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: ${{ github.ref }}

      - name: pubBeta
        if: ${{ steps.check_diff.outputs.diff && !startsWith(github.ref, 'refs/tags/0.') }}
        run: npm run pub-beta
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: 构建demo
        if: ${{ steps.check_diff.outputs.diff || steps.check_diff2.outputs.diff }}
        env:
          NODE_OPTIONS: --max_old_space_size=8192
        run: |-
          npm run build-demo
          > demo/.nojekyll

      - name: githubPage
        if: ${{ steps.check_diff.outputs.diff || steps.check_diff2.outputs.diff }}
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          branch: gh-pages
          folder: demo

      - name: gitee同步
        uses: wearerequired/git-mirror-action@master
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_KEY }}
        with:
          source-repo: **************:CcSimple/vue-plugin-hiprint.git
          destination-repo: *************:CcSimple/vue-plugin-hiprint.git

      - name: giteePages
        if: ${{ steps.check_diff.outputs.diff || steps.check_diff2.outputs.diff }}
        uses: yanglbme/gitee-pages-action@master
        with:
          gitee-username: CcSimple
          gitee-password: ${{ secrets.GITEE_PASSWORD }}
          gitee-repo: CcSimple/vue-plugin-hiprint
          branch: gh-pages
