name: 部署demo

on:
  push:
    tags:
      - 0.*

jobs:
  deploy-gh-pages:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: check args
        run: |
          echo "${{ github.event }}"
          echo "${{ github.event_path }}"
          echo "${{ github.workflow }}"
          echo "${{ github.job }}"
          echo "${{ github.run_id }}"
          echo "${{ github.repository }}"
          echo "${{ github.repository_owner }}"
          echo "${{ github.ref }}"

      - name: 设置 Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16
          registry-url: 'https://registry.npmjs.org'

      - name: 安装依赖
        run: |
          echo "${{ github.ref }}"
          npm install

      - name: new-version
        if: ${{ startsWith(github.ref, 'refs/tags/0.') }}
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "github-actions[bot]"
          npm run up-version newVersion

      - name: push new changes
        if: ${{ startsWith(github.ref, 'refs/tags/0.') }}
        uses: ad-m/github-push-action@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          branch: main

      - name: pubNew
        if: ${{ startsWith(github.ref, 'refs/tags/0.') }}
        run: npm run pub
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

      - name: gitee同步
        uses: wearerequired/git-mirror-action@master
        env:
          SSH_PRIVATE_KEY: ${{ secrets.GITEE_KEY }}
        with:
          source-repo: **************:CcSimple/vue-plugin-hiprint.git
          destination-repo: *************:CcSimple/vue-plugin-hiprint.git
