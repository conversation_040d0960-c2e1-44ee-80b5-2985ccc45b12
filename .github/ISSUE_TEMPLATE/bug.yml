name: Bug 反馈
description: 报告问题以帮助我们改进
title: "[Bug]:"
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        感谢对项目的支持与关注。在提出问题之前，请确保你已查看相关开发或使用文档：
        - https://ccsimple.gitee.io/sv-print-docs/config/start.html
        - https://mp.weixin.qq.com/mp/appmsgalbum?action=getalbum&album_id=2779135389654630403
  - type: dropdown
    id: versiontag
    attributes:
      label: 使用的版本
      description: 请告诉我们你使用的是哪个版本
      options:
        - latest
        - beta
    validations:
      required: true
  - type: input
    id: version
    attributes:
      label: 版本号
      description: 如果你准确知道你的版本号，请告诉我们
  - type: dropdown
    id: installtype
    attributes:
      label: 你的安装方式
      description: 请告诉我们你是如何安装使用的
      options:
        - NPM 依赖
        - 下载源码 融合
        - CDN 引入
        - 其他
    validations:
      required: true
  - type: dropdown
    id: os
    attributes:
      label: 你的操作系统
      description: 请告诉我们你使用的是哪个操作系统
      options:
        - Windows
        - MacOS
        - Linux
        - 其他
    validations:
      required: true
  - type: dropdown
    id: browser
    attributes:
      label: 你的浏览器
      description: 请告诉我们你使用的是哪个浏览器
      options:
        - Chrome
        - Firefox
        - Safari
        - Edge
        - 其他
    validations:
      required: true
  - type: dropdown
    id: language
    attributes:
      label: 你的项目编程语言
      description: 请告诉我们你使用的是哪个编程语言
      options:
        - Vue 2
        - Vue 3
        - React
        - Angular
        - 原生 HTML
        - 其他
  - type: textarea
    attributes:
      label: 问题描述
      description: 清晰而简洁地描述您遇到的问题。
    validations:
      required: true
  - type: textarea
    attributes:
      label: 如何复现
      description: 请详细告诉我们如何复现你遇到的问题，如涉及代码，可提供一个最小代码示例，并使用反引号```附上它
      placeholder: |
        1. ...
        2. ...
        3. ...
    validations:
      required: true
  - type: textarea
    attributes:
      label: 预期结果
      description: 请告诉我们你预期会发生什么。
    validations:
      required: true
  - type: textarea
    attributes:
      label: 实际结果
      description: 请告诉我们实际发生了什么。
    validations:
      required: true
  - type: textarea
    attributes:
      label: 截图或视频
      description: 如果可以的话，上传任何关于 bug 的截图。
      value: |
        [在这里上传图片]
  - type: checkboxes
    attributes:
      label: 这个问题是否已经存在？
      options:
        - label: 我已经搜索过现有的问题 (https://github.com/CcSimple/vue-plugin-hiprint/issues)
          required: true

