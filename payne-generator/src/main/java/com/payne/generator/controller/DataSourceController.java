package com.payne.generator.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.generator.entity.DataSource;
import com.payne.generator.param.DataSourceParam;
import com.payne.generator.service.DataSourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 数据源配置表控制器
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/datasource")
public class DataSourceController extends BaseController {
    private final DataSourceService dataSourceService;

    /**
     * 分页查询数据源配置
     */
    @GetMapping("/page")
    public PageResult<DataSource> page(DataSourceParam param) {
        PageParam<DataSource, DataSourceParam> page = new PageParam<>(param);
        page.setDefaultOrder("create_time desc");
        page = dataSourceService.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部数据源配置
     */
    @GetMapping("/list")
    public List<DataSource> list(DataSourceParam param) {
        PageParam<DataSource, DataSourceParam> page = new PageParam<>(param);
        page.setDefaultOrder("create_time desc");
        return dataSourceService.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询数据源配置
     */
    @GetMapping("/{id}")
    public DataSource get(@PathVariable("id") String id) {
        return dataSourceService.getById(id);
    }

    /**
     * 添加或修改数据源配置
     */
    @OperationLog(module = "数据源配置", comments = "保存数据源配置")
    @PostMapping("/operation")
    public void save(@RequestBody DataSource dataSource) {
        if (StringUtils.hasLength(dataSource.getId())) {
            dataSourceService.updateById(dataSource);
            dataSource.setUpdateTime(new Date());
        } else {
            dataSource.setCreateTime(new Date());
            dataSourceService.save(dataSource);
        }
    }

    /**
     * 批量删除数据源配置
     */
    @OperationLog(module = "数据源配置", comments = "批量删除数据源配置")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        dataSourceService.removeByIds(ids);
    }

    /**
     * 测试数据源连接
     */
    @PostMapping("/test")
    public void testConnection(@RequestBody DataSource dataSource) {
        dataSourceService.testConnection(dataSource);
    }
} 