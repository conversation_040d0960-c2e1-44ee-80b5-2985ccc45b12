package com.payne.generator.param;

import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class TableInfoParam extends BaseParam {

    // 表名
    private String tableName;

    // 表注释
    private String tableComment;

    // 表的主键列名
    private String primaryKey;

    // 表的主键Java类型
    private String primaryKeyType;
}
