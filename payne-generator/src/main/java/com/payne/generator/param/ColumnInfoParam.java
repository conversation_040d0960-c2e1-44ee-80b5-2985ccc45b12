package com.payne.generator.param;

import com.payne.core.web.BaseParam;

public class ColumnInfoParam extends BaseParam {

    // 列名
    private String columnName;

    // 列注释
    private String columnComment;

    // 列类型
    private String columnType;

    // 是否主键
    private Boolean isPrimaryKey;

    // Java属性名
    private String attrname;

    // Java类型
    private String attrType;

    // 是否可为空
    private Boolean isNullable;

    // 是否查询字段
    private Boolean isQuery;
}
