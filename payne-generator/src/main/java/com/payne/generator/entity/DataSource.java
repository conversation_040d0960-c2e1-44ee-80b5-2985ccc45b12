package com.payne.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Entity
@TableName("dev_datasource")
@Table(name = "dev_datasource")
public class DataSource {

    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(32)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 数据源名称
     */
    private String name;
    
    /**
     * 数据库类型
     */
    private String dbType;
    
    /**
     * 主机地址
     */
    private String host;
    
    /**
     * 端口
     */
    private Integer port;
    
    /**
     * 数据库名/服务名
     */
    private String dbName;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 密码
     */
    private String password;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 修改时间
     */
    private Date updateTime;
} 