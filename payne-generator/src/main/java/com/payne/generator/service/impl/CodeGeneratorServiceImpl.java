package com.payne.generator.service.impl;

import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.generator.config.DynamicDataSourceManager;
import com.payne.generator.model.DataSourceConfig;
import com.payne.generator.model.GenConfig;
import com.payne.generator.param.ColumnInfoParam;
import com.payne.generator.param.TableInfoParam;
import com.payne.generator.service.CodeGeneratorService;
import com.payne.generator.utils.VelocityUtils;
import com.payne.generator.vo.ColumnInfo;
import com.payne.generator.vo.TableInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 代码生成服务实现
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@RequiredArgsConstructor
@Service
public class CodeGeneratorServiceImpl implements CodeGeneratorService {

    private final DynamicDataSourceManager dataSourceManager;

    private static final String[] TABLE_PREFIX = new String[]{
        "sys_",
        "tb_",
        ""
    };

    private static final String[] PARAM_EXCLUDE_FIELDS = new String[]{
            "tenant_id",
            "create_time",
            "update_time",
            "deleted"
    };

    private static final String[] PARAM_TO_STRING_TYPE = new String[]{
            "Date",
            "LocalDate",
            "LocalTime",
            "LocalDateTime"
    };

    private static final String[] PARAM_EQ_TYPE = new String[]{
            "Integer",
            "Boolean",
            "BigDecimal",
            "Long"
    };

    private static final boolean AUTH_ANNOTATION = true;
    private static final boolean LOG_ANNOTATION = true;
    private static final String CONTROLLER_MAPPING_PREFIX = "/api";
    @Override
    public List<TableInfo> getTables(DataSourceConfig dataSource, String tableName) {
        JdbcTemplate jdbcTemplate = dataSourceManager.createJdbcTemplate(dataSource);
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();

        if ("oracle".equalsIgnoreCase(dataSource.getDbType())) {
            sql.append("SELECT t.TABLE_NAME, NVL(c.COMMENTS, t.TABLE_NAME) AS TABLE_COMMENT ")
               .append("FROM USER_TABLES t ")
               .append("LEFT JOIN USER_TAB_COMMENTS c ON t.TABLE_NAME = c.TABLE_NAME ")
               .append("WHERE t.DROPPED = 'NO' ")  // 过滤掉已删除的表
               .append("AND t.TABLE_NAME NOT LIKE 'BIN$%' ")  // 过滤回收站表
               .append("AND t.TABLE_NAME NOT LIKE 'DR$%' ")   // 过滤Oracle Text表
               .append("AND t.TABLE_NAME NOT LIKE 'MDRT_%' ") // 过滤物化视图日志表
               .append("AND t.TABLE_NAME NOT LIKE 'MLOG$%' ") // 过滤物化视图日志表
               .append("AND t.TABLE_NAME NOT LIKE 'RUPD$%' "); // 过滤其他系统表
            if (StringUtils.hasText(tableName)) {
                sql.append("AND t.TABLE_NAME LIKE ?");
                params.add("%" + tableName.toUpperCase() + "%");
            }
            sql.append("ORDER BY t.TABLE_NAME");
        } else {
            sql.append("SELECT TABLE_NAME, IFNULL(TABLE_COMMENT, TABLE_NAME) AS TABLE_COMMENT ")
               .append("FROM INFORMATION_SCHEMA.TABLES ")
               .append("WHERE TABLE_SCHEMA = ?");
            params.add(dataSource.getDbName());
            if (StringUtils.hasText(tableName)) {
                sql.append(" AND TABLE_NAME LIKE ?");
                params.add("%" + tableName + "%");
            }
            sql.append(" ORDER BY TABLE_NAME");
        }

        return jdbcTemplate.query(sql.toString(), params.toArray(), (rs, rowNum) -> {
            TableInfo info = new TableInfo();
            info.setTableName(rs.getString("TABLE_NAME"));
            info.setTableComment(rs.getString("TABLE_COMMENT"));
            return info;
        });
    }

    @Override
    public List<ColumnInfo> getColumns(DataSourceConfig dataSource, String tableName) {
        JdbcTemplate jdbcTemplate = dataSourceManager.createJdbcTemplate(dataSource);
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();

        if ("oracle".equalsIgnoreCase(dataSource.getDbType())) {
            sql.append("SELECT A.COLUMN_NAME, A.DATA_TYPE, B.COMMENTS AS COLUMN_COMMENT, ")
               .append("CASE WHEN C.COLUMN_NAME IS NOT NULL THEN 'PRI' ELSE '' END AS COLUMN_KEY, ")
               .append("CASE WHEN A.NULLABLE = 'Y' THEN 'YES' ELSE 'NO' END AS IS_NULLABLE ")
               .append("FROM USER_TAB_COLUMNS A ")
               .append("LEFT JOIN USER_COL_COMMENTS B ON A.TABLE_NAME = B.TABLE_NAME AND A.COLUMN_NAME = B.COLUMN_NAME ")
               .append("LEFT JOIN USER_CONS_COLUMNS C ON A.TABLE_NAME = C.TABLE_NAME AND A.COLUMN_NAME = C.COLUMN_NAME ")
               .append("AND C.POSITION IS NOT NULL ")
               .append("WHERE A.TABLE_NAME = ?");
        } else {
            sql.append("SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT, COLUMN_KEY, IS_NULLABLE ")
               .append("FROM INFORMATION_SCHEMA.COLUMNS ")
               .append("WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?");
            params.add(dataSource.getDbName());
        }
        params.add(tableName.toUpperCase());

        return jdbcTemplate.query(sql.toString(), params.toArray(), (rs, rowNum) -> {
            ColumnInfo info = new ColumnInfo();
            info.setColumnName(rs.getString("COLUMN_NAME"));
            info.setColumnType(rs.getString("DATA_TYPE"));
            info.setColumnComment(rs.getString("COLUMN_COMMENT"));
            info.setIsPrimaryKey("PRI".equals(rs.getString("COLUMN_KEY")));
            info.setIsNullable("YES".equals(rs.getString("IS_NULLABLE")));

            // 转换为Java属性名和类型
            info.setAttrname(columnToJava(info.getColumnName()));
            info.setAttrType(getJavaType(info.getColumnType(), dataSource));

            // 设置是否为查询字段
            info.setIsQuery(false);
            // 设置是否为列表字段 - 添加这段逻辑
            String columnName = info.getColumnName().toLowerCase();
            // 排除不需要显示的字段
            boolean exclude = Arrays.asList("id", "tenant_id", "create_time",
                    "update_time", "deleted").contains(columnName);
            info.setIsList(!exclude);


            return info;
        });
    }

    // 添加更多上下文参数
    private Map<String, Object> buildContext(GenConfig config, TableInfo table, List<ColumnInfo> columns) {
        Map<String, Object> context = new HashMap<>();
        // 基础参数
        context.put("package", config.getPackageName());
        context.put("moduleName", config.getModuleName().toLowerCase());
        context.put("author", config.getAuthor());
        context.put("datetime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        context.put("tableName", table.getTableName());
        context.put("tableComment", table.getTableComment());
        context.put("className", tableToJava(table.getTableName()));
        context.put("columns", columns);

        // 添加更多参数
        context.put("paramExcludeFields", PARAM_EXCLUDE_FIELDS);
        context.put("paramToStringType", PARAM_TO_STRING_TYPE);
        context.put("paramEqType", PARAM_EQ_TYPE);
        context.put("authAnnotation", AUTH_ANNOTATION);
        context.put("logAnnotation", LOG_ANNOTATION);
        context.put("controllerMappingPrefix", CONTROLLER_MAPPING_PREFIX);

        return context;
    }

    @Override
    public byte[] generate(GenConfig config) {
        validateConfig(config);


        // 前后端路径为空时生成zip
        if (StringUtils.isEmpty(config.getFrontPath()) && StringUtils.isEmpty(config.getBackPath())) {
            return generateZip(config);
        } else {
            // 生成到指定路径
            generateToPath(config);
            return null;
        }
    }

    /**
     * 配置校验
     */
    private void validateConfig(GenConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("代码生成配置不能为空");
        }
        if (config.getDataSource() == null) {
            throw new IllegalArgumentException("数据源配置不能为空");
        }
        if (CollectionUtils.isEmpty(config.getTables())) {
            throw new IllegalArgumentException("表信息不能为空");
        }
        if (StringUtils.isEmpty(config.getPackageName())) {
            throw new IllegalArgumentException("包名不能为空");
        }
        if (StringUtils.isEmpty(config.getModuleName())) {
            throw new IllegalArgumentException("模块名不能为空");
        }
    }

    /**
     * 生成代码到zip包
     */
    private byte[] generateZip(GenConfig config) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);

        try {
            // 遍历表生成代码
            for (String tableName : config.getTables()) {
                generateTableCode(config, tableName, zip);
            }

            zip.close();
            return outputStream.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException("生成代码失败", e);
        }
    }

    /**
     * 生成代码到指定路径
     */
    private void generateToPath(GenConfig config) {
        try {
            // 遍历表生成代码
            for (String tableName : config.getTables()) {
                // 获取表信息
                List<ColumnInfo> columns = getColumns(config.getDataSource(), tableName);
                TableInfo table = getTables(config.getDataSource(), tableName).get(0);

                // 准备模板上下文
                Map<String, Object> context = buildContext(config, table, columns);

                // 生成后端代码
                if (Boolean.TRUE.equals(config.getGenerateBack())) {
                    for (String template : getBackTemplates()) {
                        String content = VelocityUtils.render(template, context);
                        String fileName = getBackFileName(template, config.getPackageName(),
                                tableToJava(table.getTableName()), config.getModuleName());
                        if (fileName != null) {
                            String filePath = config.getBackPath() + "/" + fileName;
                            writeFile(filePath, content);
                        }
                    }
                }

                // 生成前端代码
                if (Boolean.TRUE.equals(config.getGenerateFront())) {
                    for (String template : getFrontTemplates()) {
                        String content = VelocityUtils.render(template, context);
                        String fileName = getFrontFileName(template, config.getModuleName(),
                                tableToJava(table.getTableName()).toLowerCase());
                        if (fileName != null) {
                            String filePath = config.getFrontPath() + "/" + fileName;
                            writeFile(filePath, content);
                        }
                    }
                }

                // 生成SQL文件
                if (Boolean.TRUE.equals(config.getGenerateSql())) {
                    String content = VelocityUtils.render("menu.sql.vm", context);
                    String fileName = String.format("sql/%s_menu.sql", tableToJava(table.getTableName()).toLowerCase());
                    // 获取项目根目录
                    String projectRoot = new File("").getAbsolutePath();
                    String filePath = projectRoot + "/" + fileName;
                    writeFile(filePath, content);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("生成代码失败", e);
        }
    }

    /**
     * 生成单个表的代码
     */
    private void generateTableCode(GenConfig config, String tableName, ZipOutputStream zip) {
        try {
            // 获取表信息
            List<ColumnInfo> columns = getColumns(config.getDataSource(), tableName);
            if (columns.isEmpty()) {
                throw new RuntimeException("表" + tableName + "不存在或没有字段");
            }

            TableInfo table = getTables(config.getDataSource(), tableName).get(0);
            if (table == null) {
                throw new RuntimeException("表" + tableName + "不存在");
            }

            // 准备模板上下文
            Map<String, Object> context = buildContext(config, table, columns);

            try {
                // 生成后端代码
                if (Boolean.TRUE.equals(config.getGenerateBack())) {
                    for (String template : getBackTemplates()) {
                        String content = VelocityUtils.render(template, context);
                        String fileName = getBackFileName(template, config.getPackageName(),
                                tableToJava(table.getTableName()), config.getModuleName());
                        if (fileName != null) {
                            zip.putNextEntry(new ZipEntry(fileName));
                            IOUtils.write(content, zip, StandardCharsets.UTF_8);
                            zip.closeEntry();
                        }
                    }
                }

                // 生成前端代码
                if (Boolean.TRUE.equals(config.getGenerateFront())) {
                    for (String template : getFrontTemplates()) {
                        String content = VelocityUtils.render(template, context);
                        String fileName = getFrontFileName(template, config.getModuleName(),
                                tableToJava(table.getTableName()).toLowerCase());
                        if (fileName != null) {
                            zip.putNextEntry(new ZipEntry(fileName));
                            IOUtils.write(content, zip, StandardCharsets.UTF_8);
                            zip.closeEntry();
                        }
                    }
                }

                // 生成SQL文件
                if (Boolean.TRUE.equals(config.getGenerateSql())) {
                    String content = VelocityUtils.render("menu.sql.vm", context);
                    String fileName = String.format("sql/%s_menu.sql", tableToJava(table.getTableName()).toLowerCase());
                    zip.putNextEntry(new ZipEntry(fileName));
                    IOUtils.write(content, zip, StandardCharsets.UTF_8);
                    zip.closeEntry();
                }

            } catch (Exception e) {
                throw new RuntimeException("生成表" + tableName + "的代码失败: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new RuntimeException("处理表" + tableName + "失败: " + e.getMessage(), e);
        }
    }

    /**
     * 写入文件
     */
    private void writeFile(String filePath, String content) {
        try {
            File file = new File(filePath);
            // 创建父目录
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            FileUtils.writeStringToFile(file, content, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("写入文件失败: " + filePath, e);
        }
    }

    private String columnToJava(String columnName) {
        if (StringUtils.isEmpty(columnName)) {
            return "";
        }

        // 按下划线分割
        String[] parts = columnName.toLowerCase().split("_");
        StringBuilder result = new StringBuilder();

        // 转换为驼峰命名
        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            if (part.length() > 0) {
                if (i == 0) {
                    // 第一个单词小写
                    result.append(part.toLowerCase());
                } else {
                    // 其他单词首字母大写
                    result.append(part.substring(0, 1).toUpperCase())
                          .append(part.substring(1).toLowerCase());
                }
            }
        }

        return result.toString();
    }

    private String tableToJava(String tableName) {
        if (StringUtils.isEmpty(tableName)) {
            return "";
        }

        // 去掉前缀
        String tableNameWithoutPrefix = tableName;
        for (String prefix : TABLE_PREFIX) {
            if (tableName.toLowerCase().startsWith(prefix.toLowerCase())) {
                tableNameWithoutPrefix = tableName.substring(prefix.length());
                break;
            }
        }

        // 按下划线分割
        String[] parts = tableNameWithoutPrefix.toLowerCase().split("_");
        StringBuilder result = new StringBuilder();

        // 转换为驼峰命名
        for (String part : parts) {
            if (part.length() > 0) {
                result.append(part.substring(0, 1).toUpperCase())
                      .append(part.substring(1).toLowerCase());
            }
        }

        return result.toString();
    }

    private String getJavaType(String dataType, DataSourceConfig dataSource) {
        if (isOracle(dataSource)) {
            // 提取数据类型和精度信息
            String type = dataType.toUpperCase();
            String baseType = type.contains("(") ? type.substring(0, type.indexOf("(")) : type;
            String precision = type.contains("(") ? type.substring(type.indexOf("(") + 1, type.indexOf(")")) : "";
            switch (dataType.toUpperCase()) {
                case "NUMBER":
                    if (precision.isEmpty()) {
                        return "Long";
                    }
                    String[] parts = precision.split(",");
                    if (parts.length == 1) {
                        // 没有小数位
                        int length = Integer.parseInt(parts[0]);
                        if (length == 1) {
                            return "JudgeMark";
                        } else if (length <= 9) {
                            return "Integer";
                        } else {
                            return "Long";
                        }
                    } else if (parts.length == 2) {
                        // 有小数位
                        return "Double";
                    }
                    return "Long";

                case "VARCHAR2":
                case "NVARCHAR2":
                case "CHAR":
                case "NCHAR":
                case "CLOB":
                    return "String";
                case "TIMESTAMP":
                    return "Date";
                case "DATE":
                    return "Date";
                case "BLOB":
                    return "byte[]";
                case "DECIMAL":
                    return "BigDecimal";
                case "FLOAT":
                    return "Float";
                case "DOUBLE":
                    return "Double";
                case "BOOLEAN":
                    return "Boolean";
                default:
                    return "String";
            }
        } else {
            // MySQL数据类型映射
            switch (dataType.toLowerCase()) {
                case "tinyint":
                case "smallint":
                case "mediumint":
                case "int":
                case "integer":
                    return "Integer";
                case "bigint":
                    return "Long";
                case "float":
                    return "Float";
                case "double":
                    return "Double";
                case "decimal":
                    return "BigDecimal";
                case "bit":
                    return "Boolean";
                case "char":
                case "varchar":
                case "tinytext":
                case "text":
                case "mediumtext":
                case "longtext":
                    return "String";
                case "date":
                case "datetime":
                case "timestamp":
                    return "Date";
                default:
                    return "String";
            }
        }
    }

    private boolean isOracle(DataSourceConfig dataSource) {
        return "oracle".equalsIgnoreCase(dataSource.getDbType());
    }

    @Override
    public PageResult<TableInfo> pageTable(DataSourceConfig dataSource, String tableName, PageParam<TableInfo, TableInfoParam> page) {
        JdbcTemplate jdbcTemplate = dataSourceManager.createJdbcTemplate(dataSource);
        List<Object> params = new ArrayList<>();

        // 构建查询总数的SQL
        StringBuilder countSql = new StringBuilder();
        if ("oracle".equalsIgnoreCase(dataSource.getDbType())) {
            countSql.append("SELECT COUNT(1) FROM USER_TAB_COMMENTS WHERE TABLE_TYPE = 'TABLE' AND TABLE_NAME NOT LIKE 'BIN$%' ");
            if (StringUtils.hasText(tableName)) {
                countSql.append(" AND TABLE_NAME LIKE ?");
                params.add("%" + tableName.toUpperCase() + "%");
            }
        } else {
            countSql.append("SELECT COUNT(1) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ?");
            params.add(dataSource.getDbName());
            if (StringUtils.hasText(tableName)) {
                countSql.append(" AND TABLE_NAME LIKE ?");
                params.add("%" + tableName + "%");
            }
        }

        // 查询总数
        Long total = jdbcTemplate.queryForObject(countSql.toString(), params.toArray(), Long.class);

        // 清空参数列表，重新构建查询参数
        params.clear();

        // 构建分页查询SQL
        StringBuilder sql = new StringBuilder();
        if ("oracle".equalsIgnoreCase(dataSource.getDbType())) {
            sql.append("SELECT * FROM (")
               .append("SELECT A.*, ROWNUM RN FROM (")
               .append("SELECT TABLE_NAME, NVL(COMMENTS, TABLE_NAME) AS TABLE_COMMENT ")
               .append("FROM USER_TAB_COMMENTS WHERE TABLE_TYPE = 'TABLE' AND TABLE_NAME NOT LIKE 'BIN$%' ");
            if (StringUtils.hasText(tableName)) {
                sql.append(" AND TABLE_NAME LIKE ?");
                params.add("%" + tableName.toUpperCase() + "%");
            }
            sql.append(" ORDER BY TABLE_NAME")
               .append(") A WHERE ROWNUM <= ?) ")
               .append("WHERE RN > ?");
            params.add(page.getCurrent() * page.getSize());
            params.add((page.getCurrent() - 1) * page.getSize());
        } else {
            sql.append("SELECT TABLE_NAME, IFNULL(TABLE_COMMENT, TABLE_NAME) AS TABLE_COMMENT ")
               .append("FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = ?");
            params.add(dataSource.getDbName());
            if (StringUtils.hasText(tableName)) {
                sql.append(" AND TABLE_NAME LIKE ?");
                params.add("%" + tableName + "%");
            }
            sql.append(" ORDER BY TABLE_NAME")
               .append(" LIMIT ? OFFSET ?");
            params.add(page.getSize());
            params.add((page.getCurrent() - 1) * page.getSize());
        }

        List<TableInfo> records = jdbcTemplate.query(sql.toString(), params.toArray(), (rs, rowNum) -> {
            TableInfo info = new TableInfo();
            info.setTableName(rs.getString("TABLE_NAME"));
            info.setTableComment(rs.getString("TABLE_COMMENT"));
            return info;
        });

        return new PageResult<>(records, total);
    }

    @Override
    public PageResult<ColumnInfo> pageColumn(DataSourceConfig dataSource, String tableName, PageParam<ColumnInfo, ColumnInfoParam> page) {
        JdbcTemplate jdbcTemplate = dataSourceManager.createJdbcTemplate(dataSource);
        List<Object> params = new ArrayList<>();

        // 构建查询总数的SQL
        StringBuilder countSql = new StringBuilder();
        if ("oracle".equalsIgnoreCase(dataSource.getDbType())) {
            countSql.append("SELECT COUNT(1) FROM USER_TAB_COLUMNS A ")
                   .append("LEFT JOIN USER_COL_COMMENTS B ON A.TABLE_NAME = B.TABLE_NAME AND A.COLUMN_NAME = B.COLUMN_NAME ")
                   .append("LEFT JOIN USER_CONS_COLUMNS C ON A.TABLE_NAME = C.TABLE_NAME AND A.COLUMN_NAME = C.COLUMN_NAME ")
                   .append("AND C.POSITION IS NOT NULL ")
                   .append("WHERE A.TABLE_NAME = ?");
            params.add(tableName.toUpperCase());
        } else {
            countSql.append("SELECT COUNT(1) FROM INFORMATION_SCHEMA.COLUMNS ")
                   .append("WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ?");
            params.add(dataSource.getDbName());
            params.add(tableName);
        }

        // 查询总数
        Long total = jdbcTemplate.queryForObject(countSql.toString(), params.toArray(), Long.class);

        // 清空参数列表，重新构建查询参数
        params.clear();

        // 构建分页查询SQL
        StringBuilder sql = new StringBuilder();
        if ("oracle".equalsIgnoreCase(dataSource.getDbType())) {
            sql.append("SELECT * FROM (")
               .append("SELECT A.*, ROWNUM RN FROM (")
               .append("SELECT A.COLUMN_NAME, A.DATA_TYPE, B.COMMENTS AS COLUMN_COMMENT, ")
               .append("CASE WHEN C.COLUMN_NAME IS NOT NULL THEN 'PRI' ELSE '' END AS COLUMN_KEY, ")
               .append("CASE WHEN A.NULLABLE = 'Y' THEN 'YES' ELSE 'NO' END AS IS_NULLABLE ")
               .append("FROM USER_TAB_COLUMNS A ")
               .append("LEFT JOIN USER_COL_COMMENTS B ON A.TABLE_NAME = B.TABLE_NAME AND A.COLUMN_NAME = B.COLUMN_NAME ")
               .append("LEFT JOIN USER_CONS_COLUMNS C ON A.TABLE_NAME = C.TABLE_NAME AND A.COLUMN_NAME = C.COLUMN_NAME ")
               .append("AND C.POSITION IS NOT NULL ")
               .append("WHERE A.TABLE_NAME = ? ")
               .append("ORDER BY A.COLUMN_ID")
               .append(") A WHERE ROWNUM <= ?) ")  // 注意这里的空格
               .append("WHERE RN > ?");
            params.add(tableName.toUpperCase());
            params.add(page.getCurrent() * page.getSize());
            params.add((page.getCurrent() - 1) * page.getSize());
        } else {
            sql.append("SELECT COLUMN_NAME, DATA_TYPE, COLUMN_COMMENT, COLUMN_KEY, IS_NULLABLE ")
               .append("FROM INFORMATION_SCHEMA.COLUMNS ")
               .append("WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? ")
               .append("ORDER BY ORDINAL_POSITION ")
               .append("LIMIT ? OFFSET ?");
            params.add(dataSource.getDbName());
            params.add(tableName);
            params.add(page.getSize());
            params.add((page.getCurrent() - 1) * page.getSize());
        }

        List<ColumnInfo> records = jdbcTemplate.query(sql.toString(), params.toArray(), (rs, rowNum) -> {
            ColumnInfo info = new ColumnInfo();
            info.setColumnName(rs.getString("COLUMN_NAME"));
            info.setColumnType(rs.getString("DATA_TYPE"));
            info.setColumnComment(rs.getString("COLUMN_COMMENT"));
            info.setIsPrimaryKey("PRI".equals(rs.getString("COLUMN_KEY")));
            info.setIsNullable("YES".equals(rs.getString("IS_NULLABLE")));

            // 转换为Java属性名和类型
            info.setAttrname(columnToJava(info.getColumnName()));
            info.setAttrType(getJavaType(info.getColumnType(), dataSource));

            // 设置是否为查询字段
            info.setIsQuery(false);

            return info;
        });

        return new PageResult<>(records, total);
    }

    /**
     * 获取后端代码模板列表
     */
    private List<String> getBackTemplates() {
        List<String> templates = new ArrayList<>();
        templates.add("Entity.java.vm");
        templates.add("Mapper.java.vm");
        templates.add("Service.java.vm");
        templates.add("ServiceImpl.java.vm");
        templates.add("Controller.java.vm");
        templates.add("Param.java.vm");
        return templates;
    }

    /**
     * 获取前端代码模板列表
     */
    private List<String> getFrontTemplates() {
        List<String> templates = new ArrayList<>();
        templates.add("index.vue.vm");
        templates.add("api.js.vm");
        templates.add("search.vue.vm");
        templates.add("edit.vue.vm");
        return templates;
    }

    /**
     * 获取后端代码文件名
     */
    private String getBackFileName(String template, String packageName, String className, String moduleName) {
        String packagePath = packageName.replace(".", "/");
        if (template.contains("Entity.java.vm")) {
            return String.format("%s/entity/%s.java", packagePath, className);
        }
        if (template.contains("Mapper.java.vm")) {
            return String.format("%s/mapper/%sMapper.java", packagePath, className);
        }
        if (template.contains("Service.java.vm")) {
            return String.format("%s/service/%sService.java", packagePath, className);
        }
        if (template.contains("ServiceImpl.java.vm")) {
            return String.format("%s/service/impl/%sServiceImpl.java", packagePath, className);
        }
        if (template.contains("Controller.java.vm")) {
            return String.format("%s/controller/%sController.java", packagePath, className);
        }
        if (template.contains("Param.java.vm")) {
            return String.format("%s/param/%sParam.java", packagePath, className);
        }
        // 添加SQL文件名处理
        if (template.contains("menu.sql.vm")) {
            return String.format("sql/%s_menu.sql", className.toLowerCase());
        }
        return null;
    }

    /**
     * 获取前端代码文件名
     */
    private String getFrontFileName(String template, String moduleName, String className) {
        if (template.contains("index.vue.vm")) {
            return String.format("views/%s/%s/index.vue", moduleName, className);
        }
        if (template.contains("api.js.vm")) {
            return String.format("views/%s/%s/api/index.js", moduleName, className);
        }
        if (template.contains("search.vue.vm")) {
            return String.format("views/%s/%s/components/search.vue", moduleName, className);
        }
        if (template.contains("edit.vue.vm")) {
            return String.format("views/%s/%s/components/edit.vue", moduleName, className);
        }
        return null;
    }
} 