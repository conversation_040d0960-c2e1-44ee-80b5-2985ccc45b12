package com.payne.generator.service.impl;

import com.payne.generator.config.DynamicDataSourceManager;
import com.payne.generator.model.DataSourceConfig;
import com.payne.generator.vo.DatabaseSchema;
import com.payne.generator.vo.EnhancedColumnInfo;
import com.payne.generator.vo.EnhancedTableInfo;
import com.payne.generator.vo.ForeignKeyInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class EnhancedDatabaseSchemaExtractor {

    private final DynamicDataSourceManager dataSourceManager;
    // 依赖现有的 CodeGeneratorService 获取基础信息，避免重复写 SQL
    // private final CodeGeneratorService codeGeneratorService;

    /**
     * 提取指定数据源的数据库结构信息
     *
     * @param dataSourceConfig 数据源配置
     * @param targetTableNames 可选，只提取指定表的信息，为空则提取所有表
     * @return DatabaseSchema 数据库结构信息
     */
    public DatabaseSchema extractSchema(DataSourceConfig dataSourceConfig, List<String> targetTableNames) {
        JdbcTemplate jdbcTemplate = dataSourceManager.createJdbcTemplate(dataSourceConfig);
        String dbType = dataSourceConfig.getDbType().toLowerCase();
        String dbName = dataSourceConfig.getDbName(); // 对于 Oracle，这可能是用户名/Schema名

        DatabaseSchema schema = new DatabaseSchema();
        schema.setDataSourceType(dbType);
        schema.setDatabaseName(dbName);

        try {
            // 1. 获取所有或指定的表信息
            List<EnhancedTableInfo> tables = getTables(jdbcTemplate, dbType, dbName, targetTableNames);
            if (CollectionUtils.isEmpty(tables)) {
                log.warn("No tables found for dataSource: {}, dbName: {}", dataSourceConfig.getHost(), dbName);
                schema.setTables(Collections.emptyList());
                schema.setTableMap(Collections.emptyMap());
                return schema;
            }
            Map<String, EnhancedTableInfo> tableMap = tables.stream()
                    .collect(Collectors.toMap(t -> t.getTableName().toUpperCase(), Function.identity())); // 使用大写作为 Key，方便查找

            // 2. 获取所有相关表的列信息
            List<String> tableNamesToFetchColumns = tables.stream().map(EnhancedTableInfo::getTableName).collect(Collectors.toList());
            Map<String, List<EnhancedColumnInfo>> columnsMap = getColumns(jdbcTemplate, dbType, dbName, tableNamesToFetchColumns);

            // 3. 获取外键信息 (处理所有涉及到的表)
            Map<String, List<ForeignKeyInfo>> foreignKeysMap = getForeignKeys(jdbcTemplate, dbType, dbName, tableNamesToFetchColumns);

            // 4. 组装信息
            for (EnhancedTableInfo table : tables) {
                String upperTableName = table.getTableName().toUpperCase();
                List<EnhancedColumnInfo> tableColumns = columnsMap.getOrDefault(upperTableName, Collections.emptyList());
                List<ForeignKeyInfo> tableForeignKeys = foreignKeysMap.getOrDefault(upperTableName, Collections.emptyList());

                table.setColumns(tableColumns);
                table.setForeignKeys(tableForeignKeys); // 表定义的外键（指向其他表）

                // 填充列的外键信息
                Map<String, ForeignKeyInfo> fkColumnMap = tableForeignKeys.stream()
                        .collect(Collectors.toMap(fk -> fk.getSourceColumnName().toUpperCase(), Function.identity(), (k1, k2) -> k1)); // 列可能参与多个外键，这里简化处理，取第一个

                for (EnhancedColumnInfo column : tableColumns) {
                    ForeignKeyInfo fkInfo = fkColumnMap.get(column.getColumnName().toUpperCase());
                    if (fkInfo != null) {
                        column.setIsForeignKey(true);
                        column.setReferencedTableName(fkInfo.getTargetTableName());
                        column.setReferencedColumnName(fkInfo.getTargetColumnName());
                    }
                }

                // 可以在这里添加加载和附加业务元数据的逻辑
                // loadAndAttachBusinessMetadata(table);
            }

            // 计算被引用关系 (referencedByKeys) - 可选
            Map<String, List<ForeignKeyInfo>> referencedByMap = new HashMap<>();
            foreignKeysMap.values().stream().flatMap(List::stream).forEach(fk -> {
                referencedByMap.computeIfAbsent(fk.getTargetTableName().toUpperCase(), k -> new ArrayList<>()).add(fk);
            });
            for (EnhancedTableInfo table : tables) {
                table.setReferencedByKeys(referencedByMap.getOrDefault(table.getTableName().toUpperCase(), Collections.emptyList()));
            }


            schema.setTables(tables);
            schema.setTableMap(tableMap);

            // 可以在这里附加全局业务元数据
            // schema.setGlobalBusinessDescription("...");

        } catch (Exception e) {
            log.error("Error extracting schema for dataSource: {}, dbName: {}", dataSourceConfig.getHost(), dbName, e);
            // 可以选择抛出异常或返回部分结果/空结果
            throw new RuntimeException("Failed to extract database schema", e);
        }

        return schema;
    }

    // --- 私有辅助方法 ---

    /**
     * 获取表信息 (表名, 表注释)
     */
    private List<EnhancedTableInfo> getTables(JdbcTemplate jdbcTemplate, String dbType, String dbName, List<String> targetTableNames) {
        StringBuilder sql = new StringBuilder();
        List<Object> params = new ArrayList<>();
        boolean filterTables = !CollectionUtils.isEmpty(targetTableNames);

        if ("oracle".equalsIgnoreCase(dbType)) {
            sql.append("SELECT t.TABLE_NAME, NVL(c.COMMENTS, t.TABLE_NAME) AS TABLE_COMMENT ")
               .append("FROM USER_TABLES t ")
               .append("LEFT JOIN USER_TAB_COMMENTS c ON t.TABLE_NAME = c.TABLE_NAME AND c.TABLE_TYPE = 'TABLE' ") // 确保是表注释
               .append("WHERE t.DROPPED = 'NO' ") // 过滤掉已删除的表
               .append("AND t.TABLE_NAME NOT LIKE 'BIN$%' ") // 过滤回收站表
               .append("AND t.TABLE_NAME NOT LIKE 'DR$%' ")   // 过滤Oracle Text表
               .append("AND t.TABLE_NAME NOT LIKE 'MDRT_%' ") // 过滤物化视图日志表
               .append("AND t.TABLE_NAME NOT LIKE 'MLOG$%' ") // 过滤物化视图日志表
               .append("AND t.TABLE_NAME NOT LIKE 'RUPD$%' "); // 过滤其他系统表
            // 注意：Oracle 的 dbName 通常是 Schema/User 名，默认查询当前用户的表，所以不需要 TABLE_SCHEMA 条件
             if (filterTables) {
                sql.append("AND t.TABLE_NAME IN (");
                for (int i = 0; i < targetTableNames.size(); i++) {
                    sql.append(i == 0 ? "?" : ", ?");
                    params.add(targetTableNames.get(i).toUpperCase()); // Oracle 表名通常大写
                }
                sql.append(") ");
            }
            sql.append("ORDER BY t.TABLE_NAME");
        } else { // 默认按 MySQL 处理
            sql.append("SELECT TABLE_NAME, IFNULL(TABLE_COMMENT, TABLE_NAME) AS TABLE_COMMENT ")
               .append("FROM INFORMATION_SCHEMA.TABLES ")
               .append("WHERE TABLE_SCHEMA = ? ");
            params.add(dbName);
            if (filterTables) {
                sql.append("AND TABLE_NAME IN (");
                for (int i = 0; i < targetTableNames.size(); i++) {
                    sql.append(i == 0 ? "?" : ", ?");
                    params.add(targetTableNames.get(i)); // MySQL 表名可能大小写敏感，按传入的来
                }
                sql.append(") ");
            }
            sql.append("ORDER BY TABLE_NAME");
        }

        log.debug("Executing SQL to get tables: {} with params: {}", sql, params);
        return jdbcTemplate.query(sql.toString(), params.toArray(), (rs, rowNum) -> {
            EnhancedTableInfo info = new EnhancedTableInfo();
            info.setTableName(rs.getString("TABLE_NAME"));
            info.setTableComment(rs.getString("TABLE_COMMENT"));
            // 可以在这里提前加载表级别的业务元数据
            // info.setBusinessDescription(fetchTableBusinessMetadata(info.getTableName()));
            return info;
        });
    }

    /**
     * 获取列信息 (列名, 类型, 注释, 主键, 可空)
     * 返回 Map<大写表名, List<列信息>>
     */
    private Map<String, List<EnhancedColumnInfo>> getColumns(JdbcTemplate jdbcTemplate, String dbType, String dbName, List<String> tableNames) {
         if (CollectionUtils.isEmpty(tableNames)) {
            return Collections.emptyMap();
        }
        Map<String, List<EnhancedColumnInfo>> resultMap = new HashMap<>();
        // 分批处理，避免 SQL IN 子句过长
        int batchSize = 100; // Or a reasonable number based on DB limits
        List<List<String>> batches = partitionList(tableNames, batchSize);

        for (List<String> batchTableNames : batches) {
            StringBuilder sql = new StringBuilder();
            List<Object> params = new ArrayList<>();

            if ("oracle".equalsIgnoreCase(dbType)) {
                sql.append("SELECT A.TABLE_NAME, A.COLUMN_NAME, A.DATA_TYPE, NVL(B.COMMENTS, A.COLUMN_NAME) AS COLUMN_COMMENT, ")
                   .append("A.DATA_DEFAULT AS COLUMN_DEFAULT, ") // 获取默认值
                   .append("CASE WHEN D.CONSTRAINT_TYPE = 'P' THEN 'PRI' ELSE '' END AS COLUMN_KEY, ") // 通过约束类型判断主键
                   .append("CASE WHEN A.NULLABLE = 'Y' THEN 'YES' ELSE 'NO' END AS IS_NULLABLE ")
                   .append("FROM USER_TAB_COLUMNS A ")
                   .append("LEFT JOIN USER_COL_COMMENTS B ON A.TABLE_NAME = B.TABLE_NAME AND A.COLUMN_NAME = B.COLUMN_NAME ")
                   .append("LEFT JOIN USER_CONS_COLUMNS C ON A.TABLE_NAME = C.TABLE_NAME AND A.COLUMN_NAME = C.COLUMN_NAME ")
                   .append("LEFT JOIN USER_CONSTRAINTS D ON C.CONSTRAINT_NAME = D.CONSTRAINT_NAME AND D.CONSTRAINT_TYPE = 'P' ") // 关联约束表判断是否主键
                   .append("WHERE A.TABLE_NAME IN (");
                 for (int i = 0; i < batchTableNames.size(); i++) {
                    sql.append(i == 0 ? "?" : ", ?");
                    params.add(batchTableNames.get(i).toUpperCase());
                }
                sql.append(") ORDER BY A.TABLE_NAME, A.COLUMN_ID"); // 按列 ID 排序
            } else { // 默认按 MySQL 处理
                 sql.append("SELECT TABLE_NAME, COLUMN_NAME, COLUMN_TYPE AS DATA_TYPE, IFNULL(COLUMN_COMMENT, COLUMN_NAME) AS COLUMN_COMMENT, ") // MySQL 用 COLUMN_TYPE
                    .append("COLUMN_DEFAULT, COLUMN_KEY, IS_NULLABLE ")
                    .append("FROM INFORMATION_SCHEMA.COLUMNS ")
                    .append("WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN (");
                params.add(dbName);
                for (int i = 0; i < batchTableNames.size(); i++) {
                    sql.append(i == 0 ? "?" : ", ?");
                    params.add(batchTableNames.get(i));
                }
                sql.append(") ORDER BY TABLE_NAME, ORDINAL_POSITION");
            }

            log.debug("Executing SQL to get columns for tables {}: {} with params: {}", batchTableNames, sql, params);
            jdbcTemplate.query(sql.toString(), params.toArray(), (rs) -> { // 使用 ResultSetExtractor 或 RowCallbackHandler 处理结果，避免加载整个结果集到内存
                String tableName = rs.getString("TABLE_NAME").toUpperCase();
                EnhancedColumnInfo info = mapRowToEnhancedColumnInfo(rs, dbType);
                resultMap.computeIfAbsent(tableName, k -> new ArrayList<>()).add(info);
            });
        }
        return resultMap;
    }

     /**
     * 从 ResultSet 映射到 EnhancedColumnInfo
     */
    private EnhancedColumnInfo mapRowToEnhancedColumnInfo(ResultSet rs, String dbType) throws SQLException {
        EnhancedColumnInfo info = new EnhancedColumnInfo();
        info.setColumnName(rs.getString("COLUMN_NAME"));
        // Oracle 返回 DATA_TYPE (e.g., VARCHAR2, NUMBER), MySQL 返回 COLUMN_TYPE (e.g., varchar(255), int(11))
        // 为了让 LLM 更好理解，可以考虑做一些标准化或提供原始类型和推断的通用类型
        info.setColumnType(rs.getString("DATA_TYPE"));
        info.setColumnComment(rs.getString("COLUMN_COMMENT"));
        info.setIsPrimaryKey("PRI".equalsIgnoreCase(rs.getString("COLUMN_KEY"))); // 统一用 PRI 判断
        info.setIsNullable("YES".equalsIgnoreCase(rs.getString("IS_NULLABLE")));

        // 尝试获取并设置默认值
        try {
            String defaultValue = rs.getString("COLUMN_DEFAULT");
            if (defaultValue != null && !"NULL".equalsIgnoreCase(defaultValue)) { // Oracle 默认值可能是 'NULL' 字符串
                 // 可能需要进一步处理默认值字符串，例如去除引号
                 info.setAttrname(defaultValue.trim()); // 暂时存放在 attrname，后续可能需要单独字段
            }
        } catch (SQLException e) {
            // 有些数据库或驱动可能不支持获取 COLUMN_DEFAULT，忽略错误
             log.trace("Could not retrieve COLUMN_DEFAULT for column {}", info.getColumnName(), e);
        }


        // 转换 Java 属性名和类型 (可以复用 CodeGeneratorServiceImpl 的逻辑，如果需要的话)
        // info.setAttrname(columnToJava(info.getColumnName()));
        // info.setAttrType(getJavaType(info.getColumnType(), dbType)); // 需要 getJavaType 辅助方法

        // 可以在这里加载列级别的业务元数据
        // info.setBusinessDescription(fetchColumnBusinessMetadata(rs.getString("TABLE_NAME"), info.getColumnName()));

        return info;
    }

    /**
     * 获取外键信息
     * 返回 Map<大写源表名, List<外键信息>>
     */
    private Map<String, List<ForeignKeyInfo>> getForeignKeys(JdbcTemplate jdbcTemplate, String dbType, String dbName, List<String> tableNames) {
         if (CollectionUtils.isEmpty(tableNames)) {
            return Collections.emptyMap();
        }
        Map<String, List<ForeignKeyInfo>> resultMap = new HashMap<>();
        // 分批处理
        int batchSize = 100;
        List<List<String>> batches = partitionList(tableNames, batchSize);

        for (List<String> batchTableNames : batches) {
             StringBuilder sql = new StringBuilder();
             List<Object> params = new ArrayList<>();

            if ("oracle".equalsIgnoreCase(dbType)) {
                 // 查询当前用户/Schema 下的外键约束
                 sql.append("SELECT ")
                    .append("a.constraint_name, ")
                    .append("a.table_name AS source_table_name, ")
                    .append("ac.column_name AS source_column_name, ")
                    .append("r.table_name AS target_table_name, ")
                    .append("rc.column_name AS target_column_name ")
                    .append("FROM user_constraints a ")
                    .append("JOIN user_cons_columns ac ON a.constraint_name = ac.constraint_name AND a.table_name = ac.table_name ")
                    .append("JOIN user_constraints r ON a.r_constraint_name = r.constraint_name ")
                    .append("JOIN user_cons_columns rc ON r.constraint_name = rc.constraint_name AND r.table_name = rc.table_name AND ac.position = rc.position ") // 确保复合主键列对应
                    .append("WHERE a.constraint_type = 'R' ") // 'R' for Referential integrity (Foreign Key)
                    .append("AND a.table_name IN (");
                 for (int i = 0; i < batchTableNames.size(); i++) {
                    sql.append(i == 0 ? "?" : ", ?");
                    params.add(batchTableNames.get(i).toUpperCase());
                }
                sql.append(") ORDER BY a.table_name, ac.position");

            } else { // 默认按 MySQL 处理
                 sql.append("SELECT ")
                    .append("kcu.CONSTRAINT_NAME, ")
                    .append("kcu.TABLE_NAME AS source_table_name, ")
                    .append("kcu.COLUMN_NAME AS source_column_name, ")
                    .append("kcu.REFERENCED_TABLE_NAME AS target_table_name, ")
                    .append("kcu.REFERENCED_COLUMN_NAME AS target_column_name ")
                    .append("FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu ")
                    // 可以选择性 JOIN REFERENTIAL_CONSTRAINTS 获取更多信息，但 KEY_COLUMN_USAGE 通常足够
                    // .append("JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc ")
                    // .append("ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME AND kcu.CONSTRAINT_SCHEMA = rc.CONSTRAINT_SCHEMA ")
                    .append("WHERE kcu.TABLE_SCHEMA = ? ")
                    .append("AND kcu.REFERENCED_TABLE_NAME IS NOT NULL ") // 必须是外键
                    .append("AND kcu.TABLE_NAME IN (");
                 params.add(dbName);
                 for (int i = 0; i < batchTableNames.size(); i++) {
                    sql.append(i == 0 ? "?" : ", ?");
                    params.add(batchTableNames.get(i));
                }
                 sql.append(") ORDER BY kcu.TABLE_NAME, kcu.ORDINAL_POSITION");
            }
            log.debug("Executing SQL to get foreign keys for tables {}: {} with params: {}", batchTableNames, sql, params);

            jdbcTemplate.query(sql.toString(), params.toArray(), (rs) -> {
                ForeignKeyInfo info = new ForeignKeyInfo();
                info.setConstraintName(rs.getString("constraint_name"));
                String sourceTable = rs.getString("source_table_name").toUpperCase();
                info.setSourceTableName(sourceTable);
                info.setSourceColumnName(rs.getString("source_column_name"));
                info.setTargetTableName(rs.getString("target_table_name"));
                info.setTargetColumnName(rs.getString("target_column_name"));
                resultMap.computeIfAbsent(sourceTable, k -> new ArrayList<>()).add(info);
            });
        }
        return resultMap;
    }


    /**
     * 将列表分割成指定大小的子列表
     */
     private <T> List<List<T>> partitionList(List<T> list, int size) {
        if (list == null || list.isEmpty() || size <= 0) {
            return Collections.singletonList(list);
        }
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(list.subList(i, Math.min(i + size, list.size())));
        }
        return partitions;
    }

    // --- TODO: 业务元数据加载逻辑 ---
    /*
    private String fetchTableBusinessMetadata(String tableName) {
        // 实现从配置、数据库或其他来源加载表级别的业务描述
        return "业务描述 for " + tableName;
    }

    private String fetchColumnBusinessMetadata(String tableName, String columnName) {
        // 实现从配置、数据库或其他来源加载列级别的业务描述/规则
         return "业务规则 for " + tableName + "." + columnName;
    }

    private void loadAndAttachBusinessMetadata(EnhancedTableInfo table) {
        table.setBusinessDescription(fetchTableBusinessMetadata(table.getTableName()));
        if (table.getColumns() != null) {
            for (EnhancedColumnInfo column : table.getColumns()) {
                column.setBusinessDescription(fetchColumnBusinessMetadata(table.getTableName(), column.getColumnName()));
            }
        }
    }
    */
}