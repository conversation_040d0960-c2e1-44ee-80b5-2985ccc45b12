package com.payne.generator.service;

import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import com.payne.generator.model.DataSourceConfig;
import com.payne.generator.model.GenConfig;
import com.payne.generator.param.ColumnInfoParam;
import com.payne.generator.param.TableInfoParam;
import com.payne.generator.vo.ColumnInfo;
import com.payne.generator.vo.TableInfo;

import java.util.List;

/**
 * 代码生成服务接口
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
public interface CodeGeneratorService {
    
    /**
     * 获取数据库表信息
     */
    List<TableInfo> getTables(DataSourceConfig dataSource, String tableName);
    
    /**
     * 获取表字段信息
     */
    List<ColumnInfo> getColumns(DataSourceConfig dataSource, String tableName);
    
    /**
     * 生成代码
     */
    byte[] generate(GenConfig config);
    
    /**
     * 分页查询表信息
     */
    PageResult<TableInfo> pageTable(DataSourceConfig dataSource, String tableName, PageParam<TableInfo, TableInfoParam> page);
    
    /**
     * 分页查询字段信息
     */
    PageResult<ColumnInfo> pageColumn(DataSourceConfig dataSource, String tableName, PageParam<ColumnInfo, ColumnInfoParam> page);
} 