package com.payne.generator.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.payne.generator.config.DynamicDataSourceManager;
import com.payne.generator.entity.DataSource;
import com.payne.generator.mapper.DataSourceMapper;
import com.payne.generator.model.DataSourceConfig;
import com.payne.generator.service.DataSourceService;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;


/**
 * 数据源配置服务实现
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@RequiredArgsConstructor
@Service
public class DataSourceServiceImpl extends ServiceImpl<DataSourceMapper, DataSource> implements DataSourceService {

    private final DynamicDataSourceManager dataSourceManager;

    @Override
    public void testConnection(DataSource dataSource) {
        try {
            JdbcTemplate jdbcTemplate = dataSourceManager.createJdbcTemplate(convertToConfig(dataSource));
            // 测试查询
            if ("oracle".equalsIgnoreCase(dataSource.getDbType())) {
                jdbcTemplate.queryForObject("SELECT 1 FROM DUAL", Integer.class);
            } else {
                jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            }
        } catch (Exception e) {
            throw new RuntimeException("数据源连接失败: " + e.getMessage());
        }
    }
    
    /**
     * 将DataSource实体转换为DataSourceConfig
     */
    private DataSourceConfig convertToConfig(DataSource dataSource) {
        DataSourceConfig config = new DataSourceConfig();
        config.setDbType(dataSource.getDbType());
        config.setHost(dataSource.getHost());
        config.setPort(dataSource.getPort());
        config.setDbName(dataSource.getDbName());
        config.setUsername(dataSource.getUsername());
        config.setPassword(dataSource.getPassword());
        return config;
    }
} 