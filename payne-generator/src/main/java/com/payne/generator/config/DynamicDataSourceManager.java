package com.payne.generator.config;

import com.payne.generator.model.DataSourceConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

@Component
public class DynamicDataSourceManager {
    
    /**
     * 创建数据源
     */
    public JdbcTemplate createJdbcTemplate(DataSourceConfig config) {
        HikariConfig hikariConfig = new HikariConfig();
        
        // 根据数据库类型设置不同的URL格式和字符集
        if ("oracle".equalsIgnoreCase(config.getDbType())) {
            hikariConfig.setJdbcUrl(String.format(
                "************************************************",
                config.getHost(), config.getPort(), config.getDbName()
            ));
        } else {
            hikariConfig.setJdbcUrl(String.format(
                "**********************************************************************************************************************************************************",
                config.getHost(), config.getPort(), config.getDbName()
            ));
        }
        
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName(getDriverClassName(config.getDbType()));
        hikariConfig.setConnectionTestQuery(getValidationQuery(config.getDbType()));
        
        return new JdbcTemplate(new HikariDataSource(hikariConfig));
    }
    
    private String getDriverClassName(String dbType) {
        return "oracle".equalsIgnoreCase(dbType) 
            ? "oracle.jdbc.OracleDriver" 
            : "com.mysql.cj.jdbc.Driver";
    }
    
    private String getValidationQuery(String dbType) {
        return "oracle".equalsIgnoreCase(dbType) 
            ? "SELECT 1 FROM DUAL" 
            : "SELECT 1";
    }
} 