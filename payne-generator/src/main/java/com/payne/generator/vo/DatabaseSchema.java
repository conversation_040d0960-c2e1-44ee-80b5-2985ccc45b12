package com.payne.generator.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class DatabaseSchema {
    private String dataSourceType; // mysql, oracle
    private String databaseName;
    private List<EnhancedTableInfo> tables;
    // 可以考虑用 Map 优化查找
    private Map<String, EnhancedTableInfo> tableMap;

    // 可以在这里附加全局的业务规则或描述
    private String globalBusinessDescription;
}