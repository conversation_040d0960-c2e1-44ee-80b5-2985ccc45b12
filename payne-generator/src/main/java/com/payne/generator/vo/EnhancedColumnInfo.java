package com.payne.generator.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EnhancedColumnInfo extends ColumnInfo { // 或者直接包含 ColumnInfo 成员
    // 是否外键
    private Boolean isForeignKey = false;
    // 引用的目标表名 (如果 isForeignKey 为 true)
    private String referencedTableName;
    // 引用的目标列名 (如果 isForeignKey 为 true)
    private String referencedColumnName;

    // 可以预留字段用于存储业务元数据
    private String businessDescription;
    private String businessRule;
}