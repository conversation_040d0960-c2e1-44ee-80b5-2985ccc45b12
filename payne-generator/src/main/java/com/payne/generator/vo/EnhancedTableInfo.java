package com.payne.generator.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class EnhancedTableInfo extends TableInfo { // 或者直接包含 TableInfo 成员
    // 表包含的列信息
    private List<EnhancedColumnInfo> columns;
    // 表的外键信息 (指向其他表) - 可选，也可以把外键信息放在 EnhancedColumnInfo 里
    private List<ForeignKeyInfo> foreignKeys;
    // 引用该表的外键信息 (被其他表引用) - 可选，用于更全面地了解关系
    private List<ForeignKeyInfo> referencedByKeys;

    // 可以预留字段用于存储业务元数据
    private String businessDescription;
}