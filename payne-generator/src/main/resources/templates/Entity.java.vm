package ${package}.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * $!{tableComment}
 *
 * <AUTHOR>
 * @since ${datetime}
 */

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("${tableName}")
@Entity
@Table(name = "${tableName}")
public class ${className} implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID", columnDefinition = "VARCHAR2(32)")
    @TableId(value = "ID", type = IdType.ASSIGN_UUID)
    private String id;

#foreach ($column in $columns)
#if($column.columnName != "ID")
    /**
     * $!{column.columnComment}
     */
    @Column(name = "${column.columnName}")
    @TableField("${column.columnName}")
    private ${column.attrType} ${column.attrname};

#end
#end
} 