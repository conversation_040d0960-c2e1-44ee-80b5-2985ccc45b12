<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="syt${className}"
      >
        <template #toolbar>
          <el-button
                  type="primary"
                  size="small"
                  class="ele-btn-icon"
                  :icon="PlusOutlined"
                  @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
                  type="danger"
                  size="small"
                  class="ele-btn-icon"
                  :icon="DeleteOutlined"
                  @click="remove()"
          >
            删除
          </el-button>
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
        <!-- 添加状态列的插槽 -->
        <template #status="{ row }">
          <el-tag :type="row.status === '启用' ? 'success' : 'info'">
            {{ row.status === '启用' ? '启用' : '停用' }}
          </el-tag>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />
  </ele-page>
</template>

<script setup>
  import {reactive, ref} from 'vue';
  import {ElMessageBox} from 'element-plus/es';
  import {EleMessage} from 'ele-admin-plus/es';
  import {queryPage, removes} from './api';
  import { DeleteOutlined, PlusOutlined } from '@/components/icons';
  import Edit from './components/edit.vue';
  import Search from './components/search.vue';

  /** 表格实例 */
  const tableRef = ref(null);

  /** 获取标签显示文本 */
  const getLabelByValue = (value, options) => {
    const option = options.find((item) => item.value === value);
    return option ? option.label : value;
  };

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
#foreach($column in $columns)
#if($column.isList)
    {
      prop: '${column.attrname}',
      label: '${column.columnComment}'#if($column.attrname == 'name'),
      minWidth: 110#end#if($column.attrname == 'status'),
      align: 'center',
      width: 100,
      slot: 'status'#end
    },
#end
#end
    {
      columnKey: 'action',
      label: '操作',
      slot: 'action',
      width: 140,
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return queryPage({ ...where, ...orders, ...filters, page, limit });
  };

  /** 表格搜索参数 */
  const lastWhere = reactive({});

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
            '确定要删除"' + rows.map((d) => d.name).join(', ') + '"吗?',
            '系统提示',
            { type: 'warning', draggable: true }
    )
            .then(() => {
              const loading = EleMessage.loading('请求中..');
              removes(rows.map((d) => d.id))
                      .then((msg) => {
                        loading.close();
                        EleMessage.success(msg);
                        reload();
                      })
                      .catch((e) => {
                        loading.close();
                        EleMessage.error(e.message);
                      });
            })
            .catch(() => {});
  };
</script>

<script>
  export default {
    name: '${className}'
  };
</script>

<style scoped>
  .el-link {
    font-weight: unset !important;
  }
</style>