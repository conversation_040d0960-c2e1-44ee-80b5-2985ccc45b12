package ${package}.controller;

import com.payne.core.annotation.OperationLog;
import com.payne.core.web.BaseController;
import com.payne.core.web.PageParam;
import com.payne.core.web.PageResult;
import ${package}.entity.${className};
import ${package}.param.${className}Param;
import ${package}.service.${className}Service;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ${tableComment}控制器
 *
 * <AUTHOR>
 * @since ${datetime}
 */
#set($classNameLower = $className.substring(0,1).toLowerCase() + $className.substring(1))
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/${moduleName}/${className}")
public class ${className}Controller extends BaseController {
    private final ${className}Service ${classNameLower}Service;

    /**
     * 分页查询${tableComment}（权限标识：${moduleName}:${className}:list）
     */
//    @PreAuthorize("hasAuthority('${moduleName}:${className}:list')")
    @GetMapping("/page")
    public PageResult<${className}> page(${className}Param param) {
        PageParam<${className}, ${className}Param> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        page = ${classNameLower}Service.page(page, page.getWrapper());
        return new PageResult<>(page.getRecords(), page.getTotal());
    }

    /**
     * 查询全部${tableComment}（权限标识：${moduleName}:${className}:list）
     */
//    @PreAuthorize("hasAuthority('${moduleName}:${className}:list')")
    @GetMapping()
    public List<${className}> list(${className}Param param) {
        PageParam<${className}, ${className}Param> page = new PageParam<>(param);
        //page.setDefaultOrder("create_time desc");
        return ${classNameLower}Service.list(page.getOrderWrapper());
    }

    /**
     * 根据id查询${tableComment}（权限标识：${moduleName}:${className}:list）
     */
//    @PreAuthorize("hasAuthority('${moduleName}:${className}:list')")
    @GetMapping("/{id}")
    public ${className} get(@PathVariable("id") String id) {
        return ${classNameLower}Service.getById(id);
    }

    /**
     * 添加或修改${tableComment}（权限标识：${moduleName}:${className}:operation）
     */
//    @PreAuthorize("hasAuthority('${moduleName}:${className}:operation')")
    @OperationLog(module = "${tableComment}", comments = "保存${tableComment}")
    @PostMapping("/operation")
    public void save(@RequestBody ${className} ${classNameLower}) {
        if (StringUtils.hasLength(${classNameLower}.getId())) {
            ${classNameLower}Service.updateById(${classNameLower});
        } else {
            ${classNameLower}Service.save(${classNameLower});
        }
    }

    /**
     * 批量删除${tableComment}（权限标识：${moduleName}:${className}:remove）
     */
//    @PreAuthorize("hasAuthority('${moduleName}:${className}:remove')")
    @OperationLog(module = "${tableComment}", comments = "批量删除${tableComment}")
    @PostMapping("/remove")
    public void remove(@RequestBody List<String> ids) {
        ${classNameLower}Service.removeByIds(ids);
    }
} 