package ${package}.param;

import com.payne.core.web.BaseParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ${tableComment} 参数
 *
 * <AUTHOR>
 * @since ${datetime}
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ${className}Param extends BaseParam {
#foreach ($column in $columns)

    /**
     * $!{column.columnComment}
     */
    private ${column.attrType} ${column.attrname};
#end
} 