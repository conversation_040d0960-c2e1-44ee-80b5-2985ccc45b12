package ${package}.service.impl;

import ${package}.entity.${className};
import ${package}.mapper.${className}Mapper;
import ${package}.param.${className}Param;
import ${package}.service.${className}Service;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * ${tableComment} Service实现
 *
 * <AUTHOR>
 * @since ${datetime}
 */
@RequiredArgsConstructor
@Service
public class ${className}ServiceImpl extends ServiceImpl<${className}Mapper, ${className}> implements ${className}Service {

    private final ${className}Mapper mapper;


} 