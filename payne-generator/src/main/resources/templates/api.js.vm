import request from '@/utils/request';

/**
 * 查询不分页${tableComment}列表
 */
export async function query(params) {
  const res = await request.get('/${moduleName}/${className}', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询${tableComment}
 */
export async function queryPage(params) {
  const res = await request.get('/${moduleName}/${className}/page', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 根据id查询${tableComment}
 */
export async function getById(id) {
  const res = await request.get('/${moduleName}/${className}/' + id);
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 添加&修改${tableComment}
 */
export async function operation(data) {
  const res = await request.post('/${moduleName}/${className}/operation', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除${tableComment}
 */
export async function removes(data) {
  const res = await request.post('/${moduleName}/${className}/remove', data);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
} 