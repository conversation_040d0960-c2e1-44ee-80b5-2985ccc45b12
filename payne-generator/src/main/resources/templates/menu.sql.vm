-- 菜单SQL
INSERT INTO SYS_MENU (
    MENU_ID,
    PARENT_ID,
    TITLE,
    PATH,
    COMPONENT,
    MENU_TYPE,
    SORT_NUMBER,
    AUTHORITY,
    ICON,
    HIDE,
    PATH_NAME,
    DELETED,
    CREATE_TIME,
    UPDATE_TIME
) VALUES (
    sys_guid(), -- 菜单ID
    '0',  -- 父级ID, 0为顶级
    '${tableComment}', -- 菜单名称
    '/${moduleName}/$className.toLowerCase()', -- 路由地址
    '/${moduleName}/$className.toLowerCase()/index', -- 组件地址
    1, -- 菜单类型(0目录,1菜单,2按钮)
    999, -- 排序号
    '${moduleName}:$className.toLowerCase():list', -- 权限标识
    '', -- 图标
    0, -- 是否隐藏
    '$className.toLowerCase()', -- 路由别名
    0, -- 是否删除
    sysdate, -- 创建时间
    sysdate  -- 修改时间
);

-- 获取菜单ID
DECLARE
    v_menu_id VARCHAR2(50);
BEGIN
    SELECT MENU_ID INTO v_menu_id FROM SYS_MENU
    WHERE TITLE = '${tableComment}管理' AND DELETED = 0 AND ROWNUM = 1;

    -- 按钮SQL
    -- 查询按钮
    INSERT INTO SYS_MENU (
        MENU_ID, PARENT_ID, TITLE, MENU_TYPE, SORT_NUMBER, 
        AUTHORITY, DELETED, CREATE_TIME, UPDATE_TIME
    ) VALUES (
        sys_guid(), v_menu_id, '查询${tableComment}', 2, 1,
        '${moduleName}:$className.toLowerCase():list', 0, sysdate, sysdate
    );

    -- 添加按钮
    INSERT INTO SYS_MENU (
        MENU_ID, PARENT_ID, TITLE, MENU_TYPE, SORT_NUMBER, 
        AUTHORITY, DELETED, CREATE_TIME, UPDATE_TIME
    ) VALUES (
        sys_guid(), v_menu_id, '添加${tableComment}', 2, 2,
        '${moduleName}:$className.toLowerCase():add', 0, sysdate, sysdate
    );

    -- 修改按钮
    INSERT INTO SYS_MENU (
        MENU_ID, PARENT_ID, TITLE, MENU_TYPE, SORT_NUMBER, 
        AUTHORITY, DELETED, CREATE_TIME, UPDATE_TIME
    ) VALUES (
        sys_guid(), v_menu_id, '修改${tableComment}', 2, 3,
        '${moduleName}:$className.toLowerCase():update', 0, sysdate, sysdate
    );

    -- 删除按钮
    INSERT INTO SYS_MENU (
        MENU_ID, PARENT_ID, TITLE, MENU_TYPE, SORT_NUMBER, 
        AUTHORITY, DELETED, CREATE_TIME, UPDATE_TIME
    ) VALUES (
        sys_guid(), v_menu_id, '删除${tableComment}', 2, 4,
        '${moduleName}:$className.toLowerCase():delete', 0, sysdate, sysdate
    );
END;
/ 