<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form
      size="small"
      label-width="72px"
      @keyup.enter="search"
      @submit.prevent=""
    >
      <el-row :gutter="8">
        <!-- 搜索条件 -->
#foreach($column in $columns)
#if($column.isQuery)
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="${column.columnComment}">
            <el-input
              v-model.trim="form.${column.attrname}"
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
#end
#end
#set($queryCount = 0)
#foreach($column in $columns)
#if($column.isQuery)
#set($queryCount = $queryCount + 1)
#end
#end
#set($remainingCols = 4 - $queryCount)
#set($lastColSpan = 12 + $remainingCols * 6)
        <el-col :lg="$lastColSpan" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import {useFormData} from '@/utils/use-form-data';

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
#set($last = $columns.size() - 1)
#foreach($column in $columns)
#if($foreach.index == $last)
    ${column.attrname}: ''
#else
    ${column.attrname}: '',
#end
#end
  });

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /** 重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script> 